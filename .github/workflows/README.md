# 🚀 CI/CD Workflows Guide

## Overview

This repository implements a smart CI/CD system that **automatically deploys only when you want it to**, not on every push. This gives you the flexibility to iterate quickly while maintaining production stability.

## 📋 Available Workflows

### 1. **Continuous Integration** (`ci.yml`)
**Triggers**: Every push to `main`, `master`, `develop`, `feature/*` branches and all PRs

**Purpose**: Quality gates and testing without deployment
- ✅ Backend linting (must pass)
- ✅ Backend tests (must pass) 
- ⚠️ Frontend tests (warnings allowed)
- ✅ Docker build verification
- 🔒 Security scanning
- 📊 Coverage reporting

### 2. **Production Deployment** (`deploy.yml`) 
**Triggers**: 
- Push to `release/*` or `production` branches
- Manual trigger via GitHub Actions UI

**Purpose**: Full production deployment with quality gates
- 🔍 Backend linting + testing (required)
- 🐳 Docker build + push to Artifact Registry
- ☁️ Deploy to Google Cloud Run
- 🩺 Health verification + rollback on failure
- 📝 Deployment summary with URLs

### 3. **Backend-Only Deployment** (`deploy-backend-only.yml`)
**Triggers**: Manual only (GitHub Actions UI)

**Purpose**: Deploy just the backend (when frontend has issues)
- 🔍 Quick backend verification
- 🐳 Backend-only Docker build + deploy
- ✅ Perfect for current state (frontend has TypeScript warnings)

## 🎯 Smart Triggering Strategy

### ✅ **Development Pushes** (No Deployment)
```bash
git push origin main           # ✅ CI tests only, no deployment
git push origin feature/auth   # ✅ CI tests only, no deployment  
git push origin develop        # ✅ CI tests only, no deployment
```

### 🚀 **Release Pushes** (Auto Deployment)
```bash
# Create release branch for deployment
git checkout -b release/v1.0.20250616
git push origin release/v1.0.20250616    # 🚀 Triggers auto-deployment!
```

### 🎛️ **Manual Deployment**
1. Go to **GitHub → Actions → Deploy to Production**
2. Click **"Run workflow"**
3. Choose environment (production/staging)
4. Click **"Run workflow"** button

## 📊 Quality Gates

### Backend (Required)
- ✅ Ruff linting (0 errors)
- ✅ Unit tests (pytest)
- ✅ Docker build success
- ✅ Health check passes

### Frontend (Non-blocking)
- ⚠️ TypeScript warnings allowed (known issue)
- ⚠️ Tests can fail (functionality verified manually)

## 🔧 Required GitHub Secrets

Add these secrets in **Repository Settings → Secrets → Actions**:

```
GCP_SA_KEY            # Service account JSON for Google Cloud
```

**Note**: Project ID and region are hardcoded in workflows for consistency.

## 🌍 Environment Management

### Production
- **Project**: `rezolve-poc`
- **Region**: `us-central1`
- **Service**: `giki-ai-api`
- **URL**: Provided in deployment output

### Staging
- Same infrastructure, different environment variables
- Use manual deployment with "staging" option

## 📖 Common Workflows

### 🔥 Quick Backend Deployment (Current Recommended)
```bash
# Option 1: Manual trigger (immediate)
# Go to GitHub Actions → "Deploy Backend Only" → Run workflow

# Option 2: Release branch (automatic)  
git checkout -b release/backend-$(date +%Y%m%d)
git push origin release/backend-$(date +%Y%m%d)
```

### 🚀 Full Production Release
```bash
# When frontend TypeScript issues are fixed
git checkout -b release/v1.0.$(date +%Y%m%d)
git push origin release/v1.0.$(date +%Y%m%d)
```

### 🧪 Testing Changes
```bash
# Creates PR with CI checks
git checkout -b feature/new-feature
git push origin feature/new-feature
# Create PR → CI runs automatically
```

## 🔍 Monitoring Deployments

### GitHub Actions
- **Actions tab** → View workflow runs
- **Environment** → View deployment history
- **Summary** → Quick links to deployed services

### Google Cloud
```bash
# View deployment logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=giki-ai-api" --limit 50

# Check service status  
gcloud run services describe giki-ai-api --region=us-central1

# View service URL
gcloud run services describe giki-ai-api --region=us-central1 --format='value(status.url)'
```

## 🆘 Troubleshooting

### Deployment Fails
1. **Check logs** in GitHub Actions workflow run
2. **Automatic rollback** triggers on health check failure
3. **Manual rollback**:
   ```bash
   gcloud run services update-traffic giki-ai-api \
     --to-revisions=PREVIOUS_REVISION=100 \
     --region=us-central1
   ```

### CI Fails
1. **Backend linting**: Must be fixed (blocking)
2. **Frontend warnings**: Non-blocking, known issue
3. **Tests**: Backend required, frontend optional

### Secret Issues
1. Verify `GCP_SA_KEY` secret exists
2. Check service account has proper permissions
3. Ensure Secret Manager contains Supabase keys

## 📈 Benefits of This Setup

✅ **Controlled Deployment**: Only release branches auto-deploy  
✅ **Development Freedom**: Regular pushes don't trigger deployment  
✅ **Manual Override**: Always available via GitHub UI  
✅ **Quality Gates**: Linting + tests must pass  
✅ **Fast Rollback**: Automated failure detection  
✅ **Flexibility**: Backend-only option available  
✅ **Monitoring**: Comprehensive logging and status reporting

## 🎉 Success! 

Your CI/CD pipeline is now ready. The system will:
- ✅ Test every push (quality gates)
- 🚀 Deploy only when you want (release branches)
- 🔄 Rollback automatically on failure
- 📊 Provide detailed deployment summaries

Ready to deploy? Create a release branch and push! 🚀