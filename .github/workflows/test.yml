name: Run Tests

on:
  pull_request:
  workflow_run:
    workflows: ["Deploy to Production"]
    types:
      - completed
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - backend
          - frontend
          - e2e

jobs:
  notify-start:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    steps:
      - name: Notify test start
        run: |
          echo "## 🧪 Post-Deployment Tests Starting" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Deployment completed successfully. Running automated tests..." >> $GITHUB_STEP_SUMMARY
          echo "Triggered by: ${{ github.event.workflow_run.name }} #${{ github.event.workflow_run.run_number }}" >> $GITHUB_STEP_SUMMARY

  test-backend:
    if: ${{ github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'backend' || github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Run backend linting
        run: |
          cd apps/giki-ai-api
          uv sync
          uv add --dev ruff
          uv run ruff check src/

      - name: Run backend tests
        run: |
          cd apps/giki-ai-api
          uv run pytest tests/ -v --tb=short

  test-frontend:
    if: ${{ github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'frontend' || github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        run: npm install -g pnpm@latest

      - name: Install dependencies
        run: pnpm install

      - name: Run frontend linting
        run: pnpm lint:app
        continue-on-error: true  # Known TypeScript warnings

      - name: Run frontend tests
        run: pnpm test:app

  test-e2e:
    if: ${{ github.event.inputs.test_type == 'all' || github.event.inputs.test_type == 'e2e' }}
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        run: npm install -g pnpm@latest

      - name: Install dependencies
        run: pnpm install

      - name: Install Playwright
        run: pnpm exec playwright install --with-deps

      - name: Run E2E tests
        run: pnpm test:e2e
        env:
          BACKEND_URL: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
          FRONTEND_URL: https://app-giki-ai.web.app