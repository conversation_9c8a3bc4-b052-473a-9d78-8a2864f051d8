name: Deploy to Production

on:
  push:
    branches: 
      - main
      - master
      - release/*
      - production
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
      skip_backend:
        description: 'Skip backend deployment'
        required: false
        default: false
        type: boolean
      skip_frontend:
        description: 'Skip frontend deployment'
        required: false
        default: false
        type: boolean

env:
  PROJECT_ID: rezolve-poc
  REGION: us-central1
  SERVICE_NAME: giki-ai-api
  REGISTRY: us-central1-docker.pkg.dev

jobs:
  deploy-backend:
    if: ${{ github.event.inputs.skip_backend != 'true' }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'production' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud
        run: gcloud auth configure-docker ${{ env.REGISTRY }} --quiet

      - name: Quick backend verification
        run: |
          cd apps/giki-ai-api
          uv sync
          echo "✅ Dependencies installed"

      - name: Generate image tag
        id: image_tag
        run: |
          if [[ "${{ github.ref }}" == refs/tags/* ]]; then
            TAG=${GITHUB_REF#refs/tags/}
          else
            TAG=$(date +%Y%m%d-%H%M%S)-${GITHUB_SHA::7}
          fi
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          echo "image=${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/giki-ai/${{ env.SERVICE_NAME }}:$TAG" >> $GITHUB_OUTPUT

      - name: Build Docker image
        run: |
          docker build -f apps/giki-ai-api/Dockerfile \
            -t ${{ steps.image_tag.outputs.image }} \
            -t ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/giki-ai/${{ env.SERVICE_NAME }}:latest \
            .

      - name: Push Docker image
        run: |
          docker push ${{ steps.image_tag.outputs.image }}
          docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/giki-ai/${{ env.SERVICE_NAME }}:latest

      - name: Deploy to Cloud Run
        id: deploy
        run: |
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --image ${{ steps.image_tag.outputs.image }} \
            --region ${{ env.REGION }} \
            --project ${{ env.PROJECT_ID }} \
            --service-account cloud-run-app@${{ env.PROJECT_ID }}.iam.gserviceaccount.com \
            --set-secrets="DATABASE_URL=database-url:latest,SECRET_KEY=secret-key:latest,AUTH_SECRET_KEY=auth-secret-key:latest,ADMIN_API_KEY=admin-api-key:latest" \
            --set-env-vars="GCP_PROJECT_ID=${{ env.PROJECT_ID }},VERTEX_AI_LOCATION=${{ env.REGION }},ENVIRONMENT=production,CORS_ALLOWED_ORIGINS=https://app-giki-ai.web.app" \
            --allow-unauthenticated \
            --memory=2Gi \
            --cpu=2 \
            --min-instances=0 \
            --max-instances=10 \
            --timeout=3600 \
            --format=json > deployment_result.json
          
          # Extract service URL
          SERVICE_URL=$(jq -r '.status.url' deployment_result.json)
          echo "service_url=$SERVICE_URL" >> $GITHUB_OUTPUT

      - name: Verify deployment
        run: |
          echo "🔍 Verifying deployment health..."
          SERVICE_URL="${{ steps.deploy.outputs.service_url }}"
          
          # Wait for service to be ready
          sleep 30
          
          # Check health endpoint
          HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health" || echo "000")
          
          if [ "$HTTP_STATUS" = "200" ]; then
            echo "✅ Deployment successful! Service is healthy."
            echo "🌐 Service URL: $SERVICE_URL"
            echo "📊 Health: $SERVICE_URL/health"
            echo "📖 Docs: $SERVICE_URL/docs"
          else
            echo "❌ Deployment verification failed! HTTP status: $HTTP_STATUS"
            echo "🔍 Checking logs..."
            gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${{ env.SERVICE_NAME }}" --limit 10 --project ${{ env.PROJECT_ID }}
            exit 1
          fi

      - name: Rollback on failure
        if: failure()
        run: |
          echo "🚨 Deployment failed! Attempting rollback..."
          
          # Get previous revision
          PREVIOUS_REVISION=$(gcloud run revisions list \
            --service=${{ env.SERVICE_NAME }} \
            --region=${{ env.REGION }} \
            --project=${{ env.PROJECT_ID }} \
            --format="value(metadata.name)" \
            --limit=2 | tail -n 1)
          
          if [ -n "$PREVIOUS_REVISION" ]; then
            echo "🔄 Rolling back to revision: $PREVIOUS_REVISION"
            gcloud run services update-traffic ${{ env.SERVICE_NAME }} \
              --to-revisions=$PREVIOUS_REVISION=100 \
              --region=${{ env.REGION }} \
              --project=${{ env.PROJECT_ID }}
            echo "✅ Rollback completed"
          else
            echo "⚠️ No previous revision found for rollback"
          fi

      - name: Backend deployment summary
        run: |
          echo "## 🚀 Backend Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Status**: ✅ Deployed successfully" >> $GITHUB_STEP_SUMMARY
          echo "**Service URL**: ${{ steps.deploy.outputs.service_url }}" >> $GITHUB_STEP_SUMMARY
          echo "**Health Check**: ${{ steps.deploy.outputs.service_url }}/health" >> $GITHUB_STEP_SUMMARY
          echo "**API Docs**: ${{ steps.deploy.outputs.service_url }}/docs" >> $GITHUB_STEP_SUMMARY

  deploy-frontend:
    if: ${{ github.event.inputs.skip_frontend != 'true' }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'production' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        run: npm install -g pnpm@latest

      - name: Install dependencies
        run: pnpm install

      - name: Build frontend
        run: pnpm nx build giki-ai-app --configuration=production
        continue-on-error: true  # Allow build to continue with warnings

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.GCP_SA_KEY }}'
          channelId: live
          projectId: ${{ env.PROJECT_ID }}
          target: giki-app

      - name: Frontend deployment summary
        run: |
          echo "## 🚀 Frontend Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Status**: ✅ Deployed successfully" >> $GITHUB_STEP_SUMMARY
          echo "**App URL**: https://app-giki-ai.web.app" >> $GITHUB_STEP_SUMMARY