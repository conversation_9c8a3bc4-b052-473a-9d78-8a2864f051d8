#!/usr/bin/env python3
"""Fix username field for test users."""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# Load environment
load_dotenv(".env.development")

DATABASE_URL = os.getenv("DATABASE_URL", "").replace("postgresql+asyncpg://", "postgresql://")


async def fix_usernames():
    """Update username field to match email for test users."""
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Update usernames to match emails for test users
        result = await conn.execute("""
            UPDATE users 
            SET username = email 
            WHERE email LIKE '%@test.local' AND (username IS NULL OR username = '')
        """)
        print(f"✅ Updated {result} test user usernames")
        
        # Verify the update
        users = await conn.fetch("""
            SELECT id, email, username, is_admin, is_superuser
            FROM users 
            WHERE email LIKE '%@test.local'
            ORDER BY email
        """)
        
        print("\n📋 Test Users:")
        for user in users:
            print(f"  - {user['email']} (username: {user['username']}, admin: {user['is_admin']}, super: {user['is_superuser']})")
            
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(fix_usernames())