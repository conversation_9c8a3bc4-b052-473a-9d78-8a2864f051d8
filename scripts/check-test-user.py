#!/usr/bin/env python3
"""Check test user details and reset password if needed."""

import asyncio
import asyncpg
import os
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def check_and_reset_test_user():
    """Check test user and optionally reset password."""
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")
    
    # Convert to asyncpg format
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgres://", 1)
    
    print(f"Connecting to database...")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        
        # Check test user
        test_email = "<EMAIL>"
        user = await conn.fetchrow(
            "SELECT id, email, hashed_password, tenant_id, is_active FROM users WHERE email = $1",
            test_email
        )
        
        if user:
            print(f"✅ Test user found:")
            print(f"   Email: {user['email']}")
            print(f"   ID: {user['id']}")
            print(f"   Tenant ID: {user['tenant_id']}")
            print(f"   Active: {user['is_active']}")
            print(f"   Password hash: {user['hashed_password'][:20]}...")
            
            # Reset password
            new_password = "TestPassword123!"
            new_hash = pwd_context.hash(new_password)
            
            await conn.execute(
                "UPDATE users SET hashed_password = $1, updated_at = $2 WHERE id = $3",
                new_hash,
                datetime.utcnow(),
                user['id']
            )
            
            print(f"\n✅ Password reset successfully!")
            print(f"   New password: {new_password}")
            
            # Verify the password works
            if pwd_context.verify(new_password, new_hash):
                print(f"   Password verification: PASSED")
            else:
                print(f"   Password verification: FAILED")
                
        else:
            print(f"❌ Test user not found: {test_email}")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Checking and Resetting Test User Password")
    print("=" * 60)
    asyncio.run(check_and_reset_test_user())