#!/bin/bash

# Deploy both API and Frontend to production
# This script uses the appropriate service accounts for each service

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="rezolve-poc"
REGION="us-central1"
API_SERVICE_NAME="giki-ai-api"
API_SERVICE_ACCOUNT="dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com"
FIREBASE_SERVICE_ACCOUNT_PATH="service-accounts/firebase-admin-key.json"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Check if running from workspace root
if [ ! -f "package.json" ] || [ ! -d "apps/giki-ai-api" ] || [ ! -d "apps/giki-ai-app" ]; then
    print_error "This script must be run from the workspace root directory"
    exit 1
fi

# Check if Firebase service account exists
if [ ! -f "$FIREBASE_SERVICE_ACCOUNT_PATH" ]; then
    print_error "Firebase service account not found at $FIREBASE_SERVICE_ACCOUNT_PATH"
    exit 1
fi

# Parse command line arguments
SKIP_API=false
SKIP_FRONTEND=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-api)
            SKIP_API=true
            shift
            ;;
        --skip-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Usage: $0 [--skip-api] [--skip-frontend]"
            exit 1
            ;;
    esac
done

# Start deployment
print_status "Starting unified deployment..."
print_status "Project: $PROJECT_ID"
print_status "Region: $REGION"

# Check current gcloud account
CURRENT_ACCOUNT=$(gcloud config get-value account 2>/dev/null)
print_status "Using Google Cloud account: $CURRENT_ACCOUNT"

# Deploy API
if [ "$SKIP_API" = false ]; then
    print_status "=== Deploying API to Cloud Run ==="
    
    # Build Docker image
    print_status "Building Docker image..."
    IMAGE_TAG="us-central1-docker.pkg.dev/${PROJECT_ID}/giki-ai/${API_SERVICE_NAME}:$(date +%Y%m%d-%H%M%S)"
    
    docker buildx build --platform linux/amd64 \
        -f Dockerfile \
        -t "$IMAGE_TAG" \
        -t "us-central1-docker.pkg.dev/${PROJECT_ID}/giki-ai/${API_SERVICE_NAME}:latest" \
        . || {
        print_error "Docker build failed"
        exit 1
    }
    
    # Push to Artifact Registry
    print_status "Pushing Docker image to Artifact Registry..."
    docker push "$IMAGE_TAG" || {
        print_error "Docker push failed"
        exit 1
    }
    docker push "us-central1-docker.pkg.dev/${PROJECT_ID}/giki-ai/${API_SERVICE_NAME}:latest"
    
    # Deploy to Cloud Run
    print_status "Deploying to Cloud Run..."
    gcloud run deploy "$API_SERVICE_NAME" \
        --image "$IMAGE_TAG" \
        --region "$REGION" \
        --project "$PROJECT_ID" \
        --service-account "$API_SERVICE_ACCOUNT" \
        --update-secrets="/secrets/service-account.json=dev-service-account-key:latest,DATABASE_URL=database-url:latest,SECRET_KEY=secret-key:latest,AUTH_SECRET_KEY=auth-secret-key:latest,ADMIN_API_KEY=admin-api-key:latest" \
        --set-env-vars="GCP_PROJECT_ID=${PROJECT_ID},VERTEX_AI_LOCATION=${REGION},ENVIRONMENT=production,CORS_ALLOWED_ORIGINS=https://app-giki-ai.web.app" \
        --memory=2Gi \
        --cpu=2 \
        --min-instances=0 \
        --max-instances=10 \
        --timeout=3600 \
        --allow-unauthenticated || {
        print_error "Cloud Run deployment failed"
        exit 1
    }
    
    # Get service URL
    API_URL=$(gcloud run services describe "$API_SERVICE_NAME" --region="$REGION" --format="value(status.url)")
    print_status "API deployed to: $API_URL"
else
    print_warning "Skipping API deployment"
fi

# Deploy Frontend
if [ "$SKIP_FRONTEND" = false ]; then
    print_status "=== Deploying Frontend to Firebase ==="
    
    # Build frontend
    print_status "Building frontend..."
    cd apps/giki-ai-app
    pnpm build || {
        print_error "Frontend build failed"
        exit 1
    }
    cd ../..
    
    # Deploy to Firebase
    print_status "Deploying to Firebase Hosting..."
    cd apps/giki-ai-app
    GOOGLE_APPLICATION_CREDENTIALS="../../${FIREBASE_SERVICE_ACCOUNT_PATH}" \
        firebase deploy --only hosting --project "$PROJECT_ID" || {
        print_error "Firebase deployment failed"
        exit 1
    }
    cd ../..
    
    print_status "Frontend deployed to: https://app-giki-ai.web.app"
else
    print_warning "Skipping frontend deployment"
fi

# Summary
print_status "=== Deployment Complete ==="
print_status "✅ API: ${API_URL:-https://giki-ai-api-6uyufgxcxa-uc.a.run.app}"
print_status "✅ Frontend: https://app-giki-ai.web.app"

# Verify deployments
if [ "$SKIP_API" = false ]; then
    print_status "Verifying API health..."
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "${API_URL}/health" || echo "000")
    if [ "$HTTP_STATUS" = "200" ]; then
        print_status "✅ API health check passed"
    else
        print_error "API health check failed with status: $HTTP_STATUS"
        exit 1
    fi
fi

print_status "All deployments completed successfully!"