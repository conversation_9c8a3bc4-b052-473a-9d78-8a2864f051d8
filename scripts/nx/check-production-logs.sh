#!/bin/bash

# Check production logs using service accounts

set -e

SERVICE=$1
LIMIT=${2:-50}

if [[ -z "$SERVICE" ]]; then
    echo "Usage: $0 <api|frontend> [limit]"
    exit 1
fi

if [[ "$SERVICE" == "api" ]]; then
    echo "Fetching Cloud Run logs for giki-ai-api..."
    
    # Activate service account temporarily
    export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/service-accounts/production/service-account.json"
    
    # Use gcloud with service account
    gcloud auth activate-service-account --key-file="$GOOGLE_APPLICATION_CREDENTIALS" --quiet 2>/dev/null
    
    # Fetch logs
    gcloud logging read \
        'resource.type="cloud_run_revision" AND resource.labels.service_name="giki-ai-api" AND NOT textPayload=~"health"' \
        --limit=$LIMIT \
        --format='value(timestamp,textPayload)' \
        --project=rezolve-poc \
        2>/dev/null | grep -v "^$" || echo "No recent API logs found"
    
    # Revert to user's auth
    gcloud config unset auth/impersonate_service_account 2>/dev/null || true
    
elif [[ "$SERVICE" == "frontend" ]]; then
    echo "Fetching Firebase logs..."
    
    # Firebase hosting logs aren't available via API, but we can check Cloud CDN logs
    export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/service-accounts/firebase-admin-key.json"
    
    gcloud auth activate-service-account --key-file="$GOOGLE_APPLICATION_CREDENTIALS" --quiet 2>/dev/null
    
    # Try to get any HTTP load balancer logs (Firebase uses Cloud CDN)
    gcloud logging read \
        'resource.type="http_load_balancer"' \
        --limit=$LIMIT \
        --format='value(timestamp,httpRequest.requestUrl,httpRequest.status)' \
        --project=rezolve-poc \
        2>/dev/null | grep -v "^$" || echo "No recent frontend logs found. Check Firebase Console: https://console.firebase.google.com/project/rezolve-poc/hosting/sites/app-giki-ai"
    
    # Revert to user's auth
    gcloud config unset auth/impersonate_service_account 2>/dev/null || true
    
else
    echo "Unknown service: $SERVICE"
    echo "Use 'api' or 'frontend'"
    exit 1
fi