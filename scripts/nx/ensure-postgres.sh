#!/bin/bash

# Ensure PostgreSQL is running for local development
# This script is called automatically when running nx serve commands

set -e

echo "🔍 Checking PostgreSQL status..."

# Check if PostgreSQL is already running
if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    echo "✅ PostgreSQL is already running on port 5432"
    exit 0
fi

echo "🚀 PostgreSQL not running, attempting to start..."

# Check if PostgreSQL is installed via Homebrew
if command -v brew >/dev/null 2>&1; then
    # Try to start PostgreSQL via Homebrew
    if brew services list | grep -q "postgresql@15.*stopped"; then
        echo "📦 Starting PostgreSQL@15 via Homebrew..."
        brew services start postgresql@15
        
        # Wait for PostgreSQL to be ready
        echo "⏳ Waiting for PostgreSQL to be ready..."
        for i in {1..30}; do
            if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                echo "✅ PostgreSQL is now running!"
                exit 0
            fi
            sleep 1
        done
        
        echo "❌ PostgreSQL failed to start within 30 seconds"
        exit 1
        
    elif brew services list | grep -q "postgresql@15.*started"; then
        echo "⏳ PostgreSQL@15 is starting up, waiting..."
        # Wait for it to be ready
        for i in {1..15}; do
            if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                echo "✅ PostgreSQL is now ready!"
                exit 0
            fi
            sleep 1
        done
        
        echo "❌ PostgreSQL is running but not accepting connections"
        exit 1
    fi
    
    # Try other PostgreSQL versions
    for version in postgresql@14 postgresql@13 postgresql; do
        if brew services list | grep -q "$version.*stopped"; then
            echo "📦 Starting $version via Homebrew..."
            brew services start $version
            
            # Wait for PostgreSQL to be ready
            echo "⏳ Waiting for PostgreSQL to be ready..."
            for i in {1..30}; do
                if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                    echo "✅ PostgreSQL ($version) is now running!"
                    exit 0
                fi
                sleep 1
            done
            
            echo "❌ PostgreSQL ($version) failed to start within 30 seconds"
            break
        fi
    done
fi

# Check if PostgreSQL is available via system package manager (apt/yum/etc)
if command -v systemctl >/dev/null 2>&1; then
    echo "🐧 Attempting to start PostgreSQL via systemctl..."
    sudo systemctl start postgresql
    
    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            echo "✅ PostgreSQL is now running!"
            exit 0
        fi
        sleep 1
    done
fi

# If we get here, we couldn't start PostgreSQL
echo "❌ Could not start PostgreSQL automatically"
echo ""
echo "📝 Manual setup required:"
echo "   macOS (Homebrew): brew install postgresql@15 && brew services start postgresql@15"
echo "   Ubuntu/Debian:    sudo apt install postgresql && sudo systemctl start postgresql"
echo "   CentOS/RHEL:      sudo yum install postgresql-server && sudo systemctl start postgresql"
echo ""
echo "💡 After installation, ensure the database is accessible at localhost:5432"

exit 1