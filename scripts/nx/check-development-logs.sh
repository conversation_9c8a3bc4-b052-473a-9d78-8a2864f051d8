#!/bin/bash

# Check development logs

SERVICE=$1
LINES=${2:-50}

if [[ -z "$SERVICE" ]]; then
    echo "Usage: $0 <api|frontend> [lines]"
    exit 1
fi

if [[ "$SERVICE" == "api" ]]; then
    if [[ -f "logs/api-server.log" ]]; then
        tail -n $LINES logs/api-server.log | grep -v "health check" || true
    else
        echo "No API logs found. Start the server first with: pnpm serve:api"
    fi
elif [[ "$SERVICE" == "frontend" ]]; then
    if [[ -f "logs/frontend-server.log" ]]; then
        tail -n $LINES logs/frontend-server.log | grep -v "hmr update\|page reload" || true
    else
        echo "No frontend logs found. Start the server first with: pnpm serve:app"
    fi
else
    echo "Unknown service: $SERVICE"
    echo "Use 'api' or 'frontend'"
    exit 1
fi