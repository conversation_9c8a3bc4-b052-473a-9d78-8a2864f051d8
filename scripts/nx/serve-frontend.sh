#!/bin/bash

# Start frontend server in background with proper logging

# set -e - disable strict error handling for better process management

# Check if production mode requested
MODE=${1:-development}
if [[ "$MODE" == "production" ]]; then
    API_URL="https://giki-ai-api-6uyufgxcxa-uc.a.run.app"
else
    API_URL="http://localhost:8000"
fi

# Ensure directories exist
mkdir -p .run logs

# Rotate logs
./scripts/log-manager.sh rotate frontend-server.log 2>/dev/null || true

# Kill any existing process on port 4200
lsof -t -i tcp:4200 | xargs kill -9 2>/dev/null || true

# Start frontend server in background (from workspace root)
(
    cd apps/giki-ai-app && \
    VITE_API_BASE_URL=$API_URL \
    VITE_ENVIRONMENT=$MODE \
    npx vite dev --port 4200 --host 0.0.0.0 2>&1
) > logs/frontend-server.log 2>&1 &

# Save PID
echo $! > .run/frontend.pid

# Wait and check
sleep 2
if ps -p $(cat .run/frontend.pid) > /dev/null 2>&1; then
    echo "✅ Frontend server started in background (PID: $(cat .run/frontend.pid))"
else
    echo "❌ Failed to start frontend server"
    exit 1
fi