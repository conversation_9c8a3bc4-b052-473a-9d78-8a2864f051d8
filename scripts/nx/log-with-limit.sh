#!/bin/bash

# Real-time line-limited logger
# Maintains exactly 1000 lines in log file while streaming to both terminal and file

LOG_FILE="$1"
MAX_LINES="${2:-1000}"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Create named pipe for real-time processing
PIPE_FILE="/tmp/log_pipe_$$"
mkfifo "$PIPE_FILE"

# Background process to maintain line limit
{
    while IFS= read -r line; do
        # Add timestamp and write to both terminal and file
        TIMESTAMPED_LINE="[$(date '+%Y-%m-%d %H:%M:%S')] $line"
        echo "$TIMESTAMPED_LINE"
        echo "$TIMESTAMPED_LINE" >> "$LOG_FILE"
        
        # Keep only last N lines in real-time
        if [ $(wc -l < "$LOG_FILE" 2>/dev/null || echo 0) -gt "$MAX_LINES" ]; then
            tail -n "$MAX_LINES" "$LOG_FILE" > "$LOG_FILE.tmp" && mv "$LOG_FILE.tmp" "$LOG_FILE"
        fi
    done < "$PIPE_FILE"
} &

# Redirect stdin to pipe and clean up on exit
cat > "$PIPE_FILE" &
LOGGER_PID=$!

# Cleanup function
cleanup() {
    kill $LOGGER_PID 2>/dev/null || true
    rm -f "$PIPE_FILE"
}

trap cleanup EXIT INT TERM

# Wait for logger to finish
wait $LOGGER_PID