#!/bin/bash

# Start API server in background with proper logging

# set -e - disable strict error handling for better process management

# Check if production mode requested
MODE=${1:-development}
WORKERS=1
if [[ "$MODE" == "production" ]]; then
    WORKERS=4
fi

# Ensure directories exist
mkdir -p .run logs

# Rotate logs
./scripts/log-manager.sh rotate api-server.log 2>/dev/null || true

# Kill any existing process on port 8000
lsof -t -i tcp:8000 | xargs kill -9 2>/dev/null || true

# Start API server in background (from workspace root)
(
    cd apps/giki-ai-api && \
    uv build && \
    uv run --no-dev --frozen uvicorn giki_ai_api.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers $WORKERS \
        --log-level error 2>&1
) > logs/api-server.log 2>&1 &

# Save PID
echo $! > .run/api.pid

# Wait and check
sleep 2
if ps -p $(cat .run/api.pid) > /dev/null 2>&1; then
    echo "✅ API server started in background (PID: $(cat .run/api.pid))"
else
    echo "❌ Failed to start API server"
    exit 1
fi