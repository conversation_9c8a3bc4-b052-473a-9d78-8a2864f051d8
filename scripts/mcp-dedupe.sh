#!/bin/bash
# MCP Server Deduplication - only keep one instance of each MCP server type

echo "🔍 Checking for duplicate MCP servers..."

# Find duplicate MCP servers and kill older ones
for server_type in "mcp-server-github" "context7-mcp" "mcp-server-postgres" "mcp-server-filesystem" "mcp-server-playwright" "nx-mcp"; do
    PIDS=$(pgrep -f "$server_type" | head -n -1)  # All but the newest
    if [ ! -z "$PIDS" ]; then
        echo "🧹 Killing duplicate $server_type processes: $PIDS"
        echo "$PIDS" | xargs kill 2>/dev/null || true
    fi
done

echo "✅ MCP server deduplication complete"