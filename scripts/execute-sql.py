#!/usr/bin/env python3
"""
SQL Execution Helper for Database Cleanup
Purpose: Execute SQL scripts against Supabase database safely
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

import asyncpg
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(Path(__file__).parent.parent / "apps" / "giki-ai-api" / ".env.development")

async def execute_sql_file(sql_file_path: str, confirm: bool = True):
    """Execute a SQL file against the Supabase database."""
    
    # Confirm execution
    if confirm:
        print(f"\n🔍 About to execute: {sql_file_path}")
        print("⚠️  This will modify the database. Are you sure? (y/N): ", end="")
        response = input().strip().lower()
        if response != 'y':
            print("❌ Execution cancelled by user")
            return False
    
    # Read SQL file
    try:
        with open(sql_file_path, 'r') as f:
            sql_content = f.read()
    except FileNotFoundError:
        logger.error(f"SQL file not found: {sql_file_path}")
        return False
    
    # Database connection
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        logger.error("DATABASE_URL not found in environment variables")
        return False
    
    # Convert asyncpg URL format and remove unsupported parameters
    if database_url.startswith("postgresql+asyncpg://"):
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    # Remove parameters that asyncpg doesn't support
    if "?" in database_url:
        base_url, params = database_url.split("?", 1)
        # Keep only supported parameters
        supported_params = []
        for param in params.split("&"):
            if not param.startswith(("prepared_statement_cache_size", "statement_cache_size")):
                supported_params.append(param)
        
        if supported_params:
            database_url = f"{base_url}?{'&'.join(supported_params)}"
        else:
            database_url = base_url
    
    logger.info(f"Using database URL: {database_url.split('@')[0]}@[HIDDEN]")
    
    try:
        # Connect to database
        logger.info("Connecting to database...")
        conn = await asyncpg.connect(database_url)
        
        # Execute SQL
        logger.info(f"Executing SQL from {sql_file_path}...")
        
        # Split by semicolon and execute each statement
        statements = [s.strip() for s in sql_content.split(';') if s.strip()]
        
        for i, statement in enumerate(statements):
            if statement and not statement.startswith('--'):
                try:
                    logger.info(f"Executing statement {i+1}/{len(statements)}")
                    result = await conn.fetch(statement)
                    
                    # Print results if it's a SELECT statement
                    if statement.upper().strip().startswith('SELECT'):
                        if result:
                            print(f"\n📊 Query Results ({len(result)} rows):")
                            print("-" * 80)
                            for row in result:
                                row_dict = dict(row)
                                for key, value in row_dict.items():
                                    print(f"  {key}: {value}")
                                print("-" * 40)
                            print()
                        else:
                            print("📭 No results returned\n")
                    
                except Exception as e:
                    logger.error(f"Error executing statement {i+1}: {e}")
                    logger.error(f"Statement: {statement[:100]}...")
                    # Continue with other statements
        
        await conn.close()
        logger.info("✅ SQL execution completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database error: {e}")
        return False

async def main():
    """Main execution function."""
    if len(sys.argv) < 2:
        print("Usage: python execute-sql.py <sql_file_path> [--no-confirm]")
        print("\nAvailable SQL files:")
        scripts_dir = Path(__file__).parent
        for sql_file in scripts_dir.glob("*.sql"):
            print(f"  - {sql_file.name}")
        return
    
    sql_file = sys.argv[1]
    confirm = "--no-confirm" not in sys.argv
    
    # If relative path, make it absolute
    if not os.path.isabs(sql_file):
        sql_file = Path(__file__).parent / sql_file
    
    success = await execute_sql_file(str(sql_file), confirm)
    
    if success:
        print("✅ Script executed successfully!")
    else:
        print("❌ Script execution failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())