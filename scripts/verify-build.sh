#!/bin/bash
# Script to verify the build and deployment setup

set -e

echo "🔍 Verifying Build and Deployment Setup"
echo "======================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    exit 1
fi
echo "✅ Docker is installed"

# Check if nx is available
if ! command -v nx &> /dev/null; then
    echo "⚠️  nx CLI not found globally, using pnpm nx"
    NX_CMD="pnpm nx"
else
    NX_CMD="nx"
fi
echo "✅ Using nx command: $NX_CMD"

# Test backend Docker build
echo ""
echo "🐳 Testing Backend Docker Build..."
echo "---------------------------------"
if $NX_CMD run giki-ai-api:docker:build; then
    echo "✅ Backend Docker build successful"
else
    echo "❌ Backend Docker build failed"
    exit 1
fi

# Test frontend Docker build
echo ""
echo "🐳 Testing Frontend Docker Build..."
echo "----------------------------------"
if $NX_CMD run giki-ai-app:docker:build; then
    echo "✅ Frontend Docker build successful"
else
    echo "❌ Frontend Docker build failed"
    exit 1
fi

# Check if production Dockerfiles exist
echo ""
echo "📄 Checking Production Dockerfiles..."
echo "------------------------------------"
if [ -f "apps/giki-ai-api/Dockerfile.production" ]; then
    echo "✅ Backend production Dockerfile exists"
else
    echo "❌ Backend production Dockerfile missing"
    exit 1
fi

if [ -f "apps/giki-ai-app/Dockerfile.production" ]; then
    echo "✅ Frontend production Dockerfile exists"
else
    echo "❌ Frontend production Dockerfile missing"
    exit 1
fi

# Check nginx configuration
echo ""
echo "🔧 Checking Nginx Configuration..."
echo "---------------------------------"
if [ -f "apps/giki-ai-app/nginx.conf" ]; then
    echo "✅ Frontend nginx.conf exists"
    # Check for http block
    if grep -q "^http {" apps/giki-ai-app/nginx.conf; then
        echo "✅ nginx.conf has proper http block"
    else
        echo "❌ nginx.conf missing http block"
        exit 1
    fi
else
    echo "❌ Frontend nginx.conf missing"
    exit 1
fi

# Check GitHub Actions workflow
echo ""
echo "🚀 Checking GitHub Actions Workflow..."
echo "-------------------------------------"
if [ -f ".github/workflows/deploy.yml" ]; then
    echo "✅ Deploy workflow exists"
    # Check for Docker build steps
    if grep -q "docker/build-push-action" .github/workflows/deploy.yml; then
        echo "✅ Deploy workflow uses Docker build-push-action"
    else
        echo "❌ Deploy workflow missing Docker build-push-action"
        exit 1
    fi
    # Check for Artifact Registry
    if grep -q "docker.pkg.dev" .github/workflows/deploy.yml; then
        echo "✅ Deploy workflow configured for Artifact Registry"
    else
        echo "❌ Deploy workflow not configured for Artifact Registry"
        exit 1
    fi
else
    echo "❌ Deploy workflow missing"
    exit 1
fi

echo ""
echo "✅ All build and deployment checks passed!"
echo ""
echo "📝 Next Steps:"
echo "1. Ensure Google Artifact Registry repository 'giki-ai' exists in project 'rezolve-poc'"
echo "2. Set up required GitHub secrets:"
echo "   - GCP_SERVICE_ACCOUNT"
echo "   - CLOUD_RUN_SERVICE_ACCOUNT"
echo "   - DATABASE_URL"
echo "   - AUTH_SECRET_KEY"
echo "   - ADMIN_API_KEY"
echo "3. Commit with 'deploy:' prefix to trigger deployment"
echo ""