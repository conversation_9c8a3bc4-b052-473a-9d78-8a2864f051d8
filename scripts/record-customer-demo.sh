#!/bin/bash

# Customer Journey Video Recording Script
# =======================================
# 
# This script records the complete customer journey for giki.ai
# platform demonstration. It uses the customer-journey.spec.ts
# tests to create professional video demonstrations.
#
# Usage:
#   ./scripts/record-customer-demo.sh [quick|complete|performance]
#
# Requirements:
#   - Servers must be running (API + Frontend)
#   - Use scripts/server-status.sh to check first

set -e

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

# Default to complete demo if no argument provided
DEMO_TYPE="${1:-complete}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "🎬 Starting Customer Journey Video Recording"
echo "📹 Demo Type: $DEMO_TYPE"
echo "🕐 Timestamp: $TIMESTAMP"

# Check if servers are running
echo "🔍 Checking server status..."
if ! ./scripts/server-status.sh > /dev/null 2>&1; then
    echo "❌ Servers are not running!"
    echo "   Please start servers first using:"
    echo "   pnpm serve"
    exit 1
fi

echo "✅ Servers are running"

# Set up recording environment
export RECORD_VIDEO=true
export PLAYWRIGHT_BASE_URL="http://localhost:4200"

# Create output directory
OUTPUT_DIR="customer-journey-videos"
mkdir -p "$OUTPUT_DIR"

echo "🎥 Starting video recording for customer journey..."

# Run the appropriate test variant
case $DEMO_TYPE in
    "quick")
        echo "📊 Recording Quick Demo (5-minute version)..."
        pnpm playwright test customer-journey.spec.ts --grep "Quick Demo" \
            --project=chromium \
            --headed \
            --output="$OUTPUT_DIR/quick_demo_$TIMESTAMP"
        ;;
    "performance")
        echo "📊 Recording Performance Validation..."
        pnpm playwright test customer-journey.spec.ts --grep "Performance Validation" \
            --project=chromium \
            --headed \
            --output="$OUTPUT_DIR/performance_$TIMESTAMP"
        ;;
    *)
        echo "📊 Recording Complete Customer Journey (20-minute version)..."
        pnpm playwright test customer-journey.spec.ts --grep "Complete Customer Journey" \
            --project=chromium \
            --headed \
            --output="$OUTPUT_DIR/complete_demo_$TIMESTAMP"
        ;;
esac

# Check if recording was successful
if [ $? -eq 0 ]; then
    echo "✅ Video recording completed successfully!"
    echo "📁 Videos saved to: $OUTPUT_DIR/"
    echo "🎬 Demo type: $DEMO_TYPE"
    
    # List generated videos
    echo ""
    echo "Generated videos:"
    find "$OUTPUT_DIR" -name "*.webm" -newer "$OUTPUT_DIR" 2>/dev/null | sort | while read -r video; do
        echo "  📹 $(basename "$video")"
    done
    
    echo ""
    echo "🎯 Next steps:"
    echo "   1. Review videos in $OUTPUT_DIR/"
    echo "   2. Use for customer demonstrations"
    echo "   3. Share with stakeholders"
    
else
    echo "❌ Video recording failed!"
    echo "📝 Check test-results/ for details"
    exit 1
fi

echo ""
echo "🎉 Customer Journey Video Recording Complete!"