#!/bin/bash

# Simple Log Viewer for Fixed-Size Development Logs
# Views the latest logs from API and frontend servers

LOG_DIR="/Users/<USER>/giki-ai-workspace/logs"
FOLLOW_MODE=false

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --follow|-f)
            FOLLOW_MODE=true
            shift
            ;;
        *)
            LOG_TYPE="$1"
            shift
            ;;
    esac
done

LOG_TYPE="${LOG_TYPE:-all}"

show_log() {
    local log_file="$1"
    local log_name="$2"
    local lines="${3:-50}"
    
    if [ -f "$LOG_DIR/$log_file" ]; then
        echo "📋 $log_name"
        echo "$(printf '=%.0s' {1..50})"
        
        if [ "$FOLLOW_MODE" = true ]; then
            echo "Following $log_name (Ctrl+C to stop)..."
            tail -f "$LOG_DIR/$log_file"
        else
            echo "Last $lines lines:"
            tail -n "$lines" "$LOG_DIR/$log_file"
        fi
        echo ""
    else
        echo "⚠️  $log_name: No logs found"
        echo "   Expected: $LOG_DIR/$log_file"
        echo ""
    fi
}

show_server_status() {
    echo "🔧 Development Server Status"
    echo "$(printf '=%.0s' {1..50})"
    
    # Check API server
    if lsof -i :8000 > /dev/null 2>&1; then
        API_PID=$(lsof -t -i :8000 2>/dev/null | head -1)
        echo "✅ API Server: Running (PID: $API_PID, Port: 8000)"
    else
        echo "❌ API Server: Not running"
        echo "   Start with: scripts/start-with-logging.sh api"
    fi
    
    # Check Frontend server
    if lsof -i :4200 > /dev/null 2>&1; then
        FRONTEND_PID=$(lsof -t -i :4200 2>/dev/null | head -1)
        echo "✅ Frontend Server: Running (PID: $FRONTEND_PID, Port: 4200)"
    else
        echo "❌ Frontend Server: Not running"
        echo "   Start with: scripts/start-with-logging.sh frontend"
    fi
    
    echo ""
    echo "📁 Log files:"
    if [ -f "$LOG_DIR/api-server.log" ]; then
        API_LINES=$(wc -l < "$LOG_DIR/api-server.log" 2>/dev/null || echo "0")
        echo "   📄 api-server.log: $API_LINES lines"
    fi
    if [ -f "$LOG_DIR/frontend-server.log" ]; then
        FRONTEND_LINES=$(wc -l < "$LOG_DIR/frontend-server.log" 2>/dev/null || echo "0")
        echo "   📄 frontend-server.log: $FRONTEND_LINES lines"
    fi
    echo ""
}

case "$LOG_TYPE" in
    "api"|"backend")
        show_server_status
        show_log "api-server.log" "API Server Logs"
        ;;
    "frontend"|"app"|"ui")
        show_server_status
        show_log "frontend-server.log" "Frontend Server Logs"
        ;;
    "all")
        show_server_status
        show_log "api-server.log" "API Server Logs" 30
        show_log "frontend-server.log" "Frontend Server Logs" 30
        ;;
    "status")
        show_server_status
        ;;
    *)
        echo "Usage: $0 [api|frontend|all|status] [--follow]"
        echo ""
        echo "Log Types:"
        echo "  api       - API server logs only"
        echo "  frontend  - Frontend server logs only"
        echo "  all       - Both logs (default)"
        echo "  status    - Server status only"
        echo ""
        echo "Options:"
        echo "  --follow  - Follow logs in real-time"
        echo ""
        echo "Examples:"
        echo "  $0 api                    # Show recent API logs"
        echo "  $0 all --follow          # Follow both logs"
        echo "  $0 status                # Check server status"
        exit 1
        ;;
esac