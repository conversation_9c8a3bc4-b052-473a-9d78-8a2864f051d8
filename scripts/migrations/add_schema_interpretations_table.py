#!/usr/bin/env python3
"""
Add schema_interpretations table for storing AI interpretation results.

This table stores the results of AI-powered schema interpretation to avoid
re-processing files and to provide audit trail of AI decisions.
"""

import asyncio
import asyncpg
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_schema_interpretations_table():
    """Create the schema_interpretations table."""
    
    # Connect to database
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        user="giki_ai_user",
        password="giki123",
        database="giki_ai_db"
    )
    
    try:
        # Create the table
        create_table_query = """
        CREATE TABLE IF NOT EXISTS schema_interpretations (
            id VARCHAR(36) PRIMARY KEY,
            upload_id VARCHAR(36) NOT NULL,
            tenant_id INTEGER NOT NULL,
            filename VARCHAR(255) NOT NULL,
            column_mappings JSONB NOT NULL,
            overall_confidence FLOAT NOT NULL,
            required_fields_mapped JSONB NOT NULL,
            interpretation_summary TEXT,
            debit_credit_inference JSONB DEFAULT '{}',
            regional_variations JSONB DEFAULT '[]',
            created_at TIMESTAMP NOT NULL,
            updated_at TIMESTAMP NOT NULL,
            CONSTRAINT fk_upload FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
            CONSTRAINT fk_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
        );
        """
        
        await conn.execute(create_table_query)
        logger.info("Created schema_interpretations table")
        
        # Create indexes for performance
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_schema_interpretations_upload_tenant 
            ON schema_interpretations (upload_id, tenant_id);
        """)
        logger.info("Created indexes")
        
        # Check if table was created successfully
        result = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'schema_interpretations'
        """)
        
        if result > 0:
            logger.info("✓ schema_interpretations table created successfully")
        else:
            logger.error("✗ Failed to create schema_interpretations table")
            
    except Exception as e:
        logger.error(f"Error creating table: {e}")
        raise
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(create_schema_interpretations_table())