-- Migration: Add onboarding_type column to support M1/M2/M3 scenarios
-- Date: 2025-06-27
-- Description: Enhance onboarding_status table to support three onboarding types:
--   - zero_onboarding (M1): No historical data, immediate production ready
--   - historical_data (M2): Current flow with categories and RAG corpus
--   - schema_only (M3): Category hierarchy import without transactions

-- Add onboarding_type column with default value
ALTER TABLE onboarding_status 
ADD COLUMN IF NOT EXISTS onboarding_type VARCHAR(50) DEFAULT 'historical_data' NOT NULL;

-- Update stage field to include new stages
COMMENT ON COLUMN onboarding_status.stage IS 'Stages: not_started, data_uploaded, corpus_building, validating, completed, zero_ready, schema_imported, schema_ready';

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_onboarding_status_type_stage 
ON onboarding_status(onboarding_type, stage);

-- Add constraints for valid onboarding types
ALTER TABLE onboarding_status 
ADD CONSTRAINT chk_onboarding_type 
CHECK (onboarding_type IN ('zero_onboarding', 'historical_data', 'schema_only'));

-- Add constraint for valid stages
ALTER TABLE onboarding_status 
ADD CONSTRAINT chk_onboarding_stage 
CHECK (stage IN ('not_started', 'data_uploaded', 'corpus_building', 'validating', 'completed', 'production_approved', 'zero_ready', 'schema_imported', 'schema_ready', 'failed'));

-- Update existing records to have proper default values
UPDATE onboarding_status 
SET onboarding_type = 'historical_data' 
WHERE onboarding_type IS NULL;

-- Comment on the table
COMMENT ON TABLE onboarding_status IS 'Tracks onboarding status for three scenarios: M1 (zero), M2 (historical), M3 (schema)';
COMMENT ON COLUMN onboarding_status.onboarding_type IS 'Type: zero_onboarding (M1), historical_data (M2), schema_only (M3)';