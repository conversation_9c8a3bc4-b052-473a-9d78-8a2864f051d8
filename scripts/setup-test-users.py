#!/usr/bin/env python3
"""
Set up test users with correct password hashes.
"""
import asyncio
import asyncpg
import bcrypt
import os
from datetime import datetime

# Database connection from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")

async def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

async def setup_users():
    """Set up test users with correct password hashes."""
    # Connect to database
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Hash the default password
        hashed_pw = await hash_password("password123")
        
        print(f"Setting up users with hashed password...")
        
        # Update all test users with the correct password hash
        users = [
            ('<EMAIL>', 'test_giki'),
            ('<EMAIL>', 'test_rezolve'),
            ('<EMAIL>', 'admin')
        ]
        
        for email, username in users:
            await conn.execute("""
                UPDATE users 
                SET hashed_password = $1,
                    is_active = true,
                    is_verified = true,
                    updated_at = $2
                WHERE email = $3
            """, hashed_pw, datetime.now(), email)
            
            print(f"✓ Updated {email} (username: {username})")
        
        # Verify the users exist
        result = await conn.fetch("SELECT id, email, username, is_active, is_verified, tenant_id FROM users")
        print("\nCurrent users:")
        for row in result:
            print(f"  - {row['email']} ({row['username']}) - Active: {row['is_active']}, Verified: {row['is_verified']}, Tenant: {row['tenant_id']}")
            
    finally:
        await conn.close()
        
    print("\n✓ Test users configured successfully!")
    print("\nYou can now login with:")
    print("  - <EMAIL> / password123")
    print("  - <EMAIL> / password123")
    print("  - <EMAIL> / password123")

if __name__ == "__main__":
    asyncio.run(setup_users())