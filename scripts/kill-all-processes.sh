#!/bin/bash
# Comprehensive process cleanup for giki-ai workspace

echo "🚨 EMERGENCY: Killing all giki-ai related processes..."

# Kill by process name patterns
echo "Stopping Vite builds..."
pkill -f "vite.*build" 2>/dev/null || true

echo "Stopping NX processes..."
pkill -f "nx.*build" 2>/dev/null || true
pkill -f "nx.*daemon" 2>/dev/null || true
pkill -f "nx.*plugin-worker" 2>/dev/null || true

echo "Stopping PNPM processes..."
pkill -f "pnpm.*build" 2>/dev/null || true
pkill -f "pnpm.*nx" 2>/dev/null || true

echo "Stopping esbuild services..."
pkill -f "esbuild.*service" 2>/dev/null || true

echo "Stopping MCP servers..."
./scripts/mcp-dedupe.sh
pkill -f "mcp-server" 2>/dev/null || true
pkill -f "context7-mcp" 2>/dev/null || true

echo "Stopping dev servers..."
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
lsof -ti:4200 | xargs kill -9 2>/dev/null || true

echo "Stopping Python processes..."
pkill -f "uvicorn.*giki_ai_api" 2>/dev/null || true
pkill -f "python.*giki" 2>/dev/null || true

echo "Cleaning build artifacts..."
rm -rf .nx/cache/* 2>/dev/null || true
rm -rf dist/* 2>/dev/null || true
rm -rf node_modules/.cache/* 2>/dev/null || true

# Wait for graceful shutdown
sleep 3

echo "✅ Process cleanup complete"
echo "📊 Remaining processes: $(ps aux | grep -E "(node|python)" | grep -v grep | wc -l)"