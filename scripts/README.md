# Scripts Directory - Essential Infrastructure Only

## IMPORTANT: Ad Hoc Scripts Prohibition Enforced

This directory contains ONLY essential infrastructure scripts. All ad hoc scripts have been removed.

**Script Creation Prohibited For:**
- ❌ Database fixes/changes → Use Alembic migrations
- ❌ Management utilities → Use CLI commands in `apps/giki-ai-api/src/giki_ai_api/cli/`
- ❌ Test scripts → Use proper test directories
- ❌ Quick fixes → Fix in application source code

## Current Directory Structure (Post-Cleanup)

```
scripts/
├── README.md                    # This documentation
├── nx/                         # Scripts required by nx targets
│   ├── serve-api.sh           # API server (nx serve giki-ai-api)
│   ├── serve-frontend.sh      # Frontend server (nx serve giki-ai-app)
│   ├── ensure-postgres.sh     # Database setup (nx db giki-ai-api)
│   ├── deploy-all.sh          # Deployment (nx deploy)
│   ├── check-development-logs.sh  # Development logs
│   ├── check-production-logs.sh   # Production logs
│   └── log-with-limit.sh      # Log management for nx tests
├── infrastructure/             # Cloud infrastructure management
│   ├── gcp-setup.sh           # GCP project configuration
│   ├── production-database-setup.py  # Cloud SQL provisioning
│   ├── configure-redis-production.sh # Redis configuration
│   ├── cloud-sql-production-setup.sh # Database deployment
│   └── setup-redis-and-cloudsql-optimization.sh # Performance tuning
├── start-servers.sh            # Package.json: pnpm serve
├── stop-servers.sh             # Package.json: pnpm serve:stop
├── restart-servers.sh          # Package.json: pnpm serve:restart
├── check-servers.sh            # Package.json: pnpm serve:status
├── log-manager.sh              # Package.json: pnpm logs:manage
├── check-redis-status.sh       # Redis health monitoring
├── kill-all-processes.sh       # Emergency process cleanup
├── playwright-server.sh        # Playwright MCP server
└── [8 other essential utilities] # System monitoring, demos, etc.
```

## Cleanup Results (2025-06-26)

**Removed 127+ ad hoc scripts:**
- ✅ Database fix/add/check scripts → Use Alembic migrations
- ✅ SQL files → Use Alembic migrations  
- ✅ Test utilities → Use proper test structure
- ✅ Utility scripts → Use CLI commands
- ✅ Duplicate deployment scripts → Use nx deployment
- ✅ Development workflow scripts → Use proper development process

**From 182 scripts → 31 essential scripts**

## Development Workflow (No Scripts Required)

```bash
# Database changes (Proper way)
cd apps/giki-ai-api
uv run alembic revision --autogenerate -m "add new column"
uv run alembic upgrade head

# Management tasks (Proper way)
cd apps/giki-ai-api
uv run python -m giki_ai_api.cli.main user-create --email <EMAIL>

# Development (Using package.json scripts)
pnpm serve                      # Start all servers
pnpm serve:status              # Check server health
pnpm check-logs:dev:api        # View API logs
pnpm test:e2e                  # Run E2E tests
pnpm lint                      # Lint all code

# Deployment (Using nx)
pnpm deploy:dev                # Deploy to development
pnpm deploy:prod               # Deploy to production
```

## Adding Scripts (Rare Cases Only)

**Before adding ANY script, verify:**
1. ✅ Required by package.json npm script?
2. ✅ Required by nx project.json target?
3. ✅ Essential infrastructure that can't be nx command?

**If not essential infrastructure → DO NOT ADD**

## Enforcement

Any PR adding ad hoc scripts will be rejected. Fix problems at the source:
- Database issues → Fix in Alembic migrations
- Management needs → Add CLI command
- Testing needs → Add proper test
- Build/deploy needs → Extend nx targets

## Deployment URLs

After successful deployment:
- **Backend**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
- **Frontend**: https://app-giki-ai.web.app