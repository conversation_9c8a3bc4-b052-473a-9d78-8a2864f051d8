#!/usr/bin/env python3
"""Verify password hash."""

from passlib.context import CryptContext

# Create context with bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Test password and hash
password = "GikiDev2025#Secure"
hash_from_db = "$2b$04$gbaJ0IYG4Yce5jvETMF1aeOufu8rOIdzuTNK2g/ilJHv0ubkUGYN."

# Verify
is_valid = pwd_context.verify(password, hash_from_db)
print(f"Password verification: {is_valid}")

# Also create a new hash to compare
new_hash = pwd_context.hash(password, rounds=4)
print(f"New hash: {new_hash}")
print(f"DB hash:  {hash_from_db}")