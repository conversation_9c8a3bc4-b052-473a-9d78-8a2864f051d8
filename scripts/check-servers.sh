#!/bin/bash

# Server Status Checker for Multi-Agent Development
# Prevents agents from starting duplicate servers

check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port > /dev/null 2>&1; then
        echo "✅ $service is running on port $port"
        return 0
    else
        echo "❌ $service is NOT running on port $port"
        return 1
    fi
}

echo "🔍 Checking server status for multi-agent development..."
echo

# Check API server (FastAPI on port 8000)
api_running=false
if check_port 8000 "API Server (FastAPI)"; then
    # Verify it's actually responding
    if curl -s -f http://localhost:8000/api/v1/health > /dev/null; then
        echo "   └── Health check: PASSED"
        api_running=true
    else
        echo "   └── Health check: FAILED (server not responding)"
    fi
else
    echo "   └── Recommendation: Start with 'pnpm serve:api' in a dedicated Zellij tab"
fi

echo

# Check Frontend server (React/Vite on port 4200)
frontend_running=false
if check_port 4200 "Frontend Server (React/Vite)"; then
    # Verify it's actually responding
    if curl -s -f http://localhost:4200 > /dev/null; then
        echo "   └── Health check: PASSED"
        frontend_running=true
    else
        echo "   └── Health check: FAILED (server not responding)"
    fi
else
    echo "   └── Recommendation: Start with 'pnpm serve:app' in a dedicated Zellij tab"
fi

echo

# Overall status
if $api_running && $frontend_running; then
    echo "🎉 Both servers are running - agents can proceed with development"
    exit 0
elif $api_running; then
    echo "⚠️  API server running, but frontend needs to be started"
    exit 0
elif $frontend_running; then
    echo "⚠️  Frontend server running, but API needs to be started"
    exit 0
else
    echo "🚨 Both servers need to be started before agents can work"
    exit 0
fi