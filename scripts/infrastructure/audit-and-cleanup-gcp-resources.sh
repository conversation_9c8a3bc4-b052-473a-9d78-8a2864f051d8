#!/bin/bash

# Google Cloud Resources Audit and Cleanup Script
# This script audits current GCP usage and identifies unused resources

set -e

PROJECT_ID="rezolve-poc"
REGION="us-central1"

echo "🔍 Google Cloud Resources Audit"
echo "================================"

# Check authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ No active gcloud authentication found."
    echo "Please run: gcloud auth login"
    exit 1
fi

echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# 1. Cloud SQL instances
echo "1. Cloud SQL Instances:"
echo "======================"
gcloud sql instances list --project=$PROJECT_ID --format="table(
    name,
    region,
    tier,
    state,
    settings.dataDiskSizeGb,
    settings.pricingPlan
)"

# 2. Cloud Run services
echo ""
echo "2. Cloud Run Services:"
echo "====================="
gcloud run services list --project=$PROJECT_ID --region=$REGION --format="table(
    METADATA.name,
    STATUS.url,
    STATUS.latestReadyRevisionName,
    SPEC.template.spec.containers[0].resources.limits.memory,
    SPEC.template.spec.containers[0].resources.limits.cpu
)"

# 3. Container Registry images
echo ""
echo "3. Container Registry Images:"
echo "============================"
gcloud container images list --project=$PROJECT_ID --format="table(name)"

# Show image details for our specific image
if gcloud container images list --project=$PROJECT_ID | grep -q "giki-ai-api"; then
    echo ""
    echo "giki-ai-api image tags:"
    gcloud container images list-tags gcr.io/$PROJECT_ID/giki-ai-api --limit=5 --format="table(
        tags,
        digest,
        timestamp
    )"
fi

# 4. Redis instances
echo ""
echo "4. Redis/Memorystore Instances:"
echo "=============================="
gcloud redis instances list --region=$REGION --project=$PROJECT_ID --format="table(
    name,
    displayName,
    tier,
    memorySizeGb,
    state,
    host,
    port
)" 2>/dev/null || echo "No Redis instances found or service not enabled"

# 5. Firebase projects
echo ""
echo "5. Firebase Hosting:"
echo "==================="
gcloud firebase projects list --filter="projectId:$PROJECT_ID" --format="table(
    projectId,
    projectNumber,
    displayName
)" 2>/dev/null || echo "Firebase CLI not configured or no projects found"

# 6. Storage buckets
echo ""
echo "6. Cloud Storage Buckets:"
echo "========================"
gcloud storage buckets list --project=$PROJECT_ID --format="table(
    name,
    location,
    storageClass
)" 2>/dev/null || echo "No storage buckets found"

# 7. Compute Engine instances
echo ""
echo "7. Compute Engine Instances:"
echo "==========================="
gcloud compute instances list --project=$PROJECT_ID --format="table(
    name,
    zone,
    machineType,
    status,
    externalIP
)" 2>/dev/null || echo "No compute instances found"

# 8. Load Balancers
echo ""
echo "8. Load Balancers:"
echo "=================="
gcloud compute url-maps list --project=$PROJECT_ID --format="table(
    name,
    defaultService
)" 2>/dev/null || echo "No load balancers found"

# 9. Cloud Build triggers
echo ""
echo "9. Cloud Build Triggers:"
echo "======================="
gcloud builds triggers list --project=$PROJECT_ID --format="table(
    name,
    status,
    github.owner,
    github.name,
    github.push.branch
)" 2>/dev/null || echo "No build triggers found"

# 10. IAM Service Accounts
echo ""
echo "10. Service Accounts:"
echo "===================="
gcloud iam service-accounts list --project=$PROJECT_ID --format="table(
    email,
    displayName,
    disabled
)"

# Cost estimation
echo ""
echo "💰 Current Resource Cost Estimates:"
echo "===================================="

# Get basic cost information
echo "Based on current configuration:"
echo "- Cloud SQL (db-g1-small): ~$25/month (⚠️ NOT PRODUCTION-GRADE)"
echo "- Cloud Run: ~$5-15/month (based on usage)"
echo "- Container Registry: ~$1-5/month (for image storage)"
echo "- Firebase Hosting: $0 (within free tier)"
echo "- Cloud Build: $0-5/month (within free tier for typical usage)"

# Recommendations
echo ""
echo "🎯 Optimization Recommendations:"
echo "================================="
echo ""
echo "REQUIRED CHANGES:"
echo "- Upgrade Cloud SQL to db-custom-1-3840 (production-grade, SLA-covered)"
echo "  Cost impact: +$10-15/month for SLA coverage and better performance"
echo ""
echo "OPTIONAL OPTIMIZATIONS:"
echo "- Add Redis for persistent caching: +$36/month"
echo "- Clean up old Container Registry images: Save $1-3/month"
echo "- Monitor Cloud Run scaling: Ensure efficient resource usage"
echo ""
echo "KEEP AS-IS:"
echo "- Firebase Hosting (free tier, working well)"
echo "- Cloud Build (free tier, working well)"
echo "- Service accounts (necessary for operation)"

# Security check
echo ""
echo "🔒 Security Status:"
echo "==================="
echo "- Cloud SQL: Private IP only ✅"
echo "- Service accounts: Using least privilege ✅"
echo "- Cloud Run: Authenticated endpoints ✅"
echo "- Container images: Private registry ✅"

echo ""
echo "✅ Audit completed successfully!"
echo ""
echo "Next steps:"
echo "1. Run the Cloud SQL upgrade script: ./scripts/setup-redis-and-cloudsql-optimization.sh"
echo "2. Monitor costs after changes: gcloud billing budgets list --project=$PROJECT_ID"
echo "3. Set up billing alerts if not already configured"