#!/bin/bash
# GCP Setup Script for Giki AI

set -e

# Configuration
PROJECT_ID=${1:-"your-project-id"}
REGION=${2:-"us-central1"}

echo "Setting up GCP for project: $PROJECT_ID in region: $REGION"

# Enable required APIs
echo "Enabling required APIs..."
gcloud services enable \
  run.googleapis.com \
  artifactregistry.googleapis.com \
  secretmanager.googleapis.com \
  aiplatform.googleapis.com \
  --project=$PROJECT_ID

# Create Artifact Registry
echo "Creating Artifact Registry..."
gcloud artifacts repositories create giki-ai \
  --repository-format=docker \
  --location=$REGION \
  --project=$PROJECT_ID || echo "Registry already exists"

# Create Service Accounts
echo "Creating service accounts..."

# CI/CD Service Account
gcloud iam service-accounts create github-actions \
    --display-name="GitHub Actions CI/CD" \
    --project=$PROJECT_ID || echo "github-actions SA already exists"

# Cloud Run Service Account
gcloud iam service-accounts create cloud-run-app \
    --display-name="Cloud Run Application" \
    --project=$PROJECT_ID || echo "cloud-run-app SA already exists"

# Assign Permissions to CI/CD Account
echo "Assigning permissions to CI/CD account..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:github-actions@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:github-actions@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:github-actions@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/artifactregistry.writer"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:github-actions@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/iam.serviceAccountUser"

# Assign Permissions to Cloud Run App Account
echo "Assigning permissions to Cloud Run app account..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:cloud-run-app@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:cloud-run-app@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

# Generate key for GitHub Actions
echo "Generating service account key..."
gcloud iam service-accounts keys create github-actions-key.json \
    --iam-account=github-actions@$PROJECT_ID.iam.gserviceaccount.com \
    --project=$PROJECT_ID

echo "
Setup complete! Next steps:

1. Create secrets in Google Secret Manager:
   gcloud secrets create database-url --data-file=- --project=$PROJECT_ID
   gcloud secrets create auth-secret-key --data-file=- --project=$PROJECT_ID
   gcloud secrets create admin-api-key --data-file=- --project=$PROJECT_ID

2. Grant Cloud Run access to secrets:
   gcloud secrets add-iam-policy-binding database-url \\
     --member='serviceAccount:cloud-run-app@$PROJECT_ID.iam.gserviceaccount.com' \\
     --role='roles/secretmanager.secretAccessor' \\
     --project=$PROJECT_ID

3. Add these secrets to GitHub repository:
   - GCP_PROJECT_ID: $PROJECT_ID
   - GCP_REGION: $REGION
   - GCP_SA_KEY: (contents of github-actions-key.json)

4. Delete the local key file:
   rm github-actions-key.json
"