#!/bin/bash

# Update Cloud Run deployment with Redis configuration
# Run this script after Redis instance is created

set -e

PROJECT_ID="rezolve-poc"
REGION="us-central1"
SERVICE_NAME="giki-ai-api-prod"
REDIS_INSTANCE="giki-ai-redis-cache"

echo "🔄 Updating Cloud Run service with Redis configuration"
echo "======================================================="

# Check authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ No active gcloud authentication found."
    echo "Please run: gcloud auth login"
    exit 1
fi

# Get Redis connection details
echo "1. Getting Redis connection details..."
REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(host)")

REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(port)")

if [ -z "$REDIS_HOST" ]; then
    echo "❌ Failed to get Redis host. Is the Redis instance ready?"
    exit 1
fi

echo "✅ Redis Host: $REDIS_HOST"
echo "✅ Redis Port: $REDIS_PORT"

# Update Cloud Run service
echo ""
echo "2. Updating Cloud Run service with Redis configuration..."

# Prepare environment variables
ENV_VARS="DATABASE_URL=postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres-prod"
ENV_VARS="$ENV_VARS,ENVIRONMENT=production"
ENV_VARS="$ENV_VARS,REDIS_HOST=$REDIS_HOST"
ENV_VARS="$ENV_VARS,REDIS_PORT=$REDIS_PORT"
ENV_VARS="$ENV_VARS,REDIS_URL=redis://$REDIS_HOST:$REDIS_PORT"
ENV_VARS="$ENV_VARS,REDIS_ENABLED=true"

# Update the service
gcloud run services update $SERVICE_NAME \
    --project=$PROJECT_ID \
    --region=$REGION \
    --set-env-vars="$ENV_VARS"

echo "✅ Cloud Run service updated with Redis configuration"

# Verify the deployment
echo ""
echo "3. Verifying deployment..."
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(status.url)')

echo "Service URL: $SERVICE_URL"

# Test the health endpoint
echo ""
echo "4. Testing health endpoint..."
curl -w '\nResponse Time: %{time_total}s\nStatus Code: %{http_code}\n' \
    "$SERVICE_URL/api/v1/health" || echo "Health check failed - service may still be starting"

echo ""
echo "✅ Cloud Run update completed!"
echo "💡 Monitor the service logs: gcloud logs tail --project=$PROJECT_ID"
echo "💡 Check cache stats: curl $SERVICE_URL/api/v1/cache/stats"