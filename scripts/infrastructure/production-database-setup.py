#!/usr/bin/env python3
"""
Production Database Setup
Set up Google Cloud SQL for production deployment
"""

import asyncio
import asyncpg
import time
import subprocess
import json
import os
from pathlib import Path

# Production database options
CLOUD_SQL_CONFIGS = {
    "basic": {
        "tier": "db-f1-micro",
        "storage": "10GB", 
        "cost": "$7/month",
        "description": "Minimal setup for MVP"
    },
    "standard": {
        "tier": "db-g1-small", 
        "storage": "20GB",
        "cost": "$25/month", 
        "description": "Production ready"
    },
    "performance": {
        "tier": "db-n1-standard-1",
        "storage": "50GB", 
        "cost": "$50/month",
        "description": "High performance"
    }
}

async def test_current_database():
    """Test current database performance for comparison"""
    print("=== Testing Current Database Performance ===")
    
    url = "postgresql://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db"
    
    try:
        start = time.perf_counter()
        conn = await asyncio.wait_for(asyncpg.connect(url), timeout=10.0)
        await asyncio.wait_for(conn.fetchval("SELECT 1"), timeout=10.0)
        await conn.close()
        duration = (time.perf_counter() - start) * 1000
        
        print(f"Database current: {duration:.3f}ms")
        return duration
        
    except asyncio.TimeoutError:
        print("Database: >10 seconds (CRITICAL ISSUE)")
        return 10000
    except Exception as e:
        print(f"Database failed: {e}")
        return None

def check_gcloud_setup():
    """Check if Google Cloud CLI is set up"""
    print("\n=== Google Cloud Setup Check ===")
    
    try:
        # Check if gcloud is installed
        result = subprocess.run(['which', 'gcloud'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Google Cloud CLI not installed")
            print("   Install: https://cloud.google.com/sdk/docs/install")
            return False
        
        # Check if authenticated
        result = subprocess.run(['gcloud', 'auth', 'list', '--format=json'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            accounts = json.loads(result.stdout)
            if accounts:
                active_account = next((acc for acc in accounts if acc.get('status') == 'ACTIVE'), None)
                if active_account:
                    print(f"✅ Authenticated as: {active_account['account']}")
                else:
                    print("⚠️  No active account")
                    print("   Run: gcloud auth login")
                    return False
            else:
                print("❌ No accounts found")
                print("   Run: gcloud auth login")
                return False
        
        # Check project
        result = subprocess.run(['gcloud', 'config', 'get-value', 'project'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            project = result.stdout.strip()
            print(f"✅ Project: {project}")
            return True
        else:
            print("❌ No project set")
            print("   Run: gcloud config set project YOUR_PROJECT_ID")
            return False
            
    except Exception as e:
        print(f"Google Cloud check failed: {e}")
        return False

def generate_cloud_sql_setup():
    """Generate Cloud SQL setup commands"""
    print("\n=== Cloud SQL Setup Commands ===")
    
    # Detect current project
    try:
        result = subprocess.run(['gcloud', 'config', 'get-value', 'project'], 
                              capture_output=True, text=True)
        project_id = result.stdout.strip() if result.returncode == 0 else "your-project-id"
    except:
        project_id = "your-project-id"
    
    instance_name = "giki-ai-postgres"
    region = "us-central1"
    
    print(f"Project ID: {project_id}")
    print(f"Instance: {instance_name}")
    print(f"Region: {region}")
    
    # Configuration options
    print("\n📋 Configuration Options:")
    for name, config in CLOUD_SQL_CONFIGS.items():
        print(f"  {name.upper()}: {config['tier']} - {config['cost']} ({config['description']})")
    
    # Setup commands
    print("\n🚀 Setup Commands (choose one tier):")
    
    for name, config in CLOUD_SQL_CONFIGS.items():
        print(f"\n# {name.upper()} Configuration ({config['cost']}):")
        print(f"gcloud sql instances create {instance_name} \\")
        print("  --database-version=POSTGRES_15 \\")
        print(f"  --tier={config['tier']} \\")
        print(f"  --region={region} \\")
        print("  --storage-type=SSD \\")
        print(f"  --storage-size={config['storage'].replace('GB', '')}GB \\")
        print("  --storage-auto-increase \\")
        print("  --backup \\")
        print("  --backup-start-time=02:00 \\")
        print("  --maintenance-window-day=SUN \\")
        print("  --maintenance-window-hour=03 \\")
        print("  --maintenance-release-channel=production")
        
        print("\n# Create database:")
        print(f"gcloud sql databases create giki_ai_db --instance={instance_name}")
        
        print("\n# Create user:")
        print(f"gcloud sql users create giki_ai_user --instance={instance_name} --password=SECURE_PASSWORD_HERE")
        
        print("\n# Get connection string:")
        print(f"gcloud sql instances describe {instance_name} --format='value(connectionName)'")
        
        if name == "basic":  # Only show detailed steps once
            print("\n# Enable SQL Admin API (if not already enabled):")
            print("gcloud services enable sqladmin.googleapis.com")

def generate_production_env():
    """Generate production environment configuration"""
    print("\n=== Production Environment Configuration ===")
    
    env_content = """
# Production Database Configuration
# Replace CONNECTION_NAME with output from: gcloud sql instances describe giki-ai-postgres --format='value(connectionName)'

# For Cloud Run deployment (Unix socket):
DATABASE_URL=postgresql+asyncpg://giki_ai_user:SECURE_PASSWORD@/giki_ai_db?host=/cloudsql/CONNECTION_NAME

# For local development with Cloud SQL Proxy:
# DATABASE_URL=postgresql+asyncpg://giki_ai_user:SECURE_PASSWORD@127.0.0.1:5432/giki_ai_db

# Connection pool configuration for production
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Performance monitoring
DB_ECHO=false
DB_POOL_PRE_PING=true
"""
    
    env_file = Path(".env.production")
    with open(env_file, "w") as f:
        f.write(env_content.strip())
    
    print(f"✅ Created {env_file}")
    print("📝 Update CONNECTION_NAME and password after Cloud SQL setup")

def generate_cloud_run_deployment():
    """Generate Cloud Run deployment configuration"""
    print("\n=== Cloud Run Deployment Configuration ===")
    
    
    cloudbuild_content = """
# cloudbuild.yaml for automated deployment
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/giki-ai-api', '.']
  
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/giki-ai-api']
  
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'giki-ai-api'
      - '--image=gcr.io/$PROJECT_ID/giki-ai-api'
      - '--region=us-central1'
      - '--platform=managed'
      - '--add-cloudsql-instances=$PROJECT_ID:us-central1:giki-ai-postgres'
      - '--set-env-vars=DATABASE_URL=postgresql+asyncpg://giki_ai_user:SECURE_PASSWORD@/giki_ai_db?host=/cloudsql/$PROJECT_ID:us-central1:giki-ai-postgres'
      - '--allow-unauthenticated'
"""
    
    deployment_script = """#!/bin/bash
# deploy-production.sh
set -e

echo "🚀 Deploying to Google Cloud Run with Cloud SQL..."

# Build and push container
gcloud builds submit --tag gcr.io/$PROJECT_ID/giki-ai-api

# Deploy to Cloud Run with Cloud SQL connection
gcloud run deploy giki-ai-api \\
  --image gcr.io/$PROJECT_ID/giki-ai-api \\
  --region us-central1 \\
  --platform managed \\
  --add-cloudsql-instances $PROJECT_ID:us-central1:giki-ai-postgres \\
  --set-env-vars DATABASE_URL="postgresql+asyncpg://giki_ai_user:SECURE_PASSWORD@/giki_ai_db?host=/cloudsql/$PROJECT_ID:us-central1:giki-ai-postgres" \\
  --allow-unauthenticated \\
  --memory 512Mi \\
  --cpu 1 \\
  --concurrency 100 \\
  --timeout 300

echo "✅ Deployment complete!"
echo "🔗 URL: $(gcloud run services describe giki-ai-api --region us-central1 --format 'value(status.url)')"
"""
    
    # Save files
    files = [
        ("cloudbuild.yaml", cloudbuild_content),
        ("scripts/deploy-production.sh", deployment_script),
    ]
    
    for filename, content in files:
        filepath = Path(filename)
        filepath.parent.mkdir(exist_ok=True)
        with open(filepath, "w") as f:
            f.write(content.strip())
        print(f"✅ Created {filepath}")
    
    # Make deploy script executable
    os.chmod("scripts/deploy-production.sh", 0o755)

async def main():
    print("Production Database Setup for giki.ai")
    print("=" * 50)
    
    # Test current performance
    current_performance = await test_current_database()
    
    # Check Google Cloud setup
    gcloud_ready = check_gcloud_setup()
    
    # Generate setup commands
    generate_cloud_sql_setup()
    
    # Generate configuration files
    generate_production_env()
    generate_cloud_run_deployment()
    
    # Summary and next steps
    print("\n=== PRODUCTION DEPLOYMENT PLAN ===")
    
    if current_performance and current_performance > 1000:
        print("🚨 CRITICAL: Current database too slow for production")
        print("   Immediate action required for Cloud SQL migration")
    
    if gcloud_ready:
        print("✅ Google Cloud CLI ready")
        print("📋 Run the generated setup commands")
    else:
        print("❌ Set up Google Cloud CLI first")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Set up Google Cloud SQL using generated commands")
    print("2. Update .env.production with connection details") 
    print("3. Test locally with Cloud SQL proxy")
    print("4. Deploy to Cloud Run using scripts/deploy-production.sh")
    print("5. Monitor performance (target: <200ms health endpoint)")
    
    print("\n💰 COST ESTIMATES:")
    for name, config in CLOUD_SQL_CONFIGS.items():
        print(f"  {name.upper()}: {config['cost']} + Cloud Run costs")
    
    print("\n⚡ EXPECTED PERFORMANCE:")
    print("  Cloud SQL: <50ms (20x better than current database)")
    print("  Health endpoint: <200ms (target achieved)")

if __name__ == "__main__":
    asyncio.run(main())