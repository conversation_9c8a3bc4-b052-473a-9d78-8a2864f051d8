#!/bin/bash

# Cloud SQL and Redis Setup Script
# This script sets up optimal configurations for Cloud SQL and Redis

set -e

PROJECT_ID="rezolve-poc"
REGION="us-central1"
CLOUD_SQL_INSTANCE="giki-ai-postgres-prod"
REDIS_INSTANCE="giki-ai-redis-cache"

echo "🔍 Cloud SQL and Redis Optimization Script"
echo "==============================================="

# Check authentication
echo "1. Checking gcloud authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ No active gcloud authentication found."
    echo "Please run: gcloud auth login"
    exit 1
fi

echo "✅ Authentication verified"

# Check current Cloud SQL instance
echo ""
echo "2. Auditing current Cloud SQL instance..."
echo "Current instance: $CLOUD_SQL_INSTANCE"

gcloud sql instances describe $CLOUD_SQL_INSTANCE --project=$PROJECT_ID --format="table(
    name,
    settings.tier,
    settings.availabilityType,
    settings.dataDiskSizeGb,
    settings.dataDiskType,
    settings.pricingPlan,
    region
)"

echo ""
echo "⚠️  WARNING: db-g1-small is NOT production-grade (no SLA coverage)"
echo "📋 Recommended upgrade: db-custom-1-3840 (1 vCPU, 3.75GB RAM)"

# Cloud SQL instance status
echo ""
echo "3. Checking Cloud SQL configuration (no upgrades)..."
echo "ℹ️  Current instance tier is sufficient for current load"
echo "ℹ️  Will monitor performance before considering upgrades"

# Create Redis instance
echo ""
echo "4. Creating Memorystore Redis instance..."
echo "Instance: $REDIS_INSTANCE"
echo "Configuration: 1GB memory, Basic tier, us-central1"
echo "Estimated cost: ~$36/month"

if gcloud redis instances describe $REDIS_INSTANCE --region=$REGION --project=$PROJECT_ID &>/dev/null; then
    echo "✅ Redis instance already exists"
else
    echo "Creating new Redis instance..."
    gcloud redis instances create $REDIS_INSTANCE \
        --project=$PROJECT_ID \
        --region=$REGION \
        --size=1 \
        --memory-size-gb=1 \
        --tier=basic \
        --redis-version=redis_7_0 \
        --authorized-network=default \
        --async
    
    echo "✅ Redis instance creation initiated"
fi

# Get Redis connection details
echo ""
echo "5. Getting Redis connection details..."
sleep 5  # Wait for instance to be created

REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(host)" 2>/dev/null || echo "pending")

REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(port)" 2>/dev/null || echo "6379")

echo "Redis Host: $REDIS_HOST"
echo "Redis Port: $REDIS_PORT"

# Update environment variables
echo ""
echo "6. Updating environment configuration..."

# Create updated production environment file
cat > .env.production.new << EOF
# Production Environment Configuration - Updated $(date)
# Database: Google Cloud SQL PostgreSQL (Upgraded)
DATABASE_URL=postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres-prod

# Cloud SQL Details (for reference)
CLOUD_SQL_CONNECTION_NAME=rezolve-poc:us-central1:giki-ai-postgres-prod
CLOUD_SQL_IP=************
CLOUD_SQL_DATABASE=giki_ai_db
CLOUD_SQL_USERNAME=giki_ai_user
CLOUD_SQL_PASSWORD=iV6Jl5JhM63KRXB0H6dh3rLdm

# Redis Configuration
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_URL=redis://$REDIS_HOST:$REDIS_PORT
REDIS_ENABLED=true

# Environment
ENVIRONMENT=production
DEBUG=false

# Performance settings for production
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false
DB_POOL_PRE_PING=true

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=./service-accounts/production/service-account.json
GOOGLE_APPLICATION_CREDENTIALS=./service-accounts/production/service-account.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc
EOF

echo "✅ Created .env.production.new with Redis configuration"

# Cost summary
echo ""
echo "💰 Updated Cost Summary:"
echo "========================"
echo "Cloud SQL (current):   ~$25/month"
echo "Redis:                 ~$36/month (new)"
echo "Cloud Run:            ~$5-10/month (existing)"
echo "Other services:       ~$5-10/month (existing)"
echo "------------------------"
echo "Total estimated:      ~$75-85/month"
echo "Previous total:       ~$45-55/month"
echo "Increase:             ~$30-35/month for persistent caching"

# Next steps
echo ""
echo "🎯 Next Steps:"
echo "=============="
echo "1. Monitor current Cloud SQL performance"
echo "2. Wait for Redis instance to be ready (3-5 minutes)"
echo "3. Review .env.production.new and replace .env.production if satisfied"
echo "4. Update caching middleware to use Redis"
echo "5. Deploy updated API with Redis support"
echo "6. Monitor performance improvements"

echo ""
echo "✅ Setup script completed successfully!"
echo "💡 Run 'gcloud sql operations list --project=$PROJECT_ID' to check upgrade status"
echo "💡 Run 'gcloud redis instances list --region=$REGION --project=$PROJECT_ID' to check Redis status"