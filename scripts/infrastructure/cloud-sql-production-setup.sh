#!/bin/bash
set -e

echo "🚀 Setting up Cloud SQL for giki.ai Production Environment"
echo "Using Google Cloud Shell for secure deployment"
echo "=========================================================="

# Configuration
PROJECT_ID="rezolve-poc"
INSTANCE_NAME="giki-ai-postgres-prod"
REGION="us-central1"
DATABASE_NAME="giki_ai_db"
USERNAME="giki_ai_user"

# Switch to production configuration
echo "1. Activating production configuration..."
gcloud config configurations activate production

# Verify we're in the right project and have permissions
echo "2. Verifying project and permissions..."
CURRENT_PROJECT=$(gcloud config get-value project)
echo "   Current project: $CURRENT_PROJECT"

if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
    echo "❌ Project mismatch! Expected $PROJECT_ID, got $CURRENT_PROJECT"
    exit 1
fi

# Check if instance already exists
echo "3. Checking for existing Cloud SQL instances..."
if gcloud sql instances describe $INSTANCE_NAME >/dev/null 2>&1; then
    echo "⚠️  Instance $INSTANCE_NAME already exists!"
    echo "   Getting existing connection details..."
    CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME --format='value(connectionName)')
    IP_ADDRESS=$(gcloud sql instances describe $INSTANCE_NAME --format='value(ipAddresses[0].ipAddress)')
    echo "   Connection Name: $CONNECTION_NAME"
    echo "   IP Address: $IP_ADDRESS"
    
    # Check if database exists
    if gcloud sql databases describe $DATABASE_NAME --instance=$INSTANCE_NAME >/dev/null 2>&1; then
        echo "   Database $DATABASE_NAME already exists"
    else
        echo "   Creating database $DATABASE_NAME..."
        gcloud sql databases create $DATABASE_NAME --instance=$INSTANCE_NAME
    fi
    
    # Check if user exists  
    if gcloud sql users describe $USERNAME --instance=$INSTANCE_NAME >/dev/null 2>&1; then
        echo "   User $USERNAME already exists"
        echo "   You may need to reset the password manually if needed"
        PASSWORD="<EXISTING_PASSWORD>"
    else
        echo "   Creating user $USERNAME..."
        PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        gcloud sql users create $USERNAME --instance=$INSTANCE_NAME --password=$PASSWORD
        echo "   Generated password: $PASSWORD"
    fi
else
    # Create new instance
    echo "4. Creating new Cloud SQL instance (this takes 5-10 minutes)..."
    
    # Generate secure password
    PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    echo "   Generated secure password: $PASSWORD"
    
    # Create the instance
    gcloud sql instances create $INSTANCE_NAME \
        --database-version=POSTGRES_15 \
        --tier=db-g1-small \
        --region=$REGION \
        --storage-type=SSD \
        --storage-size=20GB \
        --storage-auto-increase \
        --backup \
        --backup-start-time=02:00 \
        --maintenance-window-day=SUN \
        --maintenance-window-hour=03 \
        --maintenance-release-channel=production \
        --availability-type=zonal \
        --deletion-protection
    
    echo "5. Creating database..."
    gcloud sql databases create $DATABASE_NAME --instance=$INSTANCE_NAME
    
    echo "6. Creating user..."
    gcloud sql users create $USERNAME --instance=$INSTANCE_NAME --password=$PASSWORD
    
    echo "7. Getting connection details..."
    CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME --format='value(connectionName)')
    IP_ADDRESS=$(gcloud sql instances describe $INSTANCE_NAME --format='value(ipAddresses[0].ipAddress)')
fi

echo ""
echo "✅ Cloud SQL Setup Complete!"
echo "================================"
echo "Instance: $INSTANCE_NAME"
echo "Connection Name: $CONNECTION_NAME"
echo "IP Address: $IP_ADDRESS"
echo "Database: $DATABASE_NAME"
echo "Username: $USERNAME"
echo "Password: $PASSWORD"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"

# Generate environment configurations
echo ""
echo "8. Generating environment configurations..."

# Production environment file
cat > .env.production << EOF
# Production Environment Configuration - Generated $(date)
# Database: Google Cloud SQL PostgreSQL
DATABASE_URL=postgresql+asyncpg://$USERNAME:$PASSWORD@/giki_ai_db?host=/cloudsql/$CONNECTION_NAME

# Cloud SQL Details (for reference)
CLOUD_SQL_CONNECTION_NAME=$CONNECTION_NAME
CLOUD_SQL_IP=$IP_ADDRESS
CLOUD_SQL_DATABASE=$DATABASE_NAME
CLOUD_SQL_USERNAME=$USERNAME
CLOUD_SQL_PASSWORD=$PASSWORD

# Environment
ENVIRONMENT=production
DEBUG=false

# Performance settings for production
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false
DB_POOL_PRE_PING=true

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=./service-accounts/production/service-account.json
GOOGLE_APPLICATION_CREDENTIALS=./service-accounts/production/service-account.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc
EOF

# Development environment file (PostgreSQL)
cat > .env.development << EOF
# Development Environment Configuration - Generated $(date)
# Database: PostgreSQL (Local development)
DATABASE_URL=postgresql+asyncpg://giki_ai_user:local_dev_password@localhost:5432/giki_ai_dev

# Environment
ENVIRONMENT=development
DEBUG=true

# Google Cloud Configuration (Development)
VERTEX_SERVICE_ACCOUNT_KEY_PATH=./service-accounts/development/service-account.json
GOOGLE_APPLICATION_CREDENTIALS=./service-accounts/development/service-account.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc

# Development settings
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration - React Vite (Development)
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_ENVIRONMENT=development
EOF

echo "✅ Created .env.production (Cloud SQL)"
echo "✅ Created .env.development (PostgreSQL)"

# Cloud Run deployment configuration
cat > cloudbuild.production.yaml << EOF
# Cloud Build configuration for production deployment
steps:
  # Build the container
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/giki-ai-api:production', '.']
  
  # Push to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/giki-ai-api:production']
  
  # Deploy to Cloud Run with Cloud SQL
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'giki-ai-api-prod'
      - '--image=gcr.io/$PROJECT_ID/giki-ai-api:production'
      - '--region=$REGION'
      - '--platform=managed'
      - '--add-cloudsql-instances=$CONNECTION_NAME'
      - '--set-env-vars=DATABASE_URL=postgresql+asyncpg://$USERNAME:$PASSWORD@/giki_ai_db?host=/cloudsql/$CONNECTION_NAME'
      - '--set-env-vars=ENVIRONMENT=production'
      - '--allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--concurrency=100'
      - '--timeout=300'
      - '--max-instances=10'

options:
  logging: CLOUD_LOGGING_ONLY
EOF

echo "✅ Created cloudbuild.production.yaml"

# Local testing script
cat > scripts/test-cloud-sql-local.sh << 'EOF'
#!/bin/bash
echo "🧪 Testing Cloud SQL connection locally"
echo "======================================="

# Check if Cloud SQL Proxy is available
if ! command -v cloud_sql_proxy &> /dev/null; then
    echo "📥 Installing Cloud SQL Proxy..."
    curl -o cloud_sql_proxy https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64
    chmod +x cloud_sql_proxy
    sudo mv cloud_sql_proxy /usr/local/bin/
fi

# Load production environment
if [ ! -f .env.production ]; then
    echo "❌ .env.production not found. Run cloud-sql-production-setup.sh first"
    exit 1
fi

source .env.production

echo "🔌 Starting Cloud SQL Proxy..."
cloud_sql_proxy -instances=$CLOUD_SQL_CONNECTION_NAME=tcp:5432 &
PROXY_PID=$!

# Wait for proxy to start
sleep 3

echo "🧪 Testing connection..."
PGPASSWORD=$CLOUD_SQL_PASSWORD psql -h 127.0.0.1 -p 5432 -U $CLOUD_SQL_USERNAME -d $CLOUD_SQL_DATABASE -c "SELECT 1 as test;" || echo "❌ Connection failed"

echo "🔌 Stopping Cloud SQL Proxy..."
kill $PROXY_PID

echo "✅ Local Cloud SQL test complete"
EOF

chmod +x scripts/test-cloud-sql-local.sh
echo "✅ Created scripts/test-cloud-sql-local.sh"

echo ""
echo "🎯 NEXT STEPS:"
echo "=============="
echo "1. Test local connection:"
echo "   ./scripts/test-cloud-sql-local.sh"
echo ""
echo "2. Deploy to production:"
echo "   gcloud builds submit --config=cloudbuild.production.yaml"
echo ""
echo "3. Check deployment:"
echo "   gcloud run services describe giki-ai-api-prod --region=$REGION"
echo ""
echo "4. Test production endpoint:"
echo "   curl -w '\\nTime: %{time_total}s\\n' https://\$(gcloud run services describe giki-ai-api-prod --region=$REGION --format='value(status.url)')/api/v1/health"

echo ""
echo "💰 COST ESTIMATE:"
echo "=================="
echo "Cloud SQL (db-g1-small): ~\$25/month"
echo "Cloud Run: ~\$10/month (estimated)"
echo "Total: ~\$35/month"

echo ""
echo "⚡ EXPECTED PERFORMANCE:"
echo "========================"
echo "Current database: 3500ms"  
echo "Expected Cloud SQL: <50ms"
echo "Performance improvement: 70x faster"
echo "Target: <200ms health endpoint ✅"