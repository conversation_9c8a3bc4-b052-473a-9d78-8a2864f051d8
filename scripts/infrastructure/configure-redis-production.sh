#!/bin/bash

# Configure Redis for Production Environment
# This script updates Cloud Run with Redis environment variables

set -e

PROJECT_ID="rezolve-poc"
REGION="us-central1"
SERVICE_NAME="giki-ai-api"
REDIS_INSTANCE="giki-ai-redis-production"

echo "🔧 Configuring Redis for Production Environment"
echo "==============================================="

# Check authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ No active gcloud authentication found."
    echo "Please run: gcloud auth login"
    exit 1
fi

# Wait for Redis instance to be ready
echo "1. Waiting for Redis instance to be ready..."
while true; do
    STATE=$(gcloud redis instances describe $REDIS_INSTANCE \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format="value(state)" 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$STATE" = "READY" ]; then
        echo "✅ Redis instance is ready"
        break
    elif [ "$STATE" = "NOT_FOUND" ]; then
        echo "❌ Redis instance not found. Please create it first."
        exit 1
    else
        echo "⏳ Redis instance state: $STATE (waiting...)"
        sleep 10
    fi
done

# Get Redis connection details
echo "2. Getting Redis connection details..."
REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(host)")

REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(port)")

if [ -z "$REDIS_HOST" ] || [ -z "$REDIS_PORT" ]; then
    echo "❌ Failed to get Redis connection details"
    exit 1
fi

echo "✅ Redis Host: $REDIS_HOST"
echo "✅ Redis Port: $REDIS_PORT"

# Update Cloud Run service with Redis environment variables
echo "3. Updating Cloud Run service with Redis configuration..."

gcloud run services update $SERVICE_NAME \
    --project=$PROJECT_ID \
    --region=$REGION \
    --set-env-vars="REDIS_ENABLED=true,REDIS_HOST=$REDIS_HOST,REDIS_PORT=$REDIS_PORT,REDIS_URL=redis://$REDIS_HOST:$REDIS_PORT"

echo "✅ Cloud Run service updated with Redis configuration"

# Verify the deployment
echo "4. Verifying deployment..."
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(status.url)')

echo "Service URL: $SERVICE_URL"

# Test the health endpoint
echo "5. Testing health endpoint..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health" || echo "000")

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Health check passed (HTTP $HTTP_CODE)"
else
    echo "⚠️  Health check returned HTTP $HTTP_CODE"
fi

# Test Redis configuration
echo "6. Testing Redis configuration..."
curl -s "$SERVICE_URL/api/v1/cache/stats" | head -5 || echo "Cache stats endpoint not available yet"

echo ""
echo "✅ Redis configuration completed successfully!"
echo ""
echo "📋 Summary:"
echo "  - Redis instance: $REDIS_INSTANCE"
echo "  - Redis host: $REDIS_HOST:$REDIS_PORT"
echo "  - Service: $SERVICE_NAME"
echo "  - URL: $SERVICE_URL"
echo ""
echo "💡 Monitor the service logs: gcloud logs tail --project=$PROJECT_ID"
echo "💡 Check cache performance: curl $SERVICE_URL/api/v1/cache/stats"