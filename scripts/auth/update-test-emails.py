#!/usr/bin/env python3
"""Update test user emails to use valid domains."""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# Load environment
load_dotenv(".env.development")

DATABASE_URL = os.getenv("DATABASE_URL", "").replace("postgresql+asyncpg://", "postgresql://")


async def update_test_emails():
    """Update test user emails to use valid test domains."""
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Mapping of old emails to new emails
        email_updates = [
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),
        ]
        
        for old_email, new_email in email_updates:
            # Update email and username
            result = await conn.execute("""
                UPDATE users 
                SET email = $1, username = $1
                WHERE email = $2
            """, new_email, old_email)
            print(f"✅ Updated {old_email} → {new_email}")
        
        # Verify the updates
        users = await conn.fetch("""
            SELECT id, email, username, tenant_id, is_admin
            FROM users 
            WHERE email LIKE '%@example.com'
            ORDER BY email
        """)
        
        print("\n📋 Updated Test Users:")
        for user in users:
            tenant_name = {1: "Test Tenant", 2: "Rezolve AI", 3: "Nuvie"}.get(user['tenant_id'], "Unknown")
            print(f"  - {user['email']} ({tenant_name}, admin: {user['is_admin']})")
            
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(update_test_emails())