#!/usr/bin/env python3
"""
Verify Authentication
=====================
Tests authentication for all configured test users.
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Tuple

# Test users configuration
DEV_USERS = [
    ("<EMAIL>", "GikiDev2025#Secure", "Test Tenant"),
    ("<EMAIL>", "RezolveDev2025#Secure", "Rezolve AI"),
    ("<EMAIL>", "NuvieDev2025#Secure", "Nuvie"),
    ("<EMAIL>", "AdminDev2025#Master", "Admin - Test Tenant"),
]

PROD_USERS = [
    ("<EMAIL>", "GikiProd2025#Live", "Test Tenant"),
    ("<EMAIL>", "RezolveProd2025#Live", "Rezolve AI"),
    ("<EMAIL>", "NuvieProd2025#Live", "Nuvie"),
]


async def test_auth(base_url: str, username: str, password: str) -> Tuple[bool, str, Dict]:
    """Test authentication for a single user."""
    async with aiohttp.ClientSession() as session:
        try:
            # Attempt login
            login_data = {
                "username": username,
                "password": password
            }
            
            async with session.post(
                f"{base_url}/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            ) as response:
                response_text = await response.text()
                
                if response.status == 200:
                    data = json.loads(response_text)
                    token = data.get("access_token", "")
                    
                    # Test authenticated endpoint
                    headers = {"Authorization": f"Bearer {token}"}
                    async with session.get(
                        f"{base_url}/api/v1/dashboard/metrics",
                        headers=headers
                    ) as auth_response:
                        if auth_response.status == 200:
                            return True, token[:20] + "...", data
                        else:
                            return False, f"Auth endpoint failed: {auth_response.status}", {}
                else:
                    return False, f"Login failed: {response.status} - {response_text}", {}
                    
        except Exception as e:
            return False, f"Error: {str(e)}", {}


async def verify_all_users():
    """Verify authentication for all configured users."""
    print("=" * 60)
    print("AUTHENTICATION VERIFICATION")
    print("=" * 60)
    
    # Test development environment
    print("\n🔧 DEVELOPMENT ENVIRONMENT (http://localhost:8000)")
    print("-" * 60)
    
    for username, password, tenant in DEV_USERS:
        success, result, data = await test_auth("http://localhost:8000", username, password)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {username:25} | {tenant:20}")
        if success:
            print(f"     Token: {result}")
        else:
            print(f"     Error: {result}")
    
    # Test production environment (only if accessible)
    print("\n🚀 PRODUCTION ENVIRONMENT (https://giki-ai-api-6uyufgxcxa-uc.a.run.app)")
    print("-" * 60)
    print("⚠️  Note: Production users must be created in production database first!")
    
    for username, password, tenant in PROD_USERS:
        success, result, data = await test_auth("https://giki-ai-api-6uyufgxcxa-uc.a.run.app", username, password)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {username:25} | {tenant:20}")
        if success:
            print(f"     Token: {result}")
        else:
            print(f"     Error: {result}")
    
    print("\n" + "=" * 60)
    print("Verification complete!")


if __name__ == "__main__":
    asyncio.run(verify_all_users())