#!/usr/bin/env python3
"""Update Nuvie test user password to match documentation."""

import asyncio
import asyncpg
import os
from passlib.context import CryptContext
from dotenv import load_dotenv

# Load environment
load_dotenv(".env.development")

DATABASE_URL = os.getenv("DATABASE_URL", "").replace("postgresql+asyncpg://", "postgresql://")

# Password context with 4 rounds for dev
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=4)


async def update_nuvie_password():
    """Update Nuvie user password to match documentation."""
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Generate hash for the documented password
        new_password = "NuvieDev2025#Test"
        new_hash = pwd_context.hash(new_password)
        
        # Update password
        result = await conn.execute("""
            UPDATE users 
            SET hashed_password = $1
            WHERE email = '<EMAIL>'
        """, new_hash)
        
        print(f"✅ Updated Nuvie user password")
        print(f"   Email: <EMAIL>")
        print(f"   Password: {new_password}")
        print(f"   New hash: {new_hash}")
        
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(update_nuvie_password())