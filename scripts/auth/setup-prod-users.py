#!/usr/bin/env python3
"""
Setup Production Test Users
===========================
Creates test users for production environment with proper bcrypt hashing.
Uses 12 rounds for standard security in production.
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add the project source to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "apps" / "giki-ai-api" / "src"))

try:
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import text
    from passlib.context import CryptContext
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all dependencies are installed.")
    sys.exit(1)

# Production password hashing (12 rounds for security)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=12)

# Test users for production
PROD_USERS = [
    {
        "username": "giki_prod",
        "email": "<EMAIL>",
        "password": "GikiProd2025#Live",
        "tenant_id": 1,  # Test Tenant
        "is_admin": False,
    },
    {
        "username": "rezolve_prod",
        "email": "<EMAIL>",
        "password": "RezolveProd2025#Live",
        "tenant_id": 2,  # Rezolve AI
        "is_admin": False,
    },
    {
        "username": "nuvie_prod",
        "email": "<EMAIL>",
        "password": "NuvieProd2025#Live",
        "tenant_id": 3,  # Nuvie
        "is_admin": False,
    },
]


async def setup_prod_users():
    """Create production test users with proper credentials."""
    
    # Database connection - this should be updated for production
    # For now, using local database for setup
    DATABASE_URL = "postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db"
    
    # Use asyncpg directly to avoid greenlet issues
    import asyncpg
    
    conn = await asyncpg.connect(DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://"))
    
    try:
        print("Setting up production test users...")
        print("=" * 50)
        print("⚠️  WARNING: Run this against production database when deploying!")
        print("=" * 50)
        
        for user_data in PROD_USERS:
            # Check if user already exists
            existing = await conn.fetchrow(
                "SELECT id FROM users WHERE email = $1",
                user_data["email"]
            )
            
            # Hash the password
            hashed_password = pwd_context.hash(user_data["password"])
            
            if existing:
                # Update existing user
                await conn.execute("""
                    UPDATE users 
                    SET username = $1, 
                        hashed_password = $2, 
                        is_admin = $3,
                        tenant_id = $4,
                        updated_at = $5
                    WHERE email = $6
                """, 
                    user_data["username"],
                    hashed_password,
                    user_data["is_admin"],
                    user_data["tenant_id"],
                    datetime.utcnow(),
                    user_data["email"]
                )
                print(f"✓ Updated: {user_data['email']}")
            else:
                # Create new user
                await conn.execute("""
                    INSERT INTO users (username, email, hashed_password, is_active, is_admin, tenant_id, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                    user_data["username"],
                    user_data["email"],
                    hashed_password,
                    True,  # is_active
                    user_data["is_admin"],
                    user_data["tenant_id"],
                    datetime.utcnow(),
                    datetime.utcnow()
                )
                print(f"✓ Created: {user_data['email']}")
            
            # Print the hash for documentation
            print(f"  Password: {user_data['password']}")
            print(f"  Hash: {hashed_password}")
            print()
        
        print("=" * 50)
        print("✅ All production users ready!")
        print("\nTest authentication with:")
        print('curl -X POST https://giki-ai-api-6uyufgxcxa-uc.a.run.app/api/v1/auth/login \\')
        print('  -H "Content-Type: application/x-www-form-urlencoded" \\')
        print('  -d "username=<EMAIL>&password=GikiProd2025#Live"')
        
    except Exception as e:
        print(f"Error setting up users: {e}")
        raise
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(setup_prod_users())