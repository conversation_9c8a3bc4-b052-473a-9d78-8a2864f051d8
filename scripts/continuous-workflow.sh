#!/bin/bash

# continuous-workflow.sh - Automatic Work Discovery and Execution Engine
# Implements the continuous task discovery system from CLAUDE.md
# NEVER returns "no work found" - always discovers and executes the next most valuable task

set -e

# Color output for better visibility
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🔄 CONTINUOUS WORKFLOW ENGINE STARTING${NC}"
echo -e "${CYAN}========================================${NC}"

# LEVEL 0: PLAY<PERSON>IGHT MCP DISCOVERY (Primary Starting Point)
echo -e "\n${BLUE}🎭 LEVEL 0: P<PERSON>YWRIGHT MCP PRODUCT DISCOVERY${NC}"
echo -e "${YELLOW}Using the product to find real customer issues...${NC}"

# Check if servers are running
echo -e "\n${BLUE}📊 Server Status Check${NC}"
./scripts/server-status.sh

# If servers not running, start them automatically
if ! curl -s http://localhost:4200 > /dev/null 2>&1; then
    echo -e "${YELLOW}⚡ Starting frontend server...${NC}"
    nohup pnpm serve:app > logs/frontend-server.log 2>&1 &
    sleep 5
fi

if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${YELLOW}⚡ Starting API server...${NC}"
    nohup pnpm serve:api > logs/api-server.log 2>&1 &
    sleep 10
fi

# Comprehensive Playwright MCP discovery
echo -e "\n${BLUE}🔍 COMPREHENSIVE FEATURE DISCOVERY${NC}"
echo "Priority 1: Authentication & Core Workflow Testing"
echo "Priority 2: Individual Feature Validation" 
echo "Priority 3: User Experience Flow Testing"

# Check for immediate JavaScript errors
echo -e "\n${YELLOW}📱 Checking for JavaScript errors...${NC}"
RECENT_ERRORS=$(tail -n 50 logs/frontend-server.log | grep -i "error" | wc -l)
if [ "$RECENT_ERRORS" -gt 0 ]; then
    echo -e "${RED}⚠️  Found $RECENT_ERRORS frontend errors in logs${NC}"
    echo -e "${GREEN}➡️  IMMEDIATE WORK DISCOVERED: Fix frontend JavaScript errors${NC}"
    echo "Execute: /project:work -> Fix frontend errors found in logs"
    exit 0
fi

# Check API health
echo -e "\n${YELLOW}🔧 Checking API health...${NC}"
API_ERRORS=$(tail -n 50 logs/api-server.log | grep -E "ERROR|CRITICAL|500|503" | wc -l)
if [ "$API_ERRORS" -gt 0 ]; then
    echo -e "${RED}⚠️  Found $API_ERRORS API errors in logs${NC}"
    echo -e "${GREEN}➡️  IMMEDIATE WORK DISCOVERED: Fix API errors${NC}"
    echo "Execute: /project:work -> Investigate API errors in logs"
    exit 0
fi

# LEVEL 1: SPECIFICATION GAP DISCOVERY
echo -e "\n${BLUE}📋 LEVEL 1: SPECIFICATION GAP ANALYSIS${NC}"
echo -e "${YELLOW}Checking for incomplete specifications and missing features...${NC}"

# Check if core requirements are implemented
check_requirement() {
    local req_id="$1"
    local description="$2"
    echo -e "\n${CYAN}Checking $req_id: $description${NC}"
    
    case "$req_id" in
        "BR-001")
            # Multi-level categorization with GL codes
            if ! grep -q "gl_code" apps/giki-ai-api/src/giki_ai_api/domains/categories/models.py 2>/dev/null; then
                echo -e "${RED}❌ GL codes not implemented in category models${NC}"
                return 1
            fi
            ;;
        "BR-002")
            # >85% categorization accuracy
            if ! find . -name "*accuracy*" -type f | grep -q .; then
                echo -e "${RED}❌ Accuracy validation system not found${NC}"
                return 1
            fi
            ;;
        "FR-001")
            # Excel/CSV upload and processing
            if ! find apps/giki-ai-app/src -name "*upload*" -type f | grep -q .; then
                echo -e "${RED}❌ Upload components not found${NC}"
                return 1
            fi
            ;;
        "FR-004")
            # AI-powered categorization
            if ! find apps/giki-ai-api/src -name "*categorization*" -type f | grep -q .; then
                echo -e "${RED}❌ Categorization system not found${NC}"
                return 1
            fi
            ;;
    esac
    
    echo -e "${GREEN}✅ $req_id appears implemented${NC}"
    return 0
}

# Check core requirements
MISSING_REQUIREMENTS=0

check_requirement "BR-001" "Multi-level categorization with GL codes" || ((MISSING_REQUIREMENTS++))
check_requirement "BR-002" ">85% categorization accuracy" || ((MISSING_REQUIREMENTS++))
check_requirement "FR-001" "Excel/CSV upload and processing" || ((MISSING_REQUIREMENTS++))
check_requirement "FR-004" "AI-powered categorization" || ((MISSING_REQUIREMENTS++))

if [ "$MISSING_REQUIREMENTS" -gt 0 ]; then
    echo -e "\n${RED}📋 SPECIFICATION GAPS DISCOVERED: $MISSING_REQUIREMENTS requirements incomplete${NC}"
    echo -e "${GREEN}➡️  WORK DISCOVERED: Implement missing requirements${NC}"
    echo "Execute: /project:work -> Address specification gaps"
    exit 0
fi

# LEVEL 2: TASK QUEUE DISCOVERY
echo -e "\n${BLUE}📝 LEVEL 2: ACTIVE TASK QUEUE ANALYSIS${NC}"
echo -e "${YELLOW}Checking for ready-to-start tasks...${NC}"

# Check if there are pending high-priority tasks
if [ -f "docs/tasks/work-queue.md" ]; then
    READY_TASKS=$(grep -c "🔧 READY" docs/tasks/work-queue.md || echo "0")
    if [ "$READY_TASKS" -gt 0 ]; then
        echo -e "${GREEN}📋 DISCOVERED: $READY_TASKS ready-to-start tasks in work queue${NC}"
        echo -e "${GREEN}➡️  WORK DISCOVERED: Execute ready tasks from work queue${NC}"
        echo "Execute: /project:work -> Start with ready tasks in work-queue.md"
        exit 0
    fi
fi

# LEVEL 3: CUSTOMER EXPERIENCE IMPROVEMENTS
echo -e "\n${BLUE}💡 LEVEL 3: CUSTOMER EXPERIENCE DISCOVERY${NC}"
echo -e "${YELLOW}Looking for UX improvements and customer friction points...${NC}"

# Check for common UX improvement opportunities
UX_IMPROVEMENTS=()

# Check for missing error handling
if ! grep -r "try.*catch" apps/giki-ai-app/src/ > /dev/null 2>&1; then
    UX_IMPROVEMENTS+=("Add error handling to frontend components")
fi

# Check for loading states
if ! grep -r "loading" apps/giki-ai-app/src/ > /dev/null 2>&1; then
    UX_IMPROVEMENTS+=("Add loading states for better user feedback")
fi

# Check for input validation
if ! grep -r "validation" apps/giki-ai-app/src/ > /dev/null 2>&1; then
    UX_IMPROVEMENTS+=("Add form validation for better user experience")
fi

if [ ${#UX_IMPROVEMENTS[@]} -gt 0 ]; then
    echo -e "\n${GREEN}💡 CUSTOMER EXPERIENCE IMPROVEMENTS DISCOVERED:${NC}"
    for improvement in "${UX_IMPROVEMENTS[@]}"; do
        echo -e "${CYAN}  • $improvement${NC}"
    done
    echo -e "${GREEN}➡️  WORK DISCOVERED: Improve customer experience${NC}"
    echo "Execute: /project:work -> Implement UX improvements"
    exit 0
fi

# LEVEL 4: TECHNICAL DEBT & POLISH
echo -e "\n${BLUE}🔧 LEVEL 4: TECHNICAL DEBT DISCOVERY${NC}"
echo -e "${YELLOW}Analyzing code quality and technical improvements...${NC}"

TECH_DEBT=()

# Check for TODO comments
TODO_COUNT=$(find apps/ -type f \( -name "*.py" -o -name "*.ts" -o -name "*.tsx" \) -exec grep -l "TODO\|FIXME\|HACK" {} \; | wc -l)
if [ "$TODO_COUNT" -gt 0 ]; then
    TECH_DEBT+=("$TODO_COUNT files contain TODO/FIXME/HACK comments")
fi

# Check for console.log statements in production code
CONSOLE_LOGS=$(find apps/giki-ai-app/src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec grep -l "console\." {} \; | wc -l)
if [ "$CONSOLE_LOGS" -gt 0 ]; then
    TECH_DEBT+=("$CONSOLE_LOGS files contain console.log statements")
fi

# Check for unused imports
UNUSED_IMPORTS=$(find apps/giki-ai-app/src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec grep -l "import.*from.*;" {} \; | head -5 | wc -l)
if [ "$UNUSED_IMPORTS" -gt 0 ]; then
    TECH_DEBT+=("Potential unused imports detected")
fi

if [ ${#TECH_DEBT[@]} -gt 0 ]; then
    echo -e "\n${GREEN}🔧 TECHNICAL DEBT DISCOVERED:${NC}"
    for debt in "${TECH_DEBT[@]}"; do
        echo -e "${CYAN}  • $debt${NC}"
    done
    echo -e "${GREEN}➡️  WORK DISCOVERED: Address technical debt${NC}"
    echo "Execute: /project:work -> Clean up technical debt"
    exit 0
fi

# LEVEL 5: VALIDATION & QUALITY
echo -e "\n${BLUE}🧪 LEVEL 5: VALIDATION & QUALITY DISCOVERY${NC}"
echo -e "${YELLOW}Running comprehensive quality checks...${NC}"

# Check if E2E tests are passing
echo -e "\n${CYAN}🧪 Running E2E test validation...${NC}"
if pnpm test:e2e --reporter=json > /tmp/test-results.json 2>/dev/null; then
    PASSING_TESTS=$(jq -r '.stats.passes // 0' /tmp/test-results.json 2>/dev/null || echo "0")
    FAILING_TESTS=$(jq -r '.stats.failures // 0' /tmp/test-results.json 2>/dev/null || echo "0")
    
    if [ "$FAILING_TESTS" -gt 0 ]; then
        echo -e "${RED}❌ $FAILING_TESTS E2E tests failing${NC}"
        echo -e "${GREEN}➡️  WORK DISCOVERED: Fix failing E2E tests${NC}"
        echo "Execute: /project:work -> Fix E2E test failures"
        exit 0
    else
        echo -e "${GREEN}✅ All $PASSING_TESTS E2E tests passing${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Could not run E2E tests - may need setup${NC}"
    echo -e "${GREEN}➡️  WORK DISCOVERED: Fix E2E test infrastructure${NC}"
    echo "Execute: /project:work -> Setup and fix E2E testing"
    exit 0
fi

# LEVEL 6: ADVANCED DISCOVERY (Should rarely reach here)
echo -e "\n${BLUE}🚀 LEVEL 6: ADVANCED OPPORTUNITY DISCOVERY${NC}"
echo -e "${YELLOW}Looking for advanced improvements and optimizations...${NC}"

ADVANCED_WORK=()

# Check for performance optimization opportunities
if ! grep -r "performance\|optimization\|cache" apps/ > /dev/null 2>&1; then
    ADVANCED_WORK+=("Add performance monitoring and optimization")
fi

# Check for security improvements
if ! grep -r "security\|sanitize\|validate" apps/ > /dev/null 2>&1; then
    ADVANCED_WORK+=("Add security hardening and input sanitization")
fi

# Check for monitoring and observability
if ! find . -name "*monitor*" -o -name "*metrics*" -o -name "*logging*" | grep -q .; then
    ADVANCED_WORK+=("Add monitoring and observability features")
fi

if [ ${#ADVANCED_WORK[@]} -gt 0 ]; then
    echo -e "\n${GREEN}🚀 ADVANCED OPPORTUNITIES DISCOVERED:${NC}"
    for work in "${ADVANCED_WORK[@]}"; do
        echo -e "${CYAN}  • $work${NC}"
    done
    echo -e "${GREEN}➡️  WORK DISCOVERED: Implement advanced improvements${NC}"
    echo "Execute: /project:work -> Work on advanced features"
    exit 0
fi

# FALLBACK: This should NEVER happen according to CLAUDE.md
echo -e "\n${RED}🤔 EXTREMELY RARE: No immediate work discovered across all levels${NC}"
echo -e "${YELLOW}This suggests the system is in an unusually complete state${NC}"
echo -e "${GREEN}➡️  FALLBACK WORK: Run comprehensive system validation${NC}"
echo ""
echo -e "${CYAN}Suggested actions:${NC}"
echo -e "${CYAN}1. Run full Playwright MCP test suite with all edge cases${NC}"
echo -e "${CYAN}2. Test with different browsers and screen sizes${NC}"  
echo -e "${CYAN}3. Stress test with large data files${NC}"
echo -e "${CYAN}4. Review and update documentation${NC}"
echo -e "${CYAN}5. Plan next major feature implementation${NC}"

echo -e "\n${CYAN}========================================${NC}"
echo -e "${CYAN}🔄 CONTINUOUS WORKFLOW ENGINE COMPLETE${NC}"
echo -e "${CYAN}========================================${NC}"

exit 0