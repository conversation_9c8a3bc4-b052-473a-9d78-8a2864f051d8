#!/bin/bash

# Restart Development Servers
# Stops and then starts both servers with fresh logs

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(dirname "$(realpath "$0")")"

echo -e "${YELLOW}🔄 Restarting development servers...${NC}"
echo

# Stop servers
"$SCRIPT_DIR/stop-servers.sh"

echo
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo

# Wait a moment to ensure ports are freed
sleep 2

# Start servers
"$SCRIPT_DIR/start-servers.sh"