#!/bin/bash

# Setup Git Hooks for Real AI Tests Enforcement
# This script configures git hooks to enforce real AI testing requirements

set -e

echo "🔧 Setting up Git Hooks for Real AI Tests Enforcement"
echo "===================================================="

# Configure git to use our custom hooks directory
echo "📁 Configuring git hooks directory..."
git config core.hooksPath .githooks

# Make all hooks executable
echo "🔐 Making hooks executable..."
chmod +x .githooks/*

# Verify hooks are set up correctly
echo "✅ Verifying git hooks configuration..."
HOOKS_PATH=$(git config core.hooksPath)

if [ "$HOOKS_PATH" = ".githooks" ]; then
    echo "✅ Git hooks directory configured: $HOOKS_PATH"
else
    echo "❌ Failed to configure git hooks directory"
    exit 1
fi

# List available hooks
echo "📋 Available hooks:"
ls -la .githooks/

echo ""
echo "🎯 Git hooks successfully configured!"
echo "The following real AI enforcement is now active:"
echo "  • Pre-commit: Blocks AI mocking patterns"
echo "  • Pre-commit: Validates AI service implementations"
echo "  • Pre-commit: Checks for problematic mock dependencies"
echo ""
echo "Real AI tests are now mandatory for all commits! 🤖"
