#!/usr/bin/env python3
"""
Create Test User Script
======================
Creates a test user for API authentication validation with properly hashed password.
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# Add the project source to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "apps" / "giki-ai-api" / "src"))

try:
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import text
    from passlib.context import CryptContext
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all dependencies are installed.")
    sys.exit(1)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def create_test_user():
    """Create a test user with known credentials for API validation."""
    
    # Database connection
    DATABASE_URL = "postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db"
    
    engine = create_async_engine(DATABASE_URL, echo=True)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        try:
            # Check if test user already exists
            result = await session.execute(
                text("SELECT id FROM users WHERE email = :email"),
                {"email": "<EMAIL>"}
            )
            existing = result.fetchone()
            
            if existing:
                print("Test user already exists, updating password...")
                # Update password
                hashed_password = pwd_context.hash("testpass123")
                await session.execute(
                    text("""
                        UPDATE users 
                        SET hashed_password = :password, 
                            updated_at = :updated_at
                        WHERE email = :email
                    """),
                    {
                        "password": hashed_password,
                        "email": "<EMAIL>",
                        "updated_at": datetime.utcnow()
                    }
                )
            else:
                print("Creating new test user...")
                # Create new user
                hashed_password = pwd_context.hash("testpass123")
                await session.execute(
                    text("""
                        INSERT INTO users (username, email, hashed_password, is_active, is_admin, tenant_id, created_at, updated_at)
                        VALUES (:username, :email, :password, :is_active, :is_admin, :tenant_id, :created_at, :updated_at)
                    """),
                    {
                        "username": "testuser",
                        "email": "<EMAIL>",
                        "password": hashed_password,
                        "is_active": True,
                        "is_admin": False,
                        "tenant_id": 1,  # Test Tenant
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow()
                    }
                )
            
            await session.commit()
            print("\n✅ Test user ready!")
            print("Email: <EMAIL>")
            print("Password: testpass123")
            print("\nTest with:")
            print('curl -X POST http://localhost:8000/api/v1/auth/login -H "Content-Type: application/x-www-form-urlencoded" -d "username=<EMAIL>&password=testpass123"')
            
        except Exception as e:
            print(f"Error creating test user: {e}")
            await session.rollback()
            raise
    
    await engine.dispose()

if __name__ == "__main__":
    asyncio.run(create_test_user())