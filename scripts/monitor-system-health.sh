#!/bin/bash

# System Health Monitoring Script
# Runs E2E tests continuously to track system health as fixes are applied

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_FILE="tests/e2e/system-health.spec.ts"
INTERVAL=60  # Run tests every 60 seconds
LOG_FILE="logs/system-health-monitor.log"

# Ensure log directory exists
mkdir -p logs

echo -e "${BLUE}System Health Monitor Started${NC}"
echo "Running E2E tests every ${INTERVAL} seconds"
echo "Press Ctrl+C to stop"
echo ""

# Function to run tests and display results
run_health_check() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "\n${YELLOW}[$timestamp] Running system health check...${NC}"
    
    # Run the E2E test
    if npx playwright test "$TEST_FILE" --reporter=list 2>&1 | tee -a "$LOG_FILE"; then
        echo -e "${GREEN}✓ All health checks passed${NC}"
        return 0
    else
        echo -e "${RED}✗ Some health checks failed${NC}"
        return 1
    fi
}

# Function to display current system status
display_status() {
    echo -e "\n${BLUE}=== Current System Status ===${NC}"
    
    # Check if servers are running
    if lsof -i :8000 > /dev/null 2>&1; then
        echo -e "API Server: ${GREEN}Running${NC} (port 8000)"
    else
        echo -e "API Server: ${RED}Not Running${NC}"
    fi
    
    if lsof -i :4200 > /dev/null 2>&1; then
        echo -e "Frontend: ${GREEN}Running${NC} (port 4200)"
    else
        echo -e "Frontend: ${RED}Not Running${NC}"
    fi
    
    # Check process count
    local node_count=$(ps aux | grep -E "node|npm|pnpm" | grep -v grep | wc -l)
    local python_count=$(ps aux | grep -E "python|uvicorn" | grep -v grep | wc -l)
    echo -e "Node processes: $node_count"
    echo -e "Python processes: $python_count"
    
    echo -e "${BLUE}=========================${NC}\n"
}

# Main monitoring loop
while true; do
    display_status
    
    if run_health_check; then
        HEALTH_STATUS="${GREEN}HEALTHY${NC}"
    else
        HEALTH_STATUS="${RED}UNHEALTHY${NC}"
    fi
    
    echo -e "\nOverall System Status: $HEALTH_STATUS"
    echo -e "Next check in ${INTERVAL} seconds...\n"
    
    # Sleep for the interval
    sleep $INTERVAL
done