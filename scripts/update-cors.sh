#!/bin/bash
# Quick script to update CORS configuration on Cloud Run

set -e

echo "🔄 Updating CORS configuration on Cloud Run..."

PROJECT_ID="rezolve-poc"
REGION="us-central1"
SERVICE_NAME="giki-ai-api"

# Update only environment variables
gcloud run services update $SERVICE_NAME \
    --region $REGION \
    --project $PROJECT_ID \
    --update-env-vars="CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-273348121056.us-central1.run.app,https://app-giki-ai.web.app"

echo "✅ CORS configuration updated!"
echo ""
echo "🔍 Verifying update..."
gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID --format="get(spec.template.spec.containers[0].env)" | grep CORS || echo "CORS not found in env vars"

echo ""
echo "🌐 Service URL:"
gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID --format="value(status.url)"