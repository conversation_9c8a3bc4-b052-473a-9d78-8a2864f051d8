#!/bin/bash
# File size monitoring script
# Usage: ./scripts/check-file-sizes.sh

echo "=== File Size Audit ==="
echo "Checking for files exceeding size limits..."

violations=0

# Check Python files (CRITICAL: max 1000 lines, target 500)
echo -e "\n--- Python Files (CRITICAL: max 1000, target 500) ---"
python_critical=$(find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 1000 {print "CRITICAL: " $2 " has " $1 " lines (max 1000)"; count++} END {print count+0}')
python_warnings=$(find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 500 && $1 <= 1000 {print "WARNING: " $2 " has " $1 " lines (target 500)"; count++} END {print count+0}')
if [ "$python_critical" != "0" ]; then
    find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 1000 {print "CRITICAL: " $2 " has " $1 " lines (max 1000)"}'
    violations=$((violations + python_critical))
fi
if [ "$python_warnings" != "0" ]; then
    find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 500 && $1 <= 1000 {print "WARNING: " $2 " has " $1 " lines (target 500)"}'
fi

# Check TypeScript/JavaScript files (CRITICAL: max 1000 lines, target 500)
echo -e "\n--- TypeScript/JavaScript Files (CRITICAL: max 1000, target 500) ---"
ts_critical=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v ".vite-cache" | grep -v ".nx/cache" | xargs wc -l 2>/dev/null | awk '$1 > 1000 {print "CRITICAL: " $2 " has " $1 " lines (max 1000)"; count++} END {print count+0}')
ts_warnings=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v ".vite-cache" | grep -v ".nx/cache" | xargs wc -l 2>/dev/null | awk '$1 > 500 && $1 <= 1000 {print "WARNING: " $2 " has " $1 " lines (target 500)"; count++} END {print count+0}')
if [ "$ts_critical" != "0" ]; then
    find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v ".vite-cache" | grep -v ".nx/cache" | xargs wc -l 2>/dev/null | awk '$1 > 1000 {print "CRITICAL: " $2 " has " $1 " lines (max 1000)"}'
    violations=$((violations + ts_critical))
fi
if [ "$ts_warnings" != "0" ]; then
    find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | grep -v ".vite-cache" | grep -v ".nx/cache" | xargs wc -l 2>/dev/null | awk '$1 > 500 && $1 <= 1000 {print "WARNING: " $2 " has " $1 " lines (target 500)"}'
fi

# Check Markdown files (max 300 lines)
echo -e "\n--- Markdown Files (max 300 lines) ---"
md_violations=$(find . -name "*.md" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 300 {print "VIOLATION: " $2 " has " $1 " lines (max 300)"; count++} END {print count+0}')
if [ "$md_violations" != "0" ]; then
    find . -name "*.md" -not -path "./node_modules/*" -exec wc -l {} + | awk '$1 > 300 {print "VIOLATION: " $2 " has " $1 " lines (max 300)"}'
    violations=$((violations + md_violations))
fi

# Check JSON/YAML files (max 200 lines)
echo -e "\n--- JSON/YAML Files (max 200 lines) ---"
config_violations=$(find . -name "*.json" -o -name "*.yaml" -o -name "*.yml" | grep -v node_modules | xargs wc -l 2>/dev/null | awk '$1 > 200 {print "VIOLATION: " $2 " has " $1 " lines (max 200)"; count++} END {print count+0}')
if [ "$config_violations" != "0" ]; then
    find . -name "*.json" -o -name "*.yaml" -o -name "*.yml" | grep -v node_modules | xargs wc -l 2>/dev/null | awk '$1 > 200 {print "VIOLATION: " $2 " has " $1 " lines (max 200)"}'
    violations=$((violations + config_violations))
fi

# Summary
echo -e "\n=== Summary ==="
if [ "$violations" -eq "0" ]; then
    echo "✅ All files comply with size limits"
    exit 0
else
    echo "❌ Found $violations file size violations"
    echo "Run 'pnpm refactor:files' to automatically split large files"
    exit 1
fi