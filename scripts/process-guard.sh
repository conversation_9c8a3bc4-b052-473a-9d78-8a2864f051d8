#!/bin/bash
# Process guard - prevent starting if too many processes exist

MAX_PROCESSES=15
CURRENT_PROCESSES=$(ps aux | grep -E "(node|python)" | grep -v grep | wc -l)

echo "🔍 Current processes: $CURRENT_PROCESSES (max: $MAX_PROCESSES)"

if [ $CURRENT_PROCESSES -gt $MAX_PROCESSES ]; then
    echo "🚨 TOO MANY PROCESSES DETECTED ($CURRENT_PROCESSES > $MAX_PROCESSES)"
    echo "Run 'pnpm cleanup:processes' first, then try again"
    exit 1
fi

echo "✅ Process count OK - proceeding..."