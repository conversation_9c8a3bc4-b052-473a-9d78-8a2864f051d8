#!/bin/bash

# Multi-Agent Server Status Checker
# Prevents agents from starting duplicate servers

check_server() {
    local port=$1
    local name=$2
    local health_endpoint=$3
    
    echo "🔍 Checking $name on port $port..."
    
    # Check if port is in use
    if ! lsof -i :$port > /dev/null 2>&1; then
        echo "❌ $name: Port $port not in use"
        return 1
    fi
    
    # Check if server responds to health check
    if [ -n "$health_endpoint" ]; then
        if curl -s -f "$health_endpoint" > /dev/null 2>&1; then
            echo "✅ $name: Running and responding"
            return 0
        else
            echo "⚠️  $name: Port in use but not responding to health check"
            return 1
        fi
    else
        echo "✅ $name: Port in use"
        return 0
    fi
}

echo "🚀 Multi-Agent Development Server Status"
echo "========================================"

# Check API Server (FastAPI)
api_ok=false
if check_server 8000 "API Server (FastAPI)" "http://localhost:8000/health"; then
    api_ok=true
fi

echo

# Check Frontend Server (React/Vite)  
frontend_ok=false
if check_server 4200 "Frontend Server (Vite)" "http://localhost:4200"; then
    frontend_ok=true
fi

echo
echo "📊 Status Summary:"
echo "=================="

if $api_ok && $frontend_ok; then
    echo "🎉 ALL SYSTEMS GO: Both servers running - agents can proceed"
    echo
    echo "🔧 Development URLs:"
    echo "   • API: http://localhost:8000"
    echo "   • Frontend: http://localhost:4200"
    echo "   • API Health: http://localhost:8000/health"
    echo "   • API Docs: http://localhost:8000/docs"
    exit 0
elif $api_ok; then
    echo "⚠️  API ready, Frontend needed"
    echo "   → Build & start frontend: pnpm serve:app (builds first, then serves)"
    exit 1
elif $frontend_ok; then
    echo "⚠️  Frontend ready, API needed"  
    echo "   → Build & start API: pnpm serve:api (builds first, then serves)"
    exit 1
else
    echo "🚨 SERVERS NEEDED: Both API and Frontend must be started"
    echo "   → Build & start API: pnpm serve:api (builds first, then serves)"
    echo "   → Build & start Frontend: pnpm serve:app (builds first, then serves)"
    echo "   → Or start both: pnpm serve (builds all, then serves)"
    exit 1
fi