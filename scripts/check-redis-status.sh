#!/bin/bash

# Check Redis Configuration Status
# Shows current Redis setup across environments

set -e

PROJECT_ID="rezolve-poc"
REGION="us-central1"
SERVICE_NAME="giki-ai-api"

echo "🔍 Redis Configuration Status Check"
echo "===================================="

# Check Redis instances
echo "1. Redis Instances:"
echo "-------------------"
gcloud redis instances list --region=$REGION --project=$PROJECT_ID --format="table(name,host,port,state,tier,memorySizeGb)" || echo "No Redis instances found"

# Check Cloud Run environment variables
echo ""
echo "2. Cloud Run Environment Variables:"
echo "-----------------------------------"
REDIS_VARS=$(gcloud run services describe $SERVICE_NAME \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format="value(spec.template.spec.template.spec.containers[0].env[].name)" 2>/dev/null | grep -i redis || echo "None")

if [ "$REDIS_VARS" = "None" ]; then
    echo "❌ No Redis environment variables configured in Cloud Run"
else
    echo "✅ Redis environment variables found:"
    gcloud run services describe $SERVICE_NAME \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format="table(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" \
        2>/dev/null | grep -i redis || echo "Could not display values"
fi

# Check service health
echo ""
echo "3. Service Health Check:"
echo "------------------------"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --region=$REGION \
    --project=$PROJECT_ID \
    --format='value(status.url)' 2>/dev/null || echo "")

if [ -n "$SERVICE_URL" ]; then
    echo "Service URL: $SERVICE_URL"
    echo "Testing health endpoint..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health" 2>/dev/null || echo "000")
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ Service is healthy (HTTP $HTTP_CODE)"
        
        # Try to get cache stats
        echo "Testing cache configuration..."
        CACHE_STATS=$(curl -s "$SERVICE_URL/api/v1/cache/stats" 2>/dev/null | head -3 || echo "Cache stats not available")
        echo "$CACHE_STATS"
    else
        echo "⚠️  Service health check failed (HTTP $HTTP_CODE)"
    fi
else
    echo "❌ Could not get service URL"
fi

# Environment-aware recommendations
echo ""
echo "4. Environment-Aware Configuration:"
echo "-----------------------------------"
echo "📋 Current Setup Analysis:"

if [ "$REDIS_VARS" = "None" ]; then
    echo "  🔴 Redis: NOT CONFIGURED"
    echo "     - Caching: In-memory only (not persistent)"
    echo "     - Rate limiting: In-memory only (not distributed)"
    echo "     - Recommended: Run ./scripts/configure-redis-production.sh"
else
    echo "  🟢 Redis: CONFIGURED"
    echo "     - Caching: Redis-based (persistent)"
    echo "     - Rate limiting: Redis-based (distributed)"
    echo "     - Status: Production-ready"
fi

echo ""
echo "💡 Next Steps:"
if [ "$REDIS_VARS" = "None" ]; then
    echo "  1. Wait for Redis instance to be ready"
    echo "  2. Run: ./scripts/configure-redis-production.sh"
    echo "  3. Verify with: ./scripts/check-redis-status.sh"
else
    echo "  1. Monitor performance: curl $SERVICE_URL/api/v1/cache/stats"
    echo "  2. Check logs: gcloud logs tail --project=$PROJECT_ID"
    echo "  3. Redis is properly configured ✅"
fi