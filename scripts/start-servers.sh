#!/bin/bash

# Background Server Management for Claude Code
# Starts both API and frontend servers in background with proper PID tracking

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Directories
WORKSPACE_ROOT="$(dirname "$(dirname "$(realpath "$0")")")"
PID_DIR="$WORKSPACE_ROOT/.run"
LOG_DIR="$WORKSPACE_ROOT/logs"

# Create directories if they don't exist
mkdir -p "$PID_DIR"
mkdir -p "$LOG_DIR"

# Function to check if a process is running
is_running() {
    local pid_file=$1
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            # Process not running, remove stale PID file
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Function to start a server using nx
start_server() {
    local name=$1
    local project=$2
    
    echo -e "${YELLOW}Starting $name server via nx...${NC}"
    
    # Use nx to start the server (now backgrounded by default)
    cd "$WORKSPACE_ROOT"
    pnpm nx serve "$project"
    
    return $?
}

echo -e "${GREEN}🚀 Starting development servers in background...${NC}"
echo

# Start API server
api_success=false
if start_server "API" "giki-ai-api"; then
    api_success=true
fi

echo

# Start Frontend server
frontend_success=false
if start_server "Frontend" "giki-ai-app"; then
    frontend_success=true
fi

echo
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Summary
if $api_success && $frontend_success; then
    echo -e "${GREEN}✓ Both servers started successfully!${NC}"
    echo
    echo "API:      http://localhost:8000"
    echo "Frontend: http://localhost:4200"
    echo
    echo "Commands:"
    echo "  View logs:    tail -f logs/api-server.log"
    echo "  Check status: pnpm serve:status"
    echo "  Stop servers: pnpm serve:stop"
    exit 0
else
    echo -e "${RED}✗ Some servers failed to start${NC}"
    echo "Check the log files for details."
    exit 1
fi