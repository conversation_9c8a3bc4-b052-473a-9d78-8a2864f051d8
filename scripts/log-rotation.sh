#!/bin/bash

# Multi-Agent Development Log Rotation
# Prevents exponential log growth in shared workspace environment

LOG_DIR="/Users/<USER>/giki-ai-workspace"
MAX_LOG_SIZE="10M"  # 10 MB
MAX_LOG_FILES=5     # Keep 5 rotated files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

rotate_log() {
    local log_file=$1
    local base_name=$(basename "$log_file" .log)
    local dir_name=$(dirname "$log_file")
    
    if [ -f "$log_file" ]; then
        local file_size=$(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file" 2>/dev/null)
        local max_size_bytes=$((10 * 1024 * 1024))  # 10MB in bytes
        
        if [ "$file_size" -gt "$max_size_bytes" ]; then
            echo "🔄 Rotating log: $log_file (${file_size} bytes)"
            
            # Compress and rotate existing logs
            for i in $(seq $((MAX_LOG_FILES-1)) -1 1); do
                if [ -f "${dir_name}/${base_name}.log.$i.gz" ]; then
                    mv "${dir_name}/${base_name}.log.$i.gz" "${dir_name}/${base_name}.log.$((i+1)).gz"
                fi
            done
            
            # Compress current log and create new empty log
            gzip -c "$log_file" > "${dir_name}/${base_name}.log.1.gz"
            > "$log_file"  # Truncate to empty
            
            # Remove old logs beyond MAX_LOG_FILES
            if [ -f "${dir_name}/${base_name}.log.$((MAX_LOG_FILES+1)).gz" ]; then
                rm "${dir_name}/${base_name}.log.$((MAX_LOG_FILES+1)).gz"
            fi
            
            echo "✅ Log rotated: $log_file"
        fi
    fi
}

clean_large_files() {
    echo "🧹 Cleaning large temporary files..."
    
    # Clean up large upload files older than 7 days
    find "$LOG_DIR/apps/giki-ai-api/uploads" -name "*.xlsx" -mtime +7 -delete 2>/dev/null || true
    find "$LOG_DIR/uploads" -name "*.xlsx" -mtime +7 -delete 2>/dev/null || true
    
    # Clean up old test results
    find "$LOG_DIR/test-results" -type f -mtime +3 -delete 2>/dev/null || true
    find "$LOG_DIR/apps/giki-ai-app/test-results" -type f -mtime +3 -delete 2>/dev/null || true
    
    # Clean up old screenshots
    find "$LOG_DIR/screenshots" -name "*.png" -mtime +7 -delete 2>/dev/null || true
    
    # Clean up large coverage files
    find "$LOG_DIR" -name "htmlcov" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$LOG_DIR" -name "coverage_html_report" -type d -exec rm -rf {} + 2>/dev/null || true
    
    echo "✅ Cleanup completed"
}

main() {
    echo "🔄 Multi-Agent Log Rotation - $TIMESTAMP"
    echo "======================================================"
    
    # Find and rotate log files
    find "$LOG_DIR" -name "*.log" -type f | while read -r log_file; do
        rotate_log "$log_file"
    done
    
    # Clean up large temporary files
    clean_large_files
    
    echo ""
    echo "📊 Current workspace size:"
    du -sh "$LOG_DIR" 2>/dev/null || echo "Unable to calculate size"
    
    echo ""
    echo "💡 Log rotation completed. Agents can continue development."
    echo "💡 Use 'scripts/server-status.sh' to check server status and 'tail -f logs/*.log' to view logs"
}

# Handle command line arguments
case "${1:-rotate}" in
    "rotate")
        main
        ;;
    "clean")
        clean_large_files
        ;;
    "status")
        echo "📊 Large files in workspace:"
        find "$LOG_DIR" -type f -size +10M 2>/dev/null | head -10
        echo ""
        echo "📊 Log files status:"
        find "$LOG_DIR" -name "*.log" -type f -exec ls -lh {} \; 2>/dev/null | head -10
        ;;
    *)
        echo "Usage: $0 [rotate|clean|status]"
        echo "  rotate - Rotate large log files (default)"
        echo "  clean  - Clean large temporary files"
        echo "  status - Show current file status"
        exit 1
        ;;
esac