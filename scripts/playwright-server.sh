#!/bin/bash

# Start Playwright MCP server for customer simulation
# Uses Microsoft's official @playwright/mcp with SSE transport

# Check if server is already running
if lsof -i :3100 > /dev/null 2>&1; then
    echo "Playwright MCP server already running on port 3100"
    exit 0
fi

# Create directories
mkdir -p .playwright-mcp/user-data screenshots logs

# Start server in background (headed mode for visibility)
echo "Starting Playwright MCP server for customer simulation..."
nohup npx @playwright/mcp@latest \
  --port 3100 \
  --user-data-dir .playwright-mcp/user-data \
  --vision \
  --output-dir screenshots \
  --browser chrome \
  > logs/playwright-mcp.log 2>&1 &

echo "Server started (PID: $!) - Ready for customer simulation"