#!/bin/bash

# Log Manager - Unified logging with rotation and management
# Handles log rotation, cleanup, and provides real-time viewing capabilities

set -e

LOG_DIR="logs"
MAX_LOG_SIZE="100K"  # ~2000 lines at 50 chars/line
MAX_ROTATIONS=3
MAX_AGE_DAYS=2
MAX_TOTAL_SIZE="5M"   # Total directory size limit

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to rotate a log file if it's too large
rotate_log() {
    local log_file="$1"
    
    if [[ -f "$log_file" ]]; then
        # Check if file is larger than MAX_LOG_SIZE
        if [[ $(find "$log_file" -size +$MAX_LOG_SIZE 2>/dev/null) ]]; then
            echo -e "${YELLOW}📦 Rotating large log: $log_file${NC}"
            
            # Rotate existing numbered logs
            for ((i=MAX_ROTATIONS; i>=1; i--)); do
                if [[ -f "${log_file}.$i" ]]; then
                    if [[ $i -eq $MAX_ROTATIONS ]]; then
                        rm -f "${log_file}.$i"
                    else
                        mv "${log_file}.$i" "${log_file}.$((i+1))"
                    fi
                fi
            done
            
            # Move current log to .1 and compress
            mv "$log_file" "${log_file}.1"
            gzip "${log_file}.1"
            
            # Create new empty log
            touch "$log_file"
        fi
    fi
}

# Function to enforce total directory size limit
enforce_size_limit() {
    if [[ ! -d "$LOG_DIR" ]]; then
        return 0
    fi
    
    local total_size_bytes=$(du -sb "$LOG_DIR" 2>/dev/null | cut -f1)
    local max_size_bytes=$(echo "$MAX_TOTAL_SIZE" | sed 's/M/*1024*1024/' | bc)
    
    if [[ $total_size_bytes -gt $max_size_bytes ]]; then
        echo -e "${YELLOW}⚠️ Log directory ($MAX_TOTAL_SIZE limit exceeded), removing oldest files...${NC}"
        
        # Remove oldest files until under limit
        find "$LOG_DIR" -type f -name "*.log*" -printf '%T@ %p\n' | sort -n | while read timestamp file; do
            rm -f "$file"
            echo "  Removed: $(basename "$file")"
            
            # Check if we're now under limit
            local current_size=$(du -sb "$LOG_DIR" 2>/dev/null | cut -f1)
            if [[ $current_size -lt $max_size_bytes ]]; then
                break
            fi
        done
    fi
}

# Function to clean old logs
cleanup_old_logs() {
    echo -e "${YELLOW}🧹 Cleaning logs older than $MAX_AGE_DAYS days...${NC}"
    
    # Remove compressed logs older than MAX_AGE_DAYS
    find "$LOG_DIR" -name "*.log.*.gz" -mtime +$MAX_AGE_DAYS -delete 2>/dev/null || true
    
    # Remove other old log files
    find "$LOG_DIR" -name "*.log.*" -mtime +$MAX_AGE_DAYS -delete 2>/dev/null || true
    
    # Enforce total size limit
    enforce_size_limit
}

# Function to get log size summary
log_summary() {
    echo -e "${GREEN}📊 Log Directory Summary:${NC}"
    if [[ -d "$LOG_DIR" ]]; then
        local total_size=$(du -sh "$LOG_DIR" | cut -f1)
        echo "  Total size: $total_size"
        echo "  Files:"
        ls -lh "$LOG_DIR"/*.log 2>/dev/null | awk '{print "    " $9 " (" $5 ")"}'
        
        local compressed=$(find "$LOG_DIR" -name "*.gz" | wc -l | tr -d ' ')
        if [[ $compressed -gt 0 ]]; then
            echo "  Compressed logs: $compressed files"
        fi
    else
        echo "  No log directory found"
    fi
}

# Function to tail logs with filtering
tail_logs() {
    local service="$1"
    local log_file="$LOG_DIR/${service}-server.log"
    
    if [[ -f "$log_file" ]]; then
        echo -e "${GREEN}📋 Tailing $service logs (Ctrl+C to exit):${NC}"
        tail -f "$log_file" | grep -v "page reload\|hmr update" || true
    else
        echo -e "${RED}❌ Log file not found: $log_file${NC}"
        return 1
    fi
}

# Main command handling
case "${1:-}" in
    "rotate")
        shift
        if [[ $# -eq 0 ]]; then
            # Rotate all main logs
            rotate_log "$LOG_DIR/api-server.log"
            rotate_log "$LOG_DIR/frontend-server.log"
        else
            # Rotate specific log
            rotate_log "$LOG_DIR/$1"
        fi
        ;;
    "cleanup")
        cleanup_old_logs
        ;;
    "summary")
        log_summary
        ;;
    "tail")
        if [[ -z "${2:-}" ]]; then
            echo "Usage: $0 tail <service>"
            echo "Services: api, frontend"
            exit 1
        fi
        tail_logs "$2"
        ;;
    "manage")
        # Full log management: rotate + cleanup + size enforcement
        echo -e "${GREEN}🔧 Running full log management...${NC}"
        rotate_log "$LOG_DIR/api-server.log"
        rotate_log "$LOG_DIR/frontend-server.log"
        cleanup_old_logs
        enforce_size_limit
        log_summary
        ;;
    *)
        echo "Log Manager - Unified logging with rotation"
        echo ""
        echo "Usage: $0 <command> [args]"
        echo ""
        echo "Commands:"
        echo "  rotate [file]   - Rotate logs (all or specific file)"
        echo "  cleanup         - Remove old compressed logs"
        echo "  summary         - Show log directory summary"
        echo "  tail <service>  - Tail logs for service (api/frontend)"
        echo "  manage          - Full management (rotate + cleanup + summary)"
        echo ""
        echo "Examples:"
        echo "  $0 manage       # Full log maintenance"
        echo "  $0 tail api     # Watch API logs"
        echo "  $0 summary      # Check log sizes"
        ;;
esac