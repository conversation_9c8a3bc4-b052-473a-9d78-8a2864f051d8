#!/usr/bin/env python3
"""
Review and export AI prompts from the centralized registry.

This script allows you to:
1. View all prompts organized by category
2. Export prompts for review
3. Check performance metrics
4. Prepare for Langfuse migration
"""

import sys
import json
import argparse
from pathlib import Path
from datetime import datetime

# Add the API source to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "apps" / "giki-ai-api" / "src"))

from giki_ai_api.shared.ai.prompt_registry import get_prompt_registry, PromptCategory


def list_prompts(args):
    """List all prompts in the registry."""
    registry = get_prompt_registry()
    
    if args.category:
        try:
            category = PromptCategory(args.category)
            prompts = registry.list_prompts(category=category)
            print(f"\nPrompts in category '{category.value}':")
        except ValueError:
            print(f"Invalid category: {args.category}")
            print(f"Valid categories: {[c.value for c in PromptCategory]}")
            return
    else:
        prompts = registry.list_prompts()
        print("\nAll prompts in registry:")
    
    if not prompts:
        print("No prompts found.")
        return
    
    # Group by category
    by_category = {}
    for prompt in prompts:
        cat = prompt["category"]
        if cat not in by_category:
            by_category[cat] = []
        by_category[cat].append(prompt)
    
    # Display
    for category, category_prompts in sorted(by_category.items()):
        print(f"\n{category.upper()}:")
        for prompt in category_prompts:
            print(f"  - {prompt['name']} (v{prompt['version']})")
            print(f"    ID: {prompt['id']}")
            print(f"    Variables: {', '.join(prompt['variables'])}")


def show_prompt(args):
    """Show details of a specific prompt."""
    registry = get_prompt_registry()
    
    try:
        prompt = registry.get(args.prompt_id, args.version)
        
        print(f"\n{'='*80}")
        print(f"PROMPT: {prompt.name}")
        print(f"{'='*80}")
        print(f"ID: {prompt.id}")
        print(f"Version: {prompt.version}")
        print(f"Category: {prompt.category.value}")
        print(f"Created: {prompt.created_at}")
        print(f"Hash: {prompt.hash}")
        print(f"\nVariables: {', '.join(prompt.variables)}")
        print(f"\nModel Config:")
        for key, value in prompt.model_config.items():
            print(f"  {key}: {value}")
        
        if prompt.metadata:
            print(f"\nMetadata:")
            for key, value in prompt.metadata.items():
                print(f"  {key}: {value}")
        
        print(f"\nTemplate:")
        print("-" * 80)
        print(prompt.template)
        print("-" * 80)
        
    except ValueError as e:
        print(f"Error: {e}")


def export_prompts(args):
    """Export prompts for review."""
    registry = get_prompt_registry()
    
    # Determine output path
    if args.output:
        output_path = Path(args.output)
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = Path(f"prompt_export_{timestamp}.json")
    
    # Export
    export_data = registry.export_for_review(output_path)
    
    print(f"\nExported {export_data['total_prompts']} prompts to: {output_path}")
    print(f"\nCategories exported:")
    for category, prompts in export_data["categories"].items():
        if prompts:
            print(f"  - {category}: {len(prompts)} prompts")


def export_markdown(args):
    """Export prompts as markdown for easy review."""
    registry = get_prompt_registry()
    
    # Determine output path
    if args.output:
        output_path = Path(args.output)
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = Path(f"prompt_review_{timestamp}.md")
    
    # Generate markdown
    with open(output_path, "w") as f:
        f.write("# giki.ai Prompt Registry\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## Table of Contents\n\n")
        
        # TOC
        for category in PromptCategory:
            prompts = registry.list_prompts(category=category)
            if prompts:
                f.write(f"- [{category.value}](#{category.value.replace('_', '-')})\n")
        
        f.write("\n---\n\n")
        
        # Prompts by category
        for category in PromptCategory:
            prompts = registry.list_prompts(category=category)
            if not prompts:
                continue
            
            f.write(f"## {category.value.replace('_', ' ').title()}\n\n")
            
            for prompt_info in prompts:
                prompt = registry.get(prompt_info["id"], prompt_info["version"])
                
                f.write(f"### {prompt.name}\n\n")
                f.write(f"**ID:** `{prompt.id}`  \n")
                f.write(f"**Version:** {prompt.version}  \n")
                f.write(f"**Variables:** {', '.join(f'`{v}`' for v in prompt.variables)}  \n")
                
                if prompt.metadata:
                    f.write(f"**Metadata:**  \n")
                    for key, value in prompt.metadata.items():
                        f.write(f"- {key}: {value}  \n")
                
                f.write(f"\n**Model Config:**  \n")
                f.write("```json\n")
                f.write(json.dumps(prompt.model_config, indent=2))
                f.write("\n```\n\n")
                
                f.write("**Template:**  \n")
                f.write("```\n")
                f.write(prompt.template)
                f.write("\n```\n\n")
                f.write("---\n\n")
    
    print(f"\nExported prompts to markdown: {output_path}")


def prepare_langfuse(args):
    """Prepare prompts for Langfuse migration."""
    registry = get_prompt_registry()
    
    migration_data = registry.prepare_langfuse_migration()
    
    # Save to file
    if args.output:
        output_path = Path(args.output)
    else:
        output_path = Path("langfuse_prompts.json")
    
    with open(output_path, "w") as f:
        json.dump(migration_data, f, indent=2)
    
    print(f"\nPrepared {len(migration_data)} prompts for Langfuse migration")
    print(f"Saved to: {output_path}")
    print("\nTo import into Langfuse, use their API or UI import feature.")


def main():
    parser = argparse.ArgumentParser(
        description="Review and manage AI prompts in giki.ai",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all prompts
  python scripts/review-prompts.py list
  
  # List prompts in a specific category
  python scripts/review-prompts.py list --category schema_interpretation
  
  # Show a specific prompt
  python scripts/review-prompts.py show schema_interpretation_main
  
  # Export all prompts for review
  python scripts/review-prompts.py export
  
  # Export as markdown for easy reading
  python scripts/review-prompts.py markdown
  
  # Prepare for Langfuse migration
  python scripts/review-prompts.py langfuse
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List all prompts")
    list_parser.add_argument(
        "--category", 
        help="Filter by category",
        choices=[c.value for c in PromptCategory]
    )
    
    # Show command
    show_parser = subparsers.add_parser("show", help="Show details of a specific prompt")
    show_parser.add_argument("prompt_id", help="Prompt ID to show")
    show_parser.add_argument("--version", help="Specific version (default: latest)")
    
    # Export command
    export_parser = subparsers.add_parser("export", help="Export prompts as JSON")
    export_parser.add_argument("--output", help="Output file path")
    
    # Markdown export
    md_parser = subparsers.add_parser("markdown", help="Export prompts as Markdown")
    md_parser.add_argument("--output", help="Output file path")
    
    # Langfuse preparation
    langfuse_parser = subparsers.add_parser("langfuse", help="Prepare for Langfuse migration")
    langfuse_parser.add_argument("--output", help="Output file path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Execute command
    if args.command == "list":
        list_prompts(args)
    elif args.command == "show":
        show_prompt(args)
    elif args.command == "export":
        export_prompts(args)
    elif args.command == "markdown":
        export_markdown(args)
    elif args.command == "langfuse":
        prepare_langfuse(args)


if __name__ == "__main__":
    main()