#!/bin/bash

# Stop Background Servers
# Gracefully stops both API and frontend servers using stored PIDs

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Directories
WORKSPACE_ROOT="$(dirname "$(dirname "$(realpath "$0")")")"
PID_DIR="$WORKSPACE_ROOT/.run"

# Function to stop a server
stop_server() {
    local name=$1
    local pid_file=$2
    
    echo -e "${YELLOW}Stopping $name server...${NC}"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if ps -p "$pid" > /dev/null 2>&1; then
            # Try graceful shutdown first
            kill "$pid" 2>/dev/null || true
            
            # Wait up to 5 seconds for graceful shutdown
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 5 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if ps -p "$pid" > /dev/null 2>&1; then
                echo -e "  Forcing shutdown..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            echo -e "${GREEN}✓ $name server stopped (PID: $pid)${NC}"
        else
            echo -e "  $name server not running (stale PID file)"
        fi
        
        # Remove PID file
        rm -f "$pid_file"
    else
        echo -e "  $name server not running (no PID file)"
    fi
}

echo -e "${RED}🛑 Stopping development servers...${NC}"
echo

# Stop API server
stop_server "API" "$PID_DIR/api.pid"

echo

# Stop Frontend server
stop_server "Frontend" "$PID_DIR/frontend.pid"

echo
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Also clean up any orphaned processes on the ports
echo -e "${YELLOW}Cleaning up orphaned processes...${NC}"

# Kill any remaining processes on port 8000
if lsof -t -i tcp:8000 > /dev/null 2>&1; then
    lsof -t -i tcp:8000 | xargs kill -9 2>/dev/null || true
    echo -e "  Cleaned up processes on port 8000"
fi

# Kill any remaining processes on port 4200
if lsof -t -i tcp:4200 > /dev/null 2>&1; then
    lsof -t -i tcp:4200 | xargs kill -9 2>/dev/null || true
    echo -e "  Cleaned up processes on port 4200"
fi

echo
echo -e "${GREEN}✓ All servers stopped${NC}"