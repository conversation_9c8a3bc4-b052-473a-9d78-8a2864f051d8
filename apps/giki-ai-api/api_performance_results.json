{"overall_summary": {"test_duration_seconds": 75.49, "total_tests": 9, "successful_tests": 0, "failed_tests": 9, "success_rate_percent": 0.0, "performance_analysis": {"fast_tests_under_200ms": 0, "slow_tests_over_200ms": 0, "performance_target_met_percent": 0, "average_response_time_ms": 0, "fastest_response_ms": 0, "slowest_response_ms": 0}}, "category_results": [{"category": "public_endpoints", "tests": [{"endpoint": "/health", "method": "GET", "status_code": 0, "response_time_ms": 5185.08, "success": false, "error": "", "timestamp": **********.135766}, {"endpoint": "/health/env", "method": "GET", "status_code": 0, "response_time_ms": 5028.34, "success": false, "error": "", "timestamp": **********.164609}, {"endpoint": "/api/v1/auth-fast/performance-test", "method": "GET", "status_code": 0, "response_time_ms": 5022.42, "success": false, "error": "", "timestamp": **********.18717}, {"endpoint": "/api/v1/auth-fast/performance-comparison", "method": "GET", "status_code": 0, "response_time_ms": 5028.68, "success": false, "error": "", "timestamp": **********.215908}, {"endpoint": "/docs", "method": "GET", "status_code": 0, "response_time_ms": 5012.95, "success": false, "error": "", "timestamp": **********.228895}], "summary": {"total_tests": 5, "successful_tests": 0, "average_response_time": 5055.494000000001, "max_response_time": 5185.08, "min_response_time": 5012.95}}, {"category": "authentication", "tests": [{"endpoint": "/api/v1/auth/token", "method": "POST", "status_code": 0, "response_time_ms": 10014.87, "success": false, "error": "", "timestamp": **********.2438478}, {"endpoint": "/api/v1/auth-fast/token-fast", "method": "POST", "status_code": 0, "response_time_ms": 10147.12, "success": false, "error": "", "timestamp": **********.391099}], "summary": {"total_tests": 2, "successful_tests": 0, "average_response_time": 10080.995, "max_response_time": 10147.12, "min_response_time": 10014.87}}, {"category": "database_endpoints", "tests": [{"endpoint": "/health/db", "method": "GET", "status_code": 0, "response_time_ms": 15017.15, "success": false, "error": "", "timestamp": **********.408592}, {"endpoint": "/health/db/reset", "method": "POST", "status_code": 0, "response_time_ms": 15028.95, "success": false, "error": "", "timestamp": **********.4376729}], "summary": {"total_tests": 2, "successful_tests": 0, "average_response_time": 15023.05, "max_response_time": 15028.95, "min_response_time": 15017.15}}], "failed_tests": [{"endpoint": "/health", "method": "GET", "status_code": 0, "response_time_ms": 5185.08, "success": false, "error": "", "timestamp": **********.135766}, {"endpoint": "/health/env", "method": "GET", "status_code": 0, "response_time_ms": 5028.34, "success": false, "error": "", "timestamp": **********.164609}, {"endpoint": "/api/v1/auth-fast/performance-test", "method": "GET", "status_code": 0, "response_time_ms": 5022.42, "success": false, "error": "", "timestamp": **********.18717}, {"endpoint": "/api/v1/auth-fast/performance-comparison", "method": "GET", "status_code": 0, "response_time_ms": 5028.68, "success": false, "error": "", "timestamp": **********.215908}, {"endpoint": "/docs", "method": "GET", "status_code": 0, "response_time_ms": 5012.95, "success": false, "error": "", "timestamp": **********.228895}, {"endpoint": "/api/v1/auth/token", "method": "POST", "status_code": 0, "response_time_ms": 10014.87, "success": false, "error": "", "timestamp": **********.2438478}, {"endpoint": "/api/v1/auth-fast/token-fast", "method": "POST", "status_code": 0, "response_time_ms": 10147.12, "success": false, "error": "", "timestamp": **********.391099}, {"endpoint": "/health/db", "method": "GET", "status_code": 0, "response_time_ms": 15017.15, "success": false, "error": "", "timestamp": **********.408592}, {"endpoint": "/health/db/reset", "method": "POST", "status_code": 0, "response_time_ms": 15028.95, "success": false, "error": "", "timestamp": **********.4376729}], "slow_tests": []}