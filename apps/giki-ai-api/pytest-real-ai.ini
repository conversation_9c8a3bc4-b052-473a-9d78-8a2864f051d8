[tool:pytest]
# Configuration for running real AI integration tests
# These tests use actual Vertex AI services instead of mocks

# Test discovery
testpaths = tests/integration
python_files = test_real_*.py
python_classes = TestReal*
python_functions = test_real_*

# Markers for organizing real AI tests
markers =
    integration: Real AI integration tests using actual Vertex AI services
    real_ai: Tests that require real AI services (not mocked)
    performance: Tests that validate AI performance requirements
    collaboration: Multi-agent collaboration tests
    categorization: Real transaction categorization tests
    entity_extraction: Real entity extraction tests
    rag_corpus: Real RAG and vector search tests

# Environment requirements
env_vars_required = VERTEX_PROJECT_ID

# Test execution
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto

# Skip if environment not configured
collect_ignore_glob = test_real_*.py if not VERTEX_PROJECT_ID

# Timeout for AI operations
timeout = 30

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage for real AI functionality
addopts = --cov=giki_ai_api.adk_agents --cov=giki_ai_api.services.ai --cov=giki_ai_api.services.intelligence --cov-report=html:real_ai_coverage_html --cov-report=xml:real_ai_coverage.xml