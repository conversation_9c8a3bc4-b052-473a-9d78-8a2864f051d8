{"errors": [], "generated_at": "2025-06-15T09:13:34Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 18, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 4, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 20, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 36169, "nosec": 0, "skipped_tests": 0}, "src/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/cli/onboarding_data_cli.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 660, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/cloud_run_main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 16, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 290, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/database.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 213, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/database_health.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 152, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/database_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 358, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/database_validator.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 268, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/database_warmup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 146, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/dependencies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 142, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 954, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/core/status_check.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 57, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/admin/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/admin/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 267, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/admin/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/admin/service.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 406, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 83, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 2, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 426, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/cache/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/cache/user_cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 214, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/dependencies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 11, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 176, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 168, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/auth/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 503, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1280, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/gl_code_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 764, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 99, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/parallel_categorization_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 498, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/real_vertex_rag.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 231, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 442, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 132, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/service.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1295, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/categories/tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 332, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/debit_credit_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 886, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 209, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/router.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1576, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/schema_interpretation_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 974, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 229, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 224, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/files/tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/coordinator_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 951, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/customer_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 700, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 589, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 141, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 624, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/intelligence/ui_equivalence_tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 819, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1069, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 103, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 266, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/onboarding/service.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1107, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 887, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/custom_report_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 329, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/excel_export_service.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 454, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 517, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 147, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 172, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/reports/tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 358, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1371, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/debit_credit_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 761, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 178, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 647, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 170, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 667, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/domains/transactions/tools.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 387, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/internal_utils/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 7, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/internal_utils/css_utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/scripts/export_transactions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 105, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/scripts/prepare_rag_corpus.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 110, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/scripts/process_input_files.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 349, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/scripts/run_data_processing.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/adk_integration_demo.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 488, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/advanced_agent_patterns.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 473, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/advanced_vertex_patterns.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 361, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/enhanced_adk_integration.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 366, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/standard_giki_agent.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 233, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/unified_ai.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1218, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/ai/vertex_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 710, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/cache/simple_cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 132, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/error_handlers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/exceptions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 282, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/caching.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 192, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/error_handling.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 144, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/performance.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 217, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/rate_limiting.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/middleware/timeout_middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 123, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/routers/progress.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 143, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/services/enhanced_async_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/services/google_cloud_logging.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 276, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/services/google_cloud_secrets.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 237, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/services/progress_tracker.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 241, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/utils/error_recovery.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 192, "nosec": 0, "skipped_tests": 0}, "src/giki_ai_api/shared/utils/rate_limiter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "49 # Constants for default user\n50 DEFAULT_ONBOARDING_USER_PASSWORD = \"DefaultOnboardingP@ssw0rd!\"\n51 DEFAULT_ONBOARDING_USER_EMAIL_TEMPLATE = (\n", "col_offset": 35, "end_col_offset": 63, "filename": "src/giki_ai_api/cli/onboarding_data_cli.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'DefaultOnboardingP@ssw0rd!'", "line_number": 50, "line_range": [50], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "639             return pd_dt.date()\n640     except Exception:\n641         pass\n642     logger.warning(\n", "col_offset": 4, "end_col_offset": 12, "filename": "src/giki_ai_api/cli/onboarding_data_cli.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 640, "line_range": [640, 641], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "17         app,\n18         host=\"0.0.0.0\",  # Must bind to all interfaces for Cloud Run\n19         port=port,\n", "col_offset": 13, "end_col_offset": 22, "filename": "src/giki_ai_api/core/cloud_run_main.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 605, "link": "https://cwe.mitre.org/data/definitions/605.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible binding to all interfaces.", "line_number": 18, "line_range": [18], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b104_hardcoded_bind_all_interfaces.html", "test_id": "B104", "test_name": "hardcoded_bind_all_interfaces"}, {"code": "206                 # Add jitter to prevent thundering herd\n207                 jitter = random.uniform(0.1, 0.3) * delay\n208                 total_delay = delay + jitter\n", "col_offset": 25, "end_col_offset": 49, "filename": "src/giki_ai_api/core/database_validator.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 207, "line_range": [207], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "13 import os\n14 import subprocess\n15 from typing import Any, Dict, Optional\n", "col_offset": 0, "end_col_offset": 17, "filename": "src/giki_ai_api/domains/admin/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 14, "line_range": [14], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "403             # Run alembic upgrade head\n404             result = subprocess.run(\n405                 [\"alembic\", \"upgrade\", \"head\"],\n406                 capture_output=True,\n407                 text=True,\n408                 check=False,\n409                 cwd=\"/app\",  # Assume we're in containerized environment\n410             )\n411 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/giki_ai_api/domains/admin/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 404, "line_range": [404, 405, 406, 407, 408, 409, 410], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "403             # Run alembic upgrade head\n404             result = subprocess.run(\n405                 [\"alembic\", \"upgrade\", \"head\"],\n406                 capture_output=True,\n407                 text=True,\n408                 check=False,\n409                 cwd=\"/app\",  # Assume we're in containerized environment\n410             )\n411 \n", "col_offset": 21, "end_col_offset": 13, "filename": "src/giki_ai_api/domains/admin/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 404, "line_range": [404, 405, 406, 407, 408, 409, 410], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "248 \n249         if token_type == \"access\":\n250             # Access tokens have 'user_id:tenant_id' format\n", "col_offset": 25, "end_col_offset": 33, "filename": "src/giki_ai_api/domains/auth/auth.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'access'", "line_number": 249, "line_range": [249], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "259 \n260         elif token_type == \"refresh\":\n261             # Refresh tokens have only 'user_id'\n", "col_offset": 27, "end_col_offset": 36, "filename": "src/giki_ai_api/domains/auth/auth.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'refresh'", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "121                 kwargs.get(\"correlation_id\")\n122                 or f\"{service_name}_{operation_name}_{random.randint(1000, 9999)}\"\n123             )\n", "col_offset": 54, "end_col_offset": 80, "filename": "src/giki_ai_api/domains/categories/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 122, "line_range": [122], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "169                     if retryable and attempt < max_retries - 1:\n170                         wait_time = (2**attempt) + random.uniform(0, 1)\n171                         logger.info(f\"Retrying {operation_name} in {wait_time:.1f}s...\")\n", "col_offset": 51, "end_col_offset": 71, "filename": "src/giki_ai_api/domains/categories/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 170, "line_range": [170], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "1247                         date_obj = parsed_date.date()\n1248                     except Exception:\n1249                         continue  # Skip invalid dates\n1250 \n", "col_offset": 20, "end_col_offset": 32, "filename": "src/giki_ai_api/domains/files/router.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 1248, "line_range": [1248, 1249], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "1254                     amount_decimal = float(amount_clean)\n1255                 except Exception:\n1256                     continue  # Skip invalid amounts\n1257 \n", "col_offset": 16, "end_col_offset": 28, "filename": "src/giki_ai_api/domains/files/router.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 1255, "line_range": [1255, 1256], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "291                         break\n292                 except Exception:\n293                     continue\n294 \n", "col_offset": 16, "end_col_offset": 28, "filename": "src/giki_ai_api/domains/onboarding/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 292, "line_range": [292, 293], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}, {"code": "1334                                         categories.add(entry[\"category\"])\n1335                                 except Exception:\n1336                                     pass\n1337 \n", "col_offset": 32, "end_col_offset": 40, "filename": "src/giki_ai_api/domains/onboarding/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 1335, "line_range": [1335, 1336], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "1376                             categories.add(entry[\"category\"])\n1377                     except Exception:\n1378                         pass\n1379 \n", "col_offset": 20, "end_col_offset": 28, "filename": "src/giki_ai_api/domains/onboarding/service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 1377, "line_range": [1377, 1378], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "302                             max_length = len(str(cell.value))\n303                     except Exception:\n304                         pass\n305                 adjusted_width = min(max_length + 2, 50)\n", "col_offset": 20, "end_col_offset": 28, "filename": "src/giki_ai_api/domains/reports/excel_export_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 303, "line_range": [303, 304], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "395                         max_length = len(str(cell.value))\n396                 except Exception:\n397                     pass\n398             adjusted_width = min(max_length + 2, 30)\n", "col_offset": 16, "end_col_offset": 24, "filename": "src/giki_ai_api/domains/reports/excel_export_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 396, "line_range": [396, 397], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "474                         max_length = len(str(cell.value))\n475                 except Exception:\n476                     pass\n477             adjusted_width = min(max_length + 2, 40)\n", "col_offset": 16, "end_col_offset": 24, "filename": "src/giki_ai_api/domains/reports/excel_export_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 475, "line_range": [475, 476], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "558                         max_length = len(str(cell.value))\n559                 except Exception:\n560                     pass\n561             adjusted_width = min(max_length + 2, 25)\n", "col_offset": 16, "end_col_offset": 24, "filename": "src/giki_ai_api/domains/reports/excel_export_service.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 559, "line_range": [559, 560], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "105         key_string = json.dumps(key_data, sort_keys=True)\n106         return hashlib.md5(key_string.encode()).hexdigest()\n107 \n", "col_offset": 15, "end_col_offset": 47, "filename": "src/giki_ai_api/shared/middleware/caching.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 106, "line_range": [106], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "203 \n204             delay += random.uniform(0, 0.1 * delay)\n205 \n", "col_offset": 21, "end_col_offset": 51, "filename": "src/giki_ai_api/shared/utils/error_recovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 204, "line_range": [204], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}]}