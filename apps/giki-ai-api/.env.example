# Example environment file for giki-ai-api
# Copy this to .env.development, .env.staging, or .env.production and fill in values

# Core Settings
DEBUG=True
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/dbname

# Authentication
AUTH_SECRET_KEY=your-secret-key-here
SECRET_KEY=your-secret-key-here

# CORS Configuration
# Comma-separated list of allowed origins
# Default: "http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-273348121056.us-central1.run.app"
# Add new frontend URLs here without code changes
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,https://your-frontend-url.run.app

# Optional: Configure allowed methods (default: GET,POST,PUT,DELETE,OPTIONS)
# CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH

# Optional: Configure allowed headers (default: Authorization,Content-Type)
# CORS_ALLOW_HEADERS=Authorization,Content-Type,X-Requested-With

# Database Configuration
# For local development use postgresql://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db

# Vertex AI Configuration
VERTEX_PROJECT_ID=your-gcp-project
VERTEX_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Admin Configuration
ADMIN_API_KEY=your-admin-api-key