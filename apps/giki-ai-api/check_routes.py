#!/usr/bin/env python3
"""
Check what routes are actually loaded in the FastAPI app.
"""

import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        from giki_ai_api.core.main import app
        
        print("🔍 Checking loaded routes in FastAPI app...")
        
        # Get all routes from the app
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = getattr(route, 'methods', {'GET'})
                routes.append({
                    'path': route.path,
                    'methods': list(methods),
                    'name': getattr(route, 'name', 'unknown')
                })
        
        # Sort routes by path
        routes.sort(key=lambda x: x['path'])
        
        # Group by prefix
        grouped_routes = {}
        for route in routes:
            path = route['path']
            if path.startswith('/api/v1/'):
                prefix = '/'.join(path.split('/')[:4])  # /api/v1/domain
            elif path.startswith('/api/'):
                prefix = '/'.join(path.split('/')[:3])  # /api/domain
            else:
                prefix = '/'
            
            if prefix not in grouped_routes:
                grouped_routes[prefix] = []
            grouped_routes[prefix].append(route)
        
        print(f"\n📊 Found {len(routes)} total routes:")
        
        for prefix, group_routes in grouped_routes.items():
            print(f"\n📂 {prefix}:")
            for route in group_routes:
                methods_str = ', '.join(route['methods'])
                print(f"  {methods_str:12} {route['path']}")
        
        # Check for specific broken endpoints
        print("\n🔍 Checking specific broken endpoints:")
        
        broken_endpoints = [
            '/api/v1/transactions/',
            '/api/v1/transactions',
            '/api/v1/admin/health'
        ]
        
        for endpoint in broken_endpoints:
            found = any(route['path'] == endpoint for route in routes)
            status = '✅ FOUND' if found else '❌ MISSING'
            print(f"  {status} {endpoint}")
        
        # Check what transaction routes exist
        print("\n🔍 Transaction routes found:")
        transaction_routes = [route for route in routes if 'transaction' in route['path'].lower()]
        for route in transaction_routes:
            methods_str = ', '.join(route['methods'])
            print(f"  {methods_str:12} {route['path']}")
        
        # Check what admin routes exist  
        print("\n🔍 Admin routes found:")
        admin_routes = [route for route in routes if 'admin' in route['path'].lower()]
        for route in admin_routes:
            methods_str = ', '.join(route['methods'])
            print(f"  {methods_str:12} {route['path']}")
            
    except Exception as e:
        print(f"❌ Error checking routes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()