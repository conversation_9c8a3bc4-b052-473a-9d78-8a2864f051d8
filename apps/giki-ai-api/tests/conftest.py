"""
Test Configuration and Fixtures
===============================

Provides common test fixtures and configuration for integration tests.
"""

import os
from typing import AsyncGenerator

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

# Set test environment variables with secure defaults
os.environ["TESTING"] = "true"
if "JWT_SECRET_KEY" not in os.environ:
    # Generate secure test key
    import secrets
    os.environ["JWT_SECRET_KEY"] = secrets.token_urlsafe(64)
if "AUTH_SECRET_KEY" not in os.environ:
    import secrets
    os.environ["AUTH_SECRET_KEY"] = secrets.token_urlsafe(64)
if "SECRET_KEY" not in os.environ:
    import secrets
    os.environ["SECRET_KEY"] = secrets.token_urlsafe(64)
if "DATABASE_URL" not in os.environ:
    os.environ["DATABASE_URL"] = "postgresql+asyncpg://test:test@localhost:5432/test_db"

# Set Google Cloud environment variables for testing
if "VERTEX_PROJECT_ID" not in os.environ:
    os.environ["VERTEX_PROJECT_ID"] = "rezolve-poc"
if "VERTEX_LOCATION" not in os.environ:
    os.environ["VERTEX_LOCATION"] = "us-central1"
if "GOOGLE_APPLICATION_CREDENTIALS" not in os.environ:
    # Set path to service account file for tests
    service_account_path = os.path.join(os.path.dirname(__file__), "integration", "dev-service-account.json")
    if os.path.exists(service_account_path):
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_path


# Use default pytest-asyncio event loop handling instead of custom fixture


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""

    # Use in-memory SQLite for testing
    test_database_url = "postgresql+asyncpg://test:test@localhost:5432/test_db"

    engine = create_async_engine(
        test_database_url,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )

    # Import all models to ensure they're registered
    from giki_ai_api.shared.models import Base

    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    await engine.dispose()


@pytest.fixture(scope="session")
async def test_session_factory(test_engine):
    """Create test session factory."""
    return async_sessionmaker(test_engine, class_=AsyncSession, expire_on_commit=False)


@pytest.fixture
async def db_session(test_session_factory) -> AsyncGenerator[AsyncSession, None]:
    """Provide database session for tests."""

    async with test_session_factory() as session:
        try:
            yield session
        finally:
            await session.rollback()


# Alias for compatibility with existing tests
async_db_session = db_session


@pytest.fixture
async def test_tenant(db_session: AsyncSession):
    """
    Create test tenant for data isolation.
    
    Note: This replaces the old table prefix system (dev_, prod_).
    All environments now use the same table names, with isolation
    achieved through tenant_id filtering.
    """
    from giki_ai_api.domains.auth.models import Tenant

    tenant = Tenant(
        id=1,
        name="Test Tenant",
        email_domain="test.com",
        timezone="UTC",
        currency="USD",
    )

    db_session.add(tenant)
    await db_session.commit()
    await db_session.refresh(tenant)

    return tenant


@pytest.fixture
async def test_user(db_session: AsyncSession, test_tenant):
    """Create test user."""
    from giki_ai_api.domains.auth.auth import get_password_hash
    from giki_ai_api.domains.auth.models import User

    user = User(
        id=1,
        email="<EMAIL>",
        hashed_password=get_password_hash(os.getenv("TEST_PASSWORD", "secure_test_password_2024!")),
        full_name="Test User",
        is_active=True,
        is_superuser=False,
        tenant_id=test_tenant.id,
    )

    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)

    return user


@pytest.fixture
async def test_categories(db_session: AsyncSession, test_tenant):
    """Create test categories."""
    from giki_ai_api.domains.categories.models import Category

    categories = [
        Category(
            id=1,
            name="Business Expenses",
            tenant_id=test_tenant.id,
            level=0,
            path="Business Expenses",
            gl_code="6000",
            gl_account_type="Expense",
        ),
        Category(
            id=2,
            name="Office Supplies",
            parent_id=1,
            tenant_id=test_tenant.id,
            level=1,
            path="Business Expenses > Office Supplies",
            gl_code="6001",
            gl_account_type="Expense",
        ),
        Category(
            id=3,
            name="Travel",
            tenant_id=test_tenant.id,
            level=0,
            path="Travel",
            gl_code="6700",
            gl_account_type="Expense",
        ),
    ]

    for category in categories:
        db_session.add(category)

    await db_session.commit()

    for category in categories:
        await db_session.refresh(category)

    return categories


@pytest.fixture
async def test_transactions(db_session: AsyncSession, test_tenant, test_categories):
    """Create test transactions."""
    from datetime import date

    from giki_ai_api.domains.transactions.models import Transaction

    transactions = [
        Transaction(
            id="test_001",
            description="Office supplies purchase",
            amount=-45.67,
            date=date(2024, 7, 15),
            tenant_id=test_tenant.id,
            category_id=test_categories[1].id,  # Office Supplies
        ),
        Transaction(
            id="test_002",
            description="Business trip flight",
            amount=-350.00,
            date=date(2024, 7, 16),
            tenant_id=test_tenant.id,
            category_id=test_categories[2].id,  # Travel
        ),
        Transaction(
            id="test_003",
            description="Uncategorized transaction",
            amount=-25.30,
            date=date(2024, 7, 17),
            tenant_id=test_tenant.id,
        ),
    ]

    for transaction in transactions:
        db_session.add(transaction)

    await db_session.commit()

    for transaction in transactions:
        await db_session.refresh(transaction)

    return transactions


@pytest.fixture
def mock_ai_service():
    """Mock AI service for testing."""

    class MockAIResult:
        def __init__(self, category: str, confidence: float, reasoning: str):
            self.category = category
            self.confidence = confidence
            self.reasoning = reasoning
            self.alternatives = []

    class MockAIService:
        async def categorize_transaction(self, *args, **kwargs):
            return {
                "category": "Test Category",
                "confidence": 0.85,
                "reasoning": "Mock AI categorization for testing",
                "success": True,
            }

        async def generate_insights(self, *args, **kwargs):
            return {"insights": ["Test insight 1", "Test insight 2"], "confidence": 0.8}

        async def interpret_schema(self, *args, **kwargs):
            return {
                "column_mapping": {"Description": "description", "Amount": "amount"},
                "confidence": 0.9,
                "detected_format": "standard",
                "success": True,
            }

    return MockAIService()


@pytest.fixture
def mock_correlation_id():
    """Provide mock correlation ID for testing."""
    return "test_correlation_12345"


@pytest.fixture
async def test_user_data():
    """Provide test user data."""
    return {
        "email": "<EMAIL>",
        "password": "test-password",
        "full_name": "Test User",
        "is_active": True,
        "is_superuser": False,
        "tenant_id": 1,
    }


@pytest.fixture
async def test_tenant_data():
    """Provide test tenant data."""
    return {
        "name": "Test Tenant",
        "email_domain": "test.com",
        "timezone": "UTC",
        "currency": "USD",
    }


# Test markers
pytest.mark.integration = pytest.mark.asyncio
pytest.mark.agent_test = pytest.mark.asyncio
pytest.mark.service_test = pytest.mark.asyncio
pytest.mark.router_test = pytest.mark.asyncio
pytest.mark.error_test = pytest.mark.asyncio
pytest.mark.e2e_test = pytest.mark.asyncio


# Test configuration
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line(
        "markers", "agent_test: mark test as agent integration test"
    )
    config.addinivalue_line(
        "markers", "service_test: mark test as service integration test"
    )
    config.addinivalue_line(
        "markers", "router_test: mark test as router integration test"
    )
    config.addinivalue_line("markers", "error_test: mark test as error handling test")
    config.addinivalue_line(
        "markers", "e2e_test: mark test as end-to-end workflow test"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""

    for item in items:
        # Add integration marker to all tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Add specific markers based on test file names
        if "agent" in str(item.fspath):
            item.add_marker(pytest.mark.agent_test)

        if "service" in str(item.fspath):
            item.add_marker(pytest.mark.service_test)

        if "router" in str(item.fspath):
            item.add_marker(pytest.mark.router_test)

        if "error" in str(item.fspath):
            item.add_marker(pytest.mark.error_test)

        if "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e_test)
