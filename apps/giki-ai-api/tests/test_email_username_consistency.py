"""
Test to ensure consistent handling of email/username across the authentication system.

The OAuth2 specification requires the field name to be 'username', but we use email addresses.
This test ensures that the system correctly handles this mapping.
"""

from fastapi.testclient import TestClient

from giki_ai_api.core.main import app


def test_oauth2_username_field_accepts_email():
    """Test that the OAuth2 'username' field correctly accepts email addresses."""
    with TestClient(app) as client:
        # Test with email in 'username' field (OAuth2 standard)
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": "<EMAIL>",  # Email in username field
                "password": "password123",
                "grant_type": "password",
            },
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        # Should succeed (200) or fail with auth error (401), not validation error (422)
        assert response.status_code in [200, 401]
        
        # Ensure we're not getting a validation error
        if response.status_code == 422:
            print(f"Validation error details: {response.json()}")
        assert response.status_code != 422


def test_login_endpoint_form_data_format():
    """Test that login endpoint correctly parses form-urlencoded data."""
    with TestClient(app) as client:
        # Test raw form data string
        response = client.post(
            "/api/v1/auth/token",
            data="username=<EMAIL>&password=password123&grant_type=password",
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        # Should not get a parsing error
        assert response.status_code != 422
        assert response.status_code in [200, 401]


def test_login_with_json_fails():
    """Test that sending JSON to OAuth2 endpoint fails as expected."""
    with TestClient(app) as client:
        # OAuth2 spec requires form data, not JSON
        response = client.post(
            "/api/v1/auth/token",
            json={
                "username": "<EMAIL>",
                "password": "password123",
                "grant_type": "password",
            },
            headers={
                "Content-Type": "application/json",
            },
        )
        
        # Should get a 422 validation error because OAuth2 expects form data
        assert response.status_code == 422


def test_both_token_and_login_endpoints_work():
    """Test that both /token and /login endpoints accept the same format."""
    with TestClient(app) as client:
        form_data = {
            "username": "<EMAIL>",
            "password": "password123",
            "grant_type": "password",
        }
        
        # Test /token endpoint
        response1 = client.post(
            "/api/v1/auth/token",
            data=form_data,
        )
        assert response1.status_code in [200, 401]
        
        # Test /login endpoint (alias)
        response2 = client.post(
            "/api/v1/auth/login",
            data=form_data,
        )
        assert response2.status_code in [200, 401]
        
        # Both should have same status
        assert response1.status_code == response2.status_code