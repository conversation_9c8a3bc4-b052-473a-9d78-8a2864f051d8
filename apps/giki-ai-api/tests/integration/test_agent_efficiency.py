"""
Agent Efficiency Pattern Tests
=============================

Tests for the 2-3 tools per agent efficiency pattern.
Tests StandardGikiAgent base class and agent specialization.
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest

from giki_ai_api.domains.categories.agent import CategorizationAgent
from giki_ai_api.domains.intelligence.customer_agent import CustomerFacingAgent
from giki_ai_api.domains.onboarding.agent import OnboardingAgent
from giki_ai_api.shared.ai.standard_giki_agent import StandardGikiAgent


class TestStandardGikiAgentBase:
    """Test the StandardGikiAgent base class functionality."""
    
    async def test_standard_agent_with_tools_disabled(self, db_session, test_tenant):
        """Test that agents can disable standard tools for efficiency."""
        # Create agent with standard tools disabled
        agent = StandardGikiAgent(
            tenant_id=test_tenant.id,
            db=db_session,
            enable_standard_tools=False
        )
        
        # Verify no standard tools are registered
        tools = agent.get_tools()
        assert len(tools) == 0
        
        # Agent should still be functional
        assert agent.tenant_id == test_tenant.id
        assert agent.db == db_session
    
    async def test_standard_agent_with_tools_enabled(self, db_session, test_tenant):
        """Test standard agent with default tools enabled."""
        agent = StandardGikiAgent(
            tenant_id=test_tenant.id,
            db=db_session,
            enable_standard_tools=True
        )
        
        # Should have standard tools
        tools = agent.get_tools()
        assert len(tools) > 0
        
        # Verify standard tools are present
        tool_names = [tool.name for tool in tools]
        assert any("search" in name.lower() for name in tool_names)
    
    async def test_agent_tool_registration_limit(self, db_session, test_tenant):
        """Test that agents follow the 2-3 tools maximum pattern."""
        # Mock the agent's tool registration
        with patch.object(StandardGikiAgent, 'register_tool') as mock_register:
            agent = StandardGikiAgent(
                tenant_id=test_tenant.id,
                db=db_session,
                enable_standard_tools=False
            )
            
            # Try to register more than 3 tools
            for i in range(5):
                tool = Mock(name=f"tool_{i}")
                agent.register_tool(tool)
            
            # Should warn or limit after 3 tools
            assert mock_register.call_count == 5  # All calls made
            
            # In real implementation, should enforce limit
            # This is where we'd verify the efficiency pattern is followed


class TestCustomerAgentEfficiency:
    """Test CustomerFacingAgent with unified tools approach."""
    
    async def test_customer_agent_has_exactly_three_unified_tools(
        self, db_session, test_tenant
    ):
        """Test that CustomerFacingAgent uses exactly 3 unified tools."""
        with patch('giki_ai_api.domains.intelligence.customer_agent.FunctionTool') as mock_tool:
            # Mock tool creation
            mock_tool.side_effect = lambda func: Mock(name=func.__name__, func=func)
            
            # Need to create proper config
            from giki_ai_api.domains.intelligence.customer_agent import (
                CustomerFacingAgentConfig,
            )
            config = CustomerFacingAgentConfig()
            agent = CustomerFacingAgent(config, db_session)
            
            # Get registered tools
            tools = agent.get_tools() if hasattr(agent, 'get_tools') else []
            
            # Should have exactly 3 unified tools
            expected_tools = [
                'unified_data_access_tool_function',
                'unified_reporting_tool_function',
                'unified_ui_operations_tool_function'
            ]
            
            # Verify efficiency pattern
            assert len(tools) <= 3  # Maximum 3 tools
            
            # Verify these are unified tools (each handles multiple operations)
            for tool in tools:
                if hasattr(tool, 'name'):
                    assert 'unified' in tool.name or tool.name in expected_tools
    
    async def test_customer_agent_unified_tool_coverage(
        self, db_session, test_tenant
    ):
        """Test that unified tools cover all previous functionality."""
        from giki_ai_api.domains.intelligence.customer_agent import (
            CustomerFacingAgentConfig,
        )
        config = CustomerFacingAgentConfig()
        CustomerFacingAgent(config, db_session)
        
        # Operations that should be covered by unified tools
        operations_coverage = {
            'unified_data_access': ['query', 'search', 'qa', 'metrics'],
            'unified_reporting': ['insights', 'report', 'chart', 'export'],
            'unified_ui_operations': ['upload', 'navigate', 'voice']
        }
        
        # Verify all operations are accessible
        for _tool_type, operations in operations_coverage.items():
            for operation in operations:
                # In real implementation, would verify the tool can handle this operation
                assert operation in operations  # Placeholder assertion
    
    async def test_customer_agent_performance_with_unified_tools(
        self, db_session, test_tenant
    ):
        """Test that unified tools improve agent performance."""
        with patch('giki_ai_api.domains.intelligence.customer_agent.GenerativeModel') as mock_model:
            # Mock the AI model
            mock_instance = Mock()
            mock_model.return_value = mock_instance
            
            # Mock faster response with fewer tools
            mock_response = Mock()
            mock_response.text = '{"action": "query", "parameters": {"data_type": "transactions"}}'
            mock_instance.generate_content = AsyncMock(return_value=mock_response)
            
            from giki_ai_api.domains.intelligence.customer_agent import (
                CustomerFacingAgentConfig,
            )
            config = CustomerFacingAgentConfig()
            agent = CustomerFacingAgent(config, db_session)
            
            # Process a request
            await agent.process_message(
                "Show me my transactions",
                {"conversation_id": "test_123"}
            )
            
            # With unified tools, should process faster (mock verification)
            mock_instance.generate_content.assert_called_once()
            
            # Tool selection should be simpler with only 3 options
            call_args = mock_instance.generate_content.call_args
            assert call_args is not None


class TestOnboardingAgentEfficiency:
    """Test OnboardingAgent with minimal tools."""
    
    async def test_onboarding_agent_has_only_essential_tools(
        self, db_session, test_tenant
    ):
        """Test that OnboardingAgent has only 2 essential tools."""
        with patch('giki_ai_api.domains.onboarding.agent.FunctionTool') as mock_tool:
            mock_tool.side_effect = lambda func: Mock(name=func.__name__)
            
            agent = OnboardingAgent(
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            # Should have exactly 2 tools for efficiency
            tools = getattr(agent, 'tools', [])
            assert len(tools) <= 2
            
            # Should have only essential onboarding tools
            expected_tools = [
                'process_historical_files_tool',
                'run_temporal_validation_tool'
            ]
            
            # Verify only essential tools are present
            if hasattr(agent, 'get_tools'):
                tool_names = [t.name for t in agent.get_tools()]
                for name in tool_names:
                    assert any(expected in name for expected in expected_tools)
    
    async def test_onboarding_agent_focused_functionality(
        self, db_session, test_tenant
    ):
        """Test that OnboardingAgent maintains focus with fewer tools."""
        agent = OnboardingAgent(
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        # Agent should handle core onboarding tasks efficiently
        
        # With only 2 tools, agent is more focused
        assert hasattr(agent, 'process_file') or hasattr(agent, 'process_historical_files_tool')
        assert hasattr(agent, 'run_validation') or hasattr(agent, 'run_temporal_validation_tool')


class TestCategorizationAgentEfficiency:
    """Test CategorizationAgent optimization."""
    
    async def test_categorization_agent_rag_integration(
        self, db_session, test_tenant
    ):
        """Test that CategorizationAgent efficiently uses RAG."""
        with patch('giki_ai_api.domains.categories.agent.get_vertex_ai_client') as mock_vertex:
            mock_client = Mock()
            mock_vertex.return_value = mock_client
            
            # Mock RAG operations
            mock_client.rag_engine_search = AsyncMock(return_value={
                "contexts": ["Previous pattern: Office supplies from Staples"],
                "confidence": 0.92
            })
            
            agent = CategorizationAgent(
                tenant_id=test_tenant.id,
                db=db_session,
                rag_corpus_name="test_corpus"
            )
            
            # Should use RAG efficiently without multiple tools
            await agent.categorize_transaction({
                "description": "STAPLES OFFICE SUPPLIES",
                "amount": -45.99
            })
            
            # RAG should be called once efficiently
            mock_client.rag_engine_search.assert_called_once()


class TestUnifiedAgentInterface:
    """Test the unified 'Giki' interface pattern."""
    
    async def test_chat_endpoint_uses_unified_interface(
        self, db_session, test_tenant, test_user
    ):
        """Test that chat endpoint presents unified Giki interface."""
        from fastapi import Request

        from giki_ai_api.domains.intelligence.router import chat
        
        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.state.db = db_session
        mock_request.state.current_user = test_user
        mock_request.state.tenant = test_tenant
        
        # Mock CustomerAgent
        with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            # Mock route_to_specialized_agent method
            mock_agent.route_to_specialized_agent = AsyncMock(return_value={
                "response": "I've categorized your transactions.",
                "agent_used": "categorization",  # Hidden from user
                "metadata": {"confidence": 0.95}
            })
            
            # Make request
            from giki_ai_api.domains.intelligence.schemas import ChatRequest
            request_data = ChatRequest(message="Categorize my transactions")
            
            response = await chat(request_data, mock_request)
            
            # Response should come from "Giki" not individual agents
            assert "agent_used" not in response  # Internal detail hidden
            assert mock_agent.route_to_specialized_agent.called
    
    async def test_agent_routing_remains_internal(
        self, db_session, test_tenant
    ):
        """Test that multi-agent routing is hidden from users."""
        from giki_ai_api.domains.intelligence.customer_agent import (
            CustomerFacingAgentConfig,
        )
        config = CustomerFacingAgentConfig()
        agent = CustomerFacingAgent(config, db_session)
        
        # Internal routing methods should exist but not be exposed
        assert hasattr(agent, 'route_to_specialized_agent')
        
        # Response should always appear to come from "Giki"
        with patch.object(agent, 'route_to_specialized_agent') as mock_route:
            mock_route.return_value = {
                "response": "Task completed",
                "internal_agent": "gl_code_agent"  # Should be stripped
            }
            
            # User-facing methods should hide routing
            # In real implementation, verify response cleaning


class TestAgentEfficiencyMetrics:
    """Test metrics and monitoring for agent efficiency."""
    
    async def test_tool_usage_tracking(self, db_session, test_tenant):
        """Test that we can track tool usage for optimization."""
        from giki_ai_api.domains.intelligence.customer_agent import (
            CustomerFacingAgentConfig,
        )
        config = CustomerFacingAgentConfig()
        CustomerFacingAgent(config, db_session)
        
        # Mock tool usage tracking
        tool_metrics = {
            'unified_data_access': {'calls': 45, 'avg_time_ms': 120},
            'unified_reporting': {'calls': 23, 'avg_time_ms': 340},
            'unified_ui_operations': {'calls': 67, 'avg_time_ms': 85}
        }
        
        # Verify balanced tool usage with unified approach
        total_calls = sum(m['calls'] for m in tool_metrics.values())
        assert total_calls > 0
        
        # Each unified tool should handle multiple operations efficiently
        for _tool, metrics in tool_metrics.items():
            assert metrics['avg_time_ms'] < 500  # Efficient response times
    
    async def test_cognitive_load_reduction(self, db_session, test_tenant):
        """Test that reduced tool count improves AI decision making."""
        with patch('giki_ai_api.domains.intelligence.customer_agent.GenerativeModel') as mock_model:
            mock_instance = Mock()
            mock_model.return_value = mock_instance
            
            # With 3 tools, AI makes faster, more accurate decisions
            mock_response = Mock()
            mock_response.text = '{"tool": "unified_data_access", "confidence": 0.95}'
            mock_instance.generate_content = AsyncMock(return_value=mock_response)
            
            from giki_ai_api.domains.intelligence.customer_agent import (
                CustomerFacingAgentConfig,
            )
            config = CustomerFacingAgentConfig()
            agent = CustomerFacingAgent(config, db_session)
            
            # Process multiple requests
            confidences = []
            for _ in range(5):
                await agent.process_message("Query request", {})
                # Extract confidence from mock
                confidences.append(0.95)  # From mock response
            
            # With fewer tools, confidence should be consistently high
            avg_confidence = sum(confidences) / len(confidences)
            assert avg_confidence > 0.9  # High confidence with focused tools


@pytest.mark.integration
class TestAgentEfficiencyIntegration:
    """Integration tests for agent efficiency patterns."""
    
    async def test_end_to_end_unified_workflow(
        self, db_session, test_tenant, test_user, test_transactions
    ):
        """Test complete workflow with efficient agent architecture."""
        # 1. User interacts with unified Giki interface
        from giki_ai_api.domains.intelligence.customer_agent import (
            CustomerFacingAgentConfig,
        )
        config = CustomerFacingAgentConfig()
        customer_agent = CustomerFacingAgent(config, db_session)
        
        # 2. Giki routes internally but responds as single entity
        with patch.object(customer_agent, 'route_to_specialized_agent') as mock_route:
            # Mock efficient routing
            mock_route.side_effect = [
                {"response": "I'll analyze your spending patterns.", "status": "processing"},
                {"response": "Analysis complete. You spent $2,450 last month.", "status": "complete"}
            ]
            
            # User sees only Giki
            response1 = await customer_agent.route_to_specialized_agent(
                "Analyze my spending",
                {"conversation_id": "test"}
            )
            
            response2 = await customer_agent.route_to_specialized_agent(
                "Show me the details",
                {"conversation_id": "test"}
            )
            
            # Verify unified experience
            assert "Giki" not in response1["response"]  # Name not needed, it's implicit
            assert "agent" not in response1  # No agent details exposed
            assert response2["status"] == "complete"
    
    async def test_performance_improvement_with_efficiency_pattern(
        self, db_session, test_tenant
    ):
        """Test that efficiency pattern improves performance."""
        import time

        # Test with efficient agent (3 tools)
        from giki_ai_api.domains.intelligence.customer_agent import (
            CustomerFacingAgentConfig,
        )
        config = CustomerFacingAgentConfig()
        efficient_agent = CustomerFacingAgent(config, db_session)
        
        # Mock faster processing with fewer tools
        with patch('giki_ai_api.domains.intelligence.customer_agent.GenerativeModel') as mock_model:
            mock_instance = Mock()
            mock_model.return_value = mock_instance
            
            # Simulate faster tool selection with fewer options
            mock_response = Mock()
            mock_response.text = '{"tool": "unified_data_access", "time_ms": 50}'
            mock_instance.generate_content = AsyncMock(return_value=mock_response)
            
            start_time = time.time()
            
            # Process 10 requests
            for _ in range(10):
                await efficient_agent.process_message("Test query", {})
            
            elapsed = time.time() - start_time
            
            # With efficiency pattern, should process faster
            assert elapsed < 1.0  # Less than 100ms per request average
            assert mock_instance.generate_content.call_count == 10