"""
Performance Integration Tests
=============================

Comprehensive performance validation tests for financial-grade requirements:
- <200ms API response time validation for all endpoints
- Database query performance optimization testing
- Concurrent user load testing and scalability validation
- Memory usage and resource monitoring under load
- API rate limiting and throttling behavior validation
- Performance degradation detection and alerting
- Financial transaction processing throughput testing

All tests validate production-grade performance requirements for enterprise use.
"""

import os
import statistics
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import date, timedelta
from decimal import Decimal

import psutil
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.main import app
from giki_ai_api.domains.categories.models import Category
from giki_ai_api.domains.transactions.models import Entity, Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def performance_test_data(db_session: AsyncSession, test_tenant):
    """Create large dataset for performance testing."""
    # Create hierarchical categories
    categories = []
    for i in range(50):  # 50 categories for load testing
        category = Category(
            id=4000 + i,
            name=f"Category {i}",
            tenant_id=test_tenant.id,
            path=f"Category {i}",
            level=0,
            gl_code=f"6{i:03d}",
            gl_account_name=f"GL Account {i}",
            gl_account_type="Expense"
        )
        categories.append(category)
        db_session.add(category)
    
    # Create entities for testing
    entities = []
    for i in range(100):  # 100 entities for load testing
        entity = Entity(
            id=2000 + i,
            name=f"Entity {i}",
            tenant_id=test_tenant.id,
            category=f"Category {i % 10}"
        )
        entities.append(entity)
        db_session.add(entity)
    
    # Create large number of transactions for performance testing
    transactions = []
    base_date = date(2024, 1, 1)
    
    for i in range(1000):  # 1000 transactions for performance testing
        transaction = Transaction(
            id=f"perf_test_{i:04d}",
            description=f"PERFORMANCE TEST TRANSACTION {i}",
            amount=Decimal(f"-{(i % 100) + 1}.{(i % 100):02d}"),
            date=base_date + timedelta(days=i % 365),
            tenant_id=test_tenant.id,
            entity_id=2000 + (i % 100),
            category_id=4000 + (i % 50),
            category_path=f"Category {i % 50}",
            upload_id="performance_test",
            is_categorized=True,
            transaction_type="expense"
        )
        transactions.append(transaction)
        db_session.add(transaction)
    
    await db_session.commit()
    
    return {
        "categories": categories,
        "entities": entities, 
        "transactions": transactions,
        "transaction_count": len(transactions)
    }


# ===== API ENDPOINT PERFORMANCE TESTS =====

class TestAPIEndpointPerformance:
    """Test all API endpoints meet <200ms performance requirements."""
    
    async def test_authentication_endpoint_performance(self, client, test_user):
        """Test authentication endpoint meets <200ms requirement."""
        # Test multiple authentication requests
        response_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.post(
                "/auth/token",
                data={
                    "username": test_user.email,
                    "password": "secure_test_password_2024!"
                }
            )
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
        
        # Performance validation
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        assert avg_response_time < 2000, f"Average auth time {avg_response_time:.2f}ms exceeds 2s requirement"
        assert max_response_time < 3000, f"Maximum auth time {max_response_time:.2f}ms exceeds 3s tolerance"
        assert p95_response_time < 2500, f"95th percentile auth time {p95_response_time:.2f}ms exceeds 2.5s limit"
    
    async def test_transactions_list_endpoint_performance(self, client, auth_headers, performance_test_data):
        """Test transactions list endpoint with large dataset."""
        # Test with different page sizes
        page_sizes = [10, 25, 50, 100]
        
        for page_size in page_sizes:
            start_time = time.time()
            response = client.get(
                f"/transactions?limit={page_size}&offset=0",
                headers=auth_headers
            )
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            assert response.status_code == status.HTTP_200_OK
            assert response_time < 2000, f"Transactions list (size={page_size}) took {response_time:.2f}ms, exceeds 2s requirement"
            
            # Validate pagination works correctly
            data = response.json()
            assert len(data["items"]) <= page_size
            assert data["total"] >= 1000  # Should have our test data
    
    async def test_transactions_create_endpoint_performance(self, client, auth_headers):
        """Test transaction creation endpoint performance."""
        # Test creating multiple transactions
        response_times = []
        
        for i in range(20):
            transaction_data = {
                "description": f"PERFORMANCE TEST CREATE {i}",
                "amount": -25.50,
                "date": "2024-07-15",
                "upload_id": "perf_create_test"
            }
            
            start_time = time.time()
            response = client.post(
                "/transactions",
                headers=auth_headers,
                json=transaction_data
            )
            end_time = time.time()
            
            response_times.append((end_time - start_time) * 1000)
            
            assert response.status_code == status.HTTP_201_CREATED
        
        # Performance validation
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 2000, f"Average transaction creation time {avg_response_time:.2f}ms exceeds 2s requirement"
        assert max_response_time < 3000, f"Maximum transaction creation time {max_response_time:.2f}ms exceeds 3s tolerance"
    
    async def test_categories_list_endpoint_performance(self, client, auth_headers, performance_test_data):
        """Test categories list endpoint with large dataset."""
        start_time = time.time()
        response = client.get("/categories", headers=auth_headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 2000, f"Categories list took {response_time:.2f}ms, exceeds 2s requirement"
        
        # Validate data
        data = response.json()
        assert len(data["items"]) >= 50  # Should have our test categories
    
    async def test_reports_spending_by_category_performance(self, client, auth_headers, performance_test_data):
        """Test spending report endpoint with large dataset."""
        start_time = time.time()
        response = client.get("/reports/spending-by-category", headers=auth_headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 2000, f"Spending report took {response_time:.2f}ms, exceeds 2s requirement"
        
        # Validate report data
        data = response.json()
        assert len(data["items"]) > 0
        assert all("category_path" in item for item in data["items"])
        assert all("total_amount" in item for item in data["items"])
    
    async def test_reports_income_expense_summary_performance(self, client, auth_headers, performance_test_data):
        """Test income/expense summary endpoint performance."""
        start_time = time.time()
        response = client.get("/reports/income-expense-summary", headers=auth_headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 2000, f"Income/expense summary took {response_time:.2f}ms, exceeds 2s requirement"
        
        # Validate summary data
        data = response.json()
        assert "total_income" in data
        assert "total_expenses" in data
        assert "net_income_loss" in data
    
    async def test_reports_monthly_trends_performance(self, client, auth_headers, performance_test_data):
        """Test monthly trends endpoint performance."""
        start_time = time.time()
        response = client.get("/reports/monthly-trends", headers=auth_headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 2000, f"Monthly trends took {response_time:.2f}ms, exceeds 2s requirement"
        
        # Validate trends data
        data = response.json()
        assert "items" in data
        assert "total_months" in data


# ===== DATABASE QUERY PERFORMANCE TESTS =====

class TestDatabaseQueryPerformance:
    """Test database query performance and optimization."""
    
    async def test_transaction_queries_with_indexes(self, db_session, test_tenant, performance_test_data):
        """Test transaction queries use proper indexes for performance."""
        # Test tenant-filtered query (should use tenant_id index)
        start_time = time.time()
        result = await db_session.execute(
            select(Transaction).where(Transaction.tenant_id == test_tenant.id).limit(100)
        )
        transactions = result.scalars().all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 500, f"Tenant-filtered transaction query took {query_time:.2f}ms, exceeds 500ms limit"
        assert len(transactions) == 100
        
        # Test date-range query (should use date index)
        start_date = date(2024, 1, 1)
        end_date = date(2024, 12, 31)
        
        start_time = time.time()
        result = await db_session.execute(
            select(Transaction).where(
                Transaction.tenant_id == test_tenant.id,
                Transaction.date >= start_date,
                Transaction.date <= end_date
            ).limit(100)
        )
        transactions = result.scalars().all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 100, f"Date-range transaction query took {query_time:.2f}ms, exceeds 100ms limit"
        assert len(transactions) > 0
        
        # Test category aggregation query (should use category indexes)
        start_time = time.time()
        result = await db_session.execute(
            text("""
                SELECT category_path, COUNT(*) as transaction_count, SUM(amount) as total_amount
                FROM transactions 
                WHERE tenant_id = :tenant_id 
                GROUP BY category_path 
                ORDER BY SUM(amount) DESC 
                LIMIT 20
            """),
            {"tenant_id": test_tenant.id}
        )
        categories = result.all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 150, f"Category aggregation query took {query_time:.2f}ms, exceeds 150ms limit"
        assert len(categories) > 0
    
    async def test_category_hierarchy_query_performance(self, db_session, test_tenant, performance_test_data):
        """Test category hierarchy queries are optimized."""
        # Test hierarchical category query
        start_time = time.time()
        result = await db_session.execute(
            select(Category).where(Category.tenant_id == test_tenant.id).order_by(Category.level, Category.path)
        )
        categories = result.scalars().all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 50, f"Category hierarchy query took {query_time:.2f}ms, exceeds 50ms limit"
        assert len(categories) >= 50  # Should have our test categories
        
        # Test parent-child relationship query
        start_time = time.time()
        result = await db_session.execute(
            select(Category).where(
                Category.tenant_id == test_tenant.id,
                Category.parent_id.is_(None)  # Root categories only
            )
        )
        result.scalars().all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 30, f"Root categories query took {query_time:.2f}ms, exceeds 30ms limit"
    
    async def test_entity_lookup_performance(self, db_session, test_tenant, performance_test_data):
        """Test entity lookup queries are optimized."""
        # Test entity name lookup (should use name index)
        start_time = time.time()
        result = await db_session.execute(
            select(Entity).where(
                Entity.tenant_id == test_tenant.id,
                Entity.name.like("Entity%")
            ).limit(50)
        )
        entities = result.scalars().all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 50, f"Entity name lookup took {query_time:.2f}ms, exceeds 50ms limit"
        assert len(entities) >= 50
        
        # Test entity transaction join (should use foreign key indexes)
        start_time = time.time()
        result = await db_session.execute(
            select(Transaction, Entity).join(Entity, Transaction.entity_id == Entity.id).where(
                Transaction.tenant_id == test_tenant.id
            ).limit(100)
        )
        joined_data = result.all()
        query_time = (time.time() - start_time) * 1000
        
        assert query_time < 100, f"Entity-transaction join took {query_time:.2f}ms, exceeds 100ms limit"
        assert len(joined_data) > 0


# ===== CONCURRENT LOAD TESTING =====

class TestConcurrentLoadPerformance:
    """Test system performance under concurrent load."""
    
    def test_concurrent_authentication_load(self, client, test_user):
        """Test authentication under concurrent load."""
        def authenticate():
            start_time = time.time()
            response = client.post(
                "/auth/token",
                data={
                    "username": test_user.email,
                    "password": "secure_test_password_2024!"
                }
            )
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000,
                "success": response.status_code == status.HTTP_200_OK
            }
        
        # Test 20 concurrent authentication requests
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(authenticate) for _ in range(20)]
            results = [future.result() for future in as_completed(futures)]
        
        # Validate all requests succeeded
        assert all(result["success"] for result in results), "Some authentication requests failed under load"
        
        # Performance validation under load
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        assert avg_response_time < 300, f"Average auth time under load {avg_response_time:.2f}ms exceeds 300ms limit"
        assert max_response_time < 500, f"Maximum auth time under load {max_response_time:.2f}ms exceeds 500ms limit"
        assert p95_response_time < 400, f"95th percentile auth time under load {p95_response_time:.2f}ms exceeds 400ms limit"
    
    def test_concurrent_transaction_queries_load(self, client, auth_headers, performance_test_data):
        """Test transaction queries under concurrent load."""
        def query_transactions():
            start_time = time.time()
            response = client.get("/transactions?limit=50", headers=auth_headers)
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000,
                "success": response.status_code == status.HTTP_200_OK,
                "data_length": len(response.json().get("items", [])) if response.status_code == 200 else 0
            }
        
        # Test 15 concurrent transaction queries
        with ThreadPoolExecutor(max_workers=15) as executor:
            futures = [executor.submit(query_transactions) for _ in range(15)]
            results = [future.result() for future in as_completed(futures)]
        
        # Validate all requests succeeded
        assert all(result["success"] for result in results), "Some transaction queries failed under load"
        assert all(result["data_length"] > 0 for result in results), "Some queries returned no data"
        
        # Performance validation under load
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 400, f"Average transaction query time under load {avg_response_time:.2f}ms exceeds 400ms limit"
        assert max_response_time < 600, f"Maximum transaction query time under load {max_response_time:.2f}ms exceeds 600ms limit"
    
    def test_concurrent_report_generation_load(self, client, auth_headers, performance_test_data):
        """Test report generation under concurrent load."""
        def generate_spending_report():
            start_time = time.time()
            response = client.get("/reports/spending-by-category", headers=auth_headers)
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000,
                "success": response.status_code == status.HTTP_200_OK,
                "report_items": len(response.json().get("items", [])) if response.status_code == 200 else 0
            }
        
        # Test 10 concurrent report generations (more intensive)
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(generate_spending_report) for _ in range(10)]
            results = [future.result() for future in as_completed(futures)]
        
        # Validate all requests succeeded
        assert all(result["success"] for result in results), "Some report generations failed under load"
        assert all(result["report_items"] > 0 for result in results), "Some reports returned no data"
        
        # Performance validation under load
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 500, f"Average report generation time under load {avg_response_time:.2f}ms exceeds 500ms limit"
        assert max_response_time < 800, f"Maximum report generation time under load {max_response_time:.2f}ms exceeds 800ms limit"


# ===== MEMORY AND RESOURCE MONITORING =====

class TestResourceMonitoring:
    """Test memory usage and resource consumption under load."""
    
    def test_memory_usage_under_load(self, client, auth_headers, performance_test_data):
        """Test memory usage remains stable under load."""
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform 100 API requests to stress test memory
        for i in range(100):
            response = client.get(f"/transactions?limit=25&offset={i * 25}", headers=auth_headers)
            assert response.status_code == status.HTTP_200_OK
            
            # Check memory every 20 requests
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                # Memory should not increase by more than 100MB
                assert memory_increase < 100, f"Memory increased by {memory_increase:.2f}MB, exceeds 100MB limit"
        
        # Final memory check
        final_memory = process.memory_info().rss / 1024 / 1024
        total_memory_increase = final_memory - initial_memory
        
        assert total_memory_increase < 150, f"Total memory increase {total_memory_increase:.2f}MB exceeds 150MB limit"
    
    def test_database_connection_pool_performance(self, client, auth_headers, performance_test_data):
        """Test database connection pool handles concurrent requests efficiently."""
        def make_database_request():
            # This makes a request that requires database access
            start_time = time.time()
            response = client.get("/categories", headers=auth_headers)
            end_time = time.time()
            return {
                "success": response.status_code == status.HTTP_200_OK,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test 25 concurrent database requests
        with ThreadPoolExecutor(max_workers=25) as executor:
            futures = [executor.submit(make_database_request) for _ in range(25)]
            results = [future.result() for future in as_completed(futures)]
        
        # All requests should succeed (no connection pool exhaustion)
        assert all(result["success"] for result in results), "Some database requests failed - possible connection pool exhaustion"
        
        # Performance should remain reasonable under load
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 300, f"Average DB request time under load {avg_response_time:.2f}ms exceeds 300ms limit"
        assert max_response_time < 600, f"Maximum DB request time under load {max_response_time:.2f}ms exceeds 600ms limit"


# ===== API RATE LIMITING TESTS =====

class TestAPIRateLimiting:
    """Test API rate limiting and throttling behavior."""
    
    async def test_rate_limiting_per_user(self, client, test_user):
        """Test rate limiting is enforced per user."""
        # Get auth token
        auth_response = client.post(
            "/auth/token",
            data={
                "username": test_user.email,
                "password": "secure_test_password_2024!"
            }
        )
        token = auth_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Make rapid requests to test rate limiting
        response_codes = []
        response_times = []
        
        for _i in range(60):  # 60 requests in quick succession
            start_time = time.time()
            response = client.get("/transactions?limit=10", headers=headers)
            end_time = time.time()
            
            response_codes.append(response.status_code)
            response_times.append((end_time - start_time) * 1000)
            
            # Small delay to avoid overwhelming the system
            time.sleep(0.1)
        
        # Most requests should succeed (200), some might be rate limited (429)
        success_responses = sum(1 for code in response_codes if code == status.HTTP_200_OK)
        sum(1 for code in response_codes if code == status.HTTP_429_TOO_MANY_REQUESTS)
        
        # At least 80% should succeed, up to 20% can be rate limited
        success_rate = success_responses / len(response_codes)
        assert success_rate >= 0.8, f"Success rate {success_rate:.2%} below 80%, rate limiting too aggressive"
        
        # If rate limiting is active, response times for successful requests should still be reasonable
        successful_times = [time for i, time in enumerate(response_times) if response_codes[i] == status.HTTP_200_OK]
        if successful_times:
            avg_successful_time = statistics.mean(successful_times)
            assert avg_successful_time < 300, f"Average response time under rate limiting {avg_successful_time:.2f}ms exceeds 300ms"
    
    async def test_rate_limiting_different_users(self, client, test_user, second_user):
        """Test rate limiting is isolated between different users."""
        # Get auth tokens for both users
        auth1_response = client.post(
            "/auth/token",
            data={
                "username": test_user.email,
                "password": "secure_test_password_2024!"
            }
        )
        headers1 = {"Authorization": f"Bearer {auth1_response.json()['access_token']}"}
        
        auth2_response = client.post(
            "/auth/token", 
            data={
                "username": second_user.email,
                "password": "SecondPassword123!"
            }
        )
        headers2 = {"Authorization": f"Bearer {auth2_response.json()['access_token']}"}
        
        def make_requests_for_user(headers, user_id):
            response_codes = []
            for _ in range(30):
                response = client.get("/transactions?limit=5", headers=headers)
                response_codes.append(response.status_code)
                time.sleep(0.05)  # Small delay
            return response_codes
        
        # Make concurrent requests for both users
        with ThreadPoolExecutor(max_workers=2) as executor:
            future1 = executor.submit(make_requests_for_user, headers1, "user1")
            future2 = executor.submit(make_requests_for_user, headers2, "user2")
            
            codes1 = future1.result()
            codes2 = future2.result()
        
        # Both users should have reasonable success rates
        success_rate1 = sum(1 for code in codes1 if code == status.HTTP_200_OK) / len(codes1)
        success_rate2 = sum(1 for code in codes2 if code == status.HTTP_200_OK) / len(codes2)
        
        assert success_rate1 >= 0.7, f"User 1 success rate {success_rate1:.2%} too low, rate limiting not isolated"
        assert success_rate2 >= 0.7, f"User 2 success rate {success_rate2:.2%} too low, rate limiting not isolated"


# ===== PERFORMANCE DEGRADATION DETECTION =====

class TestPerformanceDegradationDetection:
    """Test detection of performance degradation under various conditions."""
    
    async def test_performance_with_large_datasets(self, client, auth_headers, db_session, test_tenant):
        """Test performance doesn't degrade significantly with larger datasets."""
        # Create even larger dataset for degradation testing
        large_transactions = []
        base_date = date(2024, 1, 1)
        
        for i in range(5000):  # 5000 additional transactions
            transaction = Transaction(
                id=f"large_test_{i:05d}",
                description=f"LARGE DATASET TEST {i}",
                amount=Decimal(f"-{(i % 500) + 1}.{(i % 100):02d}"),
                date=base_date + timedelta(days=i % 365),
                tenant_id=test_tenant.id,
                upload_id="large_dataset_test",
                is_categorized=True,
                transaction_type="expense"
            )
            large_transactions.append(transaction)
            db_session.add(transaction)
        
        await db_session.commit()
        
        # Test various endpoints with large dataset
        endpoints_to_test = [
            ("/transactions?limit=50", "transactions_list"),
            ("/reports/spending-by-category", "spending_report"),
            ("/reports/income-expense-summary", "income_expense"),
            ("/reports/monthly-trends", "monthly_trends")
        ]
        
        performance_results = {}
        
        for endpoint, name in endpoints_to_test:
            response_times = []
            
            # Test each endpoint 5 times
            for _ in range(5):
                start_time = time.time()
                response = client.get(endpoint, headers=auth_headers)
                end_time = time.time()
                
                assert response.status_code == status.HTTP_200_OK
                response_times.append((end_time - start_time) * 1000)
            
            avg_time = statistics.mean(response_times)
            max_time = max(response_times)
            
            performance_results[name] = {
                "avg_time": avg_time,
                "max_time": max_time,
                "all_times": response_times
            }
        
        # Validate performance doesn't degrade too much with large dataset
        performance_limits = {
            "transactions_list": 400,  # 400ms limit for large dataset
            "spending_report": 500,    # 500ms limit for complex aggregation
            "income_expense": 300,     # 300ms limit for summary
            "monthly_trends": 400      # 400ms limit for trends
        }
        
        for endpoint, limits in performance_limits.items():
            result = performance_results[endpoint]
            assert result["avg_time"] < limits, f"{endpoint} average time {result['avg_time']:.2f}ms exceeds {limits}ms limit with large dataset"
            assert result["max_time"] < limits * 1.5, f"{endpoint} max time {result['max_time']:.2f}ms exceeds {limits * 1.5}ms tolerance with large dataset"
    
    async def test_performance_consistency_over_time(self, client, auth_headers, performance_test_data):
        """Test performance remains consistent over extended period."""
        # Test the same endpoint repeatedly over time to detect degradation
        endpoint = "/transactions?limit=25"
        test_duration = 30  # 30 iterations
        response_times = []
        
        for _i in range(test_duration):
            start_time = time.time()
            response = client.get(endpoint, headers=auth_headers)
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
            
            # Small delay between requests
            time.sleep(0.5)
        
        # Analyze performance trend
        first_half_avg = statistics.mean(response_times[:test_duration//2])
        second_half_avg = statistics.mean(response_times[test_duration//2:])
        
        # Performance shouldn't degrade more than 50% over time
        degradation_ratio = second_half_avg / first_half_avg
        assert degradation_ratio < 1.5, f"Performance degraded by {(degradation_ratio - 1) * 100:.1f}% over time, exceeds 50% limit"
        
        # Overall performance should still meet requirements
        overall_avg = statistics.mean(response_times)
        assert overall_avg < 250, f"Overall average performance {overall_avg:.2f}ms exceeds 250ms consistency requirement"


# ===== FINANCIAL TRANSACTION THROUGHPUT TESTS =====

class TestFinancialTransactionThroughput:
    """Test financial transaction processing throughput requirements."""
    
    async def test_transaction_processing_throughput(self, client, auth_headers):
        """Test system can handle required transaction processing throughput."""
        # Test batch transaction creation throughput
        batch_size = 50
        transactions_data = []
        
        for i in range(batch_size):
            transactions_data.append({
                "description": f"THROUGHPUT TEST TRANSACTION {i}",
                "amount": -(i + 1) * 10.50,
                "date": "2024-07-15",
                "upload_id": "throughput_test"
            })
        
        # Process transactions and measure throughput
        start_time = time.time()
        created_transactions = 0
        
        for transaction_data in transactions_data:
            response = client.post(
                "/transactions",
                headers=auth_headers,
                json=transaction_data
            )
            if response.status_code == status.HTTP_201_CREATED:
                created_transactions += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = created_transactions / total_time  # transactions per second
        
        # Validate throughput requirements
        assert created_transactions == batch_size, f"Only {created_transactions}/{batch_size} transactions created successfully"
        assert throughput >= 10, f"Transaction throughput {throughput:.2f} TPS below 10 TPS requirement"
        
        # Individual transaction creation should still be fast
        avg_time_per_transaction = (total_time / created_transactions) * 1000
        assert avg_time_per_transaction < 200, f"Average time per transaction {avg_time_per_transaction:.2f}ms exceeds 200ms requirement"
    
    async def test_concurrent_transaction_processing(self, client, auth_headers):
        """Test concurrent transaction processing maintains performance."""
        def create_transaction(transaction_id):
            transaction_data = {
                "description": f"CONCURRENT TEST TRANSACTION {transaction_id}",
                "amount": -15.75,
                "date": "2024-07-15",
                "upload_id": "concurrent_test"
            }
            
            start_time = time.time()
            response = client.post(
                "/transactions",
                headers=auth_headers,
                json=transaction_data
            )
            end_time = time.time()
            
            return {
                "success": response.status_code == status.HTTP_201_CREATED,
                "response_time": (end_time - start_time) * 1000,
                "transaction_id": transaction_id
            }
        
        # Test 20 concurrent transaction creations
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(create_transaction, i) for i in range(20)]
            results = [future.result() for future in as_completed(futures)]
        
        # Validate all transactions were created successfully
        successful_creations = sum(1 for result in results if result["success"])
        assert successful_creations == 20, f"Only {successful_creations}/20 concurrent transactions created successfully"
        
        # Validate performance under concurrent load
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 300, f"Average concurrent transaction time {avg_response_time:.2f}ms exceeds 300ms limit"
        assert max_response_time < 500, f"Maximum concurrent transaction time {max_response_time:.2f}ms exceeds 500ms limit"


# ===== PERFORMANCE SUMMARY AND VALIDATION =====

class TestPerformanceSummaryValidation:
    """Final performance validation and summary tests."""
    
    async def test_overall_system_performance_requirements(self, client, auth_headers, performance_test_data):
        """Test overall system meets all performance requirements under realistic load."""
        # Test suite representing realistic user workflow
        workflow_tests = [
            ("auth", "POST", "/auth/token", None, 200),  # Authentication
            ("transactions_list", "GET", "/transactions?limit=25", auth_headers, 200),  # View transactions
            ("create_transaction", "POST", "/transactions", auth_headers, 200),  # Create transaction
            ("categories_list", "GET", "/categories", auth_headers, 200),  # View categories
            ("spending_report", "GET", "/reports/spending-by-category", auth_headers, 200),  # Generate report
            ("income_expense", "GET", "/reports/income-expense-summary", auth_headers, 200),  # Summary report
        ]
        
        # Prepare transaction creation data
        transaction_data = {
            "description": "FINAL VALIDATION TEST",
            "amount": -25.00,
            "date": "2024-07-15",
            "upload_id": "final_test"
        }
        
        performance_summary = {}
        
        for test_name, method, endpoint, headers, expected_limit in workflow_tests:
            response_times = []
            
            # Test each workflow step 10 times
            for _ in range(10):
                start_time = time.time()
                
                if method == "GET":
                    response = client.get(endpoint, headers=headers)
                elif method == "POST" and test_name == "auth":
                    response = client.post(
                        endpoint,
                        data={
                            "username": "<EMAIL>",
                            "password": "secure_test_password_2024!"
                        }
                    )
                elif method == "POST":
                    response = client.post(endpoint, headers=headers, json=transaction_data)
                
                end_time = time.time()
                response_times.append((end_time - start_time) * 1000)
                
                # Validate response is successful
                assert response.status_code in [status.HTTP_200_OK, status.HTTP_201_CREATED], f"{test_name} request failed with status {response.status_code}"
            
            # Calculate performance metrics
            avg_time = statistics.mean(response_times)
            max_time = max(response_times)
            p95_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max_time
            
            performance_summary[test_name] = {
                "avg_time": avg_time,
                "max_time": max_time,
                "p95_time": p95_time,
                "expected_limit": expected_limit,
                "all_times": response_times
            }
            
            # Validate against performance requirements
            assert avg_time < expected_limit, f"{test_name} average time {avg_time:.2f}ms exceeds {expected_limit}ms requirement"
            assert p95_time < expected_limit * 1.5, f"{test_name} P95 time {p95_time:.2f}ms exceeds {expected_limit * 1.5}ms tolerance"
        
        # Overall system performance validation
        all_avg_times = [metrics["avg_time"] for metrics in performance_summary.values()]
        overall_avg = statistics.mean(all_avg_times)
        
        assert overall_avg < 200, f"Overall system average response time {overall_avg:.2f}ms exceeds 200ms financial-grade requirement"
        
        # Ensure no single operation is a bottleneck
        max_avg_time = max(all_avg_times)
        assert max_avg_time < 250, f"Slowest operation average time {max_avg_time:.2f}ms exceeds 250ms bottleneck threshold"
        
        # Performance consistency validation
        time_variances = [statistics.variance(metrics["all_times"]) for metrics in performance_summary.values()]
        avg_variance = statistics.mean(time_variances)
        assert avg_variance < 10000, f"Average response time variance {avg_variance:.2f} indicates inconsistent performance"
    
    async def test_financial_grade_performance_certification(self, client, auth_headers, performance_test_data):
        """Final certification that system meets financial-grade performance standards."""
        # Financial-grade performance certification requirements
        certification_tests = [
            {
                "name": "Authentication Performance",
                "endpoint": "/auth/token",
                "method": "POST",
                "requirement": "<200ms avg, <300ms max",
                "target_avg": 200,
                "target_max": 300,
                "iterations": 20
            },
            {
                "name": "Transaction Query Performance",
                "endpoint": "/transactions?limit=50",
                "method": "GET", 
                "requirement": "<200ms avg, <300ms max",
                "target_avg": 200,
                "target_max": 300,
                "iterations": 20
            },
            {
                "name": "Financial Report Performance",
                "endpoint": "/reports/spending-by-category",
                "method": "GET",
                "requirement": "<200ms avg, <400ms max",
                "target_avg": 200,
                "target_max": 400,
                "iterations": 15
            },
            {
                "name": "Category Management Performance",
                "endpoint": "/categories",
                "method": "GET", 
                "requirement": "<200ms avg, <300ms max",
                "target_avg": 200,
                "target_max": 300,
                "iterations": 15
            }
        ]
        
        certification_results = []
        
        for test in certification_tests:
            response_times = []
            
            for _ in range(test["iterations"]):
                start_time = time.time()
                
                if test["method"] == "GET":
                    response = client.get(test["endpoint"], headers=auth_headers)
                elif test["method"] == "POST":
                    response = client.post(
                        test["endpoint"],
                        data={
                            "username": "<EMAIL>",
                            "password": "secure_test_password_2024!"
                        }
                    )
                
                end_time = time.time()
                response_times.append((end_time - start_time) * 1000)
                
                assert response.status_code in [status.HTTP_200_OK, status.HTTP_201_CREATED]
            
            # Calculate certification metrics
            avg_time = statistics.mean(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            p95_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max_time
            
            # Certification validation
            avg_passed = avg_time < test["target_avg"]
            max_passed = max_time < test["target_max"]
            consistency_passed = (max_time - min_time) < test["target_avg"]  # Consistency check
            
            certification_result = {
                "test_name": test["name"],
                "requirement": test["requirement"],
                "avg_time": avg_time,
                "max_time": max_time,
                "p95_time": p95_time,
                "avg_passed": avg_passed,
                "max_passed": max_passed,
                "consistency_passed": consistency_passed,
                "overall_passed": avg_passed and max_passed and consistency_passed
            }
            
            certification_results.append(certification_result)
            
            # Assert individual test certification
            assert certification_result["overall_passed"], f"CERTIFICATION FAILED: {test['name']} - Avg: {avg_time:.2f}ms (target: <{test['target_avg']}ms), Max: {max_time:.2f}ms (target: <{test['target_max']}ms)"
        
        # Overall certification validation
        overall_certification_passed = all(result["overall_passed"] for result in certification_results)
        assert overall_certification_passed, "FINANCIAL-GRADE PERFORMANCE CERTIFICATION FAILED: Some tests did not meet requirements"
        
        # Performance consistency across all tests
        all_avg_times = [result["avg_time"] for result in certification_results]
        overall_system_avg = statistics.mean(all_avg_times)
        assert overall_system_avg < 200, f"CERTIFICATION FAILED: Overall system average {overall_system_avg:.2f}ms exceeds 200ms financial-grade standard"