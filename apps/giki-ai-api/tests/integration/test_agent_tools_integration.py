"""
Agent Tools Integration Tests
=============================

Comprehensive integration tests for AI agent tools including:
- Categorization agent accuracy validation
- UI equivalence tools testing  
- Agent coordination and workflow testing
- Performance requirements for AI operations (<200ms)
- Multi-tenant isolation for AI services
- Financial-grade reliability and error handling

Tests cover the full agent ecosystem with 100% accuracy requirements.
"""

import tempfile
import time
from datetime import date, datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.main import app

# Import agent classes and tools
from giki_ai_api.domains.categories.agent import (
    AgentConfig,
    CategorizationAgent,
    get_category_taxonomy_tool_function,
)
from giki_ai_api.domains.categories.models import Category
from giki_ai_api.domains.intelligence.coordinator_agent import (
    CoordinatorAgent,
    CoordinatorAgentConfig,
    combine_agent_responses_tool_function,
    coordinate_multi_agent_task_tool_function,
    route_query_to_agent_tool_function,
)
from giki_ai_api.domains.intelligence.ui_equivalence_tools import (
    analyze_spending_patterns_lookup_tool_function,
    answer_transaction_questions_lookup_tool_function,
    navigate_user_to_page_tool_function,
)
from giki_ai_api.domains.onboarding.models import RAGCorpus
from giki_ai_api.domains.transactions.models import Entity, Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def agent_test_data(db_session: AsyncSession, test_tenant):
    """Create comprehensive test data for agent testing."""
    # Create hierarchical categories with GL codes
    categories = [
        Category(
            id=3001,
            name="Operating Expenses",
            tenant_id=test_tenant.id,
            path="Operating Expenses",
            level=0,
            gl_code="6000",
            gl_account_name="Operating Expenses",
            gl_account_type="Expense"
        ),
        Category(
            id=3002,
            name="Office Supplies",
            parent_id=3001,
            tenant_id=test_tenant.id,
            path="Operating Expenses > Office Supplies",
            level=1,
            gl_code="6100",
            gl_account_name="Office Supplies",
            gl_account_type="Expense"
        ),
        Category(
            id=3003,
            name="Technology",
            parent_id=3001,
            tenant_id=test_tenant.id,
            path="Operating Expenses > Technology",
            level=1,
            gl_code="6200",
            gl_account_name="Technology Expenses",
            gl_account_type="Expense"
        ),
        Category(
            id=3004,
            name="Revenue",
            tenant_id=test_tenant.id,
            path="Revenue",
            level=0,
            gl_code="4000",
            gl_account_name="Revenue",
            gl_account_type="Revenue"
        )
    ]
    
    for category in categories:
        db_session.add(category)
    
    # Create test entities
    entities = [
        Entity(
            id=1001,
            name="Starbucks",
            tenant_id=test_tenant.id,
            category="Food & Beverages"
        ),
        Entity(
            id=1002,
            name="Microsoft",
            tenant_id=test_tenant.id,
            category="Technology"
        ),
        Entity(
            id=1003,
            name="Amazon",
            tenant_id=test_tenant.id,
            category="Office Supplies"
        )
    ]
    
    for entity in entities:
        db_session.add(entity)
    
    # Create test transactions for patterns
    transactions = [
        Transaction(
            id="agent_test_001",
            description="STARBUCKS COFFEE #123",
            amount=Decimal("-5.50"),
            date=date(2024, 7, 15),
            tenant_id=test_tenant.id,
            entity_id=1001,
            category_id=3002,
            category_path="Operating Expenses > Office Supplies",
            upload_id="agent_test",
            is_categorized=True,
            transaction_type="expense"
        ),
        Transaction(
            id="agent_test_002",
            description="MICROSOFT 365 SUBSCRIPTION",
            amount=Decimal("-15.99"),
            date=date(2024, 7, 16),
            tenant_id=test_tenant.id,
            entity_id=1002,
            category_id=3003,
            category_path="Operating Expenses > Technology",
            upload_id="agent_test",
            is_categorized=True,
            transaction_type="expense"
        ),
        Transaction(
            id="agent_test_003",
            description="AMAZON OFFICE SUPPLIES",
            amount=Decimal("-25.75"),
            date=date(2024, 7, 17),
            tenant_id=test_tenant.id,
            entity_id=1003,
            category_id=3002,
            category_path="Operating Expenses > Office Supplies",
            upload_id="agent_test",
            is_categorized=True,
            transaction_type="expense"
        ),
        Transaction(
            id="agent_test_004",
            description="CLIENT PAYMENT - INVOICE 001",
            amount=Decimal("1500.00"),
            date=date(2024, 7, 18),
            tenant_id=test_tenant.id,
            category_id=3004,
            category_path="Revenue",
            upload_id="agent_test",
            is_categorized=True,
            transaction_type="income"
        )
    ]
    
    for transaction in transactions:
        db_session.add(transaction)
    
    # Create mock RAG corpus
    rag_corpus = RAGCorpus(
        id=1,
        tenant_id=test_tenant.id,
        corpus_name="agent_test_corpus",
        corpus_resource_name="projects/test/locations/us-central1/ragCorpora/test",
        is_active=True,
        created_at=datetime.utcnow()
    )
    db_session.add(rag_corpus)
    
    await db_session.commit()
    await db_session.refresh(rag_corpus)
    
    return {
        "categories": categories,
        "entities": entities,
        "transactions": transactions,
        "rag_corpus": rag_corpus
    }


# ===== CATEGORIZATION AGENT TESTS =====

class TestCategorizationAgentAccuracy:
    """Test categorization agent for 100% accuracy requirements."""
    
    async def test_categorization_agent_initialization_success(self, agent_test_data):
        """Test categorization agent initializes correctly."""
        config = AgentConfig(
            model_name="gemini-2.0-flash-001",
            rag_enabled=True,
            project="test-project",
            location="us-central1"
        )
        
        agent = CategorizationAgent(config=config)
        
        assert agent._model_name == "gemini-2.0-flash-001"
        assert agent._rag_enabled is True
        assert agent._project == "test-project"
        assert agent._location == "us-central1"
        assert hasattr(agent, '_vertex_model')
    
    async def test_category_taxonomy_tool_function_success(self, db_session, test_tenant, agent_test_data):
        """Test category taxonomy tool returns accurate category data."""
        start_time = time.time()
        
        result = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=test_tenant.id,
            query=None
        )
        
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <200ms
        assert execution_time < 200, f"Category taxonomy lookup took {execution_time:.2f}ms, exceeds 200ms requirement"
        
        # Accuracy validation
        assert "categories" in result
        assert len(result["categories"]) == 4  # Operating Expenses, Office Supplies, Technology, Revenue
        
        expected_categories = {"Operating Expenses", "Office Supplies", "Technology", "Revenue"}
        actual_categories = set(result["categories"])
        assert expected_categories == actual_categories, f"Category mismatch: expected {expected_categories}, got {actual_categories}"
    
    async def test_category_taxonomy_tool_multi_tenant_isolation(self, db_session, test_tenant, second_tenant, agent_test_data):
        """Test category taxonomy tool enforces tenant isolation."""
        # Test first tenant
        result_tenant1 = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=test_tenant.id,
            query=None
        )
        
        # Test second tenant (should have no categories)
        result_tenant2 = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=second_tenant.id,
            query=None
        )
        
        # Tenant isolation validation
        assert len(result_tenant1["categories"]) == 4
        assert len(result_tenant2["categories"]) == 0, "Second tenant should have no categories, tenant isolation failed"
    
    @patch('giki_ai_api.domains.categories.agent.GenerativeModel')
    async def test_categorization_agent_transaction_categorization_accuracy(self, mock_model, db_session, test_tenant, agent_test_data):
        """Test transaction categorization with mocked AI for 100% accuracy."""
        # Mock AI response for consistent testing
        mock_response = MagicMock()
        mock_response.text = """
        {
            "category": "Operating Expenses > Technology",
            "confidence": 0.95,
            "reasoning": "Microsoft subscription clearly falls under technology expenses",
            "alternatives": ["Operating Expenses > Office Supplies"],
            "patterns_applied": [],
            "rag_used": false
        }
        """
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.return_value = mock_model_instance
        
        config = AgentConfig(model_name="gemini-2.0-flash-001", rag_enabled=False)
        agent = CategorizationAgent(config=config)
        
        transaction_details = {
            "description": "MICROSOFT 365 SUBSCRIPTION",
            "amount": -15.99,
            "transaction_type": "expense",
            "tenant_id": test_tenant.id
        }
        
        start_time = time.time()
        result = await agent.categorize_transaction(transaction_details)
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <200ms for AI categorization
        assert execution_time < 200, f"AI categorization took {execution_time:.2f}ms, exceeds 200ms requirement"
        
        # Accuracy validation
        assert result == "Operating Expenses > Technology"
        
        # Verify detailed results
        details = agent.get_last_categorization_details()
        assert details["category"] == "Operating Expenses > Technology"
        assert details["confidence"] == 0.95
        assert "microsoft" in details["reasoning"].lower()
        assert details["method"] == "ai_prediction"
    
    @patch('giki_ai_api.domains.categories.agent.get_vector_rag_context_tool_function')
    async def test_categorization_agent_rag_integration_accuracy(self, mock_rag, db_session, test_tenant, agent_test_data):
        """Test RAG integration for high-confidence categorization lookup."""
        # Mock RAG response with high similarity
        mock_rag.return_value = {
            "retrieved_entries": [
                {
                    "category": "Operating Expenses > Office Supplies",
                    "description": "STARBUCKS COFFEE #456",
                    "similarity_score": 0.85,
                    "metadata": {"confidence": 0.9}
                }
            ],
            "rag_context": "Similar Transaction:\nCategory: Operating Expenses > Office Supplies\nContent: STARBUCKS COFFEE #456\nSimilarity: 0.850",
            "rag_used": True,
            "average_similarity": 0.85
        }
        
        config = AgentConfig(model_name="gemini-2.0-flash-001", rag_enabled=True)
        agent = CategorizationAgent(config=config)
        
        transaction_details = {
            "description": "STARBUCKS COFFEE #789",
            "amount": -6.25,
            "transaction_type": "expense", 
            "tenant_id": test_tenant.id
        }
        
        start_time = time.time()
        result = await agent.categorize_transaction(transaction_details)
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement
        assert execution_time < 200, f"RAG categorization took {execution_time:.2f}ms, exceeds 200ms requirement"
        
        # RAG lookup accuracy (85% similarity triggers direct lookup)
        assert result == "Operating Expenses > Office Supplies"
        
        # Verify RAG was used
        details = agent.get_last_categorization_details()
        assert details["method"] == "rag_lookup"
        assert details["rag_used"] is True
        assert details["confidence"] == 0.85
        assert "direct match" in details["reasoning"].lower()


class TestCategorizationAgentHierarchyInference:
    """Test category hierarchy inference capabilities."""
    
    @patch('giki_ai_api.domains.categories.agent.GenerativeModel')
    async def test_infer_category_hierarchy_success(self, mock_model, test_tenant):
        """Test AI-powered category hierarchy inference."""
        # Mock AI response for hierarchy inference
        mock_response = MagicMock()
        mock_response.text = """
        {
            "hierarchies": [
                {
                    "path": ["Operating Expenses", "Office Supplies"],
                    "confidence": 0.9,
                    "frequency": 0.8,
                    "suggested_gl_type": "Expense",
                    "reasoning": "Groups office-related expenses"
                },
                {
                    "path": ["Operating Expenses", "Technology"],
                    "confidence": 0.85,
                    "frequency": 0.7,
                    "suggested_gl_type": "Expense",
                    "reasoning": "Groups technology-related expenses"
                }
            ],
            "flat_categories": [
                {
                    "name": "Revenue",
                    "confidence": 0.95,
                    "suggested_gl_type": "Revenue"
                }
            ],
            "gl_suggestions": [
                {
                    "category": "Operating Expenses",
                    "gl_code": "6000",
                    "gl_account_name": "Operating Expenses",
                    "gl_account_type": "Expense"
                }
            ]
        }
        """
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.return_value = mock_model_instance
        
        config = AgentConfig(model_name="gemini-2.0-flash-001")
        agent = CategorizationAgent(config=config)
        
        categories = ["Office Supplies", "Technology", "Revenue"]
        category_patterns = {
            "Office Supplies": [
                {"description": "STAPLES OFFICE SUPPLIES", "amount": -25.50},
                {"description": "AMAZON OFFICE ITEMS", "amount": -15.75}
            ],
            "Technology": [
                {"description": "MICROSOFT 365", "amount": -15.99},
                {"description": "GITHUB SUBSCRIPTION", "amount": -7.00}
            ],
            "Revenue": [
                {"description": "CLIENT PAYMENT", "amount": 1500.00}
            ]
        }
        
        start_time = time.time()
        result = await agent.infer_category_hierarchy(categories, category_patterns, test_tenant.id)
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement
        assert execution_time < 5000, f"Hierarchy inference took {execution_time:.2f}ms, exceeds 5s limit"
        
        # Hierarchy inference accuracy
        assert "hierarchies" in result
        assert "flat_categories" in result
        assert "gl_suggestions" in result
        
        # Validate hierarchies
        hierarchies = result["hierarchies"]
        assert len(hierarchies) == 2
        
        office_hierarchy = next(h for h in hierarchies if "Office Supplies" in h["path"])
        assert office_hierarchy["path"] == ["Operating Expenses", "Office Supplies"]
        assert office_hierarchy["confidence"] == 0.9
        assert office_hierarchy["suggested_gl_type"] == "Expense"
        
        tech_hierarchy = next(h for h in hierarchies if "Technology" in h["path"])
        assert tech_hierarchy["path"] == ["Operating Expenses", "Technology"]
        assert tech_hierarchy["confidence"] == 0.85
        
        # Validate flat categories
        flat_categories = result["flat_categories"]
        revenue_category = next(c for c in flat_categories if c["name"] == "Revenue")
        assert revenue_category["suggested_gl_type"] == "Revenue"
        assert revenue_category["confidence"] == 0.95


# ===== COORDINATOR AGENT TESTS =====

class TestCoordinatorAgentOrchestration:
    """Test multi-agent coordination and orchestration."""
    
    async def test_coordinator_agent_initialization_success(self, db_session):
        """Test coordinator agent initializes with proper configuration."""
        config = CoordinatorAgentConfig(
            model_name="gemini-2.0-flash-001",
            project="test-project",
            location="us-central1",
            available_agents=["customer_agent", "categorization_agent", "reports_agent"]
        )
        
        coordinator = CoordinatorAgent(config=config, db=db_session)
        
        assert coordinator._model_name == "gemini-2.0-flash-001"
        assert coordinator._project == "test-project"
        assert coordinator._location == "us-central1"
        assert len(coordinator._available_agents) == 3
        assert "customer_agent" in coordinator._available_agents
    
    @patch('giki_ai_api.domains.intelligence.coordinator_agent.GenerativeModel')
    async def test_route_query_to_agent_accuracy(self, mock_model):
        """Test intelligent query routing to appropriate agents."""
        # Mock AI response for routing decision
        mock_response = MagicMock()
        mock_response.text = """
        {
            "primary_agent": "categorization_agent",
            "secondary_agents": [],
            "routing_reason": "Clear transaction categorization request",
            "workflow_type": "simple",
            "confidence": 0.95,
            "suggested_sequence": ["categorization_agent"],
            "context_requirements": ["tenant_id", "transaction_data"]
        }
        """
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.return_value = mock_model_instance
        
        available_agents = ["customer_agent", "categorization_agent", "reports_agent"]
        query = "Please categorize this transaction: STARBUCKS COFFEE"
        context = {"tenant_id": 1}
        
        start_time = time.time()
        result = await route_query_to_agent_tool_function(
            query=query,
            available_agents=available_agents,
            context=context
        )
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <200ms for routing decision
        assert execution_time < 200, f"Query routing took {execution_time:.2f}ms, exceeds 200ms requirement"
        
        # Routing accuracy
        assert result["primary_agent"] == "categorization_agent"
        assert result["workflow_type"] == "simple"
        assert result["confidence"] == 0.95
        assert "categorization" in result["routing_reason"].lower()
        assert "tenant_id" in result["context_requirements"]
    
    @patch('giki_ai_api.domains.intelligence.coordinator_agent.GenerativeModel')
    async def test_coordinate_multi_agent_task_complex_workflow(self, mock_model):
        """Test complex multi-agent workflow coordination."""
        # Mock AI response for coordination plan
        mock_response = MagicMock()
        mock_response.text = """
        {
            "execution_plan": {
                "phases": [
                    {
                        "phase_id": "phase_1",
                        "phase_name": "File Processing",
                        "agents": ["files_agent", "schema_interpretation_agent"],
                        "execution_mode": "parallel",
                        "dependencies": [],
                        "expected_duration": "30s"
                    },
                    {
                        "phase_id": "phase_2", 
                        "phase_name": "Transaction Categorization",
                        "agents": ["categorization_agent"],
                        "execution_mode": "sequential",
                        "dependencies": ["phase_1"],
                        "expected_duration": "45s"
                    },
                    {
                        "phase_id": "phase_3",
                        "phase_name": "Report Generation",
                        "agents": ["reports_agent"],
                        "execution_mode": "sequential", 
                        "dependencies": ["phase_2"],
                        "expected_duration": "20s"
                    }
                ],
                "total_phases": 3,
                "estimated_total_time": "95s"
            },
            "agent_assignments": {
                "files_agent": {
                    "task": "Process uploaded Excel file",
                    "input_required": ["file_path"],
                    "output_expected": ["processed_data"],
                    "phase": "phase_1"
                },
                "categorization_agent": {
                    "task": "Categorize extracted transactions",
                    "input_required": ["transaction_data"],
                    "output_expected": ["categorized_transactions"],
                    "phase": "phase_2"
                }
            },
            "coordination_metadata": {
                "strategy": "sequential",
                "parallelism_level": 2,
                "critical_path": ["phase_1", "phase_2", "phase_3"],
                "fallback_plan": "Retry failed agents once, then fail gracefully"
            }
        }
        """
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.return_value = mock_model_instance
        
        task_description = "Process uploaded file and generate categorized transaction report"
        agents_involved = [
            {"agent": "files_agent", "task": "Process file", "priority": 1},
            {"agent": "categorization_agent", "task": "Categorize transactions", "priority": 2},
            {"agent": "reports_agent", "task": "Generate report", "priority": 3}
        ]
        
        start_time = time.time()
        result = await coordinate_multi_agent_task_tool_function(
            task_description=task_description,
            agents_involved=agents_involved,
            coordination_strategy="sequential"
        )
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <1s for coordination planning
        assert execution_time < 1000, f"Coordination planning took {execution_time:.2f}ms, exceeds 1s limit"
        
        # Coordination plan accuracy
        assert "execution_plan" in result
        assert "agent_assignments" in result
        assert "coordination_metadata" in result
        
        execution_plan = result["execution_plan"]
        assert execution_plan["total_phases"] == 3
        assert execution_plan["estimated_total_time"] == "95s"
        
        # Validate phase dependencies
        phases = execution_plan["phases"]
        phase_1 = next(p for p in phases if p["phase_id"] == "phase_1")
        phase_2 = next(p for p in phases if p["phase_id"] == "phase_2") 
        phase_3 = next(p for p in phases if p["phase_id"] == "phase_3")
        
        assert phase_1["dependencies"] == []  # No dependencies
        assert phase_2["dependencies"] == ["phase_1"]  # Depends on phase 1
        assert phase_3["dependencies"] == ["phase_2"]  # Depends on phase 2
        
        # Validate parallel execution in phase 1
        assert phase_1["execution_mode"] == "parallel"
        assert len(phase_1["agents"]) == 2
    
    @patch('giki_ai_api.domains.intelligence.coordinator_agent.GenerativeModel')
    async def test_combine_agent_responses_synthesis(self, mock_model):
        """Test response aggregation and synthesis from multiple agents."""
        # Mock AI response for synthesis
        mock_response = MagicMock()
        mock_response.text = """
        {
            "synthesized_response": {
                "summary": "Successfully processed file with 150 transactions, categorized with 95% accuracy, and generated comprehensive financial report",
                "key_findings": [
                    "150 transactions processed from uploaded file",
                    "95% categorization accuracy achieved",
                    "Top spending category: Operating Expenses (60%)",
                    "Report generated with GL code mappings"
                ],
                "detailed_sections": {
                    "file_processing": {
                        "content": "Successfully processed Excel file with 150 transactions",
                        "source_agents": ["files_agent"],
                        "confidence": 0.98
                    },
                    "categorization": {
                        "content": "Achieved 95% categorization accuracy using AI and RAG lookup",
                        "source_agents": ["categorization_agent"],
                        "confidence": 0.95
                    },
                    "reporting": {
                        "content": "Generated comprehensive financial report with GL codes",
                        "source_agents": ["reports_agent"],
                        "confidence": 0.92
                    }
                },
                "action_items": [
                    "Review and confirm 5% of transactions with low confidence",
                    "Set up automated categorization rules for common patterns"
                ],
                "caveats": ["Manual review recommended for transactions below 80% confidence"]
            },
            "metadata": {
                "agents_consulted": ["files_agent", "categorization_agent", "reports_agent"],
                "response_quality": "high",
                "conflicts_found": [],
                "aggregation_strategy": "synthesis"
            },
            "user_response": "I've successfully processed your file with 150 transactions and generated a comprehensive report. The AI achieved 95% categorization accuracy, with top spending in Operating Expenses. Please review the 5% of transactions below 80% confidence for final verification."
        }
        """
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
        mock_model.return_value = mock_model_instance
        
        agent_responses = [
            {
                "agent": "files_agent",
                "success": True,
                "confidence": 0.98,
                "result": "Processed 150 transactions from Excel file",
                "details": {"rows_processed": 150, "errors": 0}
            },
            {
                "agent": "categorization_agent", 
                "success": True,
                "confidence": 0.95,
                "result": "Categorized transactions with 95% accuracy",
                "details": {"total_categorized": 150, "accuracy": 0.95}
            },
            {
                "agent": "reports_agent",
                "success": True,
                "confidence": 0.92,
                "result": "Generated financial report with GL codes",
                "details": {"report_sections": 5, "charts": 3}
            }
        ]
        
        start_time = time.time()
        result = await combine_agent_responses_tool_function(
            agent_responses=agent_responses,
            aggregation_strategy="synthesis",
            original_query="Process my financial data file and create a report"
        )
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <500ms for response synthesis
        assert execution_time < 500, f"Response synthesis took {execution_time:.2f}ms, exceeds 500ms limit"
        
        # Synthesis accuracy and completeness
        assert "synthesized_response" in result
        assert "metadata" in result
        assert "user_response" in result
        
        synthesized = result["synthesized_response"]
        assert "150 transactions" in synthesized["summary"]
        assert "95% accuracy" in synthesized["summary"]
        
        # Validate key findings
        key_findings = synthesized["key_findings"]
        assert len(key_findings) == 4
        assert any("150 transactions" in finding for finding in key_findings)
        assert any("95% categorization" in finding for finding in key_findings)
        
        # Validate detailed sections
        detailed_sections = synthesized["detailed_sections"]
        assert "file_processing" in detailed_sections
        assert "categorization" in detailed_sections
        assert "reporting" in detailed_sections
        
        # Validate metadata
        metadata = result["metadata"]
        assert metadata["response_quality"] == "high"
        assert len(metadata["agents_consulted"]) == 3
        assert metadata["aggregation_strategy"] == "synthesis"
        
        # Validate statistics
        statistics = result["statistics"]
        assert statistics["total_agents"] == 3
        assert statistics["successful_responses"] == 3
        assert statistics["average_confidence"] > 0.9


# ===== UI EQUIVALENCE TOOLS TESTS =====

class TestUIEquivalenceTools:
    """Test UI equivalence tools for chat interface functionality."""
    
    async def test_navigate_user_to_page_success(self):
        """Test navigation guidance tool accuracy."""
        start_time = time.time()
        
        result = await navigate_user_to_page_tool_function(
            page_name="transactions",
            context={"file_id": "test_file_123"}
        )
        
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <50ms for navigation
        assert execution_time < 50, f"Navigation guidance took {execution_time:.2f}ms, exceeds 50ms limit"
        
        # Navigation accuracy
        assert result["success"] is True
        assert result["navigation"]["page"] == "transactions"
        assert result["navigation"]["url"] == "/files/test_file_123"  # Context applied
        assert result["navigation"]["icon"] == "📊"
        assert "📊" in result["message"]
        assert "transactions" in result["message"].lower()
        assert result["ui_equivalent_action"] == "navigation_guidance"
    
    async def test_navigate_user_to_page_invalid_page(self):
        """Test navigation guidance for invalid page."""
        result = await navigate_user_to_page_tool_function(
            page_name="invalid_page"
        )
        
        # Error handling
        assert result["success"] is False
        assert "Available pages:" in result["message"]
        assert result["ui_equivalent_action"] == "navigation_help"
        
        # Should list available pages
        assert "transactions" in result["message"].lower()
        assert "reports" in result["message"].lower()
        assert "categories" in result["message"].lower()
    
    async def test_analyze_spending_patterns_lookup_accuracy(self, db_session, test_tenant, agent_test_data):
        """Test spending pattern analysis with database lookup for 100% accuracy."""
        start_time = time.time()
        
        result = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            analysis_period="last_30_days",
            category_filter=None
        )
        
        execution_time = (time.time() - start_time) * 1000
        
        # Performance requirement: <200ms for spending analysis
        assert execution_time < 200, f"Spending analysis took {execution_time:.2f}ms, exceeds 200ms requirement"
        
        # Analysis accuracy (100% database lookup)
        assert result["success"] is True
        assert "analysis" in result
        
        analysis = result["analysis"]
        assert analysis["period"] == "last_30_days"
        assert analysis["total_spending"] > 0  # Should have expense transactions
        assert analysis["transaction_count"] >= 3  # At least 3 expense transactions
        assert analysis["daily_average"] > 0
        
        # Validate top categories
        top_categories = analysis["top_categories"]
        assert len(top_categories) > 0
        assert all(isinstance(amount, (int, float)) for amount in top_categories.values())
        
        # Message formatting
        assert "💰 Spending Analysis" in result["message"]
        assert "$" in result["message"]  # Currency formatting
        assert result["ui_equivalent_action"] == "spending_analysis_complete"
    
    async def test_analyze_spending_patterns_tenant_isolation(self, db_session, test_tenant, second_tenant, agent_test_data):
        """Test spending analysis enforces tenant isolation."""
        # Analyze first tenant (should have data)
        result_tenant1 = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            analysis_period="last_30_days"
        )
        
        # Analyze second tenant (should have no data)
        result_tenant2 = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=second_tenant.id,
            db=db_session,
            analysis_period="last_30_days"
        )
        
        # Tenant isolation validation
        assert result_tenant1["success"] is True
        assert result_tenant1["analysis"]["transaction_count"] >= 3
        assert result_tenant1["analysis"]["total_spending"] > 0
        
        assert result_tenant2["success"] is True
        assert result_tenant2["analysis"]["transaction_count"] == 0
        assert result_tenant2["analysis"]["total_spending"] == 0
    
    async def test_answer_transaction_questions_lookup_accuracy(self, db_session, test_tenant, agent_test_data):
        """Test transaction question answering with 100% database accuracy."""
        questions_and_expected = [
            {
                "question": "How much did I spend on office supplies?",
                "expected_keywords": ["office supplies", "$"],
                "query_type": "amount"
            },
            {
                "question": "How many transactions do I have this month?",
                "expected_keywords": ["transactions"],
                "query_type": "count"
            },
            {
                "question": "What technology expenses do I have?",
                "expected_keywords": ["technology", "microsoft"],
                "query_type": "list"
            }
        ]
        
        for test_case in questions_and_expected:
            start_time = time.time()
            
            result = await answer_transaction_questions_lookup_tool_function(
                question=test_case["question"],
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            execution_time = (time.time() - start_time) * 1000
            
            # Performance requirement: <200ms per question
            assert execution_time < 200, f"Question answering took {execution_time:.2f}ms for '{test_case['question']}', exceeds 200ms requirement"
            
            # Answer accuracy (100% database lookup)
            assert result["success"] is True
            assert "answer" in result
            
            answer = result["answer"]
            assert answer["query_type"] == test_case["query_type"]
            assert answer["confidence"] == 1.0  # 100% confidence from database
            assert answer["data_source"] == "database_lookup"
            
            # Content validation
            message_lower = result["message"].lower()
            for keyword in test_case["expected_keywords"]:
                assert keyword.lower() in message_lower, f"Expected keyword '{keyword}' not found in answer for question '{test_case['question']}'"
            
            assert result["ui_equivalent_action"] == "question_answered"


# ===== PERFORMANCE AND RELIABILITY TESTS =====

class TestAgentPerformanceReliability:
    """Test agent performance requirements and error handling."""
    
    async def test_categorization_agent_performance_requirements(self, db_session, test_tenant, agent_test_data):
        """Test categorization agent meets <200ms performance requirement."""
        config = AgentConfig(model_name="gemini-2.0-flash-001", rag_enabled=False)
        
        # Test multiple transactions for consistent performance
        transaction_descriptions = [
            "STARBUCKS COFFEE #123",
            "MICROSOFT 365 SUBSCRIPTION", 
            "AMAZON OFFICE SUPPLIES",
            "UBER RIDE TO OFFICE",
            "GITHUB PREMIUM PLAN"
        ]
        
        with patch('giki_ai_api.domains.categories.agent.GenerativeModel') as mock_model:
            # Mock consistent AI responses
            mock_response = MagicMock()
            mock_response.text = '{"category": "Operating Expenses > Office Supplies", "confidence": 0.9, "reasoning": "Test categorization", "alternatives": [], "patterns_applied": [], "rag_used": false}'
            
            mock_model_instance = MagicMock()
            mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
            mock_model.return_value = mock_model_instance
            
            agent = CategorizationAgent(config=config)
            response_times = []
            
            for description in transaction_descriptions:
                transaction_details = {
                    "description": description,
                    "amount": -25.00,
                    "transaction_type": "expense",
                    "tenant_id": test_tenant.id
                }
                
                start_time = time.time()
                await agent.categorize_transaction(transaction_details)
                end_time = time.time()
                
                response_times.append((end_time - start_time) * 1000)
            
            # Performance validation
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            assert avg_response_time < 200, f"Average categorization time {avg_response_time:.2f}ms exceeds 200ms requirement"
            assert max_response_time < 300, f"Maximum categorization time {max_response_time:.2f}ms exceeds 300ms tolerance"
            assert all(rt < 500 for rt in response_times), "Some responses exceeded 500ms absolute limit"
    
    async def test_coordinator_agent_error_handling_reliability(self, db_session):
        """Test coordinator agent error handling and recovery mechanisms."""
        config = CoordinatorAgentConfig(model_name="gemini-2.0-flash-001")
        coordinator = CoordinatorAgent(config=config, db=db_session)
        
        # Test routing fallback on AI failure
        with patch('giki_ai_api.domains.intelligence.coordinator_agent.GenerativeModel') as mock_model:
            # Simulate AI failure
            mock_model_instance = MagicMock()
            mock_model_instance.generate_content_async = AsyncMock(side_effect=Exception("AI service unavailable"))
            mock_model.return_value = mock_model_instance
            
            result = await coordinator.route_query_to_agent(
                query="Test query",
                context={"tenant_id": 1}
            )
            
            # Should fall back to customer agent
            assert result["primary_agent"] == "customer_agent"
            assert result["confidence"] == 0.5
            assert "routing failed" in result["routing_reason"].lower()
            assert "error" in result
    
    async def test_ui_equivalence_tools_error_handling(self, db_session, test_tenant):
        """Test UI equivalence tools error handling."""
        # Test spending analysis with invalid tenant
        result = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=99999,  # Non-existent tenant
            db=db_session,
            analysis_period="last_30_days"
        )
        
        # Should handle gracefully
        assert result["success"] is True  # No data is still success
        assert result["analysis"]["transaction_count"] == 0
        assert result["analysis"]["total_spending"] == 0
        
        # Test question answering with database error simulation
        with patch.object(db_session, 'execute', side_effect=Exception("Database connection failed")):
            result = await answer_transaction_questions_lookup_tool_function(
                question="How much did I spend?",
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            # Should handle database errors gracefully
            assert result["success"] is False
            assert "error" in result
            assert result["ui_equivalent_action"] == "question_error"


# ===== INTEGRATION WORKFLOW TESTS =====

class TestAgentIntegrationWorkflows:
    """Test complete agent integration workflows."""
    
    async def test_file_processing_categorization_reporting_workflow(self, db_session, test_tenant, agent_test_data):
        """Test end-to-end workflow: file processing → categorization → reporting."""
        start_time = time.time()
        
        # Simulate file upload via chat
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
            temp_file.write("Date,Description,Amount\n")
            temp_file.write("2024-07-15,STARBUCKS COFFEE,-5.50\n")
            temp_file.write("2024-07-16,MICROSOFT 365,-15.99\n")
            temp_file.write("2024-07-17,CLIENT PAYMENT,1500.00\n")
            temp_file_path = temp_file.name
        
        # Mock file upload function (would normally process the file)
        upload_result = {
            "success": True,
            "file_id": "workflow_test_file",
            "row_count": 3,
            "columns": ["Date", "Description", "Amount"]
        }
        
        # Test navigation after file upload
        nav_result = await navigate_user_to_page_tool_function(
            page_name="files",
            context={"file_id": upload_result["file_id"]}
        )
        
        assert nav_result["success"] is True
        assert nav_result["navigation"]["url"] == f"/files/{upload_result['file_id']}"
        
        # Test spending analysis after processing
        analysis_result = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            analysis_period="last_30_days"
        )
        
        assert analysis_result["success"] is True
        assert analysis_result["analysis"]["transaction_count"] >= 3
        
        # Test question answering about the data
        qa_result = await answer_transaction_questions_lookup_tool_function(
            question="What are my technology expenses?",
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        assert qa_result["success"] is True
        assert qa_result["answer"]["confidence"] == 1.0
        
        total_time = (time.time() - start_time) * 1000
        
        # Complete workflow should finish within reasonable time
        assert total_time < 1000, f"Complete workflow took {total_time:.2f}ms, exceeds 1s limit"
        
        # Clean up
        import os
        os.unlink(temp_file_path)
    
    async def test_multi_tenant_agent_isolation_comprehensive(self, db_session, test_tenant, second_tenant, agent_test_data):
        """Test comprehensive multi-tenant isolation across all agent tools."""
        # Test category taxonomy isolation
        taxonomy_result_1 = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=test_tenant.id
        )
        
        taxonomy_result_2 = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=second_tenant.id
        )
        
        # First tenant should have categories, second should not
        assert len(taxonomy_result_1["categories"]) == 4
        assert len(taxonomy_result_2["categories"]) == 0
        
        # Test spending analysis isolation
        spending_result_1 = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        spending_result_2 = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=second_tenant.id,
            db=db_session
        )
        
        # First tenant should have transactions, second should not
        assert spending_result_1["analysis"]["transaction_count"] >= 3
        assert spending_result_2["analysis"]["transaction_count"] == 0
        
        # Test question answering isolation
        qa_result_1 = await answer_transaction_questions_lookup_tool_function(
            question="How many transactions do I have?",
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        qa_result_2 = await answer_transaction_questions_lookup_tool_function(
            question="How many transactions do I have?",
            tenant_id=second_tenant.id,
            db=db_session
        )
        
        # Verify different results for different tenants
        assert qa_result_1["success"] is True
        assert qa_result_2["success"] is True
        assert "0 transactions" in qa_result_2["message"] or "no" in qa_result_2["message"].lower()


class TestAgentToolsFinalValidation:
    """Final validation tests for agent tools integration."""
    
    async def test_agent_tools_financial_grade_accuracy(self, db_session, test_tenant, agent_test_data):
        """Test all agent tools meet financial-grade accuracy requirements."""
        accuracy_tests = []
        
        # Test 1: Category taxonomy accuracy (100% database)
        start_time = time.time()
        taxonomy_result = await get_category_taxonomy_tool_function(
            db=db_session,
            tenant_id=test_tenant.id
        )
        accuracy_tests.append({
            "test": "category_taxonomy",
            "time": (time.time() - start_time) * 1000,
            "accuracy": 1.0,  # 100% database accuracy
            "success": len(taxonomy_result["categories"]) == 4
        })
        
        # Test 2: Spending analysis accuracy (100% database)
        start_time = time.time()
        spending_result = await analyze_spending_patterns_lookup_tool_function(
            tenant_id=test_tenant.id,
            db=db_session
        )
        accuracy_tests.append({
            "test": "spending_analysis",
            "time": (time.time() - start_time) * 1000,
            "accuracy": 1.0,  # 100% database accuracy
            "success": spending_result["success"] and spending_result["analysis"]["transaction_count"] >= 3
        })
        
        # Test 3: Question answering accuracy (100% database)
        start_time = time.time()
        qa_result = await answer_transaction_questions_lookup_tool_function(
            question="How much total spending do I have?",
            tenant_id=test_tenant.id,
            db=db_session
        )
        accuracy_tests.append({
            "test": "question_answering",
            "time": (time.time() - start_time) * 1000,
            "accuracy": qa_result["answer"]["confidence"],
            "success": qa_result["success"] and qa_result["answer"]["confidence"] == 1.0
        })
        
        # Validate all tests pass financial-grade requirements
        for test in accuracy_tests:
            assert test["success"], f"{test['test']} failed accuracy requirements"
            assert test["accuracy"] >= 0.99, f"{test['test']} accuracy {test['accuracy']} below 99% requirement"
            assert test["time"] < 200, f"{test['test']} took {test['time']:.2f}ms, exceeds 200ms requirement"
        
        # Overall financial-grade validation
        avg_accuracy = sum(test["accuracy"] for test in accuracy_tests) / len(accuracy_tests)
        avg_time = sum(test["time"] for test in accuracy_tests) / len(accuracy_tests)
        
        assert avg_accuracy >= 0.99, f"Average accuracy {avg_accuracy} below 99% financial-grade requirement"
        assert avg_time < 200, f"Average response time {avg_time:.2f}ms exceeds 200ms requirement"
        assert all(test["success"] for test in accuracy_tests), "Some agent tools failed financial-grade requirements"