"""
File Processing E2E Workflow Integration Tests
=============================================

Comprehensive end-to-end integration tests for file processing workflow including:
- File upload and validation (CSV/Excel support)
- AI-powered schema interpretation 
- Column mapping confirmation and processing
- Transaction creation from file data
- Database storage with multi-tenant isolation
- Performance validation (<200ms requirements)
- Financial-grade accuracy and error handling
"""

import io
import os
import tempfile
import time
from datetime import date

import pandas as pd
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy import select

from giki_ai_api.core.main import app
from giki_ai_api.domains.transactions.models import Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def second_tenant_auth_headers(client, second_tenant_user):
    """Get authentication headers for second tenant user."""
    response = client.post(
        "/auth/token",
        data={
            "username": second_tenant_user.email,
            "password": "SecondPassword123!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_csv_data():
    """Create sample CSV data for testing."""
    return """Date,Description,Amount,Account,Type
2024-07-01,Amazon Web Services,"-450.00",Business Checking,Debit
2024-07-02,Microsoft Office 365,"-120.00",Business Checking,Debit
2024-07-03,Client Payment - ABC Corp,"2500.00",Business Checking,Credit
2024-07-04,Office Supplies - Staples,"-85.50",Business Checking,Debit
2024-07-05,Travel Expense - Flight,"-650.00",Corporate Credit Card,Debit
2024-07-06,Software License,"-299.99",Business Checking,Debit
2024-07-07,Consulting Revenue,"1800.00",Business Checking,Credit
2024-07-08,Rent Payment,"-2200.00",Business Checking,Debit
2024-07-09,Utilities - Electric,"-180.75",Business Checking,Debit
2024-07-10,Marketing Campaign,"-750.00",Business Checking,Debit"""


@pytest.fixture
def sample_excel_data():
    """Create sample Excel data for testing."""
    data = {
        'Transaction Date': ['2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19'],
        'Transaction Description': [
            'Professional Services Revenue',
            'Internet Service Provider',
            'Equipment Purchase - Dell',
            'Insurance Premium',
            'Client Meeting Expenses'
        ],
        'Debit Amount': ['', '89.99', '1250.00', '450.00', '125.75'],
        'Credit Amount': ['3500.00', '', '', '', ''],
        'Account Name': ['Revenue Account', 'Operating Account', 'Operating Account', 'Operating Account', 'Operating Account'],
        'Reference Number': ['REF001', 'REF002', 'REF003', 'REF004', 'REF005']
    }
    return data


@pytest.fixture
def complex_excel_data():
    """Create complex Excel data with multiple formats for testing."""
    data = {
        'Trans_Date': ['07/20/2024', '07/21/2024', '07/22/2024', '07/23/2024'],
        'Memo': [
            'AMAZON.COM AMZN.COM/BILL WA',
            'SQ *COFFEE SHOP SEATTLE WA',
            'PAYROLL DEPOSIT - COMPANY XYZ',
            'ATM WITHDRAWAL FEE'
        ],
        'Amount ($)': ['-45.67', '-12.50', '2,850.00', '-3.50'],
        'Balance': ['1,234.56', '1,222.06', '4,072.06', '4,068.56'],
        'Transaction_Type': ['Purchase', 'Purchase', 'Deposit', 'Fee'],
        'Category': ['Technology', 'Meals', 'Income', 'Bank Fees']
    }
    return data


class TestFileUploadEndpoint:
    """Test file upload functionality and validation."""
    
    async def test_upload_csv_file_success(self, client, auth_headers, sample_csv_data):
        """Test successful CSV file upload."""
        # Create temporary CSV file
        csv_file = io.StringIO(sample_csv_data)
        csv_bytes = csv_file.getvalue().encode('utf-8')
        
        files = {
            "file": ("test_transactions.csv", io.BytesIO(csv_bytes), "text/csv")
        }
        
        start_time = time.time()
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        end_time = time.time()
        upload_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert upload response structure
        assert "upload_id" in data
        assert data["filename"] == "test_transactions.csv"
        assert data["content_type"] == "text/csv"
        assert data["status"] == "uploaded"
        assert "headers" in data
        assert len(data["headers"]) == 5  # Date, Description, Amount, Account, Type
        
        # Assert performance requirement
        assert upload_time_ms < 2000, f"File upload took {upload_time_ms:.2f}ms, exceeds 2000ms requirement"
        
        return data["upload_id"]
        
    async def test_upload_excel_file_success(self, client, auth_headers, sample_excel_data):
        """Test successful Excel file upload."""
        # Create temporary Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df = pd.DataFrame(sample_excel_data)
            df.to_excel(tmp_file.name, index=False)
            tmp_file.flush()
            
            with open(tmp_file.name, 'rb') as excel_file:
                files = {
                    "file": ("test_transactions.xlsx", excel_file, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                }
                
                response = client.post(
                    "/api/v1/uploads/upload",
                    headers=auth_headers,
                    files=files
                )
        
        # Clean up temporary file
        os.unlink(tmp_file.name)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert Excel upload success
        assert data["filename"] == "test_transactions.xlsx"
        assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in data["content_type"]
        assert data["status"] == "uploaded"
        assert len(data["headers"]) == 6  # All Excel columns
        
        return data["upload_id"]
        
    async def test_upload_invalid_file_type_rejected(self, client, auth_headers):
        """Test that invalid file types are rejected."""
        # Create a text file (not supported)
        text_content = "This is not a CSV or Excel file"
        files = {
            "file": ("test.txt", io.BytesIO(text_content.encode()), "text/plain")
        }
        
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should reject unsupported file type
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_422_UNPROCESSABLE_ENTITY]
        
    async def test_upload_large_file_handling(self, client, auth_headers):
        """Test handling of large files within limits."""
        # Create a large CSV (but within limits)
        large_csv_rows = []
        large_csv_rows.append("Date,Description,Amount")
        
        # Add 1000 rows of data
        for i in range(1000):
            date_str = f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}"
            large_csv_rows.append(f"{date_str},Transaction {i},-{100 + i}.00")
        
        large_csv_content = "\n".join(large_csv_rows)
        csv_bytes = large_csv_content.encode('utf-8')
        
        files = {
            "file": ("large_transactions.csv", io.BytesIO(csv_bytes), "text/csv")
        }
        
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should handle large file successfully
        assert data["status"] == "uploaded"
        assert data["size"] > 10000  # Should be substantial size


class TestColumnExtractionEndpoint:
    """Test column extraction functionality."""
    
    async def test_get_columns_success(self, client, auth_headers, sample_csv_data):
        """Test retrieving columns from uploaded file."""
        # First upload a file
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Get columns
        response = client.get(
            f"/api/v1/uploads/{upload_id}/columns",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert column extraction
        assert data["upload_id"] == upload_id
        assert "columns" in data
        expected_columns = ["Date", "Description", "Amount", "Account", "Type"]
        assert data["columns"] == expected_columns
        
    async def test_get_columns_performance(self, client, auth_headers, sample_csv_data):
        """Test column extraction performance."""
        # Upload file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("perf_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Measure column extraction performance
        start_time = time.time()
        response = client.get(
            f"/api/v1/uploads/{upload_id}/columns",
            headers=auth_headers
        )
        end_time = time.time()
        extraction_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert extraction_time_ms < 200, f"Column extraction took {extraction_time_ms:.2f}ms, exceeds 200ms requirement"


class TestSchemaInterpretationEndpoint:
    """Test AI-powered schema interpretation functionality."""
    
    async def test_schema_interpretation_success(self, client, auth_headers, sample_csv_data):
        """Test successful AI schema interpretation."""
        # Upload file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("schema_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Get schema interpretation
        start_time = time.time()
        response = client.get(
            f"/api/v1/uploads/{upload_id}/schema-interpretation",
            headers=auth_headers
        )
        end_time = time.time()
        interpretation_time_ms = (end_time - start_time) * 1000
        
        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            
            # Assert interpretation structure
            assert data["upload_id"] == upload_id
            assert data["filename"] == "schema_test.csv"
            assert "column_mappings" in data
            assert "overall_confidence" in data
            assert "required_fields_mapped" in data
            assert "interpretation_summary" in data
            
            # Check required fields mapping
            required_fields = ["date", "description", "amount"]
            for field in required_fields:
                assert field in data["required_fields_mapped"]
            
            # Validate column mappings structure
            for mapping in data["column_mappings"]:
                assert "original_name" in mapping
                assert "mapped_field" in mapping
                assert "confidence" in mapping
                assert "reasoning" in mapping
                assert 0.0 <= mapping["confidence"] <= 1.0
            
            # Performance validation
            assert interpretation_time_ms < 15000, f"Schema interpretation took {interpretation_time_ms:.2f}ms, exceeds 15000ms timeout"
            
        elif response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE:
            # AI service unavailable - this is acceptable in testing
            assert "AI interpretation service temporarily unavailable" in response.json()["detail"]
        else:
            # Other errors should not occur
            raise AssertionError(f"Unexpected response status: {response.status_code}")
            
    async def test_complex_schema_interpretation(self, client, auth_headers, complex_excel_data):
        """Test schema interpretation with complex Excel format."""
        # Create complex Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df = pd.DataFrame(complex_excel_data)
            df.to_excel(tmp_file.name, index=False)
            tmp_file.flush()
            
            with open(tmp_file.name, 'rb') as excel_file:
                files = {
                    "file": ("complex_transactions.xlsx", excel_file, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                }
                
                upload_response = client.post(
                    "/api/v1/uploads/upload",
                    headers=auth_headers,
                    files=files
                )
        
        os.unlink(tmp_file.name)
        upload_id = upload_response.json()["upload_id"]
        
        # Get schema interpretation for complex format
        response = client.get(
            f"/api/v1/uploads/{upload_id}/schema-interpretation",
            headers=auth_headers
        )
        
        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            
            # Should handle complex column names intelligently
            mapped_fields = [mapping["mapped_field"] for mapping in data["column_mappings"]]
            
            # Should map required fields even with complex names
            assert "date" in mapped_fields or "Trans_Date" in [m["original_name"] for m in data["column_mappings"]]
            assert "description" in mapped_fields or "Memo" in [m["original_name"] for m in data["column_mappings"]]
            assert "amount" in mapped_fields or "Amount ($)" in [m["original_name"] for m in data["column_mappings"]]


class TestColumnMappingAndProcessing:
    """Test column mapping confirmation and transaction processing."""
    
    async def test_process_mapped_file_success(self, client, auth_headers, sample_csv_data, db_session, test_tenant):
        """Test successful file processing with column mappings."""
        # Upload file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("mapping_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Define column mappings
        column_mappings = {
            "Date": "date",
            "Description": "description", 
            "Amount": "amount",
            "Account": "account",
            "Type": "transaction_type"
        }
        
        # Process file with mappings
        start_time = time.time()
        response = client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        end_time = time.time()
        processing_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert processing success
        assert "message" in data
        assert data["records_processed"] > 0
        assert data["upload_id"] == upload_id
        assert data["errors"] == []
        
        # Verify transactions were created in database
        result = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == test_tenant.id
            )
        )
        transactions = result.scalars().all()
        
        assert len(transactions) == data["records_processed"]
        assert len(transactions) >= 8  # Should process most of the sample data
        
        # Verify transaction data accuracy
        for transaction in transactions:
            assert transaction.tenant_id == test_tenant.id
            assert transaction.upload_id == upload_id
            assert transaction.description is not None
            assert transaction.amount is not None
            assert transaction.date is not None
            
        # Performance validation
        assert processing_time_ms < 5000, f"File processing took {processing_time_ms:.2f}ms, exceeds 5000ms requirement"
        
        return upload_id, transactions
        
    async def test_missing_required_fields_rejected(self, client, auth_headers, sample_csv_data):
        """Test that missing required fields are rejected."""
        # Upload file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("missing_fields.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Try to process with missing required fields
        incomplete_mappings = {
            "Date": "date",
            "Description": "description"
            # Missing amount field
        }
        
        response = client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": incomplete_mappings}
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Required fields not mapped" in response.json()["detail"]
        
    async def test_batch_transaction_creation_performance(self, client, auth_headers, db_session, test_tenant):
        """Test performance of batch transaction creation."""
        # Create large CSV for batch testing
        large_csv_rows = ["Date,Description,Amount"]
        for i in range(500):  # 500 transactions
            date_str = f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}"
            large_csv_rows.append(f"{date_str},Transaction {i},-{100 + i}.00")
        
        large_csv_content = "\n".join(large_csv_rows)
        csv_bytes = large_csv_content.encode('utf-8')
        
        # Upload large file
        files = {"file": ("batch_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Process with batch operations
        column_mappings = {
            "Date": "date",
            "Description": "description",
            "Amount": "amount"
        }
        
        start_time = time.time()
        response = client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        end_time = time.time()
        batch_processing_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should process large batch efficiently
        assert data["records_processed"] >= 450  # Most records should be processed
        assert batch_processing_time_ms < 10000, f"Batch processing took {batch_processing_time_ms:.2f}ms, exceeds 10000ms requirement"
        
        # Verify batch insert worked
        result = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == test_tenant.id
            )
        )
        transactions = result.scalars().all()
        assert len(transactions) == data["records_processed"]


class TestTransactionRetrievalEndpoint:
    """Test transaction retrieval from processed uploads."""
    
    async def test_get_transactions_for_upload_success(self, client, auth_headers, sample_csv_data, db_session, test_tenant):
        """Test retrieving transactions for a specific upload."""
        # Process a file first to create transactions
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("retrieval_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Process file
        column_mappings = {
            "Date": "date",
            "Description": "description",
            "Amount": "amount",
            "Account": "account"
        }
        
        client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        # Retrieve transactions
        response = client.get(
            f"/api/v1/uploads/{upload_id}/transactions",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert transaction retrieval structure
        assert "items" in data
        assert "total" in data
        assert len(data["items"]) > 0
        
        # Verify transaction data structure
        for transaction in data["items"]:
            assert "id" in transaction
            assert "description" in transaction
            assert "amount" in transaction
            assert "date" in transaction
            assert "upload_id" in transaction
            assert transaction["upload_id"] == upload_id
            
    async def test_transaction_retrieval_performance(self, client, auth_headers, sample_csv_data):
        """Test transaction retrieval performance."""
        # Process a file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("perf_retrieval.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Process file
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        # Measure transaction retrieval performance
        start_time = time.time()
        response = client.get(
            f"/api/v1/uploads/{upload_id}/transactions",
            headers=auth_headers
        )
        end_time = time.time()
        retrieval_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert retrieval_time_ms < 500, f"Transaction retrieval took {retrieval_time_ms:.2f}ms, exceeds 500ms requirement"


class TestMultiTenantIsolation:
    """Test multi-tenant data isolation in file processing."""
    
    async def test_upload_tenant_isolation(self, client, auth_headers, second_tenant_auth_headers, sample_csv_data, db_session, test_tenant, second_tenant):
        """Test that uploaded files are isolated by tenant."""
        # First tenant uploads file
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("tenant1_file.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response1 = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id1 = upload_response1.json()["upload_id"]
        
        # Second tenant uploads different file
        files2 = {"file": ("tenant2_file.csv", io.BytesIO(csv_bytes), "text/csv")}
        upload_response2 = client.post(
            "/api/v1/uploads/upload",
            headers=second_tenant_auth_headers,
            files=files2
        )
        upload_id2 = upload_response2.json()["upload_id"]
        
        # First tenant should only see their upload
        uploads_response1 = client.get(
            "/api/v1/uploads",
            headers=auth_headers
        )
        uploads1 = uploads_response1.json()
        upload_ids1 = [upload["upload_id"] for upload in uploads1]
        
        assert upload_id1 in upload_ids1
        assert upload_id2 not in upload_ids1
        
        # Second tenant should only see their upload
        uploads_response2 = client.get(
            "/api/v1/uploads",
            headers=second_tenant_auth_headers
        )
        uploads2 = uploads_response2.json()
        upload_ids2 = [upload["upload_id"] for upload in uploads2]
        
        assert upload_id2 in upload_ids2
        assert upload_id1 not in upload_ids2
        
    async def test_cross_tenant_upload_access_denied(self, client, auth_headers, second_tenant_auth_headers, sample_csv_data):
        """Test that tenants cannot access other tenant's uploads."""
        # First tenant uploads file
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("cross_tenant_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Second tenant tries to access first tenant's upload
        response = client.get(
            f"/api/v1/uploads/{upload_id}/columns",
            headers=second_tenant_auth_headers
        )
        
        # Should be denied
        assert response.status_code == status.HTTP_403_FORBIDDEN
        
    async def test_transaction_tenant_isolation(self, client, auth_headers, second_tenant_auth_headers, sample_csv_data, db_session, test_tenant, second_tenant):
        """Test that transactions created from uploads are tenant-isolated."""
        # Process file for first tenant
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("tenant_isolation.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Process file
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        # Verify transactions are isolated by tenant in database
        result1 = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == test_tenant.id
            )
        )
        tenant1_transactions = result1.scalars().all()
        
        result2 = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == second_tenant.id
            )
        )
        tenant2_transactions = result2.scalars().all()
        
        # First tenant should have transactions, second should not
        assert len(tenant1_transactions) > 0
        assert len(tenant2_transactions) == 0


class TestErrorHandlingAndValidation:
    """Test error handling and validation in file processing workflow."""
    
    async def test_invalid_file_format_handling(self, client, auth_headers):
        """Test handling of invalid file formats."""
        # Create invalid file content
        invalid_content = "This is not a valid CSV or Excel file"
        files = {"file": ("invalid.txt", io.BytesIO(invalid_content.encode()), "text/plain")}
        
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should reject invalid format
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_422_UNPROCESSABLE_ENTITY]
        
    async def test_malformed_csv_handling(self, client, auth_headers):
        """Test handling of malformed CSV files."""
        # Create malformed CSV
        malformed_csv = """Date,Description,Amount
2024-07-01,"Unclosed quote transaction,-100.00
2024-07-02,Normal transaction,-50.00"""
        
        csv_bytes = malformed_csv.encode('utf-8')
        files = {"file": ("malformed.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should handle gracefully or provide clear error
        if response.status_code != status.HTTP_200_OK:
            assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_422_UNPROCESSABLE_ENTITY]
        
    async def test_empty_file_handling(self, client, auth_headers):
        """Test handling of empty files."""
        empty_content = ""
        files = {"file": ("empty.csv", io.BytesIO(empty_content.encode()), "text/csv")}
        
        response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should reject empty file
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_422_UNPROCESSABLE_ENTITY]
        
    async def test_unauthorized_access_handling(self, client, sample_csv_data):
        """Test handling of unauthorized access attempts."""
        # Try to upload without authentication
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("unauthorized.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        response = client.post(
            "/api/v1/uploads/upload",
            files=files
        )
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    async def test_invalid_column_mapping_handling(self, client, auth_headers, sample_csv_data):
        """Test handling of invalid column mappings."""
        # Upload file first
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("invalid_mapping.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Try invalid mappings
        invalid_mappings = {
            "NonExistentColumn": "date",
            "Description": "description",
            "Amount": "amount"
        }
        
        response = client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": invalid_mappings}
        )
        
        # Should reject invalid column reference
        assert response.status_code == status.HTTP_400_BAD_REQUEST


class TestDataIntegrityAndAccuracy:
    """Test data integrity and financial accuracy in file processing."""
    
    async def test_transaction_data_accuracy(self, client, auth_headers, db_session, test_tenant):
        """Test that transaction data maintains financial accuracy."""
        # Create precise financial data
        precise_csv = """Date,Description,Amount
2024-07-01,Test Transaction 1,-123.45
2024-07-02,Test Transaction 2,67.89
2024-07-03,Test Transaction 3,-1000.00"""
        
        csv_bytes = precise_csv.encode('utf-8')
        files = {"file": ("precision_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        # Upload and process
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        # Verify data accuracy in database
        result = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == test_tenant.id
            ).order_by(Transaction.date)
        )
        transactions = result.scalars().all()
        
        assert len(transactions) == 3
        
        # Check financial precision
        assert abs(float(transactions[0].amount) - (-123.45)) < 0.001
        assert abs(float(transactions[1].amount) - 67.89) < 0.001  
        assert abs(float(transactions[2].amount) - (-1000.00)) < 0.001
        
        # Check date accuracy
        expected_dates = [date(2024, 7, 1), date(2024, 7, 2), date(2024, 7, 3)]
        actual_dates = [t.date for t in transactions]
        assert actual_dates == expected_dates
        
    async def test_large_amount_handling(self, client, auth_headers, db_session, test_tenant):
        """Test handling of large financial amounts."""
        # Create data with large amounts
        large_amount_csv = """Date,Description,Amount
2024-07-01,Large Credit Transaction,999999.99
2024-07-02,Large Debit Transaction,-888888.88
2024-07-03,Very Large Transaction,1234567.89"""
        
        csv_bytes = large_amount_csv.encode('utf-8')
        files = {"file": ("large_amounts.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        # Upload and process
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        response = client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify large amounts are stored correctly
        result = await db_session.execute(
            select(Transaction).where(
                Transaction.upload_id == upload_id,
                Transaction.tenant_id == test_tenant.id
            )
        )
        transactions = result.scalars().all()
        
        amounts = [float(t.amount) for t in transactions]
        assert 999999.99 in amounts
        assert -888888.88 in amounts
        assert 1234567.89 in amounts


class TestInterpretationStorageAndRetrieval:
    """Test interpretation storage and retrieval functionality."""
    
    async def test_interpretation_storage_retrieval(self, client, auth_headers, sample_csv_data, db_session):
        """Test storing and retrieving interpretation results."""
        # Upload and interpret a file
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("storage_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_id = upload_response.json()["upload_id"]
        
        # Get schema interpretation (which should store results)
        interpretation_response = client.get(
            f"/api/v1/uploads/{upload_id}/schema-interpretation",
            headers=auth_headers
        )
        
        if interpretation_response.status_code == status.HTTP_200_OK:
            # Try to retrieve stored interpretation results
            storage_response = client.get(
                f"/api/v1/uploads/{upload_id}/interpretation",
                headers=auth_headers
            )
            
            if storage_response.status_code == status.HTTP_200_OK:
                data = storage_response.json()
                
                # Assert storage structure
                assert "interpretation_id" in data
                assert data["upload_id"] == upload_id
                assert "overall_confidence" in data
                assert "interpretation_status" in data
                assert "column_mappings" in data
                assert "created_at" in data


class TestPerformanceValidation:
    """Test performance requirements across the file processing workflow."""
    
    async def test_end_to_end_workflow_performance(self, client, auth_headers, sample_csv_data):
        """Test complete E2E workflow performance."""
        # Measure complete workflow timing
        workflow_start = time.time()
        
        # Step 1: Upload
        csv_bytes = sample_csv_data.encode('utf-8')
        files = {"file": ("e2e_perf.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_start = time.time()
        upload_response = client.post(
            "/api/v1/uploads/upload",
            headers=auth_headers,
            files=files
        )
        upload_end = time.time()
        upload_id = upload_response.json()["upload_id"]
        
        # Step 2: Column extraction
        columns_start = time.time()
        client.get(f"/api/v1/uploads/{upload_id}/columns", headers=auth_headers)
        columns_end = time.time()
        
        # Step 3: Processing
        processing_start = time.time()
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        client.post(
            f"/api/v1/uploads/{upload_id}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        processing_end = time.time()
        
        # Step 4: Transaction retrieval
        retrieval_start = time.time()
        client.get(f"/api/v1/uploads/{upload_id}/transactions", headers=auth_headers)
        retrieval_end = time.time()
        
        workflow_end = time.time()
        
        # Calculate timing
        upload_time = (upload_end - upload_start) * 1000
        columns_time = (columns_end - columns_start) * 1000
        processing_time = (processing_end - processing_start) * 1000
        retrieval_time = (retrieval_end - retrieval_start) * 1000
        total_workflow_time = (workflow_end - workflow_start) * 1000
        
        # Assert performance requirements
        assert upload_time < 2000, f"Upload step took {upload_time:.2f}ms, exceeds 2000ms"
        assert columns_time < 200, f"Column extraction took {columns_time:.2f}ms, exceeds 200ms"
        assert processing_time < 5000, f"Processing step took {processing_time:.2f}ms, exceeds 5000ms"
        assert retrieval_time < 500, f"Retrieval step took {retrieval_time:.2f}ms, exceeds 500ms"
        assert total_workflow_time < 10000, f"Complete E2E workflow took {total_workflow_time:.2f}ms, exceeds 10000ms"


class TestFinancialGradeReliability:
    """Test financial-grade reliability and consistency."""
    
    async def test_duplicate_upload_handling(self, client, auth_headers, sample_csv_data):
        """Test handling of duplicate file uploads."""
        csv_bytes = sample_csv_data.encode('utf-8')
        
        # Upload same file twice
        files1 = {"file": ("duplicate_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        files2 = {"file": ("duplicate_test.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        response1 = client.post("/api/v1/uploads/upload", headers=auth_headers, files=files1)
        response2 = client.post("/api/v1/uploads/upload", headers=auth_headers, files=files2)
        
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        # Should create separate upload records
        upload_id1 = response1.json()["upload_id"]
        upload_id2 = response2.json()["upload_id"]
        assert upload_id1 != upload_id2
        
    async def test_concurrent_processing_isolation(self, client, auth_headers, sample_csv_data):
        """Test that concurrent file processing operations don't interfere."""
        csv_bytes = sample_csv_data.encode('utf-8')
        
        # Upload multiple files concurrently
        files1 = {"file": ("concurrent1.csv", io.BytesIO(csv_bytes), "text/csv")}
        files2 = {"file": ("concurrent2.csv", io.BytesIO(csv_bytes), "text/csv")}
        
        upload_response1 = client.post("/api/v1/uploads/upload", headers=auth_headers, files=files1)
        upload_response2 = client.post("/api/v1/uploads/upload", headers=auth_headers, files=files2)
        
        upload_id1 = upload_response1.json()["upload_id"]
        upload_id2 = upload_response2.json()["upload_id"]
        
        # Process both files
        column_mappings = {"Date": "date", "Description": "description", "Amount": "amount"}
        
        response1 = client.post(
            f"/api/v1/uploads/{upload_id1}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        response2 = client.post(
            f"/api/v1/uploads/{upload_id2}/map",
            headers=auth_headers,
            json={"mapping": column_mappings}
        )
        
        # Both should succeed independently
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        # Should create different transaction sets
        transactions1 = client.get(f"/api/v1/uploads/{upload_id1}/transactions", headers=auth_headers)
        transactions2 = client.get(f"/api/v1/uploads/{upload_id2}/transactions", headers=auth_headers)
        
        data1 = transactions1.json()
        data2 = transactions2.json()
        
        # Should have separate transaction sets
        transaction_ids1 = {t["id"] for t in data1["items"]}
        transaction_ids2 = {t["id"] for t in data2["items"]}
        assert transaction_ids1.isdisjoint(transaction_ids2)