"""
Intelligence Router Integration Tests
===================================

Tests for the Intelligence Router endpoints including:
- Customer query processing with agent integration
- Audio input processing workflow
- Customer agent initialization and tool execution
- Error handling and validation
"""

import asyncio
from unittest.mock import AsyncMock, Mock, patch

import pytest

pytestmark = pytest.mark.asyncio

from giki_ai_api.domains.intelligence.router import (
    CustomerQueryRequest,
    process_customer_query,
)


class TestCustomerQueryProcessing:
    """Test customer query processing endpoint."""
    
    @pytest.fixture
    def sample_query_request(self):
        """Sample customer query request."""
        return CustomerQueryRequest(
            query="Show me my transactions from last month",
            query_type="transactions",
            parameters={
                "date_range": {"start": "2024-06-01", "end": "2024-06-30"},
                "limit": 50
            }
        )
    
    @pytest.fixture
    def mock_customer_agent(self):
        """Mock customer facing agent."""
        mock_agent = Mock()
        mock_agent.execute_tool = AsyncMock()
        return mock_agent
    
    async def test_process_customer_query_transactions_success(
        self, 
        sample_query_request, 
        mock_customer_agent,
        db_session,
        test_user,
        test_tenant
    ):
        """Test successful transaction query processing."""
        # Mock the customer agent initialization and tool execution
        with patch('giki_ai_api.domains.intelligence.customer_agent.CustomerFacingAgent') as mock_agent_class:
            with patch('giki_ai_api.domains.intelligence.customer_agent.CustomerFacingAgentConfig') as mock_config_class:
                with patch('giki_ai_api.domains.intelligence.customer_agent.query_customer_transactions_tool_function') as mock_tool:
                    
                    # Setup mocks
                    mock_config = Mock()
                    mock_config_class.return_value = mock_config
                    mock_agent_class.return_value = mock_customer_agent
                    
                    mock_tool.return_value = {
                        "success": True,
                        "transactions": [
                            {
                                "id": "txn_001",
                                "description": "Test transaction",
                                "amount": -50.00,
                                "date": "2024-06-15",
                                "category": "Office Supplies"
                            }
                        ],
                        "total_count": 1,
                        "real_data": True
                    }
                    
                    # Set up query request for transactions
                    sample_query_request.query_type = "transactions"
                    
                    # Execute the function
                    result = await process_customer_query(
                        request=sample_query_request,
                        current_user=test_user,
                        tenant_id=test_tenant.id,
                        db=db_session
                    )
                    
                    # Verify response structure matches AgentApiResponse
                    assert result.success is True
                    assert result.agent_type == "customer"
                    assert "transactions" in result.result
                    assert len(result.result["transactions"]) == 1
                    assert result.result["transactions"][0]["id"] == "txn_001"
                    
                    # Verify customer agent was initialized correctly
                    mock_config_class.assert_called_once_with(
                        model_name="gemini-2.0-flash-001",
                        enable_audio=True
                    )
                    mock_agent_class.assert_called_once_with(config=mock_config, db=db_session)
                    
                    # Verify tool was called with correct parameters
                    mock_tool.assert_called_once_with(
                        tenant_id=test_tenant.id,
                        query_params=sample_query_request.parameters,
                        db=db_session
                    )
    
    async def test_process_customer_query_accuracy_metrics(
        self,
        sample_query_request,
        mock_customer_agent,
        db_session,
        test_user,
        test_tenant
    ):
        """Test accuracy metrics query processing."""
        with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgent') as mock_agent_class:
            with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgentConfig') as mock_config_class:
                with patch('giki_ai_api.domains.intelligence.router.get_accuracy_metrics_tool_function') as mock_tool:
                    
                    # Setup mocks
                    mock_config = Mock()
                    mock_config_class.return_value = mock_config
                    mock_agent_class.return_value = mock_customer_agent
                    
                    mock_tool.return_value = {
                        "success": True,
                        "metrics": {
                            "precision": 0.87,
                            "recall": 0.83,
                            "f1_score": 0.85,
                            "average_confidence": 0.89
                        },
                        "transaction_count": 150,
                        "real_data": True
                    }
                    
                    # Set up query request for accuracy
                    sample_query_request.query_type = "accuracy"
                    sample_query_request.parameters = {"period": "June_2024"}
                    
                    # Execute the function
                    result = await process_customer_query(
                        request=sample_query_request,
                        current_user=test_user,
                        tenant_id=test_tenant.id,
                        db=db_session
                    )
                    
                    # Verify response structure matches AgentApiResponse
                    assert result.success is True
                    assert result.agent_type == "customer"
                    assert "metrics" in result.result
                    assert result.result["metrics"]["precision"] == 0.87
                    assert result.result["transaction_count"] == 150
                    
                    # Verify tool was called with correct period
                    mock_tool.assert_called_once_with(
                        tenant_id=test_tenant.id,
                        db=db_session,
                        period="June_2024"
                    )
    
    async def test_process_customer_query_insights_generation(
        self,
        sample_query_request,
        mock_customer_agent,
        db_session,
        test_user,
        test_tenant
    ):
        """Test insights generation query processing."""
        with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgent') as mock_agent_class:
            with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgentConfig') as mock_config_class:
                with patch('giki_ai_api.domains.intelligence.router.generate_insights_tool_function') as mock_tool:
                    
                    # Setup mocks
                    mock_config = Mock()
                    mock_config_class.return_value = mock_config
                    mock_agent_class.return_value = mock_customer_agent
                    
                    mock_tool.return_value = {
                        "success": True,
                        "insights": {
                            "ai_analysis": "Spending patterns show increased travel expenses",
                            "key_findings": ["Travel up 25%", "Office supplies down 10%"],
                            "recommendations": ["Consider travel budget review"]
                        },
                        "data_points": 200,
                        "real_data": True
                    }
                    
                    # Set up query request for insights
                    sample_query_request.query_type = "insights"
                    sample_query_request.parameters = {"insight_type": "spending"}
                    
                    # Execute the function
                    result = await process_customer_query(
                        request=sample_query_request,
                        current_user=test_user,
                        tenant_id=test_tenant.id,
                        db=db_session
                    )
                    
                    # Verify response structure matches AgentApiResponse
                    assert result.success is True
                    assert result.agent_type == "customer"
                    assert "insights" in result.result
                    assert "Travel up 25%" in result.result["insights"]["key_findings"]
                    
                    # Verify tool was called with correct insight type
                    mock_tool.assert_called_once_with(
                        tenant_id=test_tenant.id,
                        db=db_session,
                        insight_type="spending"
                    )
    
    async def test_process_customer_query_search_functionality(
        self,
        sample_query_request,
        mock_customer_agent,
        db_session,
        test_user,
        test_tenant
    ):
        """Test search query processing."""
        with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgent') as mock_agent_class:
            with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgentConfig') as mock_config_class:
                with patch('giki_ai_api.domains.intelligence.router.search_customer_data_tool_function') as mock_tool:
                    
                    # Setup mocks
                    mock_config = Mock()
                    mock_config_class.return_value = mock_config
                    mock_agent_class.return_value = mock_customer_agent
                    
                    mock_tool.return_value = {
                        "success": True,
                        "results": {
                            "transactions": [
                                {"id": "txn_search_1", "description": "Starbucks Coffee", "amount": -5.50}
                            ],
                            "categories": [
                                {"id": "cat_1", "name": "Food & Beverage"}
                            ],
                            "vendors": [
                                {"id": "vendor_1", "name": "Starbucks"}
                            ]
                        },
                        "total_results": 3,
                        "real_data": True
                    }
                    
                    # Set up query request for search
                    sample_query_request.query_type = "search"
                    sample_query_request.parameters = {
                        "search_query": "Starbucks",
                        "data_type": "all"
                    }
                    
                    # Execute the function
                    result = await process_customer_query(
                        request=sample_query_request,
                        current_user=test_user,
                        tenant_id=test_tenant.id,
                        db=db_session
                    )
                    
                    # Verify response structure matches AgentApiResponse
                    assert result.success is True
                    assert result.agent_type == "customer"
                    assert "results" in result.result
                    assert result.result["total_results"] == 3
                    
                    # Verify tool was called with correct search parameters
                    mock_tool.assert_called_once_with(
                        tenant_id=test_tenant.id,
                        db=db_session,
                        search_query="Starbucks",
                        data_type="all"
                    )
    
    async def test_process_customer_query_error_handling(
        self,
        sample_query_request,
        db_session,
        test_user,
        test_tenant
    ):
        """Test error handling in customer query processing."""
        with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgent') as mock_agent_class:
            with patch('giki_ai_api.domains.intelligence.router.CustomerFacingAgentConfig') as mock_config_class:
                with patch('giki_ai_api.domains.intelligence.router.query_customer_transactions_tool_function'):
                    
                    # Setup mocks to raise exception
                    mock_config = Mock()
                    mock_config_class.return_value = mock_config
                    mock_agent_class.side_effect = Exception("Agent initialization failed")
                    
                    # Set up query request
                    sample_query_request.query_type = "transactions"
                    
                    # Execute the function and expect exception handling
                    result = await process_customer_query(
                        request=sample_query_request,
                        current_user=test_user,
                        tenant_id=test_tenant.id,
                        db=db_session
                    )
                    
                    # Should return error response
                    assert result.success is False
                    assert "error" in result.result
                    assert "Agent initialization failed" in result.result["error"]


# Audio processing tests will be added when audio endpoints are implemented


@pytest.mark.integration
class TestIntelligenceRouterIntegration:
    """Integration tests for the intelligence router with real components."""
    
    async def test_customer_agent_integration_with_real_components(
        self,
        db_session,
        test_user,
        test_tenant,
        test_transactions
    ):
        """Test customer agent integration with real database components."""
        # This test uses real database fixtures but mocks AI components
        # Await the test_transactions fixture to get actual data
        transactions_data = await test_transactions if asyncio.iscoroutine(test_transactions) else test_transactions
        
        with patch('giki_ai_api.domains.intelligence.customer_agent.query_customer_transactions_tool_function') as mock_tool:
            # Use real transaction data from fixtures
            mock_tool.return_value = {
                "success": True,
                "transactions": [
                    {
                        "id": str(txn.id),
                        "description": txn.description,
                        "amount": float(txn.amount),
                        "date": txn.date.isoformat(),
                        "category": "Test Category"
                    } for txn in transactions_data
                ],
                "total_count": len(transactions_data),
                "real_data": True
            }
            
            # Create query request
            query_request = CustomerQueryRequest(
                query="Show all my transactions",
                query_type="transactions",
                parameters={"limit": 100}
            )
            
            # Process query
            result = await process_customer_query(
                request=query_request,
                current_user=test_user,
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            # Verify real data integration
            assert result.success is True
            assert len(result.result["transactions"]) == len(transactions_data)
            assert all("id" in txn for txn in result.result["transactions"])
            
            # Verify tool was called with correct tenant isolation
            mock_tool.assert_called_once_with(
                tenant_id=test_tenant.id,
                query_params={"limit": 100},
                db=db_session
            )
    
    # End-to-end audio workflow tests will be added when audio endpoints are implemented