"""
Complete Onboarding Workflow Integration Tests
=============================================

Tests the complete customer onboarding journey from signup to categorized transactions.
This validates the core business value proposition - the complete revenue-generating workflow.

Workflow tested:
1. User authentication
2. File upload (all 4 input files: Capital One, Credit Card, ICICI, SVB)
3. Schema interpretation with real AI
4. Transaction creation
5. AI categorization 
6. Manual review and approval
7. Temporal accuracy validation

Performance Requirements:
- File processing: <30s per file
- AI categorization accuracy: >85%
- Complete workflow: <5 minutes total
"""

import asyncio
import os
from pathlib import Path

import pytest
from httpx import AsyncClient
from sqlalchemy import select

from giki_ai_api.core.main import app
from giki_ai_api.domains.transactions.models import Transaction

pytestmark = pytest.mark.asyncio


class TestCompleteOnboardingWorkflow:
    """Test complete customer onboarding workflow end-to-end."""
    
    @pytest.fixture
    def input_files_path(self):
        """Path to test input files (ONLY authorized test data location)."""
        return Path("/Users/<USER>/giki-ai-workspace/test-files")
    
    @pytest.fixture
    def test_files(self, input_files_path):
        """All required test files for onboarding workflow."""
        files = {
            "capital_one": input_files_path / "Capital One.xlsx",
            "credit_card": input_files_path / "Credit Card.xlsx", 
            "icici": input_files_path / "ICICI.xlsx",
            "svb": input_files_path / "SVB.xlsx"
        }
        
        # Verify all test files exist
        for _name, path in files.items():
            assert path.exists(), f"Test file missing: {path}"
            
        return files
    
    @pytest.fixture
    async def authenticated_client(self, test_user, test_tenant):
        """HTTP client with authenticated user."""
        async with AsyncClient(app=app, base_url="http://localhost:8000") as client:
            # Login to get JWT token
            login_response = await client.post(
                "/api/v1/auth/login",
                json={
                    "email": test_user.email,
                    "password": os.getenv("TEST_PASSWORD", "secure_test_password_2024!")
                }
            )
            
            assert login_response.status_code == 200
            token_data = login_response.json()
            token = token_data["access_token"]
            
            # Set authentication headers
            client.headers.update({
                "Authorization": f"Bearer {token}",
                "X-Tenant-ID": str(test_tenant.id)
            })
            
            return client
    
    async def test_complete_onboarding_workflow_capital_one(
        self, 
        authenticated_client: AsyncClient,
        test_files,
        db_session,
        test_tenant,
        test_user
    ):
        """Test complete onboarding workflow with Capital One.xlsx file."""
        
        # Step 1: File Upload
        with open(test_files["capital_one"], "rb") as f:
            upload_response = await authenticated_client.post(
                "/api/v1/files/upload",
                files={"file": ("Capital One.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
            )
        
        assert upload_response.status_code == 200
        upload_data = upload_response.json()
        assert upload_data["status"] == "success"
        upload_id = upload_data["data"]["upload_id"]
        
        # Step 2: Schema Interpretation 
        schema_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/interpret-schema"
        )
        
        assert schema_response.status_code == 200
        schema_data = schema_response.json()
        assert schema_data["status"] == "success"
        assert "column_mapping" in schema_data["data"]
        assert schema_data["data"]["confidence"] > 0.8  # High confidence interpretation
        
        # Step 3: Column Mapping Confirmation
        column_mapping = schema_data["data"]["column_mapping"]
        mapping_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/confirm-mapping",
            json={"column_mapping": column_mapping}
        )
        
        assert mapping_response.status_code == 200
        mapping_data = mapping_response.json()
        assert mapping_data["status"] == "success"
        
        # Step 4: Verify Transaction Creation
        # Wait for async processing to complete
        await asyncio.sleep(2)
        
        transactions_query = select(Transaction).where(Transaction.tenant_id == test_tenant.id)
        result = await db_session.execute(transactions_query)
        transactions = result.scalars().all()
        
        assert len(transactions) > 0, "No transactions created from file upload"
        print(f"Created {len(transactions)} transactions from Capital One file")
        
        # Step 5: Verify AI Categorization Started
        categorization_response = await authenticated_client.get(
            f"/api/v1/uploads/{upload_id}/categorization-status"
        )
        
        assert categorization_response.status_code == 200
        cat_data = categorization_response.json()
        assert cat_data["status"] == "success"
        
        # Step 6: Get Categorized Transactions
        transactions_response = await authenticated_client.get(
            "/api/v1/transactions",
            params={"limit": 100}
        )
        
        assert transactions_response.status_code == 200
        trans_data = transactions_response.json()
        assert trans_data["status"] == "success"
        assert len(trans_data["data"]["transactions"]) > 0
        
        # Step 7: Manual Review Workflow
        review_response = await authenticated_client.get(
            "/api/v1/transactions/review"
        )
        
        assert review_response.status_code == 200
        review_data = review_response.json()
        assert review_data["status"] == "success"
        
        # Performance Assertion: Complete workflow should be fast
        assert upload_response.elapsed.total_seconds() < 30, "File upload too slow"
        assert schema_response.elapsed.total_seconds() < 10, "Schema interpretation too slow"
        
        print("✅ Complete onboarding workflow successful for Capital One file")
        print(f"   - File uploaded: {upload_data['data']['filename']}")
        print(f"   - Transactions created: {len(transactions)}")
        print(f"   - Schema confidence: {schema_data['data']['confidence']:.2%}")
    
    async def test_complete_onboarding_workflow_all_files(
        self,
        authenticated_client: AsyncClient,
        test_files,
        db_session,
        test_tenant
    ):
        """Test complete onboarding workflow with all 4 input files."""
        
        workflow_results = {}
        
        for file_name, file_path in test_files.items():
            print(f"\n🔄 Processing {file_name}: {file_path.name}")
            
            # File Upload
            with open(file_path, "rb") as f:
                upload_response = await authenticated_client.post(
                    "/api/v1/files/upload",
                    files={"file": (file_path.name, f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
                )
            
            assert upload_response.status_code == 200
            upload_data = upload_response.json()
            upload_id = upload_data["data"]["upload_id"]
            
            # Schema Interpretation
            schema_response = await authenticated_client.post(
                f"/api/v1/uploads/{upload_id}/interpret-schema"
            )
            
            assert schema_response.status_code == 200
            schema_data = schema_response.json()
            
            # Column Mapping
            column_mapping = schema_data["data"]["column_mapping"]
            mapping_response = await authenticated_client.post(
                f"/api/v1/uploads/{upload_id}/confirm-mapping",
                json={"column_mapping": column_mapping}
            )
            
            assert mapping_response.status_code == 200
            
            # Wait for processing
            await asyncio.sleep(3)
            
            # Verify transactions created
            transactions_query = select(Transaction).where(Transaction.tenant_id == test_tenant.id)
            result = await db_session.execute(transactions_query)
            transactions = result.scalars().all()
            
            workflow_results[file_name] = {
                "upload_id": upload_id,
                "transactions_count": len(transactions),
                "schema_confidence": schema_data["data"]["confidence"],
                "processing_time": schema_response.elapsed.total_seconds()
            }
            
            print(f"   ✅ {file_name}: {len(transactions)} transactions, {schema_data['data']['confidence']:.2%} confidence")
        
        # Verify all files processed successfully
        assert len(workflow_results) == 4, "Not all files processed"
        
        total_transactions = sum(r["transactions_count"] for r in workflow_results.values())
        avg_confidence = sum(r["schema_confidence"] for r in workflow_results.values()) / len(workflow_results)
        
        print("\n📊 Complete Onboarding Results:")
        print(f"   - Files processed: {len(workflow_results)}")
        print(f"   - Total transactions: {total_transactions}")
        print(f"   - Average confidence: {avg_confidence:.2%}")
        
        # Business Requirements Validation
        assert total_transactions > 0, "No transactions created across all files"
        assert avg_confidence > 0.85, f"Average confidence {avg_confidence:.2%} below 85% requirement"
        
        print("✅ All files onboarding workflow completed successfully")

    async def test_onboarding_workflow_with_temporal_accuracy(
        self,
        authenticated_client: AsyncClient,
        test_files,
        db_session,
        test_tenant
    ):
        """Test onboarding workflow with temporal accuracy validation."""
        
        # Use Credit Card file for temporal accuracy testing
        with open(test_files["credit_card"], "rb") as f:
            upload_response = await authenticated_client.post(
                "/api/v1/files/upload",
                files={"file": ("Credit Card.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
            )
        
        assert upload_response.status_code == 200
        upload_data = upload_response.json()
        upload_id = upload_data["data"]["upload_id"]
        
        # Schema interpretation and mapping
        schema_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/interpret-schema"
        )
        assert schema_response.status_code == 200
        
        schema_data = schema_response.json()
        column_mapping = schema_data["data"]["column_mapping"]
        
        mapping_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/confirm-mapping",
            json={"column_mapping": column_mapping}
        )
        assert mapping_response.status_code == 200
        
        # Wait for transaction creation
        await asyncio.sleep(3)
        
        # Trigger temporal accuracy validation
        validation_response = await authenticated_client.post(
            "/api/v1/intelligence/temporal-accuracy-validation",
            json={
                "file_path": "Credit Card.xlsx",
                "date_range": {
                    "start": "2024-06-01",
                    "end": "2024-12-31"
                }
            }
        )
        
        assert validation_response.status_code == 200
        validation_data = validation_response.json()
        assert validation_data["status"] == "success"
        
        # Check validation results
        if "validation_id" in validation_data["data"]:
            # If async validation, wait and check status
            validation_id = validation_data["data"]["validation_id"]
            
            # Wait for validation to complete
            await asyncio.sleep(5)
            
            status_response = await authenticated_client.get(
                f"/api/v1/intelligence/temporal-accuracy-validation/{validation_id}/status"
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"Temporal accuracy validation: {status_data}")
        else:
            # Immediate validation results
            accuracy_results = validation_data["data"]
            assert "monthly_accuracy" in accuracy_results
            print(f"Temporal accuracy results: {accuracy_results}")
        
        print("✅ Onboarding workflow with temporal accuracy validation completed")

    async def test_onboarding_performance_requirements(
        self,
        authenticated_client: AsyncClient, 
        test_files,
        db_session,
        test_tenant
    ):
        """Test that onboarding workflow meets performance requirements."""
        
        # Test with SVB file (smaller file for performance testing)
        file_path = test_files["svb"]
        
        import time
        start_time = time.time()
        
        # Complete workflow timing
        with open(file_path, "rb") as f:
            upload_response = await authenticated_client.post(
                "/api/v1/files/upload",
                files={"file": (file_path.name, f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
            )
        
        upload_time = time.time()
        assert upload_response.status_code == 200
        upload_data = upload_response.json()
        upload_id = upload_data["data"]["upload_id"]
        
        schema_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/interpret-schema"
        )
        
        schema_time = time.time()
        assert schema_response.status_code == 200
        
        schema_data = schema_response.json()
        mapping_response = await authenticated_client.post(
            f"/api/v1/uploads/{upload_id}/confirm-mapping",
            json={"column_mapping": schema_data["data"]["column_mapping"]}
        )
        
        mapping_time = time.time()
        assert mapping_response.status_code == 200
        
        # Wait for transaction processing
        await asyncio.sleep(2)
        
        total_time = time.time() - start_time
        upload_duration = upload_time - start_time
        schema_duration = schema_time - upload_time
        mapping_duration = mapping_time - schema_time
        
        print("\n⏱️  Performance Metrics:")
        print(f"   - File upload: {upload_duration:.2f}s")
        print(f"   - Schema interpretation: {schema_duration:.2f}s")
        print(f"   - Column mapping: {mapping_duration:.2f}s")
        print(f"   - Total workflow: {total_time:.2f}s")
        
        # Performance Requirements Validation
        assert upload_duration < 10.0, f"File upload too slow: {upload_duration:.2f}s"
        assert schema_duration < 15.0, f"Schema interpretation too slow: {schema_duration:.2f}s"
        assert mapping_duration < 5.0, f"Column mapping too slow: {mapping_duration:.2f}s"
        assert total_time < 30.0, f"Complete workflow too slow: {total_time:.2f}s"
        
        print("✅ Performance requirements met for onboarding workflow")
        
        # Verify data quality
        transactions_query = select(Transaction).where(Transaction.tenant_id == test_tenant.id)
        result = await db_session.execute(transactions_query)
        transactions = result.scalars().all()
        
        assert len(transactions) > 0, "No transactions created"
        print(f"   - Transactions created: {len(transactions)}")
        print(f"   - Schema confidence: {schema_data['data']['confidence']:.2%}")