"""
Google Cloud Logging Integration Tests
====================================

Tests for Google Cloud Logging integration including:
- Structured logging setup and configuration
- Performance, security, business, and AI event logging
- Fallback to standard Python logging
- Log level handling and formatting
"""

import logging
import os
from unittest.mock import Mock, patch

import pytest

from giki_ai_api.shared.services.google_cloud_logging import (
    GoogleCloudLoggingSetup,
    get_logging_setup,
    log_ai_operation,
    log_business,
    log_performance,
    log_security,
    setup_google_cloud_logging,
)


class TestGoogleCloudLoggingSetup:
    """Test Google Cloud Logging integration setup."""
    
    @pytest.fixture
    def mock_cloud_logging_client(self):
        """Mock Google Cloud Logging client."""
        mock_client = Mock()
        mock_client.setup_logging = Mock()
        return mock_client
    
    @pytest.fixture
    def mock_cloud_logging_handler(self):
        """Mock Google Cloud Logging handler."""
        mock_handler = Mock()
        return mock_handler
    
    def test_logging_setup_initialization_without_google_cloud(self):
        """Test logging setup initializes correctly when Google Cloud SDK not available."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
            logging_setup = GoogleCloudLoggingSetup()
            assert logging_setup.cloud_client is None
            assert logging_setup.is_setup_complete() is True
            assert logging_setup.project_id == "rezolve-poc"
            assert logging_setup.service_name == "giki-ai-api"
    
    def test_logging_setup_initialization_with_google_cloud_error(self):
        """Test logging setup handles Google Cloud initialization errors gracefully."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.cloud_logging') as mock_logging:
            mock_logging.Client.side_effect = Exception("Authentication failed")
            
            logging_setup = GoogleCloudLoggingSetup()
            assert logging_setup.cloud_client is None
            assert logging_setup.is_setup_complete() is True
    
    def test_logging_setup_with_google_cloud_success(self, mock_cloud_logging_client):
        """Test successful Google Cloud Logging setup."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.cloud_logging') as mock_logging:
            with patch('giki_ai_api.shared.services.google_cloud_logging.CloudLoggingHandler') as mock_handler_class:
                mock_logging.Client.return_value = mock_cloud_logging_client
                mock_handler = Mock()
                mock_handler_class.return_value = mock_handler
                
                logging_setup = GoogleCloudLoggingSetup(project_id="test-project", service_name="test-service")
                
                assert logging_setup.cloud_client == mock_cloud_logging_client
                assert logging_setup.project_id == "test-project"
                assert logging_setup.service_name == "test-service"
                assert logging_setup.is_setup_complete() is True
                
                # Verify setup_logging was called
                mock_cloud_logging_client.setup_logging.assert_called_once()
    
    def test_standard_logging_fallback(self):
        """Test fallback to standard Python logging."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
            # Clear any existing handlers
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            logging_setup = GoogleCloudLoggingSetup()
            
            assert logging_setup.cloud_client is None
            assert logging_setup.is_setup_complete() is True
            # Should have added console handler
            assert len(root_logger.handlers) >= 1
    
    def test_project_id_from_environment(self):
        """Test that project ID is correctly read from environment."""
        test_project_id = "test-env-project-456"
        
        with patch.dict(os.environ, {"VERTEX_PROJECT_ID": test_project_id}):
            with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
                logging_setup = GoogleCloudLoggingSetup()
                assert logging_setup.project_id == test_project_id
    
    def test_debug_mode_console_handler(self):
        """Test that debug mode adds console handler even with Google Cloud."""
        with patch.dict(os.environ, {"DEBUG": "true"}):
            with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
                GoogleCloudLoggingSetup()
                
                # Should have console handler for debug mode
                app_logger = logging.getLogger("giki_ai_api")
                assert len(app_logger.handlers) >= 1


class TestStructuredLogging:
    """Test structured logging functionality."""
    
    @pytest.fixture
    def logging_setup(self):
        """Create logging setup for testing."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
            return GoogleCloudLoggingSetup(project_id="test-project", service_name="test-service")
    
    def test_log_performance_metric_success(self, logging_setup):
        """Test logging performance metrics for successful operations."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            logging_setup.log_performance_metric(
                operation="api_request",
                duration_ms=1500.5,  # > 1000ms to trigger INFO level
                status="success",
                metadata={"endpoint": "/api/v1/test", "user_id": 123}
            )
            
            # Should log at INFO level for normal performance
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            assert "api_request" in call_args[0][0]
            
            # Check extra data structure
            extra_data = call_args[1]['extra']
            assert extra_data['metric_type'] == 'performance'
            assert extra_data['operation'] == 'api_request'
            assert extra_data['duration_ms'] == 1500.5
            assert extra_data['status'] == 'success'
            assert extra_data['endpoint'] == '/api/v1/test'
            assert extra_data['user_id'] == 123
    
    def test_log_performance_metric_slow_operation(self, logging_setup):
        """Test logging performance metrics for slow operations."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            logging_setup.log_performance_metric(
                operation="database_query",
                duration_ms=6000.0,  # 6 seconds - very slow
                status="success"
            )
            
            # Should log at WARNING level for very slow operations
            mock_logger.warning.assert_called_once()
            call_args = mock_logger.warning.call_args
            assert "SLOW_OPERATION" in call_args[0][0]
            assert "database_query" in call_args[0][0]
    
    def test_log_security_event_critical(self, logging_setup):
        """Test logging critical security events."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            logging_setup.log_security_event(
                event_type="unauthorized_access",
                severity="critical",
                details={
                    "ip_address": "***********00",
                    "user_agent": "malicious_bot",
                    "attempted_endpoint": "/admin"
                }
            )
            
            # Should log at CRITICAL level
            mock_logger.critical.assert_called_once()
            call_args = mock_logger.critical.call_args
            assert "SECURITY_CRITICAL" in call_args[0][0]
            assert "unauthorized_access" in call_args[0][0]
            
            # Check security event structure
            extra_data = call_args[1]['extra']
            assert extra_data['event_type'] == 'security'
            assert extra_data['security_event'] == 'unauthorized_access'
            assert extra_data['severity'] == 'critical'
            assert extra_data['ip_address'] == '***********00'
    
    def test_log_security_event_levels(self, logging_setup):
        """Test different security event severity levels."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            # Test each severity level
            test_cases = [
                ("critical", "critical", "SECURITY_CRITICAL"),
                ("high", "error", "SECURITY_HIGH"),
                ("medium", "warning", "SECURITY_MEDIUM"),
                ("low", "info", "SECURITY_LOW")
            ]
            
            for severity, expected_method, expected_prefix in test_cases:
                mock_logger.reset_mock()
                
                logging_setup.log_security_event(
                    event_type="test_event",
                    severity=severity,
                    details={"test": "data"}
                )
                
                # Check correct logging method was called
                getattr(mock_logger, expected_method).assert_called_once()
                call_args = getattr(mock_logger, expected_method).call_args
                assert expected_prefix in call_args[0][0]
    
    def test_log_business_event(self, logging_setup):
        """Test logging business events."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            logging_setup.log_business_event(
                event_type="file_uploaded",
                tenant_id=123,
                user_id=456,
                metadata={
                    "file_name": "transactions.xlsx",
                    "file_size": 1024000,
                    "processing_time": 2.5
                }
            )
            
            # Should log at INFO level
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            assert "BUSINESS_EVENT" in call_args[0][0]
            assert "file_uploaded" in call_args[0][0]
            
            # Check business event structure
            extra_data = call_args[1]['extra']
            assert extra_data['event_type'] == 'business'
            assert extra_data['business_event'] == 'file_uploaded'
            assert extra_data['tenant_id'] == 123
            assert extra_data['user_id'] == 456
            assert extra_data['file_name'] == 'transactions.xlsx'
    
    def test_log_ai_event(self, logging_setup):
        """Test logging AI/ML operation events."""
        with patch('logging.getLogger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            logging_setup.log_ai_event(
                operation="transaction_categorization",
                model_used="gemini-2.0-flash",
                confidence_score=0.87,
                processing_time_ms=245.0,
                metadata={
                    "transaction_count": 50,
                    "categories_assigned": 12,
                    "batch_id": "batch_123"
                }
            )
            
            # Should log at INFO level
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            assert "AI_OPERATION" in call_args[0][0]
            assert "transaction_categorization" in call_args[0][0]
            
            # Check AI event structure
            extra_data = call_args[1]['extra']
            assert extra_data['event_type'] == 'ai_operation'
            assert extra_data['ai_operation'] == 'transaction_categorization'
            assert extra_data['model_used'] == 'gemini-2.0-flash'
            assert extra_data['confidence_score'] == 0.87
            assert extra_data['processing_time_ms'] == 245.0
            assert extra_data['transaction_count'] == 50


class TestGlobalLoggingFunctions:
    """Test global convenience functions."""
    
    @pytest.fixture
    def mock_logging_setup(self):
        """Mock logging setup instance."""
        mock_setup = Mock()
        return mock_setup
    
    def test_setup_google_cloud_logging_singleton(self):
        """Test that setup_google_cloud_logging returns singleton instance."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.GoogleCloudLoggingSetup') as mock_class:
            mock_instance = Mock()
            mock_class.return_value = mock_instance
            
            setup1 = setup_google_cloud_logging()
            setup_google_cloud_logging()
            
            # Should create instance only once
            assert mock_class.call_count == 1
            assert setup1 == mock_instance
    
    def test_get_logging_setup(self):
        """Test get_logging_setup returns current instance."""
        # Reset global state
        import giki_ai_api.shared.services.google_cloud_logging as logging_module
        logging_module._logging_setup = None
        
        # Should return None when not initialized
        assert get_logging_setup() is None
        
        # Initialize and check
        with patch('giki_ai_api.shared.services.google_cloud_logging.GoogleCloudLoggingSetup') as mock_class:
            mock_instance = Mock()
            mock_class.return_value = mock_instance
            
            setup_google_cloud_logging()
            assert get_logging_setup() == mock_instance
    
    def test_log_performance_convenience_function(self, mock_logging_setup):
        """Test convenience function for logging performance."""
        with patch('giki_ai_api.shared.services.google_cloud_logging._logging_setup', mock_logging_setup):
            log_performance("test_operation", 150.0, "success", test_data="test")
            
            mock_logging_setup.log_performance_metric.assert_called_once_with(
                "test_operation", 150.0, "success", {"test_data": "test"}
            )
    
    def test_log_security_convenience_function(self, mock_logging_setup):
        """Test convenience function for logging security events."""
        with patch('giki_ai_api.shared.services.google_cloud_logging._logging_setup', mock_logging_setup):
            log_security("auth_failure", "high", ip="***********", user="test")
            
            mock_logging_setup.log_security_event.assert_called_once_with(
                "auth_failure", "high", {"ip": "***********", "user": "test"}
            )
    
    def test_log_business_convenience_function(self, mock_logging_setup):
        """Test convenience function for logging business events."""
        with patch('giki_ai_api.shared.services.google_cloud_logging._logging_setup', mock_logging_setup):
            log_business("user_signup", tenant_id=123, user_id=456, plan="premium")
            
            mock_logging_setup.log_business_event.assert_called_once_with(
                "user_signup", 123, 456, {"plan": "premium"}
            )
    
    def test_log_ai_operation_convenience_function(self, mock_logging_setup):
        """Test convenience function for logging AI operations."""
        with patch('giki_ai_api.shared.services.google_cloud_logging._logging_setup', mock_logging_setup):
            log_ai_operation("categorization", "gemini-2.0", confidence_score=0.9, batch_size=100)
            
            mock_logging_setup.log_ai_event.assert_called_once_with(
                "categorization", "gemini-2.0", 0.9, None, {"batch_size": 100}
            )
    
    def test_convenience_functions_without_setup(self):
        """Test convenience functions handle missing setup gracefully."""
        with patch('giki_ai_api.shared.services.google_cloud_logging._logging_setup', None):
            # These should not raise exceptions
            log_performance("test", 100.0)
            log_security("test", "low")
            log_business("test")
            log_ai_operation("test", "model")


@pytest.mark.integration
class TestGoogleCloudLoggingIntegration:
    """Integration tests that may connect to actual Google Cloud Logging."""
    
    @pytest.mark.requires_ai
    def test_real_google_cloud_logging_if_available(self):
        """Test real Google Cloud Logging connection if credentials are available."""
        try:
            logging_setup = GoogleCloudLoggingSetup()
            if logging_setup.cloud_client is not None:
                # Test actual logging - this should work if credentials are available
                logging_setup.log_performance_metric("integration_test", 100.0, "success")
                logging_setup.log_business_event("test_event", metadata={"test": True})
                # The test passes if no exceptions are thrown
                assert logging_setup.is_setup_complete() is True
            else:
                pytest.skip("Google Cloud Logging not available (credentials not configured)")
        except Exception as e:
            # If there's an authentication error, that's expected in test environments
            if "authentication" in str(e).lower() or "credential" in str(e).lower():
                pytest.skip(f"Google Cloud authentication not available: {e}")
            else:
                raise
    
    def test_structured_logging_format_integration(self):
        """Test that structured logging produces correctly formatted output."""
        # Use standard logging fallback for predictable testing
        with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
            logging_setup = GoogleCloudLoggingSetup()
            
            # Capture log output
            with patch('logging.StreamHandler.emit') as mock_emit:
                logging_setup.log_performance_metric("test_operation", 200.0, "success")
                
                # Should have emitted a log record
                assert mock_emit.called
                log_record = mock_emit.call_args[0][0]
                
                # Check that extra data is preserved
                assert hasattr(log_record, 'metric_type')
                assert log_record.metric_type == 'performance'
                assert hasattr(log_record, 'operation')
                assert log_record.operation == 'test_operation'
    
    def test_logging_level_configuration(self):
        """Test that logging levels are correctly configured."""
        with patch('giki_ai_api.shared.services.google_cloud_logging.GOOGLE_CLOUD_LOGGING_AVAILABLE', False):
            GoogleCloudLoggingSetup()
            
            # Check that root logger is configured
            root_logger = logging.getLogger()
            assert root_logger.level == logging.INFO
            
            # Check that application logger is configured
            app_logger = logging.getLogger("giki_ai_api")
            assert app_logger.level == logging.INFO