#!/usr/bin/env python3
"""
Test dashboard API endpoints.
"""

import asyncio

import httpx

API_BASE = "http://localhost:8000"


async def test_dashboard_api():
    """Test the new dashboard endpoints."""
    
    async with httpx.AsyncClient() as client:
        # Login first
        login_response = await client.post(
            f"{API_BASE}/api/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "password123",
                "grant_type": "password"
            },
            headers={
                "Content-Type": "application/x-www-form-urlencoded"
            }
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return
            
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Logged in successfully")
        
        # Test dashboard metrics
        print("\n📊 Testing Dashboard Metrics...")
        metrics_response = await client.get(
            f"{API_BASE}/api/v1/dashboard/metrics",
            headers=headers
        )
        
        if metrics_response.status_code == 200:
            metrics = metrics_response.json()
            print("✅ Dashboard metrics loaded:")
            print(f"  - Total Transactions: {metrics.get('total_transactions', 0)}")
            print(f"  - Total Income: ${metrics.get('total_income', 0):,.2f}")
            print(f"  - Total Expenses: ${metrics.get('total_expenses', 0):,.2f}")
            print(f"  - Net Income: ${metrics.get('net_income', 0):,.2f}")
            print(f"  - Categorized: {metrics.get('categorized_transactions', 0)}")
            print(f"  - Uncategorized: {metrics.get('uncategorized_transactions', 0)}")
            print(f"  - Categorization Rate: {metrics.get('categorization_rate', 0):.1f}%")
        else:
            print(f"❌ Dashboard metrics failed: {metrics_response.status_code}")
            print(f"   Error: {metrics_response.text}")
            
        # Test recent transactions
        print("\n📄 Testing Recent Transactions...")
        recent_response = await client.get(
            f"{API_BASE}/api/v1/dashboard/recent-transactions",
            headers=headers
        )
        
        if recent_response.status_code == 200:
            recent = recent_response.json()
            print(f"✅ Recent transactions loaded: {recent.get('total', 0)} items")
            for tx in recent.get('items', [])[:3]:
                print(f"  - {tx['date']}: {tx['description'][:30]}... ${tx['amount']:.2f}")
        else:
            print(f"❌ Recent transactions failed: {recent_response.status_code}")
            
        # Test category breakdown
        print("\n📊 Testing Category Breakdown...")
        category_response = await client.get(
            f"{API_BASE}/api/v1/dashboard/category-breakdown",
            headers=headers
        )
        
        if category_response.status_code == 200:
            categories = category_response.json()
            print(f"✅ Category breakdown loaded: {categories.get('total_categories', 0)} categories")
            print(f"   Total spending: ${categories.get('total_spending', 0):,.2f}")
            for cat in categories.get('items', [])[:3]:
                print(f"  - {cat['category']}: ${cat['amount']:,.2f} ({cat['percentage']:.1f}%)")
        else:
            print(f"❌ Category breakdown failed: {category_response.status_code}")


if __name__ == "__main__":
    asyncio.run(test_dashboard_api())