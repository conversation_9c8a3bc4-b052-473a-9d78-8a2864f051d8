"""
Category Management Integration Tests
===================================

Comprehensive integration tests for category management including:
- CRUD operations with hierarchical structure
- GL code mapping and validation
- Multi-tenant isolation and security
- Category learning from onboarding data
- Performance validation (<200ms requirement)
- Financial-grade accuracy and reliability
- Bulk operations and export functionality
"""

import time
from datetime import date
from decimal import Decimal

import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.main import app
from giki_ai_api.domains.categories.models import Category
from giki_ai_api.domains.transactions.models import Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def second_tenant_auth_headers(client, second_tenant_user):
    """Get authentication headers for second tenant user."""
    response = client.post(
        "/auth/token",
        data={
            "username": second_tenant_user.email,
            "password": "SecondPassword123!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def hierarchical_categories(db_session: AsyncSession, test_tenant):
    """Create hierarchical category structure for testing."""
    # Root categories
    root_expense = Category(
        id=1001,
        name="Expenses",
        tenant_id=test_tenant.id,
        path="Expenses",
        level=0,
        gl_code="4000",
        gl_account_name="Operating Expenses",
        gl_account_type="Expense"
    )
    
    root_income = Category(
        id=1002,
        name="Income",
        tenant_id=test_tenant.id,
        path="Income",
        level=0,
        gl_code="3000",
        gl_account_name="Revenue",
        gl_account_type="Revenue"
    )
    
    # Level 1 categories
    office_expenses = Category(
        id=1003,
        name="Office Expenses",
        parent_id=1001,
        tenant_id=test_tenant.id,
        path="Expenses > Office Expenses",
        level=1,
        gl_code="4100",
        gl_account_name="Office Operating Expenses",
        gl_account_type="Expense"
    )
    
    travel_expenses = Category(
        id=1004,
        name="Travel",
        parent_id=1001,
        tenant_id=test_tenant.id,
        path="Expenses > Travel",
        level=1,
        gl_code="4200",
        gl_account_name="Travel Expenses",
        gl_account_type="Expense"
    )
    
    # Level 2 categories
    office_supplies = Category(
        id=1005,
        name="Office Supplies",
        parent_id=1003,
        tenant_id=test_tenant.id,
        path="Expenses > Office Expenses > Office Supplies",
        level=2,
        gl_code="4110",
        gl_account_name="Office Supplies and Materials",
        gl_account_type="Expense"
    )
    
    software_subscriptions = Category(
        id=1006,
        name="Software Subscriptions",
        parent_id=1003,
        tenant_id=test_tenant.id,
        path="Expenses > Office Expenses > Software Subscriptions",
        level=2,
        gl_code="4120",
        gl_account_name="Software and Licenses",
        gl_account_type="Expense"
    )
    
    categories = [
        root_expense, root_income, office_expenses, 
        travel_expenses, office_supplies, software_subscriptions
    ]
    
    for category in categories:
        db_session.add(category)
    
    await db_session.commit()
    
    for category in categories:
        await db_session.refresh(category)
    
    return categories


class TestCategoryCRUDOperations:
    """Test category CRUD operations with hierarchical structure."""
    
    async def test_create_root_category_success(self, client, auth_headers):
        """Test creating a root category."""
        category_data = {
            "name": "Marketing Expenses",
            "gl_code": "4300",
            "gl_account_name": "Marketing and Advertising",
            "gl_account_type": "Expense"
        }
        
        response = client.post(
            "/categories",
            headers=auth_headers,
            json=category_data
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        
        # Assert category data
        assert data["name"] == "Marketing Expenses"
        assert data["gl_code"] == "4300"
        assert data["gl_account_name"] == "Marketing and Advertising"
        assert data["gl_account_type"] == "Expense"
        assert data["parent_id"] is None
        assert data["level"] == 0
        assert data["path"] == "Marketing Expenses"
        
    async def test_create_child_category_success(self, client, auth_headers, hierarchical_categories):
        """Test creating a child category with proper hierarchy."""
        parent_category = hierarchical_categories[2]  # Office Expenses
        
        category_data = {
            "name": "Equipment",
            "parent_id": parent_category.id,
            "gl_code": "4130",
            "gl_account_name": "Office Equipment",
            "gl_account_type": "Expense"
        }
        
        response = client.post(
            "/categories",
            headers=auth_headers,
            json=category_data
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        
        # Assert hierarchy is correct
        assert data["name"] == "Equipment"
        assert data["parent_id"] == parent_category.id
        assert data["level"] == 2  # Should be one level deeper than parent
        assert "Office Expenses > Equipment" in data["path"]
        
    async def test_get_category_hierarchy_success(self, client, auth_headers, hierarchical_categories):
        """Test retrieving complete category hierarchy."""
        response = client.get(
            "/categories/hierarchy",
            headers=auth_headers,
            params={"include_usage_counts": True}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert hierarchy structure
        assert "root_categories" in data
        assert "total_categories" in data
        assert "max_depth" in data
        assert data["total_categories"] >= 6  # We created 6 categories
        assert data["max_depth"] >= 2  # We have 3 levels (0, 1, 2)
        
    async def test_get_single_category_with_children(self, client, auth_headers, hierarchical_categories):
        """Test retrieving single category with its children."""
        office_expenses = hierarchical_categories[2]  # Office Expenses
        
        response = client.get(
            f"/categories/{office_expenses.id}",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert category structure
        assert data["id"] == office_expenses.id
        assert data["name"] == "Office Expenses"
        assert "children" in data
        assert len(data["children"]) >= 2  # Should have Office Supplies and Software Subscriptions
        
    async def test_update_category_success(self, client, auth_headers, hierarchical_categories):
        """Test updating category information."""
        category = hierarchical_categories[4]  # Office Supplies
        
        update_data = {
            "name": "Office Supplies & Materials",
            "gl_code": "4115",
            "gl_account_name": "Office Supplies and Materials Updated"
        }
        
        response = client.put(
            f"/categories/{category.id}",
            headers=auth_headers,
            json=update_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert updates applied
        assert data["name"] == "Office Supplies & Materials"
        assert data["gl_code"] == "4115"
        assert data["gl_account_name"] == "Office Supplies and Materials Updated"
        
    async def test_delete_leaf_category_success(self, client, auth_headers, hierarchical_categories):
        """Test deleting a leaf category (no children)."""
        leaf_category = hierarchical_categories[5]  # Software Subscriptions
        
        response = client.delete(
            f"/categories/{leaf_category.id}",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify category is deleted
        get_response = client.get(
            f"/categories/{leaf_category.id}",
            headers=auth_headers
        )
        assert get_response.status_code == status.HTTP_404_NOT_FOUND


class TestGLCodeManagement:
    """Test GL code mapping and validation functionality."""
    
    async def test_validate_gl_code_success(self, client, auth_headers):
        """Test GL code validation for proper format."""
        response = client.post(
            "/categories/validate-gl-code",
            headers=auth_headers,
            json={"gl_code": "4400"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should indicate valid format
        assert "valid" in data
        assert data["valid"] is True
        
    async def test_validate_duplicate_gl_code(self, client, auth_headers, hierarchical_categories):
        """Test validation catches duplicate GL codes."""
        existing_gl_code = hierarchical_categories[0].gl_code  # "4000"
        
        response = client.post(
            "/categories/validate-gl-code",
            headers=auth_headers,
            json={"gl_code": existing_gl_code}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should indicate duplicate
        assert "valid" in data
        assert data["valid"] is False
        assert "duplicate" in data.get("message", "").lower()
        
    async def test_suggest_gl_codes_success(self, client, auth_headers):
        """Test AI-powered GL code suggestions."""
        suggestion_request = {
            "category_name": "Internet Services",
            "category_path": "Expenses > Office Expenses > Internet Services",
            "account_type": "Expense"
        }
        
        response = client.post(
            "/categories/suggest-gl-codes",
            headers=auth_headers,
            json=suggestion_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should provide suggestions
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)
        
        if data["suggestions"]:
            suggestion = data["suggestions"][0]
            assert "gl_code" in suggestion
            assert "gl_account_name" in suggestion
            assert "confidence" in suggestion
            
    async def test_update_category_gl_mapping_success(self, client, auth_headers, hierarchical_categories):
        """Test updating GL code mapping for a category."""
        category = hierarchical_categories[4]  # Office Supplies
        
        gl_update = {
            "gl_code": "4111",
            "gl_account_name": "Office Supplies - Updated",
            "gl_account_type": "Expense"
        }
        
        response = client.put(
            f"/categories/{category.id}/gl-mapping",
            headers=auth_headers,
            json=gl_update
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert GL mapping updated
        assert data["gl_code"] == "4111"
        assert data["gl_account_name"] == "Office Supplies - Updated"
        assert data["gl_account_type"] == "Expense"
        
    async def test_bulk_gl_update_success(self, client, auth_headers, hierarchical_categories):
        """Test bulk updating GL codes for multiple categories."""
        mappings = [
            {
                "category_id": hierarchical_categories[4].id,
                "gl_code": "4112",
                "gl_account_name": "Bulk Updated Supplies"
            },
            {
                "category_id": hierarchical_categories[5].id,
                "gl_code": "4122",
                "gl_account_name": "Bulk Updated Software"
            }
        ]
        
        response = client.post(
            "/categories/bulk-gl-update",
            headers=auth_headers,
            json={"mappings": mappings}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert bulk update succeeded
        assert data["success"] is True
        assert "updated" in data.get("message", "").lower()
        
    async def test_export_gl_mappings_csv(self, client, auth_headers, hierarchical_categories):
        """Test exporting GL mappings in CSV format."""
        response = client.get(
            "/categories/gl-mappings/export",
            headers=auth_headers,
            params={"format_type": "csv"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        
        # Should contain category and GL code data
        csv_content = response.text
        assert "gl_code" in csv_content.lower()
        assert "category" in csv_content.lower()


class TestCategoryLearning:
    """Test category learning from onboarding data."""
    
    async def test_learn_categories_from_onboarding_success(self, client, auth_headers):
        """Test learning multilevel categories from transaction data."""
        onboarding_transactions = [
            {
                "description": "Amazon Web Services - Cloud Computing",
                "amount": -450.00,
                "date": "2024-07-01",
                "original_category": "Technology > Cloud Services"
            },
            {
                "description": "Microsoft Office 365 Subscription",
                "amount": -120.00,
                "date": "2024-07-02",
                "original_category": "Technology > Software"
            },
            {
                "description": "Staples Office Supplies",
                "amount": -85.50,
                "date": "2024-07-03",
                "original_category": "Office > Supplies"
            },
            {
                "description": "Business Travel - Flight to Denver",
                "amount": -650.00,
                "date": "2024-07-04",
                "original_category": "Travel > Flights"
            }
        ]
        
        response = client.post(
            "/categories/learn-from-onboarding",
            headers=auth_headers,
            json=onboarding_transactions
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert learning results
        assert "categories_created" in data
        assert "hierarchy_depth" in data
        assert data["categories_created"] > 0
        assert data["hierarchy_depth"] >= 2  # Should create multilevel structure
        
        # Verify categories were actually created
        hierarchy_response = client.get(
            "/categories/hierarchy",
            headers=auth_headers
        )
        
        hierarchy_data = hierarchy_response.json()
        assert hierarchy_data["total_categories"] > 0
        assert hierarchy_data["max_depth"] >= 1


class TestMultiTenantIsolation:
    """Test multi-tenant data isolation for categories."""
    
    async def test_tenant_category_isolation(self, client, auth_headers, second_tenant_auth_headers, hierarchical_categories, db_session, second_tenant):
        """Test that tenants can only see their own categories."""
        # Create category for second tenant
        second_tenant_category = Category(
            id=2001,
            name="Second Tenant Category",
            tenant_id=second_tenant.id,
            path="Second Tenant Category",
            level=0,
            gl_code="9000"
        )
        db_session.add(second_tenant_category)
        await db_session.commit()
        
        # First tenant should only see their categories
        response1 = client.get(
            "/categories",
            headers=auth_headers
        )
        
        assert response1.status_code == status.HTTP_200_OK
        data1 = response1.json()
        
        category_names1 = [cat["name"] for cat in data1]
        assert "Second Tenant Category" not in category_names1
        
        # Second tenant should only see their categories
        response2 = client.get(
            "/categories",
            headers=second_tenant_auth_headers
        )
        
        assert response2.status_code == status.HTTP_200_OK
        data2 = response2.json()
        
        category_names2 = [cat["name"] for cat in data2]
        assert "Second Tenant Category" in category_names2
        assert "Expenses" not in category_names2  # From first tenant
        
    async def test_cross_tenant_category_access_denied(self, client, auth_headers, hierarchical_categories, db_session, second_tenant):
        """Test that users cannot access other tenant's categories."""
        # Create category for second tenant
        second_tenant_category = Category(
            id=2002,
            name="Cross Tenant Test",
            tenant_id=second_tenant.id,
            path="Cross Tenant Test",
            level=0
        )
        db_session.add(second_tenant_category)
        await db_session.commit()
        
        # First tenant tries to access second tenant's category
        response = client.get(
            f"/categories/{second_tenant_category.id}",
            headers=auth_headers
        )
        
        # Should be 404 (not found) rather than 403 to avoid information leakage
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestCategoryTransactionIntegration:
    """Test category integration with transaction management."""
    
    async def test_category_assignment_to_transaction(self, client, auth_headers, hierarchical_categories, db_session, test_tenant):
        """Test assigning categories to transactions."""
        # Create test transaction
        transaction = Transaction(
            id="category_test_001",
            description="Test transaction for categorization",
            amount=Decimal("-75.00"),
            date=date(2024, 7, 20),
            tenant_id=test_tenant.id,
            upload_id="category_test"
        )
        db_session.add(transaction)
        await db_session.commit()
        
        category = hierarchical_categories[4]  # Office Supplies
        
        # Assign category to transaction
        response = client.put(
            "/transactions/category_test_001/category",
            headers=auth_headers,
            json={"category_id": category.id}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert category assignment
        assert data["category_id"] == category.id
        assert data["category_path"] == category.path
        assert data["is_categorized"] is True
        
    async def test_category_usage_statistics(self, client, auth_headers, hierarchical_categories, db_session, test_tenant):
        """Test category usage statistics and frequency tracking."""
        category = hierarchical_categories[4]  # Office Supplies
        
        # Create multiple transactions with this category
        for i in range(3):
            transaction = Transaction(
                id=f"usage_test_{i:03d}",
                description=f"Usage test transaction {i}",
                amount=Decimal(f"-{50 + i * 10}.00"),
                date=date(2024, 7, 20 + i),
                tenant_id=test_tenant.id,
                upload_id="usage_test",
                category_id=category.id,
                is_categorized=True
            )
            db_session.add(transaction)
        
        await db_session.commit()
        
        # Get hierarchy with usage counts
        response = client.get(
            "/categories/hierarchy",
            headers=auth_headers,
            params={"include_usage_counts": True}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should include usage statistics
        assert "category_counts" in data


class TestPerformanceValidation:
    """Test performance requirements for category endpoints."""
    
    async def test_category_listing_performance(self, client, auth_headers, hierarchical_categories):
        """Test category listing meets <200ms performance requirement."""
        # Warm up endpoint
        client.get("/categories", headers=auth_headers)
        
        # Measure performance over multiple requests
        response_times = []
        
        for _ in range(5):
            start_time = time.time()
            response = client.get(
                "/categories",
                headers=auth_headers
            )
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
        
        # Assert performance requirement
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 200, f"Average category listing time {avg_response_time:.2f}ms exceeds 200ms requirement"
        assert max_response_time < 300, f"Max category listing time {max_response_time:.2f}ms exceeds acceptable threshold"
        
    async def test_hierarchy_query_performance(self, client, auth_headers, hierarchical_categories):
        """Test category hierarchy query performance."""
        # Warm up endpoint
        client.get("/categories/hierarchy", headers=auth_headers)
        
        # Measure performance
        response_times = []
        
        for _ in range(3):
            start_time = time.time()
            response = client.get(
                "/categories/hierarchy",
                headers=auth_headers,
                params={"include_usage_counts": True}
            )
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
        
        # Assert performance requirement
        avg_response_time = sum(response_times) / len(response_times)
        
        assert avg_response_time < 200, f"Average hierarchy query time {avg_response_time:.2f}ms exceeds 200ms requirement"
        
    async def test_gl_code_operations_performance(self, client, auth_headers, hierarchical_categories):
        """Test GL code operations performance."""
        category = hierarchical_categories[4]  # Office Supplies
        
        # Test GL mapping update performance
        start_time = time.time()
        response = client.put(
            f"/categories/{category.id}/gl-mapping",
            headers=auth_headers,
            json={
                "gl_code": "4113",
                "gl_account_name": "Performance Test Supplies"
            }
        )
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time_ms < 200, f"GL mapping update took {response_time_ms:.2f}ms, exceeds 200ms requirement"


class TestDataIntegrityAndValidation:
    """Test data integrity and validation for categories."""
    
    async def test_category_hierarchy_integrity(self, client, auth_headers, hierarchical_categories):
        """Test category hierarchy maintains referential integrity."""
        # Get complete hierarchy
        response = client.get(
            "/categories/hierarchy",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify hierarchy structure is consistent
        root_categories = data["root_categories"]
        
        for root_cat in root_categories:
            if "children" in root_cat:
                for child in root_cat["children"]:
                    # Child should reference parent correctly
                    assert child["parent_id"] == root_cat["id"]
                    # Child level should be parent level + 1
                    assert child["level"] == root_cat["level"] + 1
                    
    async def test_gl_code_uniqueness_constraint(self, client, auth_headers, hierarchical_categories):
        """Test GL code uniqueness is enforced."""
        existing_gl_code = hierarchical_categories[0].gl_code  # "4000"
        
        # Try to create category with duplicate GL code
        category_data = {
            "name": "Duplicate GL Code Test",
            "gl_code": existing_gl_code,  # Should conflict
            "gl_account_name": "Test Account",
            "gl_account_type": "Expense"
        }
        
        response = client.post(
            "/categories",
            headers=auth_headers,
            json=category_data
        )
        
        # Should reject duplicate GL code
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_422_UNPROCESSABLE_ENTITY]
        
    async def test_category_path_consistency(self, client, auth_headers, db_session, test_tenant):
        """Test category path is maintained consistently."""
        # Create parent category
        parent_response = client.post(
            "/categories",
            headers=auth_headers,
            json={
                "name": "Test Parent",
                "gl_code": "5000"
            }
        )
        
        assert parent_response.status_code == status.HTTP_201_CREATED
        parent_data = parent_response.json()
        
        # Create child category
        child_response = client.post(
            "/categories",
            headers=auth_headers,
            json={
                "name": "Test Child",
                "parent_id": parent_data["id"],
                "gl_code": "5100"
            }
        )
        
        assert child_response.status_code == status.HTTP_201_CREATED
        child_data = child_response.json()
        
        # Verify path consistency
        expected_path = f"{parent_data['name']} > {child_data['name']}"
        assert expected_path in child_data["path"]


class TestErrorHandling:
    """Test error handling in category management."""
    
    async def test_unauthorized_access(self, client):
        """Test unauthorized access to category endpoints."""
        response = client.get("/categories")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    async def test_invalid_category_creation(self, client, auth_headers):
        """Test handling of invalid category creation data."""
        # Missing required fields
        response = client.post(
            "/categories",
            headers=auth_headers,
            json={"invalid_field": "invalid_value"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
    async def test_nonexistent_parent_category(self, client, auth_headers):
        """Test creating category with non-existent parent."""
        category_data = {
            "name": "Invalid Parent Test",
            "parent_id": 99999,  # Non-existent parent
            "gl_code": "6000"
        }
        
        response = client.post(
            "/categories",
            headers=auth_headers,
            json=category_data
        )
        
        # Should reject invalid parent reference
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
        
    async def test_circular_hierarchy_prevention(self, client, auth_headers, hierarchical_categories):
        """Test prevention of circular hierarchy references."""
        parent_category = hierarchical_categories[2]  # Office Expenses
        child_category = hierarchical_categories[4]   # Office Supplies (child of Office Expenses)
        
        # Try to make parent a child of its own child (circular reference)
        response = client.put(
            f"/categories/{parent_category.id}",
            headers=auth_headers,
            json={"parent_id": child_category.id}
        )
        
        # Should prevent circular reference
        assert response.status_code == status.HTTP_400_BAD_REQUEST


class TestFinancialAccuracy:
    """Test financial-grade accuracy and reliability."""
    
    async def test_gl_code_format_validation(self, client, auth_headers):
        """Test GL code format validation for accounting standards."""
        # Valid GL code formats should be accepted
        valid_gl_codes = ["1000", "4000", "1234", "9999"]
        
        for gl_code in valid_gl_codes:
            response = client.post(
                "/categories/validate-gl-code",
                headers=auth_headers,
                json={"gl_code": gl_code}
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["valid"] is True
            
    async def test_category_assignment_audit_trail(self, client, auth_headers, hierarchical_categories):
        """Test category operations maintain audit trail."""
        category = hierarchical_categories[4]  # Office Supplies
        
        # Update category
        update_response = client.put(
            f"/categories/{category.id}",
            headers=auth_headers,
            json={
                "name": "Updated Office Supplies",
                "gl_code": "4114"
            }
        )
        
        assert update_response.status_code == status.HTTP_200_OK
        
        # Verify updated data includes audit information
        get_response = client.get(
            f"/categories/{category.id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == status.HTTP_200_OK
        data = get_response.json()
        
        # Should reflect updates
        assert data["name"] == "Updated Office Supplies"
        assert data["gl_code"] == "4114"
        
    async def test_financial_reporting_accuracy(self, client, auth_headers, hierarchical_categories):
        """Test category data accuracy for financial reporting."""
        # Export GL mappings for financial integration
        response = client.get(
            "/categories/gl-mappings/export",
            headers=auth_headers,
            params={"format_type": "csv"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        csv_content = response.text
        lines = csv_content.strip().split('\n')
        
        # Should have header row plus data rows
        assert len(lines) >= 2  # Header + at least one data row
        
        # Verify CSV structure contains required financial fields
        header = lines[0].lower()
        required_fields = ["category", "gl_code", "account_name", "account_type"]
        
        for field in required_fields:
            assert field in header, f"Missing required field: {field}"