"""
Transaction Management Integration Tests
======================================

Comprehensive integration tests for transaction management including:
- CRUD operations with tenant isolation
- Fast vs regular pagination endpoints
- Categorization workflow (uncategorized → AI suggested → user confirmed)
- Batch operations and data integrity
- Performance validation (<200ms requirement)
- Financial-grade data accuracy and consistency
"""

import time
from datetime import date, datetime, timedelta
from decimal import Decimal

import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.main import app
from giki_ai_api.domains.transactions.models import Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def second_tenant_auth_headers(client, second_tenant_user):
    """Get authentication headers for second tenant user."""
    response = client.post(
        "/auth/token",
        data={
            "username": second_tenant_user.email,
            "password": "SecondPassword123!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def large_transaction_dataset(db_session: AsyncSession, test_tenant, test_categories):
    """Create large transaction dataset for performance testing."""
    transactions = []
    
    for i in range(100):
        transaction = Transaction(
            id=f"perf_test_{i:03d}",
            description=f"Performance test transaction {i}",
            amount=Decimal(f"-{100 + i}.50"),
            date=date(2024, 7, 1) + timedelta(days=i % 30),
            tenant_id=test_tenant.id,
            upload_id="perf_test_upload",
            is_categorized=i % 3 == 0,
            category_id=test_categories[i % len(test_categories)].id if i % 3 == 0 else None,
            ai_suggested_category_id=test_categories[i % len(test_categories)].id if i % 2 == 0 else None,
            ai_category_confidence=0.8 if i % 2 == 0 else None,
            is_user_modified=i % 5 == 0
        )
        transactions.append(transaction)
        db_session.add(transaction)
    
    await db_session.commit()
    
    for transaction in transactions:
        await db_session.refresh(transaction)
    
    return transactions


class TestTransactionListingEndpoints:
    """Test transaction listing endpoints functionality."""
    
    async def test_fast_pagination_endpoint_success(self, client, auth_headers, test_transactions):
        """Test fast pagination endpoint with cursor-based pagination."""
        start_time = time.time()
        
        response = client.get(
            "/transactions/fast",
            headers=auth_headers,
            params={"limit": 10}
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        # Assert response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert response structure
        assert "items" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        
        # Assert transactions returned
        assert len(data["items"]) <= 10
        assert isinstance(data["items"], list)
        
        # Assert performance requirement
        assert response_time_ms < 200, f"Fast endpoint took {response_time_ms:.2f}ms, exceeds 200ms requirement"
        
        # Validate transaction structure
        if data["items"]:
            transaction = data["items"][0]
            assert "id" in transaction
            assert "date" in transaction
            assert "description" in transaction
            assert "amount" in transaction
            assert "status" in transaction
            
    async def test_fast_pagination_with_cursor(self, client, auth_headers, large_transaction_dataset):
        """Test cursor-based pagination functionality."""
        # Get first page
        response1 = client.get(
            "/transactions/fast",
            headers=auth_headers,
            params={"limit": 10}
        )
        
        assert response1.status_code == status.HTTP_200_OK
        data1 = response1.json()
        
        # Should have transactions
        assert len(data1["items"]) > 0
        
        # Create cursor from last item
        if data1["items"]:
            last_item = data1["items"][-1]
            cursor = f"{last_item['date']}:{last_item['id']}"
            
            # Get second page with cursor
            response2 = client.get(
                "/transactions/fast",
                headers=auth_headers,
                params={"limit": 10, "cursor": cursor}
            )
            
            assert response2.status_code == status.HTTP_200_OK
            data2 = response2.json()
            
            # Pages should be different
            if data2["items"]:
                assert data1["items"][0]["id"] != data2["items"][0]["id"]
                
    async def test_regular_pagination_endpoint(self, client, auth_headers, test_transactions):
        """Test regular pagination endpoint with filters."""
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={
                "page": 1,
                "limit": 10,
                "status": "uncategorized"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert paginated structure
        assert "items" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
        
        # Assert filtering worked
        for transaction in data["items"]:
            assert transaction["status"] == "uncategorized"


class TestTransactionCRUDOperations:
    """Test transaction CRUD operations."""
    
    async def test_get_single_transaction_success(self, client, auth_headers, test_transactions):
        """Test retrieving a single transaction."""
        transaction_id = test_transactions[0].id
        
        response = client.get(
            f"/transactions/{transaction_id}",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert transaction data
        assert data["id"] == transaction_id
        assert "description" in data
        assert "amount" in data
        assert "date" in data
        assert "status" in data
        
    async def test_get_nonexistent_transaction(self, client, auth_headers):
        """Test retrieving nonexistent transaction."""
        response = client.get(
            "/transactions/nonexistent_id",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
    async def test_update_transaction_category_success(self, client, auth_headers, test_transactions, test_categories):
        """Test updating transaction category."""
        transaction_id = test_transactions[2].id  # Uncategorized transaction
        category_id = test_categories[0].id
        
        response = client.put(
            f"/transactions/{transaction_id}/category",
            headers=auth_headers,
            json={"category_id": category_id}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert category was updated
        assert data["category_id"] == category_id
        assert data["is_user_modified"] is True
        assert data["status"] == "user_modified"
        
    async def test_batch_category_update_success(self, client, auth_headers, test_transactions, test_categories):
        """Test batch updating transaction categories."""
        transaction_ids = [t.id for t in test_transactions[:2]]
        category_id = test_categories[0].id
        
        response = client.put(
            "/transactions/batch/category",
            headers=auth_headers,
            json={
                "transaction_ids": transaction_ids,
                "category_id": category_id
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert batch update response
        assert data["success"] is True
        assert "message" in data
        assert str(len(transaction_ids)) in data["message"]
        
    async def test_batch_ai_categorization(self, client, auth_headers, test_transactions, mock_ai_service):
        """Test batch AI categorization."""
        transaction_ids = [t.id for t in test_transactions[:2]]
        
        response = client.put(
            "/transactions/batch/category",
            headers=auth_headers,
            json={
                "transaction_ids": transaction_ids,
                "use_ai": True,
                "confidence_threshold": 0.8
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["success"] is True
        assert "AI categorization" in data["message"] or "categorization" in data["message"]


class TestTransactionFiltering:
    """Test transaction filtering functionality."""
    
    async def test_filter_by_date_range(self, client, auth_headers, test_transactions):
        """Test filtering transactions by date range."""
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={
                "start_date": "2024-07-15",
                "end_date": "2024-07-16"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert all transactions are within date range
        for transaction in data["items"]:
            transaction_date = datetime.fromisoformat(transaction["date"]).date()
            assert date(2024, 7, 15) <= transaction_date <= date(2024, 7, 16)
            
    async def test_filter_by_status(self, client, auth_headers, test_transactions):
        """Test filtering transactions by categorization status."""
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={"status": "categorized"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert all transactions have categorized status
        for transaction in data["items"]:
            assert transaction["status"] in ["categorized", "user_modified"]
            
    async def test_filter_by_category(self, client, auth_headers, test_transactions, test_categories):
        """Test filtering transactions by category."""
        category_id = test_categories[0].id
        
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={"category_id": str(category_id)}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert all transactions belong to specified category
        for transaction in data["items"]:
            if transaction["category_id"]:
                assert transaction["category_id"] == category_id
                
    async def test_filter_by_amount_range(self, client, auth_headers, test_transactions):
        """Test filtering transactions by amount range."""
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={
                "min_amount": "30.00",
                "max_amount": "100.00"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert all transactions are within amount range
        for transaction in data["items"]:
            amount = abs(float(transaction["amount"]))
            assert 30.00 <= amount <= 100.00
            
    async def test_search_by_description(self, client, auth_headers, test_transactions):
        """Test searching transactions by description."""
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={"search_term": "office"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert all transactions contain search term
        for transaction in data["items"]:
            assert "office" in transaction["description"].lower()


class TestMultiTenantIsolation:
    """Test multi-tenant data isolation for transactions."""
    
    async def test_tenant_transaction_isolation(self, client, auth_headers, second_tenant_auth_headers, test_transactions, db_session, second_tenant):
        """Test that tenants can only see their own transactions."""
        # Create transaction for second tenant
        second_tenant_transaction = Transaction(
            id="second_tenant_001",
            description="Second tenant transaction",
            amount=Decimal("-200.00"),
            date=date(2024, 7, 20),
            tenant_id=second_tenant.id,
            upload_id="second_tenant_upload"
        )
        db_session.add(second_tenant_transaction)
        await db_session.commit()
        
        # First tenant should only see their transactions
        response1 = client.get(
            "/transactions/fast",
            headers=auth_headers
        )
        
        assert response1.status_code == status.HTTP_200_OK
        data1 = response1.json()
        
        transaction_ids1 = [t["id"] for t in data1["items"]]
        assert "second_tenant_001" not in transaction_ids1
        
        # Second tenant should only see their transactions
        response2 = client.get(
            "/transactions/fast",
            headers=second_tenant_auth_headers
        )
        
        assert response2.status_code == status.HTTP_200_OK
        data2 = response2.json()
        
        transaction_ids2 = [t["id"] for t in data2["items"]]
        assert "second_tenant_001" in transaction_ids2
        
        # Verify no overlap in transaction IDs
        assert set(transaction_ids1).isdisjoint(set(transaction_ids2))
        
    async def test_cross_tenant_transaction_access_denied(self, client, auth_headers, second_tenant_auth_headers, db_session, second_tenant):
        """Test that users cannot access other tenant's transactions."""
        # Create transaction for second tenant
        second_tenant_transaction = Transaction(
            id="cross_tenant_test",
            description="Cross tenant test",
            amount=Decimal("-100.00"),
            date=date(2024, 7, 21),
            tenant_id=second_tenant.id,
            upload_id="cross_tenant_upload"
        )
        db_session.add(second_tenant_transaction)
        await db_session.commit()
        
        # First tenant tries to access second tenant's transaction
        response = client.get(
            "/transactions/cross_tenant_test",
            headers=auth_headers
        )
        
        # Should be 404 (not found) rather than 403 to avoid information leakage
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestCategorizationWorkflow:
    """Test transaction categorization workflow."""
    
    async def test_uncategorized_to_ai_suggested_workflow(self, client, auth_headers, db_session, test_tenant, test_categories, mock_ai_service):
        """Test workflow from uncategorized to AI suggested."""
        # Create uncategorized transaction
        transaction = Transaction(
            id="workflow_test_001",
            description="Test categorization workflow",
            amount=Decimal("-50.00"),
            date=date(2024, 7, 22),
            tenant_id=test_tenant.id,
            upload_id="workflow_test",
            is_categorized=False
        )
        db_session.add(transaction)
        await db_session.commit()
        
        # Trigger AI categorization
        response = client.post(
            "/transactions/workflow_test_001/categorize",
            headers=auth_headers
        )
        
        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            
            # Should now have AI suggestion
            assert data["ai_suggested_category_id"] is not None
            assert data["ai_category_confidence"] is not None
            assert data["status"] == "ai_suggested"
            
    async def test_ai_suggested_to_user_confirmed_workflow(self, client, auth_headers, db_session, test_tenant, test_categories):
        """Test workflow from AI suggested to user confirmed."""
        # Create AI-suggested transaction
        transaction = Transaction(
            id="workflow_test_002",
            description="AI suggested workflow test",
            amount=Decimal("-75.00"),
            date=date(2024, 7, 23),
            tenant_id=test_tenant.id,
            upload_id="workflow_test",
            ai_suggested_category_id=test_categories[0].id,
            ai_category_confidence=Decimal("0.85")
        )
        db_session.add(transaction)
        await db_session.commit()
        
        # User confirms category
        response = client.put(
            "/transactions/workflow_test_002/category",
            headers=auth_headers,
            json={"category_id": test_categories[0].id}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should now be user confirmed
        assert data["category_id"] == test_categories[0].id
        assert data["is_user_modified"] is True
        assert data["status"] == "user_modified"
        
    async def test_user_correction_workflow(self, client, auth_headers, db_session, test_tenant, test_categories):
        """Test user correcting AI suggestion."""
        # Create AI-suggested transaction
        transaction = Transaction(
            id="workflow_test_003",
            description="User correction test",
            amount=Decimal("-90.00"),
            date=date(2024, 7, 24),
            tenant_id=test_tenant.id,
            upload_id="workflow_test",
            ai_suggested_category_id=test_categories[0].id,
            ai_category_confidence=Decimal("0.75")
        )
        db_session.add(transaction)
        await db_session.commit()
        
        # User assigns different category
        response = client.put(
            "/transactions/workflow_test_003/category",
            headers=auth_headers,
            json={"category_id": test_categories[1].id}  # Different category
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should mark as user corrected
        assert data["category_id"] == test_categories[1].id
        assert data["is_user_modified"] is True
        assert data["user_corrected"] is True
        assert data["status"] == "user_modified"


class TestPerformanceValidation:
    """Test performance requirements for transaction endpoints."""
    
    async def test_fast_endpoint_performance_requirement(self, client, auth_headers, large_transaction_dataset):
        """Test fast endpoint meets <200ms performance requirement."""
        # Warm up endpoint
        client.get("/transactions/fast", headers=auth_headers)
        
        # Measure performance over multiple requests
        response_times = []
        
        for _ in range(5):
            start_time = time.time()
            response = client.get(
                "/transactions/fast",
                headers=auth_headers,
                params={"limit": 50}
            )
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
        
        # Assert performance requirement
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 200, f"Average fast endpoint time {avg_response_time:.2f}ms exceeds 200ms requirement"
        assert max_response_time < 300, f"Max fast endpoint time {max_response_time:.2f}ms exceeds acceptable threshold"
        
    async def test_single_transaction_lookup_performance(self, client, auth_headers, test_transactions):
        """Test single transaction lookup performance."""
        transaction_id = test_transactions[0].id
        
        # Warm up endpoint
        client.get(f"/transactions/{transaction_id}", headers=auth_headers)
        
        # Measure performance
        response_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get(
                f"/transactions/{transaction_id}",
                headers=auth_headers
            )
            end_time = time.time()
            
            assert response.status_code == status.HTTP_200_OK
            response_times.append((end_time - start_time) * 1000)
        
        # Assert performance requirement
        avg_response_time = sum(response_times) / len(response_times)
        
        assert avg_response_time < 100, f"Average single transaction lookup {avg_response_time:.2f}ms exceeds 100ms requirement"
        
    async def test_batch_operations_performance(self, client, auth_headers, large_transaction_dataset, test_categories):
        """Test batch operations performance."""
        transaction_ids = [t.id for t in large_transaction_dataset[:20]]  # 20 transactions
        
        start_time = time.time()
        response = client.put(
            "/transactions/batch/category",
            headers=auth_headers,
            json={
                "transaction_ids": transaction_ids,
                "category_id": test_categories[0].id
            }
        )
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        
        # Batch operations should complete in reasonable time
        assert response_time_ms < 1000, f"Batch update of 20 transactions took {response_time_ms:.2f}ms, exceeds 1000ms threshold"


class TestDataIntegrityAndValidation:
    """Test data integrity and validation."""
    
    async def test_transaction_amount_precision(self, client, auth_headers, db_session, test_tenant):
        """Test financial precision is maintained."""
        # Create transaction with precise decimal
        transaction = Transaction(
            id="precision_test",
            description="Precision test transaction",
            amount=Decimal("123.456789"),  # High precision
            date=date(2024, 7, 25),
            tenant_id=test_tenant.id,
            upload_id="precision_test"
        )
        db_session.add(transaction)
        await db_session.commit()
        
        # Retrieve transaction
        response = client.get(
            "/transactions/precision_test",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Amount should be properly rounded to 2 decimal places for financial data
        assert abs(float(data["amount"]) - 123.46) < 0.001
        
    async def test_invalid_category_assignment(self, client, auth_headers, test_transactions):
        """Test invalid category assignment is rejected."""
        transaction_id = test_transactions[0].id
        
        response = client.put(
            f"/transactions/{transaction_id}/category",
            headers=auth_headers,
            json={"category_id": 99999}  # Non-existent category
        )
        
        # Should reject invalid category
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
        
    async def test_batch_operation_atomicity(self, client, auth_headers, test_transactions, test_categories):
        """Test batch operations are atomic (all succeed or all fail)."""
        valid_ids = [t.id for t in test_transactions[:2]]
        invalid_ids = ["nonexistent_1", "nonexistent_2"]
        mixed_ids = valid_ids + invalid_ids
        
        response = client.put(
            "/transactions/batch/category",
            headers=auth_headers,
            json={
                "transaction_ids": mixed_ids,
                "category_id": test_categories[0].id
            }
        )
        
        # Should handle partial failures gracefully
        # (Implementation dependent - may partially succeed or fully fail)
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        
        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            # Should indicate partial success
            assert "partial" in data["message"].lower() or data["success"] is False


class TestErrorHandling:
    """Test error handling in transaction management."""
    
    async def test_unauthorized_access(self, client):
        """Test unauthorized access to transaction endpoints."""
        response = client.get("/transactions/fast")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    async def test_malformed_request_data(self, client, auth_headers):
        """Test handling of malformed request data."""
        # Invalid JSON for category update
        response = client.put(
            "/transactions/test_001/category",
            headers=auth_headers,
            json={"invalid_field": "invalid_value"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
    async def test_pagination_parameter_validation(self, client, auth_headers):
        """Test pagination parameter validation."""
        # Invalid limit value
        response = client.get(
            "/transactions/fast",
            headers=auth_headers,
            params={"limit": 2000}  # Exceeds maximum
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
    async def test_invalid_cursor_handling(self, client, auth_headers):
        """Test handling of invalid cursor values."""
        response = client.get(
            "/transactions/fast",
            headers=auth_headers,
            params={"cursor": "invalid:cursor:format"}
        )
        
        # Should handle gracefully (ignore cursor or return error)
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]


class TestFinancialAccuracy:
    """Test financial-grade accuracy and consistency."""
    
    async def test_category_assignment_consistency(self, client, auth_headers, db_session, test_transactions, test_categories):
        """Test category assignments are consistent and accurate."""
        transaction_id = test_transactions[0].id
        category_id = test_categories[0].id
        
        # Assign category
        response = client.put(
            f"/transactions/{transaction_id}/category",
            headers=auth_headers,
            json={"category_id": category_id}
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify in database
        result = await db_session.execute(
            select(Transaction).where(Transaction.id == transaction_id)
        )
        db_transaction = result.scalar_one()
        
        assert db_transaction.category_id == category_id
        assert db_transaction.is_user_modified is True
        
    async def test_audit_trail_for_changes(self, client, auth_headers, test_transactions, test_categories):
        """Test that financial changes maintain audit trail."""
        transaction_id = test_transactions[0].id
        
        # Get original state
        original_response = client.get(
            f"/transactions/{transaction_id}",
            headers=auth_headers
        )
        original_data = original_response.json()
        
        # Make change
        client.put(
            f"/transactions/{transaction_id}/category",
            headers=auth_headers,
            json={"category_id": test_categories[0].id}
        )
        
        # Verify change tracking
        updated_response = client.get(
            f"/transactions/{transaction_id}",
            headers=auth_headers
        )
        updated_data = updated_response.json()
        
        # Should track that user made modification
        assert updated_data["is_user_modified"] is True
        assert updated_data["category_id"] != original_data.get("category_id")
        
    async def test_financial_calculation_accuracy(self, client, auth_headers, large_transaction_dataset):
        """Test financial calculations maintain precision."""
        # Get all transactions
        response = client.get(
            "/transactions/",
            headers=auth_headers,
            params={"limit": 1000}  # Get all
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Calculate total manually
        manual_total = sum(float(t["amount"]) for t in data["items"])
        
        # Verify amounts are properly formatted financial values
        for transaction in data["items"]:
            amount = float(transaction["amount"])
            # Should be properly rounded to 2 decimal places
            assert amount == round(amount, 2)
            
        # Manual calculation should be consistent
        assert isinstance(manual_total, float)
        assert manual_total == round(manual_total, 2)