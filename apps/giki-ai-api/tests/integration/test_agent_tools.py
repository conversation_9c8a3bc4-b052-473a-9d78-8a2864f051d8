"""
Agent Tools Integration Tests
===========================

Tests for all agent tool functionality including:
- Customer Agent UI equivalence tools
- GL Code Agent tools
- Debit Credit Agent tools
- Reports Agent tools
- Tool integration and error handling
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest

from giki_ai_api.domains.intelligence.customer_agent import (
    export_customer_data_tool_function,
    generate_insights_tool_function,
    get_accuracy_metrics_tool_function,
    process_audio_input_tool_function,
    query_customer_transactions_tool_function,
    search_customer_data_tool_function,
)


class TestCustomerAgentTools:
    """Test Customer Agent tool functions."""
    
    async def test_query_customer_transactions_tool_success(
        self, 
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test successful transaction querying."""
        # Mock the transaction service
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            
            # Mock get_transactions to return test data
            mock_service.get_transactions = AsyncMock(return_value=test_transactions)
            
            # Execute tool function
            result = await query_customer_transactions_tool_function(
                tenant_id=test_tenant.id,
                query_params={
                    "date_range": {"start": "2024-07-01", "end": "2024-07-31"},
                    "limit": 100
                },
                db=db_session
            )
            
            # Verify result
            assert result["success"] is True
            assert len(result["transactions"]) == len(test_transactions)
            assert result["total_count"] == len(test_transactions)
            assert result["real_data"] is True
            
            # Verify service was called correctly
            mock_service.get_transactions.assert_called_once_with(
                tenant_id=test_tenant.id,
                filters={"date_range": {"start": "2024-07-01", "end": "2024-07-31"}},
                limit=100,
                offset=0
            )
    
    async def test_query_customer_transactions_tool_with_filters(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test transaction querying with various filters."""
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.get_transactions = AsyncMock(return_value=test_transactions[:1])
            
            # Execute with multiple filters
            result = await query_customer_transactions_tool_function(
                tenant_id=test_tenant.id,
                query_params={
                    "category": "Office Supplies",
                    "amount_range": {"min": 10, "max": 100},
                    "search": "office",
                    "limit": 50,
                    "offset": 10
                },
                db=db_session
            )
            
            # Verify filters were passed correctly
            mock_service.get_transactions.assert_called_once_with(
                tenant_id=test_tenant.id,
                filters={
                    "category": "Office Supplies",
                    "amount_range": {"min": 10, "max": 100},
                    "search": "office"
                },
                limit=50,
                offset=10
            )
            
            assert result["success"] is True
            assert len(result["transactions"]) == 1
    
    async def test_query_customer_transactions_tool_error_handling(
        self,
        db_session,
        test_tenant
    ):
        """Test error handling in transaction querying."""
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.get_transactions = AsyncMock(side_effect=Exception("Database error"))
            
            # Execute tool function
            result = await query_customer_transactions_tool_function(
                tenant_id=test_tenant.id,
                query_params={},
                db=db_session
            )
            
            # Verify error handling
            assert result["success"] is False
            assert "error" in result
            assert "Database error" in result["error"]
    
    async def test_get_accuracy_metrics_tool_success(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test successful accuracy metrics calculation."""
        # Mock database queries
        with patch.object(db_session, 'execute') as mock_execute:
            # Mock transaction query result
            mock_transactions_result = Mock()
            mock_transactions_result.scalars.return_value.all.return_value = test_transactions
            
            # Mock corrected count query result
            mock_corrected_result = Mock()
            mock_corrected_result.scalar.return_value = 5
            
            # Set up execute to return different results for different queries
            mock_execute.side_effect = [mock_transactions_result, mock_corrected_result]
            
            # Add confidence scores to test transactions
            for i, txn in enumerate(test_transactions):
                txn.confidence_score = 0.8 + (i * 0.05)  # Varying confidence scores
                txn.user_corrected = False
            
            # Execute tool function
            result = await get_accuracy_metrics_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                period="July_2024"
            )
            
            # Verify result structure
            assert result["success"] is True
            assert "metrics" in result
            assert "precision" in result["metrics"]
            assert "recall" in result["metrics"]
            assert "f1_score" in result["metrics"]
            assert "average_confidence" in result["metrics"]
            assert result["transaction_count"] == len(test_transactions)
            assert result["real_data"] is True
    
    async def test_get_accuracy_metrics_tool_no_transactions(
        self,
        db_session,
        test_tenant
    ):
        """Test accuracy metrics when no transactions found."""
        with patch.object(db_session, 'execute') as mock_execute:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = []
            mock_execute.return_value = mock_result
            
            result = await get_accuracy_metrics_tool_function(
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            assert result["success"] is True
            assert result["metrics"]["precision"] == 0.0
            assert result["metrics"]["recall"] == 0.0
            assert result["metrics"]["f1_score"] == 0.0
            assert result["transaction_count"] == 0
    
    async def test_search_customer_data_tool_comprehensive_search(
        self,
        db_session,
        test_tenant,
        test_transactions,
        test_categories
    ):
        """Test comprehensive data search across all data types."""
        # Mock database queries for different data types
        with patch.object(db_session, 'execute') as mock_execute:
            # Mock transaction search result
            mock_txn_result = Mock()
            mock_txn_result.scalars.return_value.all.return_value = test_transactions[:1]
            
            # Mock category search result
            mock_cat_result = Mock()
            mock_cat_result.scalars.return_value.all.return_value = test_categories[:1]
            
            # Mock entity search result
            mock_entity_result = Mock()
            mock_entity_result.scalars.return_value.all.return_value = []
            
            # Mock transaction count result
            mock_count_result = Mock()
            mock_count_result.scalar.return_value = 5
            
            # Set up execute to return results in order
            mock_execute.side_effect = [
                mock_txn_result,    # Transaction search
                mock_cat_result,    # Category search
                mock_count_result,  # Category transaction count
                mock_entity_result  # Entity search
            ]
            
            # Execute search
            result = await search_customer_data_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                search_query="office",
                data_type="all"
            )
            
            # Verify comprehensive search results
            assert result["success"] is True
            assert "results" in result
            assert "transactions" in result["results"]
            assert "categories" in result["results"]
            assert "vendors" in result["results"]
            assert result["total_results"] >= 1
            assert result["search_query"] == "office"
            assert result["data_type"] == "all"
            assert result["real_data"] is True
    
    async def test_search_customer_data_tool_specific_data_type(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test search limited to specific data type."""
        with patch.object(db_session, 'execute') as mock_execute:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = test_transactions
            mock_execute.return_value = mock_result
            
            # Search only transactions
            result = await search_customer_data_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                search_query="test",
                data_type="transactions"
            )
            
            # Should only have transaction results
            assert result["success"] is True
            assert len(result["results"]["transactions"]) > 0
            assert len(result["results"]["categories"]) == 0
            assert len(result["results"]["vendors"]) == 0
    
    async def test_generate_insights_tool_with_transactions(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test insights generation with transaction data."""
        # Mock database query
        with patch.object(db_session, 'execute') as mock_execute:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = test_transactions
            mock_execute.return_value = mock_result
            
            # Add required attributes to test transactions
            for i, txn in enumerate(test_transactions):
                txn.confidence_score = 0.85
                txn.amount = -50.00 - (i * 10)  # Negative for expenses
            
            # Mock AI service
            with patch('giki_ai_api.domains.intelligence.customer_agent.UnifiedAI') as mock_ai_class:
                mock_ai = Mock()
                mock_ai_class.return_value = mock_ai
                mock_ai.generate_insights = AsyncMock(return_value={
                    "analysis": "Spending patterns analysis completed",
                    "findings": ["Office supplies are the main expense category"],
                    "recommendations": ["Consider bulk purchasing for office supplies"]
                })
                
                # Execute insights generation
                result = await generate_insights_tool_function(
                    tenant_id=test_tenant.id,
                    db=db_session,
                    insight_type="spending"
                )
                
                # Verify insights structure
                assert result["success"] is True
                assert "insights" in result
                assert "ai_analysis" in result["insights"]
                assert "key_findings" in result["insights"]
                assert "recommendations" in result["insights"]
                assert "data_summary" in result["insights"]
                assert result["data_points"] > 0
                assert result["real_data"] is True
    
    async def test_generate_insights_tool_no_data(
        self,
        db_session,
        test_tenant
    ):
        """Test insights generation with no transaction data."""
        with patch.object(db_session, 'execute') as mock_execute:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = []
            mock_execute.return_value = mock_result
            
            result = await generate_insights_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                insight_type="comprehensive"
            )
            
            assert result["success"] is True
            assert "No recent transaction data available" in result["insights"]["message"]
            assert result["data_points"] == 0
    
    async def test_export_customer_data_tool_csv_format(
        self,
        db_session,
        test_tenant,
        test_transactions,
        test_categories
    ):
        """Test data export in CSV format."""
        # Mock services
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_txn_service_class:
            with patch('giki_ai_api.domains.categories.service.CategoryService') as mock_cat_service_class:
                # Setup mock services
                mock_txn_service = Mock()
                mock_cat_service = Mock()
                mock_txn_service_class.return_value = mock_txn_service
                mock_cat_service_class.return_value = mock_cat_service
                
                mock_txn_service.get_transactions = AsyncMock(return_value=test_transactions)
                mock_cat_service.get_categories = AsyncMock(return_value=test_categories)
                
                # Add required methods to test transactions
                for txn in test_transactions:
                    txn.get_display_category = Mock(return_value="Test Category")
                
                # Execute export
                result = await export_customer_data_tool_function(
                    tenant_id=test_tenant.id,
                    db=db_session,
                    export_format="csv",
                    data_selection={"date_range": {"start": "2024-07-01", "end": "2024-07-31"}}
                )
                
                # Verify export result
                assert result["success"] is True
                assert "export_result" in result
                assert result["export_result"]["format"] == "csv"
                assert result["export_result"]["records_exported"] == len(test_transactions)
                assert "transactions_export" in result["export_result"]["file_name"]
                assert result["real_data"] is True
    
    async def test_export_customer_data_tool_no_data(
        self,
        db_session,
        test_tenant
    ):
        """Test data export with no data available."""
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.get_transactions = AsyncMock(return_value=[])
            
            result = await export_customer_data_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                export_format="excel"
            )
            
            assert result["success"] is False
            assert "No data available for export" in result["error"]
    
    def test_process_audio_input_tool_success(self):
        """Test successful audio processing."""
        # Mock GenerativeModel
        with patch('giki_ai_api.domains.intelligence.customer_agent.GenerativeModel') as mock_model_class:
            with patch('giki_ai_api.domains.intelligence.customer_agent.Part') as mock_part_class:
                # Setup mocks
                mock_model = Mock()
                mock_model_class.return_value = mock_model
                mock_response = Mock()
                mock_response.text = '{"transcription": "Show me my transactions", "intent": "transactions", "parameters": {"limit": 10}, "confidence": 0.9, "language_detected": "en-US", "processing_method": "direct_audio_tokens"}'
                mock_model.generate_content.return_value = mock_response
                
                mock_part = Mock()
                mock_part_class.from_data.return_value = mock_part
                
                # Test audio data
                audio_data = b"fake_audio_content"
                
                # Execute tool
                result = process_audio_input_tool_function(
                    tenant_id=123,
                    _db=Mock(),
                    audio_data=audio_data,
                    user_id="456"
                )
                
                # Verify result
                assert result["success"] is True
                assert "audio_result" in result
                assert result["audio_result"]["transcription"] == "Show me my transactions"
                assert result["audio_result"]["intent"] == "transactions"
                assert result["audio_result"]["confidence"] == 0.9
                assert "processing_time_ms" in result
    
    def test_process_audio_input_tool_no_audio_data(self):
        """Test audio processing with no audio data."""
        result = process_audio_input_tool_function(
            tenant_id=123,
            _db=Mock(),
            audio_data=None,
            user_id="456"
        )
        
        assert result["success"] is False
        assert "No audio data provided" in result["error"]
    
    def test_process_audio_input_tool_invalid_json_response(self):
        """Test audio processing with invalid JSON response from AI."""
        with patch('giki_ai_api.domains.intelligence.customer_agent.GenerativeModel') as mock_model_class:
            with patch('giki_ai_api.domains.intelligence.customer_agent.Part') as mock_part_class:
                # Setup mocks with invalid JSON
                mock_model = Mock()
                mock_model_class.return_value = mock_model
                mock_response = Mock()
                mock_response.text = "Invalid JSON response from AI"
                mock_model.generate_content.return_value = mock_response
                
                mock_part = Mock()
                mock_part_class.from_data.return_value = mock_part
                
                # Execute tool
                result = process_audio_input_tool_function(
                    tenant_id=123,
                    _db=Mock(),
                    audio_data=b"audio_data",
                    user_id="456"
                )
                
                # Should handle invalid JSON gracefully
                assert result["success"] is True
                assert result["audio_result"]["transcription"] == "Invalid JSON response from AI"
                assert result["audio_result"]["intent"] == "general_question"


class TestAgentToolErrorHandling:
    """Test error handling across all agent tools."""
    
    async def test_all_tools_handle_database_errors_gracefully(
        self,
        db_session,
        test_tenant
    ):
        """Test that all tools handle database errors gracefully."""
        # Mock database to raise errors
        with patch.object(db_session, 'execute', side_effect=Exception("Database connection failed")):
            
            # Test each tool function
            tools_to_test = [
                (query_customer_transactions_tool_function, {"tenant_id": test_tenant.id, "query_params": {}, "db": db_session}),
                (get_accuracy_metrics_tool_function, {"tenant_id": test_tenant.id, "db": db_session}),
                (search_customer_data_tool_function, {"tenant_id": test_tenant.id, "db": db_session, "search_query": "test"}),
                (generate_insights_tool_function, {"tenant_id": test_tenant.id, "db": db_session}),
                (export_customer_data_tool_function, {"tenant_id": test_tenant.id, "db": db_session})
            ]
            
            for tool_func, kwargs in tools_to_test:
                result = await tool_func(**kwargs)
                
                # All tools should handle errors gracefully
                assert result["success"] is False
                assert "error" in result
                assert "Database connection failed" in result["error"] or "Failed" in result["message"]
    
    async def test_tools_validate_required_parameters(
        self,
        db_session,
        test_tenant
    ):
        """Test that tools validate required parameters."""
        # Test search tool without search query
        result = await search_customer_data_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            search_query=""  # Empty search query
        )
        
        # Should handle empty search gracefully
        assert isinstance(result, dict)
        assert "success" in result


@pytest.mark.integration
class TestAgentToolsIntegration:
    """Integration tests for agent tools with real database components."""
    
    async def test_transaction_query_tool_with_real_database(
        self,
        db_session,
        test_tenant,
        test_user,
        test_transactions
    ):
        """Test transaction query tool with real database integration."""
        # This test uses real database session and fixtures
        # Mock only the service layer, not the database
        
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            
            # Use real transaction data
            mock_service.get_transactions = AsyncMock(return_value=test_transactions)
            
            result = await query_customer_transactions_tool_function(
                tenant_id=test_tenant.id,
                query_params={"limit": 10},
                db=db_session
            )
            
            # Verify integration works with real data
            assert result["success"] is True
            assert len(result["transactions"]) == len(test_transactions)
            
            # Verify tenant isolation in service call
            mock_service.get_transactions.assert_called_once()
            call_args = mock_service.get_transactions.call_args
            assert call_args[1]["tenant_id"] == test_tenant.id
    
    async def test_full_workflow_integration(
        self,
        db_session,
        test_tenant,
        test_user,
        test_transactions,
        test_categories
    ):
        """Test full workflow integration across multiple tools."""
        # Test a workflow that uses multiple tools in sequence
        
        # 1. Query transactions
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_txn_service:
            mock_service = Mock()
            mock_txn_service.return_value = mock_service
            mock_service.get_transactions = AsyncMock(return_value=test_transactions)
            
            query_result = await query_customer_transactions_tool_function(
                tenant_id=test_tenant.id,
                query_params={"limit": 100},
                db=db_session
            )
            
            assert query_result["success"] is True
        
        # 2. Search based on query results
        with patch.object(db_session, 'execute') as mock_execute:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = test_transactions[:1]
            mock_execute.return_value = mock_result
            
            search_result = await search_customer_data_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                search_query="office",
                data_type="transactions"
            )
            
            assert search_result["success"] is True
        
        # 3. Generate insights based on data
        with patch.object(db_session, 'execute') as mock_execute:
            with patch('giki_ai_api.domains.intelligence.customer_agent.UnifiedAI') as mock_ai:
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = test_transactions
                mock_execute.return_value = mock_result
                
                mock_ai_instance = Mock()
                mock_ai.return_value = mock_ai_instance
                mock_ai_instance.generate_insights = AsyncMock(return_value={
                    "analysis": "Workflow integration test analysis",
                    "findings": ["Integration working correctly"],
                    "recommendations": ["Continue testing"]
                })
                
                # Add required attributes
                for txn in test_transactions:
                    txn.confidence_score = 0.9
                
                insights_result = await generate_insights_tool_function(
                    tenant_id=test_tenant.id,
                    db=db_session,
                    insight_type="comprehensive"
                )
                
                assert insights_result["success"] is True
                assert "Integration working correctly" in insights_result["insights"]["key_findings"]
        
        # Verify workflow maintained tenant isolation throughout
        assert query_result["success"] is True
        assert search_result["success"] is True
        assert insights_result["success"] is True