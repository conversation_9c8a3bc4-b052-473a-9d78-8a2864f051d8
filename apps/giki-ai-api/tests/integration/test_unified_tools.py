"""
Unified Tools Integration Tests
==============================

Tests for the new unified tools approach with 2-3 tools per agent.
Tests real-world scenarios without oversimplification.
"""

from datetime import datetime, timedelta
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Add asyncio mark for all tests in this module
pytestmark = pytest.mark.asyncio

from giki_ai_api.domains.intelligence.unified_tools import (
    unified_data_access_tool_function,
    unified_reporting_tool_function,
    unified_ui_operations_tool_function,
)


class TestUnifiedDataAccessTool:
    """Test the unified data access tool that handles query, search, qa, and metrics."""
    
    async def test_query_operation_with_complex_filters(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test query operation with realistic complex filters."""
        # Setup test transactions with realistic data
        for i, txn in enumerate(test_transactions):
            txn.date = datetime.now() - timedelta(days=i)
            txn.amount = -150.50 * (i + 1)  # Varying amounts
            txn.confidence_score = 0.85 + (i * 0.02)
            txn.get_display_category = Mock(return_value=f"Category {i % 3}")
        
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.get_transactions = AsyncMock(return_value=test_transactions)
            
            # Execute unified tool with complex query
            result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="query",
                parameters={
                    "data_type": "transactions",
                    "filters": {
                        "date_range": {
                            "start": (datetime.now() - timedelta(days=30)).isoformat(),
                            "end": datetime.now().isoformat()
                        },
                        "amount_range": {"min": 100, "max": 1000},
                        "categories": ["Office Supplies", "Travel"],
                        "confidence_threshold": 0.8
                    },
                    "sort_by": "date",
                    "sort_order": "desc",
                    "limit": 50,
                    "offset": 0
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "query"
            assert "data" in result
            assert len(result["data"]["transactions"]) == len(test_transactions)
            assert result["data"]["total_count"] == len(test_transactions)
            assert result["data"]["has_more"] is False
            
            # Verify service was called with correct parameters
            call_args = mock_service.get_transactions.call_args[1]
            assert call_args["tenant_id"] == test_tenant.id
            assert "date_range" in call_args["filters"]
            assert call_args["limit"] == 50
    
    async def test_search_operation_across_multiple_entities(
        self,
        db_session,
        test_tenant,
        test_transactions,
        test_categories
    ):
        """Test search operation across transactions, categories, and entities."""
        # Mock comprehensive search results
        with patch.object(db_session, 'execute') as mock_execute:
            # Transaction search
            mock_txn_result = Mock()
            mock_txn_result.scalars.return_value.all.return_value = test_transactions[:2]
            
            # Category search
            mock_cat_result = Mock()
            mock_cat_result.scalars.return_value.all.return_value = test_categories[:1]
            
            # Entity search
            mock_entity_result = Mock()
            mock_entity_result.scalars.return_value.all.return_value = []
            
            # Category count
            mock_count_result = Mock()
            mock_count_result.scalar.return_value = 15
            
            mock_execute.side_effect = [
                mock_txn_result,
                mock_cat_result,
                mock_count_result,
                mock_entity_result
            ]
            
            # Execute search
            result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="search",
                parameters={
                    "query": "office supplies amazon",
                    "search_scope": ["transactions", "categories", "vendors"],
                    "fuzzy_match": True,
                    "date_filter": {
                        "start": "2024-01-01",
                        "end": "2024-12-31"
                    }
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "search"
            assert "transactions" in result["data"]["results"]
            assert "categories" in result["data"]["results"]
            assert "vendors" in result["data"]["results"]
            assert result["data"]["total_matches"] >= 3
            assert result["data"]["search_metadata"]["query"] == "office supplies amazon"
    
    async def test_qa_operation_with_rag_context(
        self,
        db_session,
        test_tenant
    ):
        """Test question-answering with RAG context and financial data."""
        # Mock AI service for QA
        with patch('giki_ai_api.domains.intelligence.unified_tools.UnifiedAIService') as mock_ai_class:
            mock_ai = Mock()
            mock_ai_class.return_value = mock_ai
            
            # Mock sophisticated QA response
            mock_ai.answer_question = AsyncMock(return_value={
                "answer": "Based on your transaction history, you spent $2,450 on office supplies in Q3 2024, which is a 15% increase from Q2. The main vendors were Staples ($1,200), Amazon ($800), and Office Depot ($450).",
                "confidence": 0.92,
                "sources": [
                    {"type": "transactions", "count": 45, "date_range": "2024-07-01 to 2024-09-30"},
                    {"type": "rag_corpus", "name": "financial_insights_2024"}
                ],
                "follow_up_questions": [
                    "Would you like to see a breakdown by month?",
                    "Do you want to compare this with last year's spending?"
                ]
            })
            
            result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="qa",
                parameters={
                    "question": "How much did I spend on office supplies last quarter?",
                    "context_window": "3_months",
                    "include_sources": True,
                    "rag_corpus": "tenant_financial_history"
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "qa"
            assert "answer" in result["data"]
            assert result["data"]["confidence"] > 0.9
            assert len(result["data"]["sources"]) > 0
            assert "follow_up_questions" in result["data"]
            
            # Verify AI was called with proper context
            mock_ai.answer_question.assert_called_once()
            call_args = mock_ai.answer_question.call_args[1]
            assert "question" in call_args
            assert call_args["context_window"] == "3_months"
    
    async def test_metrics_operation_with_temporal_analysis(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test metrics calculation with temporal analysis and accuracy tracking."""
        # Setup realistic transaction data with corrections
        corrected_count = 0
        for i, txn in enumerate(test_transactions):
            txn.confidence_score = 0.75 + (i * 0.03)
            txn.user_corrected = i % 4 == 0  # 25% corrected
            txn.original_category_label = f"Category_{i}"
            txn.ai_category_label = f"Category_{i if i % 4 != 0 else i + 1}"
            if txn.user_corrected:
                corrected_count += 1
        
        with patch.object(db_session, 'execute') as mock_execute:
            # Mock transaction query
            mock_txn_result = Mock()
            mock_txn_result.scalars.return_value.all.return_value = test_transactions
            
            # Mock corrected count
            mock_corrected_result = Mock()
            mock_corrected_result.scalar.return_value = corrected_count
            
            # Mock temporal stats
            mock_temporal_result = Mock()
            mock_temporal_result.all.return_value = [
                ("2024-07", 100, 85, 0.85),
                ("2024-08", 120, 105, 0.875),
                ("2024-09", 110, 99, 0.90)
            ]
            
            mock_execute.side_effect = [
                mock_txn_result,
                mock_corrected_result,
                mock_temporal_result
            ]
            
            result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="metrics",
                parameters={
                    "metric_type": "accuracy",
                    "period": "last_quarter",
                    "include_temporal": True,
                    "breakdown_by": ["category", "confidence_band"]
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "metrics"
            assert "accuracy_metrics" in result["data"]
            assert result["data"]["accuracy_metrics"]["precision"] > 0
            assert result["data"]["accuracy_metrics"]["recall"] > 0
            assert "temporal_analysis" in result["data"]
            assert len(result["data"]["temporal_analysis"]) == 3
            assert result["data"]["summary"]["total_transactions"] == len(test_transactions)
            assert result["data"]["summary"]["corrections_rate"] == 0.25


class TestUnifiedReportingTool:
    """Test the unified reporting tool that handles insights, reports, charts, and exports."""
    
    async def test_insights_generation_with_ai_analysis(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test AI-powered insights generation with real transaction patterns."""
        # Setup transaction patterns
        for i, txn in enumerate(test_transactions):
            txn.amount = -100 * (1 + (i % 3))  # Pattern: 100, 200, 300, repeat
            txn.date = datetime.now() - timedelta(days=i)
            txn.get_display_category = Mock(
                return_value="Recurring" if i % 3 == 0 else "Variable"
            )
        
        with patch.object(db_session, 'execute') as mock_execute:
            with patch('giki_ai_api.domains.intelligence.unified_tools.UnifiedAIService') as mock_ai_class:
                mock_execute.return_value.scalars.return_value.all.return_value = test_transactions
                
                mock_ai = Mock()
                mock_ai_class.return_value = mock_ai
                mock_ai.generate_insights = AsyncMock(return_value={
                    "executive_summary": "Spending shows clear patterns with recurring expenses every 3 days.",
                    "key_findings": [
                        "33% of transactions are recurring expenses averaging $100",
                        "Variable expenses show 2x volatility compared to recurring",
                        "Weekend spending is 40% higher than weekdays"
                    ],
                    "recommendations": [
                        {"priority": "high", "action": "Consolidate recurring payments to save on fees"},
                        {"priority": "medium", "action": "Review weekend spending habits"}
                    ],
                    "anomalies": [
                        {"date": "2024-09-15", "description": "Unusual spike in variable expenses"}
                    ]
                })
                
                result = await unified_reporting_tool_function(
                    tenant_id=test_tenant.id,
                    db=db_session,
                    operation="insights",
                    parameters={
                        "insight_type": "spending_patterns",
                        "analysis_depth": "comprehensive",
                        "time_period": "last_30_days",
                        "include_anomalies": True,
                        "ai_model": "advanced"
                    }
                )
                
                assert result["success"] is True
                assert result["operation"] == "insights"
                assert "executive_summary" in result["data"]
                assert len(result["data"]["key_findings"]) >= 3
                assert len(result["data"]["recommendations"]) >= 2
                assert result["data"]["recommendations"][0]["priority"] == "high"
                assert "anomalies" in result["data"]
    
    async def test_report_generation_with_multiple_sections(
        self,
        db_session,
        test_tenant
    ):
        """Test comprehensive financial report generation."""
        with patch('giki_ai_api.domains.reports.service.ReportService') as mock_report_service_class:
            mock_service = Mock()
            mock_report_service_class.return_value = mock_service
            
            # Mock sophisticated report generation
            mock_service.generate_report = AsyncMock(return_value={
                "report_id": "rpt_2024_q3_financial",
                "title": "Q3 2024 Financial Analysis Report",
                "generated_at": datetime.now().isoformat(),
                "sections": [
                    {
                        "title": "Executive Summary",
                        "content": "Q3 showed 15% reduction in operational expenses...",
                        "charts": ["expense_trend_q3"]
                    },
                    {
                        "title": "Category Analysis",
                        "content": "Top 5 categories accounted for 78% of total spend...",
                        "data_tables": [
                            {
                                "name": "category_breakdown",
                                "headers": ["Category", "Amount", "% of Total", "YoY Change"],
                                "rows": [
                                    ["Office Supplies", "$12,450", "23%", "+5%"],
                                    ["Travel", "$10,200", "19%", "-12%"]
                                ]
                            }
                        ]
                    },
                    {
                        "title": "Vendor Analysis",
                        "content": "Vendor consolidation opportunity identified...",
                        "recommendations": ["Consider negotiating volume discounts with top 3 vendors"]
                    }
                ],
                "export_formats": ["pdf", "excel", "powerpoint"],
                "shareable_link": "https://app.giki.ai/reports/shared/rpt_2024_q3_financial"
            })
            
            result = await unified_reporting_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="report",
                parameters={
                    "report_type": "quarterly_financial",
                    "period": "2024_Q3",
                    "sections": ["executive_summary", "category_analysis", "vendor_analysis"],
                    "include_charts": True,
                    "include_recommendations": True,
                    "format_options": {
                        "currency": "USD",
                        "decimal_places": 2,
                        "percentage_format": "0.0%"
                    }
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "report"
            assert "report_id" in result["data"]
            assert len(result["data"]["sections"]) >= 3
            assert "data_tables" in result["data"]["sections"][1]
            assert "shareable_link" in result["data"]
    
    async def test_chart_generation_with_interactive_options(
        self,
        db_session,
        test_tenant
    ):
        """Test chart generation with real data visualization options."""
        with patch('giki_ai_api.domains.intelligence.unified_tools.generate_chart_data') as mock_chart_gen:
            mock_chart_gen.return_value = {
                "chart_id": "chart_spending_trend_2024",
                "type": "interactive_line",
                "title": "Monthly Spending Trend - 2024",
                "data": {
                    "labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    "datasets": [
                        {
                            "label": "Total Spending",
                            "data": [15000, 14500, 16200, 15800, 14900, 15500],
                            "trend": "stable",
                            "forecast": [15400, 15600, 15500]
                        },
                        {
                            "label": "Discretionary",
                            "data": [3000, 2800, 3500, 3200, 2900, 3100],
                            "trend": "volatile"
                        }
                    ]
                },
                "interactive_features": {
                    "zoom": True,
                    "pan": True,
                    "tooltips": "detailed",
                    "drill_down": "category_level",
                    "export_options": ["png", "svg", "csv"]
                },
                "insights": {
                    "trend_analysis": "Spending remains stable with 3.3% variance",
                    "seasonality": "Minor uptick in March correlates with Q1 close"
                }
            }
            
            result = await unified_reporting_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="chart",
                parameters={
                    "chart_type": "spending_trend",
                    "visualization": "interactive_line",
                    "time_range": {
                        "start": "2024-01-01",
                        "end": "2024-06-30"
                    },
                    "grouping": "monthly",
                    "include_forecast": True,
                    "interactive": True,
                    "comparison": "discretionary_vs_total"
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "chart"
            assert result["data"]["type"] == "interactive_line"
            assert len(result["data"]["data"]["datasets"]) >= 2
            assert "forecast" in result["data"]["data"]["datasets"][0]
            assert result["data"]["interactive_features"]["drill_down"] == "category_level"
            assert "insights" in result["data"]
    
    async def test_export_operation_with_multiple_formats(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test export functionality with real-world format requirements."""
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_txn_service:
            with patch('giki_ai_api.domains.categories.service.CategoryService') as mock_cat_service:
                # Mock services
                mock_txn_service.return_value.get_transactions = AsyncMock(
                    return_value=test_transactions
                )
                mock_cat_service.return_value.get_categories = AsyncMock(
                    return_value=[]
                )
                
                # Setup transaction data
                for txn in test_transactions:
                    txn.get_display_category = Mock(return_value="Test Category")
                
                result = await unified_reporting_tool_function(
                    tenant_id=test_tenant.id,
                    db=db_session,
                    operation="export",
                    parameters={
                        "export_type": "transactions",
                        "format": "excel",
                        "options": {
                            "include_formulas": True,
                            "add_summary_sheet": True,
                            "styling": "professional",
                            "filters": {
                                "date_range": {
                                    "start": "2024-01-01",
                                    "end": "2024-12-31"
                                }
                            },
                            "columns": [
                                "date", "description", "amount", "category",
                                "gl_code", "confidence", "notes"
                            ],
                            "grouping": "monthly",
                            "subtotals": True
                        }
                    }
                )
                
                assert result["success"] is True
                assert result["operation"] == "export"
                assert result["data"]["format"] == "excel"
                assert result["data"]["records_exported"] == len(test_transactions)
                assert "file_path" in result["data"]
                assert result["data"]["export_options"]["include_formulas"] is True


class TestUnifiedUIOperationsTool:
    """Test the unified UI operations tool for upload, navigation, and voice."""
    
    async def test_upload_operation_with_schema_detection(
        self,
        db_session,
        test_tenant
    ):
        """Test file upload with automatic schema detection."""
        with patch('giki_ai_api.domains.files.schema_interpretation_agent.suggest_schema_mapping_tool_function') as mock_schema:
            mock_schema.return_value = {
                "success": True,
                "suggestions": {
                    "date_column": "Transaction Date",
                    "amount_column": "Amount",
                    "description_column": "Description",
                    "category_column": "Category",
                    "confidence": 0.95,
                    "detected_format": "bank_statement",
                    "validation_warnings": []
                }
            }
            
            result = await unified_ui_operations_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="upload",
                parameters={
                    "action": "prepare",
                    "file_info": {
                        "name": "Chase_Checking_2024.xlsx",
                        "size": 245678,
                        "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    },
                    "auto_detect_schema": True,
                    "validation_level": "strict"
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "upload"
            assert result["data"]["schema_detected"] is True
            assert result["data"]["confidence"] > 0.9
            assert result["data"]["detected_format"] == "bank_statement"
    
    async def test_navigation_operation_with_context_preservation(
        self,
        db_session,
        test_tenant
    ):
        """Test navigation with context and state preservation."""
        result = await unified_ui_operations_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            operation="navigate",
            parameters={
                "destination": "transactions",
                "context": {
                    "filter": "uncategorized",
                    "date_range": "last_30_days",
                    "highlight": ["high_amount", "low_confidence"]
                },
                "preserve_state": True,
                "open_mode": "same_tab"
            }
        )
        
        assert result["success"] is True
        assert result["operation"] == "navigate"
        assert result["data"]["url"] == "/transactions"
        assert "filter=uncategorized" in result["data"]["full_url"]
        assert result["data"]["context_preserved"] is True
    
    async def test_voice_operation_with_multimodal_response(
        self,
        db_session,
        test_tenant
    ):
        """Test voice interaction with multimodal response generation."""
        with patch('giki_ai_api.domains.intelligence.unified_tools.process_voice_command') as mock_voice:
            mock_voice.return_value = {
                "command_understood": True,
                "intent": "financial_summary",
                "parameters": {
                    "period": "this_month",
                    "detail_level": "summary"
                },
                "response": {
                    "text": "This month you've spent $4,250 across 47 transactions. Your largest category is Travel at $1,200.",
                    "audio_url": "https://audio.giki.ai/responses/resp_123.mp3",
                    "visual_elements": [
                        {
                            "type": "mini_chart",
                            "data": {"Travel": 1200, "Food": 800, "Office": 650, "Other": 1600}
                        }
                    ],
                    "follow_up_actions": [
                        {"label": "View details", "action": "navigate", "target": "/reports/monthly"},
                        {"label": "Compare to last month", "action": "voice", "prompt": "Compare spending"}
                    ]
                },
                "confidence": 0.94
            }
            
            result = await unified_ui_operations_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="voice",
                parameters={
                    "action": "process",
                    "audio_data": b"fake_audio_for_testing",
                    "response_format": "multimodal",
                    "include_visuals": True,
                    "language": "en-US"
                }
            )
            
            assert result["success"] is True
            assert result["operation"] == "voice"
            assert result["data"]["intent"] == "financial_summary"
            assert "audio_url" in result["data"]["response"]
            assert len(result["data"]["response"]["visual_elements"]) > 0
            assert len(result["data"]["response"]["follow_up_actions"]) > 0


class TestUnifiedToolsIntegration:
    """Integration tests for unified tools working together."""
    
    async def test_complete_workflow_with_all_unified_tools(
        self,
        db_session,
        test_tenant,
        test_transactions
    ):
        """Test a complete workflow using all three unified tools."""
        # Step 1: Query data using unified data access
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_txn_service:
            mock_txn_service.return_value.get_transactions = AsyncMock(
                return_value=test_transactions
            )
            
            query_result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="query",
                parameters={
                    "data_type": "transactions",
                    "filters": {"date_range": {"start": "2024-09-01", "end": "2024-09-30"}}
                }
            )
            
            assert query_result["success"] is True
            transaction_data = query_result["data"]["transactions"]
        
        # Step 2: Generate insights from the data
        with patch('giki_ai_api.domains.intelligence.unified_tools.UnifiedAIService') as mock_ai:
            mock_ai.return_value.generate_insights = AsyncMock(return_value={
                "executive_summary": "September spending analysis complete",
                "key_findings": ["Spending increased by 12% compared to August"],
                "recommendations": [{"priority": "high", "action": "Review recurring subscriptions"}]
            })
            
            insights_result = await unified_reporting_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="insights",
                parameters={
                    "insight_type": "monthly_analysis",
                    "data": transaction_data
                }
            )
            
            assert insights_result["success"] is True
        
        # Step 3: Navigate to detailed view
        nav_result = await unified_ui_operations_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            operation="navigate",
            parameters={
                "destination": "reports",
                "context": {
                    "report_type": "monthly",
                    "month": "2024-09",
                    "highlight_insights": True
                }
            }
        )
        
        assert nav_result["success"] is True
        assert all(result["success"] for result in [query_result, insights_result, nav_result])
    
    async def test_error_handling_across_unified_tools(
        self,
        db_session,
        test_tenant
    ):
        """Test that all unified tools handle errors gracefully."""
        # Test data access tool error
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service:
            mock_service.side_effect = Exception("Database connection failed")
            
            result = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="query",
                parameters={"data_type": "transactions"}
            )
            
            assert result["success"] is False
            assert "error" in result
            assert "Database" in result["error"]
        
        # Test reporting tool error
        with patch('giki_ai_api.domains.reports.service.ReportService') as mock_report:
            mock_report.side_effect = Exception("Report generation failed")
            
            result = await unified_reporting_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="report",
                parameters={"report_type": "quarterly"}
            )
            
            assert result["success"] is False
            assert "error" in result
        
        # Test UI operations tool error
        result = await unified_ui_operations_tool_function(
            tenant_id=test_tenant.id,
            db=db_session,
            operation="invalid_operation",
            parameters={}
        )
        
        assert result["success"] is False
        assert "Unsupported operation" in result["error"]
    
    async def test_tenant_isolation_in_unified_tools(
        self,
        db_session,
        test_tenant,
        alt_tenant
    ):
        """Test that unified tools properly isolate data between tenants."""
        # Create tenant-specific test data
        tenant_1_data = [Mock(tenant_id=test_tenant.id, id=i) for i in range(3)]
        tenant_2_data = [Mock(tenant_id=alt_tenant.id, id=i+10) for i in range(2)]
        
        with patch('giki_ai_api.domains.transactions.service.TransactionService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            
            # Service should only return data for the requesting tenant
            def get_transactions_for_tenant(tenant_id, **kwargs):
                if tenant_id == test_tenant.id:
                    return tenant_1_data
                elif tenant_id == alt_tenant.id:
                    return tenant_2_data
                return []
            
            mock_service.get_transactions = AsyncMock(side_effect=get_transactions_for_tenant)
            
            # Query for tenant 1
            result1 = await unified_data_access_tool_function(
                tenant_id=test_tenant.id,
                db=db_session,
                operation="query",
                parameters={"data_type": "transactions"}
            )
            
            # Query for tenant 2
            result2 = await unified_data_access_tool_function(
                tenant_id=alt_tenant.id,
                db=db_session,
                operation="query",
                parameters={"data_type": "transactions"}
            )
            
            # Verify tenant isolation
            assert len(result1["data"]["transactions"]) == 3
            assert len(result2["data"]["transactions"]) == 2
            assert result1["data"]["transactions"][0]["id"] != result2["data"]["transactions"][0]["id"]


@pytest.fixture
def alt_tenant(db_session):
    """Create an alternative tenant for isolation testing."""
    from giki_ai_api.domains.auth.models import Tenant
    
    alt_tenant = Tenant(
        id=999,
        name="Alternative Test Tenant",
        status="active"
    )
    db_session.add(alt_tenant)
    db_session.commit()
    return alt_tenant