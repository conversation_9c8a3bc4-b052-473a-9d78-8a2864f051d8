"""
Financial Reporting E2E Workflow Integration Tests
==================================================

Comprehensive end-to-end integration tests for financial reporting workflow including:
- Standard financial reports (spending by category, income/expense, trends)
- GL code integration and accounting system compatibility 
- Custom report generation and configuration management
- Multi-tenant data isolation and security
- Performance validation (<200ms requirements)
- Financial-grade accuracy and export capabilities
"""

import time
from datetime import date, datetime, timedelta
from decimal import Decimal

import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.main import app
from giki_ai_api.domains.categories.models import Category
from giki_ai_api.domains.reports.models import CustomReport
from giki_ai_api.domains.transactions.models import Entity, Transaction

pytestmark = pytest.mark.integration


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def auth_headers(client, test_user):
    """Get authentication headers for API requests."""
    response = client.post(
        "/auth/token",
        data={
            "username": test_user.email,
            "password": "secure_test_password_2024!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def second_tenant_auth_headers(client, second_tenant_user):
    """Get authentication headers for second tenant user."""
    response = client.post(
        "/auth/token",
        data={
            "username": second_tenant_user.email,
            "password": "SecondPassword123!"
        }
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def financial_reporting_data(db_session: AsyncSession, test_tenant):
    """Create comprehensive financial test data for reporting tests."""
    # Create hierarchical GL-coded categories
    categories = [
        Category(
            id=2001,
            name="Operating Expenses",
            tenant_id=test_tenant.id,
            path="Operating Expenses",
            level=0,
            gl_code="4000",
            gl_account_name="Operating Expenses",
            gl_account_type="Expense"
        ),
        Category(
            id=2002,
            name="Technology Expenses",
            parent_id=2001,
            tenant_id=test_tenant.id,
            path="Operating Expenses > Technology Expenses",
            level=1,
            gl_code="4100",
            gl_account_name="Technology and Software",
            gl_account_type="Expense"
        ),
        Category(
            id=2003,
            name="Office Expenses",
            parent_id=2001,
            tenant_id=test_tenant.id,
            path="Operating Expenses > Office Expenses",
            level=1,
            gl_code="4200",
            gl_account_name="Office and Administrative",
            gl_account_type="Expense"
        ),
        Category(
            id=2004,
            name="Revenue",
            tenant_id=test_tenant.id,
            path="Revenue",
            level=0,
            gl_code="3000",
            gl_account_name="Operating Revenue",
            gl_account_type="Revenue"
        ),
        Category(
            id=2005,
            name="Professional Services Revenue",
            parent_id=2004,
            tenant_id=test_tenant.id,
            path="Revenue > Professional Services Revenue",
            level=1,
            gl_code="3100",
            gl_account_name="Professional Services Income",
            gl_account_type="Revenue"
        )
    ]
    
    for category in categories:
        db_session.add(category)
    
    await db_session.commit()
    
    # Create entities for vendor tracking
    entities = [
        Entity(
            id="entity_aws",
            name="Amazon Web Services",
            normalized_name="amazon web services",
            entity_type="vendor",
            tenant_id=test_tenant.id,
            confidence_score=0.95,
            extraction_method="ai"
        ),
        Entity(
            id="entity_microsoft",
            name="Microsoft Corporation",
            normalized_name="microsoft corporation",
            entity_type="vendor",
            tenant_id=test_tenant.id,
            confidence_score=0.98,
            extraction_method="ai"
        ),
        Entity(
            id="entity_client_abc",
            name="ABC Consulting Corp",
            normalized_name="abc consulting corp",
            entity_type="customer",
            tenant_id=test_tenant.id,
            confidence_score=0.92,
            extraction_method="ai"
        )
    ]
    
    for entity in entities:
        db_session.add(entity)
    
    await db_session.commit()
    
    # Create comprehensive transaction dataset spanning multiple months
    
    # Q1 2024 - January
    january_transactions = [
        Transaction(
            id="report_tx_001",
            description="AWS Cloud Services - Production",
            amount=Decimal("-850.00"),
            date=date(2024, 1, 15),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2002,  # Technology Expenses
            category_path="Operating Expenses > Technology Expenses",
            entity_id="entity_aws",
            transaction_type="expense",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_002",
            description="Microsoft Office 365 Subscription",
            amount=Decimal("-120.00"),
            date=date(2024, 1, 20),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2002,  # Technology Expenses
            category_path="Operating Expenses > Technology Expenses",
            entity_id="entity_microsoft",
            transaction_type="expense",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_003",
            description="Professional Services - ABC Corp",
            amount=Decimal("5000.00"),
            date=date(2024, 1, 25),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2005,  # Professional Services Revenue
            category_path="Revenue > Professional Services Revenue",
            entity_id="entity_client_abc",
            transaction_type="income",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_004",
            description="Office Supplies - Staples",
            amount=Decimal("-75.50"),
            date=date(2024, 1, 28),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2003,  # Office Expenses
            category_path="Operating Expenses > Office Expenses",
            transaction_type="expense",
            is_categorized=True
        )
    ]
    
    # February transactions
    february_transactions = [
        Transaction(
            id="report_tx_005",
            description="AWS Cloud Services - February",
            amount=Decimal("-920.00"),
            date=date(2024, 2, 15),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2002,
            category_path="Operating Expenses > Technology Expenses",
            entity_id="entity_aws",
            transaction_type="expense",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_006",
            description="Consulting Revenue - February",
            amount=Decimal("7500.00"),
            date=date(2024, 2, 20),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2005,
            category_path="Revenue > Professional Services Revenue",
            entity_id="entity_client_abc",
            transaction_type="income",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_007",
            description="Office Rent - February",
            amount=Decimal("-2200.00"),
            date=date(2024, 2, 1),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2003,
            category_path="Operating Expenses > Office Expenses",
            transaction_type="expense",
            is_categorized=True
        )
    ]
    
    # March transactions
    march_transactions = [
        Transaction(
            id="report_tx_008",
            description="AWS Cloud Services - March",
            amount=Decimal("-1050.00"),
            date=date(2024, 3, 15),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2002,
            category_path="Operating Expenses > Technology Expenses",
            entity_id="entity_aws",
            transaction_type="expense",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_009",
            description="Large Contract - March",
            amount=Decimal("12000.00"),
            date=date(2024, 3, 25),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2005,
            category_path="Revenue > Professional Services Revenue",
            entity_id="entity_client_abc",
            transaction_type="income",
            is_categorized=True
        ),
        Transaction(
            id="report_tx_010",
            description="Equipment Purchase - Laptops",
            amount=Decimal("-3500.00"),
            date=date(2024, 3, 10),
            tenant_id=test_tenant.id,
            upload_id="reporting_test",
            category_id=2002,
            category_path="Operating Expenses > Technology Expenses",
            transaction_type="expense",
            is_categorized=True
        )
    ]
    
    all_transactions = january_transactions + february_transactions + march_transactions
    
    for transaction in all_transactions:
        db_session.add(transaction)
    
    await db_session.commit()
    
    # Refresh all objects to get updated data
    for transaction in all_transactions:
        await db_session.refresh(transaction)
    
    for category in categories:
        await db_session.refresh(category)
        
    for entity in entities:
        await db_session.refresh(entity)
    
    return {
        "categories": categories,
        "entities": entities,
        "transactions": all_transactions,
        "summary": {
            "total_income": Decimal("24500.00"),  # 5000 + 7500 + 12000
            "total_expenses": Decimal("8735.50"),  # Sum of all expenses
            "net_income": Decimal("15764.50")
        }
    }


class TestSpendingByCategoryReport:
    """Test spending by category report with GL code integration."""
    
    async def test_spending_by_category_success(self, client, auth_headers, financial_reporting_data):
        """Test successful spending by category report generation."""
        start_time = time.time()
        
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert report structure
        assert "items" in data
        assert "total_records" in data
        assert isinstance(data["items"], list)
        assert len(data["items"]) > 0
        
        # Verify category data with GL code integration
        category_paths = [item["category_path"] for item in data["items"]]
        assert "Operating Expenses > Technology Expenses" in category_paths
        assert "Operating Expenses > Office Expenses" in category_paths
        
        # Verify financial accuracy
        tech_expenses = next(
            (item for item in data["items"] 
             if item["category_path"] == "Operating Expenses > Technology Expenses"),
            None
        )
        
        if tech_expenses:
            # Should aggregate AWS + Microsoft + Equipment purchases
            expected_tech_total = 850.00 + 120.00 + 920.00 + 1050.00 + 3500.00  # 6440.00
            assert abs(tech_expenses["total_amount"] - expected_tech_total) < 1.0
            assert tech_expenses["transaction_count"] >= 5
            
        # Performance validation
        assert response_time_ms < 200, f"Spending by category report took {response_time_ms:.2f}ms, exceeds 200ms requirement"
        
    async def test_spending_by_category_with_date_filter(self, client, auth_headers, financial_reporting_data):
        """Test spending by category with date range filtering."""
        # Filter for February only
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers,
            params={
                "start_date": "2024-02-01",
                "end_date": "2024-02-29"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should only include February transactions
        assert len(data["items"]) > 0
        
        # Verify amounts match February data only
        tech_item = next(
            (item for item in data["items"] 
             if "Technology Expenses" in item["category_path"]),
            None
        )
        
        if tech_item:
            assert abs(tech_item["total_amount"] - 920.00) < 1.0  # Only February AWS
            
    async def test_spending_by_category_gl_code_accuracy(self, client, auth_headers, financial_reporting_data, db_session, test_tenant):
        """Test that category GL codes are properly reflected in reports."""
        # Get the report data
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify GL code mapping by checking category data
        for item in data["items"]:
            category_path = item["category_path"]
            
            # Look up the category to verify GL code integration
            result = await db_session.execute(
                select(Category).where(
                    Category.path == category_path,
                    Category.tenant_id == test_tenant.id
                )
            )
            category = result.scalar_one_or_none()
            
            if category:
                # Verify GL code exists for financial categories
                if "Technology Expenses" in category_path:
                    assert category.gl_code == "4100"
                    assert category.gl_account_type == "Expense"
                elif "Office Expenses" in category_path:
                    assert category.gl_code == "4200"
                    assert category.gl_account_type == "Expense"


class TestSpendingByEntityReport:
    """Test spending by entity/vendor report functionality."""
    
    async def test_spending_by_entity_success(self, client, auth_headers, financial_reporting_data):
        """Test successful spending by entity report generation."""
        response = client.get(
            "/reports/spending-by-entity",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert report structure
        assert "items" in data
        assert "total_records" in data
        assert isinstance(data["items"], list)
        
        # Verify entity aggregation
        entity_names = [item["entity_name"] for item in data["items"]]
        assert "Amazon Web Services" in entity_names
        
        # Verify AWS spending aggregation (3 months of AWS charges)
        aws_item = next(
            (item for item in data["items"] 
             if "Amazon Web Services" in item["entity_name"]),
            None
        )
        
        if aws_item:
            expected_aws_total = 850.00 + 920.00 + 1050.00  # 2820.00
            assert abs(aws_item["total_amount"] - expected_aws_total) < 1.0
            assert aws_item["transaction_count"] == 3
            
    async def test_entity_extraction_accuracy(self, client, auth_headers, financial_reporting_data):
        """Test accuracy of entity extraction and aggregation."""
        response = client.get(
            "/reports/spending-by-entity",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should include all major entities
        entity_names = [item["entity_name"] for item in data["items"]]
        
        # Check for major vendors
        has_aws = any("Amazon Web Services" in name or "AWS" in name for name in entity_names)
        has_microsoft = any("Microsoft" in name for name in entity_names)
        
        assert has_aws, f"AWS not found in entities: {entity_names}"
        assert has_microsoft, f"Microsoft not found in entities: {entity_names}"


class TestIncomeExpenseSummaryReport:
    """Test income vs expense summary report functionality."""
    
    async def test_income_expense_summary_success(self, client, auth_headers, financial_reporting_data):
        """Test successful income vs expense summary generation."""
        response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert report structure
        assert "total_income" in data
        assert "total_expenses" in data
        assert "net_income_loss" in data
        
        # Verify financial calculations
        expected_income = 24500.00  # 5000 + 7500 + 12000
        expected_expenses = 8735.50  # Sum of all expense amounts
        expected_net = expected_income - expected_expenses
        
        # Allow for small floating point differences
        assert abs(data["total_income"] - expected_income) < 10.0
        assert abs(data["total_expenses"] - expected_expenses) < 10.0
        assert abs(data["net_income_loss"] - expected_net) < 10.0
        
        # Verify positive net income
        assert data["net_income_loss"] > 0, "Should show positive net income"
        
    async def test_income_expense_date_filtering(self, client, auth_headers, financial_reporting_data):
        """Test income vs expense summary with date filtering."""
        # Test February only
        response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers,
            params={
                "start_date": "2024-02-01",
                "end_date": "2024-02-29"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # February: 7500 income, 3120 expenses (920 + 2200)
        assert abs(data["total_income"] - 7500.00) < 1.0
        assert abs(data["total_expenses"] - 3120.00) < 1.0
        assert data["net_income_loss"] > 4000.0  # Should be positive
        
    async def test_income_expense_performance(self, client, auth_headers, financial_reporting_data):
        """Test income vs expense summary performance."""
        # Warm up endpoint
        client.get("/reports/income-expense-summary", headers=auth_headers)
        
        # Measure performance
        start_time = time.time()
        response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers
        )
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time_ms < 200, f"Income/expense summary took {response_time_ms:.2f}ms, exceeds 200ms requirement"


class TestMonthlyTrendsReport:
    """Test monthly trends report functionality."""
    
    async def test_monthly_trends_success(self, client, auth_headers, financial_reporting_data):
        """Test successful monthly trends report generation."""
        response = client.get(
            "/reports/monthly-trends",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert report structure
        assert "items" in data
        assert "total_months" in data
        assert isinstance(data["items"], list)
        assert data["total_months"] >= 3  # Should have Jan, Feb, Mar
        
        # Verify monthly data structure
        for item in data["items"]:
            assert "month" in item
            assert "total_income" in item
            assert "total_expenses" in item
            assert "net_amount" in item
            assert "transaction_count" in item
            
        # Find March data (highest revenue month)
        march_data = next(
            (item for item in data["items"] if item["month"] == "2024-03"),
            None
        )
        
        if march_data:
            # March: 12000 income, 4550 expenses (1050 + 3500)
            assert abs(march_data["total_income"] - 12000.00) < 1.0
            assert abs(march_data["total_expenses"] - 4550.00) < 10.0
            assert march_data["net_amount"] > 7000.0  # Strong positive
            
    async def test_monthly_trends_chronological_order(self, client, auth_headers, financial_reporting_data):
        """Test that monthly trends are in chronological order."""
        response = client.get(
            "/reports/monthly-trends",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        months = [item["month"] for item in data["items"]]
        
        # Should be in chronological order
        assert months == sorted(months)
        
        # Should include the expected months
        expected_months = ["2024-01", "2024-02", "2024-03"]
        for month in expected_months:
            assert month in months


class TestCustomReportGeneration:
    """Test custom report generation and management."""
    
    async def test_custom_report_generation_success(self, client, auth_headers, financial_reporting_data):
        """Test successful custom report generation."""
        custom_report_request = {
            "report_type": "spending_analysis",
            "filters": {
                "amount_range": {"min": 100.0, "max": 10000.0}
            },
            "grouping": ["category_path"],
            "aggregations": ["sum", "count"],
            "date_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-03-31"
            },
            "include_raw_data": False
        }
        
        response = client.post(
            "/reports/custom/generate",
            headers=auth_headers,
            json=custom_report_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert custom report structure
        assert "report_type" in data
        assert "filters" in data
        assert "date_range" in data
        assert "data" in data
        assert "metadata" in data
        assert "generated_at" in data
        
        # Verify report type
        assert data["report_type"] == "spending_analysis"
        
        # Verify data structure
        assert isinstance(data["data"], dict)
        
    async def test_save_custom_report_success(self, client, auth_headers, db_session, test_tenant, test_user):
        """Test saving custom report configuration."""
        save_request = {
            "name": "Monthly Technology Spending",
            "description": "Track monthly technology expenses with GL code 4100",
            "report_type": "category_spending",
            "configuration": {
                "filters": {
                    "category_gl_code": "4100",
                    "date_range": "monthly"
                },
                "grouping": ["month", "vendor"],
                "visualizations": ["line_chart", "bar_chart"]
            }
        }
        
        response = client.post(
            "/reports/custom/save",
            headers=auth_headers,
            json=save_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert saved report structure
        assert "id" in data
        assert data["name"] == "Monthly Technology Spending"
        assert data["report_type"] == "category_spending"
        assert "configuration" in data
        assert "created_at" in data
        
        # Verify saved in database
        result = await db_session.execute(
            select(CustomReport).where(
                CustomReport.id == data["id"],
                CustomReport.tenant_id == test_tenant.id
            )
        )
        saved_report = result.scalar_one_or_none()
        
        assert saved_report is not None
        assert saved_report.name == "Monthly Technology Spending"
        assert saved_report.tenant_id == test_tenant.id
        assert saved_report.user_id == test_user.id
        
    async def test_list_custom_reports_success(self, client, auth_headers):
        """Test listing saved custom reports."""
        response = client.get(
            "/reports/custom/list",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Assert list structure
        assert "reports" in data
        assert "total" in data
        assert isinstance(data["reports"], list)
        assert data["total"] >= 0
        
    async def test_get_custom_report_by_id(self, client, auth_headers, db_session, test_tenant, test_user):
        """Test retrieving a specific custom report."""
        # First create a report to retrieve
        custom_report = CustomReport(
            tenant_id=test_tenant.id,
            user_id=test_user.id,
            name="Test GL Code Report",
            description="Test report for GL code integration",
            report_type="gl_analysis",
            configuration={
                "gl_codes": ["4100", "4200"],
                "period": "quarterly"
            }
        )
        
        db_session.add(custom_report)
        await db_session.commit()
        await db_session.refresh(custom_report)
        
        # Retrieve the report
        response = client.get(
            f"/reports/custom/{custom_report.id}",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["id"] == custom_report.id
        assert data["name"] == "Test GL Code Report"
        assert data["report_type"] == "gl_analysis"
        assert "gl_codes" in data["configuration"]


class TestMultiTenantReportIsolation:
    """Test multi-tenant isolation in financial reporting."""
    
    async def test_spending_report_tenant_isolation(self, client, auth_headers, second_tenant_auth_headers, financial_reporting_data, db_session, second_tenant):
        """Test that spending reports are isolated by tenant."""
        # Create transaction for second tenant
        second_tenant_transaction = Transaction(
            id="second_tenant_report_tx",
            description="Second Tenant Transaction",
            amount=Decimal("-500.00"),
            date=date(2024, 3, 15),
            tenant_id=second_tenant.id,
            upload_id="second_tenant_test",
            transaction_type="expense"
        )
        db_session.add(second_tenant_transaction)
        await db_session.commit()
        
        # First tenant report
        response1 = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        # Second tenant report
        response2 = client.get(
            "/reports/spending-by-category",
            headers=second_tenant_auth_headers
        )
        
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        data1 = response1.json()
        data2 = response2.json()
        
        # Verify data isolation
        # First tenant should have comprehensive data
        assert len(data1["items"]) > 0
        
        # Second tenant should have different/limited data
        category_paths_1 = [item["category_path"] for item in data1["items"]]
        category_paths_2 = [item["category_path"] for item in data2["items"]]
        
        # Tenant data should be isolated
        if len(data2["items"]) > 0:
            # If second tenant has categories, they should be different
            set(category_paths_1) & set(category_paths_2)
            # Some overlap is possible if both tenants have similar category names
            # but the amounts should be different
            
    async def test_custom_report_tenant_isolation(self, client, auth_headers, second_tenant_auth_headers, db_session, test_tenant, second_tenant, test_user, second_tenant_user):
        """Test that custom reports are isolated by tenant."""
        # Create custom report for first tenant
        report1 = CustomReport(
            tenant_id=test_tenant.id,
            user_id=test_user.id,
            name="Tenant 1 Report",
            report_type="test_report",
            configuration={"test": "tenant1"}
        )
        
        # Create custom report for second tenant
        report2 = CustomReport(
            tenant_id=second_tenant.id,
            user_id=second_tenant_user.id,
            name="Tenant 2 Report",
            report_type="test_report",
            configuration={"test": "tenant2"}
        )
        
        db_session.add(report1)
        db_session.add(report2)
        await db_session.commit()
        
        # First tenant lists reports
        response1 = client.get(
            "/reports/custom/list",
            headers=auth_headers
        )
        
        # Second tenant lists reports
        response2 = client.get(
            "/reports/custom/list",
            headers=second_tenant_auth_headers
        )
        
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        data1 = response1.json()
        data2 = response2.json()
        
        # Verify isolation
        report_names_1 = [report["name"] for report in data1["reports"]]
        report_names_2 = [report["name"] for report in data2["reports"]]
        
        assert "Tenant 1 Report" in report_names_1
        assert "Tenant 1 Report" not in report_names_2
        assert "Tenant 2 Report" in report_names_2
        assert "Tenant 2 Report" not in report_names_1


class TestReportPerformanceValidation:
    """Test performance requirements for financial reports."""
    
    async def test_all_reports_performance_requirements(self, client, auth_headers, financial_reporting_data):
        """Test that all report endpoints meet performance requirements."""
        report_endpoints = [
            "/reports/spending-by-category",
            "/reports/spending-by-entity",
            "/reports/income-expense-summary",
            "/reports/monthly-trends"
        ]
        
        for endpoint in report_endpoints:
            # Warm up endpoint
            client.get(endpoint, headers=auth_headers)
            
            # Measure performance
            response_times = []
            for _ in range(3):
                start_time = time.time()
                response = client.get(endpoint, headers=auth_headers)
                end_time = time.time()
                
                assert response.status_code == status.HTTP_200_OK
                response_times.append((end_time - start_time) * 1000)
            
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            # Performance assertions
            assert avg_response_time < 200, f"{endpoint} average time {avg_response_time:.2f}ms exceeds 200ms requirement"
            assert max_response_time < 500, f"{endpoint} max time {max_response_time:.2f}ms exceeds 500ms threshold"
            
    async def test_report_caching_performance(self, client, auth_headers, financial_reporting_data):
        """Test that report caching improves performance."""
        endpoint = "/reports/spending-by-category"
        
        # First request (cache miss)
        start_time = time.time()
        response1 = client.get(endpoint, headers=auth_headers)
        end_time = time.time()
        first_request_time = (end_time - start_time) * 1000
        
        # Second request (cache hit)
        start_time = time.time()
        response2 = client.get(endpoint, headers=auth_headers)
        end_time = time.time()
        second_request_time = (end_time - start_time) * 1000
        
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        # Second request should be faster (cached)
        # Allow some tolerance for system variance
        cache_improvement = first_request_time - second_request_time
        assert cache_improvement > 0 or second_request_time < 50, f"Caching didn't improve performance: first={first_request_time:.2f}ms, second={second_request_time:.2f}ms"


class TestGLCodeIntegrationAccuracy:
    """Test GL code integration accuracy in financial reports."""
    
    async def test_gl_code_mapping_in_reports(self, client, auth_headers, financial_reporting_data, db_session, test_tenant):
        """Test that GL codes are accurately reflected in report data."""
        # Get spending by category report
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify GL code integration by checking database mapping
        for item in data["items"]:
            category_path = item["category_path"]
            
            # Look up category GL code
            result = await db_session.execute(
                select(Category).where(
                    Category.path == category_path,
                    Category.tenant_id == test_tenant.id
                )
            )
            category = result.scalar_one_or_none()
            
            if category:
                # Verify GL code exists and follows accounting standards
                if category.gl_code:
                    assert len(category.gl_code) >= 4  # Standard GL code length
                    assert category.gl_code.isdigit()  # Should be numeric
                    
                    # Verify expense categories use 4xxx codes
                    if category.gl_account_type == "Expense":
                        assert category.gl_code.startswith("4")
                    
                    # Verify revenue categories use 3xxx codes
                    elif category.gl_account_type == "Revenue":
                        assert category.gl_code.startswith("3")
                        
    async def test_gl_account_type_consistency(self, client, auth_headers, financial_reporting_data, db_session, test_tenant):
        """Test that GL account types are consistent with transaction types."""
        # Get all transactions with categories
        result = await db_session.execute(
            select(Transaction, Category).join(
                Category, Transaction.category_id == Category.id
            ).where(
                Transaction.tenant_id == test_tenant.id,
                Transaction.is_categorized
            )
        )
        transaction_category_pairs = result.all()
        
        for transaction, category in transaction_category_pairs:
            if category.gl_account_type:
                # Expense transactions should map to Expense GL accounts
                if transaction.transaction_type == "expense":
                    assert category.gl_account_type == "Expense"
                
                # Income transactions should map to Revenue GL accounts
                elif transaction.transaction_type == "income":
                    assert category.gl_account_type == "Revenue"


class TestFinancialAccuracyAndIntegrity:
    """Test financial accuracy and data integrity in reports."""
    
    async def test_report_data_consistency(self, client, auth_headers, financial_reporting_data):
        """Test that report totals are consistent across different reports."""
        # Get spending by category
        spending_response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        # Get income/expense summary
        summary_response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers
        )
        
        assert spending_response.status_code == status.HTTP_200_OK
        assert summary_response.status_code == status.HTTP_200_OK
        
        spending_data = spending_response.json()
        summary_data = summary_response.json()
        
        # Calculate total spending from category report
        total_category_spending = sum(item["total_amount"] for item in spending_data["items"])
        
        # Should approximately match expenses in summary report
        summary_expenses = summary_data["total_expenses"]
        
        # Allow for some variance due to rounding and filtering differences
        difference = abs(total_category_spending - summary_expenses)
        variance_percent = (difference / summary_expenses) * 100 if summary_expenses > 0 else 0
        
        assert variance_percent < 5.0, f"Category spending ({total_category_spending}) differs significantly from summary expenses ({summary_expenses})"
        
    async def test_monthly_trends_data_accuracy(self, client, auth_headers, financial_reporting_data):
        """Test that monthly trends data accurately reflects transaction data."""
        # Get monthly trends
        trends_response = client.get(
            "/reports/monthly-trends",
            headers=auth_headers
        )
        
        # Get income/expense summary for validation
        summary_response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers
        )
        
        assert trends_response.status_code == status.HTTP_200_OK
        assert summary_response.status_code == status.HTTP_200_OK
        
        trends_data = trends_response.json()
        summary_data = summary_response.json()
        
        # Calculate totals from monthly data
        total_monthly_income = sum(item["total_income"] for item in trends_data["items"])
        total_monthly_expenses = sum(item["total_expenses"] for item in trends_data["items"])
        
        # Should match summary totals
        assert abs(total_monthly_income - summary_data["total_income"]) < 10.0
        assert abs(total_monthly_expenses - summary_data["total_expenses"]) < 10.0
        
    async def test_financial_precision_maintenance(self, client, auth_headers, financial_reporting_data):
        """Test that financial precision is maintained in calculations."""
        response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify precision in financial calculations
        income = data["total_income"]
        expenses = data["total_expenses"]
        net = data["net_income_loss"]
        
        # Net should equal income minus expenses with proper precision
        calculated_net = income - expenses
        precision_error = abs(net - calculated_net)
        
        assert precision_error < 0.01, f"Financial precision error: {precision_error} (net={net}, calculated={calculated_net})"


class TestErrorHandlingAndValidation:
    """Test error handling and validation in financial reporting."""
    
    async def test_unauthorized_report_access(self, client):
        """Test unauthorized access to report endpoints."""
        response = client.get("/reports/spending-by-category")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    async def test_invalid_date_range_handling(self, client, auth_headers):
        """Test handling of invalid date ranges."""
        # Invalid date format
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers,
            params={
                "start_date": "invalid-date",
                "end_date": "2024-03-31"
            }
        )
        
        # Should handle gracefully
        assert response.status_code in [status.HTTP_422_UNPROCESSABLE_ENTITY, status.HTTP_400_BAD_REQUEST]
        
    async def test_future_date_range_handling(self, client, auth_headers):
        """Test handling of future date ranges."""
        future_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d")
        
        response = client.get(
            "/reports/income-expense-summary",
            headers=auth_headers,
            params={
                "start_date": future_date,
                "end_date": future_date
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should return zero values for future dates
        assert data["total_income"] == 0.0
        assert data["total_expenses"] == 0.0
        assert data["net_income_loss"] == 0.0
        
    async def test_nonexistent_custom_report_handling(self, client, auth_headers):
        """Test handling of requests for non-existent custom reports."""
        response = client.get(
            "/reports/custom/99999",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestReportExportCapabilities:
    """Test report export and integration capabilities."""
    
    async def test_report_data_export_format(self, client, auth_headers, financial_reporting_data):
        """Test that report data is in exportable format."""
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify data structure is suitable for export
        assert isinstance(data["items"], list)
        
        for item in data["items"]:
            # Should have all required fields for export
            assert "category_path" in item
            assert "total_amount" in item
            assert "transaction_count" in item
            
            # Values should be properly typed
            assert isinstance(item["total_amount"], (int, float))
            assert isinstance(item["transaction_count"], int)
            assert isinstance(item["category_path"], str)
            
    async def test_gl_code_export_compatibility(self, client, auth_headers, financial_reporting_data, db_session, test_tenant):
        """Test that GL code data is export-ready for accounting systems."""
        # Get spending by category report
        response = client.get(
            "/reports/spending-by-category",
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify GL code integration for export
        gl_code_mappings = []
        
        for item in data["items"]:
            category_path = item["category_path"]
            
            # Look up GL code
            result = await db_session.execute(
                select(Category).where(
                    Category.path == category_path,
                    Category.tenant_id == test_tenant.id
                )
            )
            category = result.scalar_one_or_none()
            
            if category and category.gl_code:
                gl_mapping = {
                    "category_path": category_path,
                    "gl_code": category.gl_code,
                    "gl_account_name": category.gl_account_name,
                    "gl_account_type": category.gl_account_type,
                    "amount": item["total_amount"]
                }
                gl_code_mappings.append(gl_mapping)
        
        # Should have GL code mappings ready for export
        assert len(gl_code_mappings) > 0
        
        # Verify export format compatibility
        for mapping in gl_code_mappings:
            assert all(key in mapping for key in ["gl_code", "gl_account_name", "amount"])
            assert mapping["gl_code"].isdigit()  # Standard accounting format
            assert mapping["amount"] != 0  # Non-zero amounts for export