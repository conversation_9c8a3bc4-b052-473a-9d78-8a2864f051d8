[tool:pytest]
# Pytest configuration for integration tests
minversion = 6.0
addopts = 
    -ra
    -q
    --strict-markers
    --disable-warnings
    --tb=short
    --maxfail=5
    --cov=giki_ai_api
    --cov-report=html
    --cov-report=term-missing

testpaths = tests/integration

# Async test configuration
asyncio_mode = auto

# Custom markers
markers =
    integration: Integration tests
    agent_test: Agent integration tests
    service_test: Service integration tests
    router_test: Router integration tests
    error_test: Error handling tests
    e2e_test: End-to-end workflow tests
    slow: Slow running tests
    requires_ai: Tests that require AI service

# Test discovery
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Coverage configuration (merged with main addopts above)

# Timeout configuration
timeout = 300
