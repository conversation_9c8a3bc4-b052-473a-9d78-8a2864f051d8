"""
Unit tests for authentication core functionality.

Tests critical security functions including password hashing, JWT operations,
and authentication performance optimizations.
"""

from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock

import pytest
from jose import jwt

from giki_ai_api.core.config import settings
from giki_ai_api.domains.auth.auth import (
    OptimizedTokenData,
    RefreshTokenData,
    TokenData,
    argon2_hasher,
    create_access_token,
    create_optimized_access_token,
    create_refresh_token,
    decode_optimized_token,
    decode_token,
    get_password_hash,
    needs_rehash,
    pwd_context,
    verify_password,
    verify_password_async,
)


class TestPasswordHashing:
    """Test password hashing and verification functions."""

    def test_password_hashing_argon2(self):
        """Test Argon2 password hashing meets security standards."""
        password = "test_password_2024!"
        hashed = get_password_hash(password)
        
        # Verify hash starts with Argon2 identifier
        assert hashed.startswith("$argon2id$")
        
        # Verify password verification works
        assert verify_password(password, hashed) is True
        assert verify_password("wrong_password", hashed) is False

    def test_password_hashing_different_passwords_different_hashes(self):
        """Test that different passwords produce different hashes."""
        password1 = "password1"
        password2 = "password2"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2

    def test_password_hashing_same_password_different_salts(self):
        """Test that same password produces different hashes (due to random salt)."""
        password = "same_password"
        
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Different salts should produce different hashes
        assert hash1 != hash2
        
        # But both should verify the same password
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True

    def test_bcrypt_backward_compatibility(self):
        """Test that bcrypt hashes are still supported for backward compatibility."""
        # Create a bcrypt hash using the old method
        bcrypt_hash = pwd_context.hash("test_password", scheme="bcrypt")
        
        # Should still verify correctly
        assert verify_password("test_password", bcrypt_hash) is True
        assert verify_password("wrong_password", bcrypt_hash) is False

    def test_needs_rehash_detection(self):
        """Test detection of hashes that need upgrading."""
        # Argon2 hash (current) should not need rehash
        argon2_hash = get_password_hash("test_password")
        assert needs_rehash(argon2_hash) is False
        
        # bcrypt hash should need rehash to Argon2
        bcrypt_hash = pwd_context.hash("test_password", scheme="bcrypt")
        assert needs_rehash(bcrypt_hash) is True

    @pytest.mark.asyncio
    async def test_verify_password_async(self):
        """Test async password verification."""
        password = "async_test_password"
        hashed = get_password_hash(password)
        
        # Test correct password
        result = await verify_password_async(password, hashed)
        assert result is True
        
        # Test incorrect password
        result = await verify_password_async("wrong_password", hashed)
        assert result is False

    def test_argon2_security_parameters(self):
        """Test that Argon2 parameters meet OWASP security standards."""
        # Check that our Argon2 configuration meets security requirements
        # Memory cost should be at least 64KB (OWASP minimum)
        assert argon2_hasher.memory_cost >= 64 * 1024
        
        # Time cost should be at least 3 (OWASP minimum)
        assert argon2_hasher.time_cost >= 3
        
        # Hash length should be at least 16 bytes (OWASP minimum)
        assert argon2_hasher.hash_len >= 16
        
        # Salt length should be at least 8 bytes (OWASP minimum)
        assert argon2_hasher.salt_len >= 8


class TestJWTOperations:
    """Test JWT token creation, validation, and decoding."""

    def setup_method(self):
        """Set up test data for JWT tests."""
        self.test_user_id = 123
        self.test_tenant_id = 456
        self.test_email = "<EMAIL>"

    def test_create_access_token_basic(self):
        """Test basic access token creation."""
        subject = f"{self.test_user_id}:{self.test_tenant_id}"
        token = create_access_token(subject)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode and verify payload
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        assert payload["sub"] == subject
        assert payload["type"] == "access"
        assert "exp" in payload

    def test_create_access_token_custom_expiration(self):
        """Test access token creation with custom expiration."""
        subject = f"{self.test_user_id}:{self.test_tenant_id}"
        expires_delta = timedelta(minutes=30)
        
        token = create_access_token(subject, expires_delta)
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        
        # Check expiration is approximately 30 minutes from now
        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
        expected_exp = datetime.now(timezone.utc) + expires_delta
        
        # Allow 5 second tolerance for test execution time
        time_diff = abs((exp_time - expected_exp).total_seconds())
        assert time_diff < 5

    def test_create_refresh_token(self):
        """Test refresh token creation."""
        token = create_refresh_token(self.test_user_id)
        
        assert isinstance(token, str)
        
        # Decode and verify payload
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        assert payload["sub"] == str(self.test_user_id)
        assert payload["type"] == "refresh"
        assert "exp" in payload

    def test_create_optimized_access_token(self):
        """Test optimized access token with embedded user data."""
        # Mock user object
        mock_user = MagicMock()
        mock_user.id = self.test_user_id
        mock_user.tenant_id = self.test_tenant_id
        mock_user.email = self.test_email
        mock_user.is_active = True
        mock_user.full_name = "Test User"
        
        token = create_optimized_access_token(mock_user)
        
        # Decode and verify embedded data
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        assert payload["sub"] == f"{self.test_user_id}:{self.test_tenant_id}"
        assert payload["type"] == "access"
        assert payload["user_id"] == self.test_user_id
        assert payload["tenant_id"] == self.test_tenant_id
        assert payload["email"] == self.test_email
        assert payload["is_active"] is True
        assert payload["full_name"] == "Test User"

    def test_decode_token_access_token(self):
        """Test decoding access tokens."""
        subject = f"{self.test_user_id}:{self.test_tenant_id}"
        token = create_access_token(subject)
        
        result = decode_token(token)
        
        assert isinstance(result, TokenData)
        assert result.user_id == self.test_user_id
        assert result.tenant_id == self.test_tenant_id

    def test_decode_token_refresh_token(self):
        """Test decoding refresh tokens."""
        token = create_refresh_token(self.test_user_id)
        
        result = decode_token(token)
        
        assert isinstance(result, RefreshTokenData)
        assert result.user_id == self.test_user_id

    def test_decode_token_invalid(self):
        """Test decoding invalid tokens."""
        # Invalid token
        result = decode_token("invalid.token.here")
        assert result is None
        
        # Expired token (manually create with past expiration)
        past_time = datetime.now(timezone.utc) - timedelta(hours=1)
        expired_payload = {
            "sub": f"{self.test_user_id}:{self.test_tenant_id}",
            "type": "access",
            "exp": past_time.timestamp()
        }
        expired_token = jwt.encode(expired_payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        result = decode_token(expired_token)
        assert result is None

    def test_decode_optimized_token(self):
        """Test decoding optimized tokens with embedded data."""
        mock_user = MagicMock()
        mock_user.id = self.test_user_id
        mock_user.tenant_id = self.test_tenant_id
        mock_user.email = self.test_email
        mock_user.is_active = True
        mock_user.full_name = "Test User"
        
        token = create_optimized_access_token(mock_user)
        result = decode_optimized_token(token)
        
        assert isinstance(result, OptimizedTokenData)
        assert result.user_id == self.test_user_id
        assert result.tenant_id == self.test_tenant_id
        assert result.email == self.test_email
        assert result.is_active is True
        assert result.full_name == "Test User"

    def test_decode_optimized_token_invalid(self):
        """Test decoding invalid optimized tokens."""
        # Non-access token (refresh token)
        refresh_token = create_refresh_token(self.test_user_id)
        result = decode_optimized_token(refresh_token)
        assert result is None
        
        # Completely invalid token
        result = decode_optimized_token("invalid.token")
        assert result is None

    def test_token_security_tampering_detection(self):
        """Test that token tampering is detected."""
        subject = f"{self.test_user_id}:{self.test_tenant_id}"
        token = create_access_token(subject)
        
        # Tamper with the token by changing a character
        tampered_token = token[:-5] + "XXXXX"
        
        result = decode_token(tampered_token)
        assert result is None

    def test_malformed_access_token_subject(self):
        """Test handling of malformed access token subjects."""
        # Create token with invalid subject format (missing tenant_id)
        invalid_payload = {
            "sub": str(self.test_user_id),  # Missing ":tenant_id"
            "type": "access",
            "exp": (datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()
        }
        invalid_token = jwt.encode(invalid_payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        result = decode_token(invalid_token)
        assert result is None


class TestPerformanceRequirements:
    """Test that authentication operations meet performance requirements."""

    @pytest.mark.asyncio
    async def test_password_verification_performance(self):
        """Test that password verification is fast enough for <200ms auth target."""
        import time
        
        password = "performance_test_password"
        hashed = get_password_hash(password)
        
        # Measure async password verification time
        start_time = time.time()
        result = await verify_password_async(password, hashed)
        end_time = time.time()
        
        assert result is True
        
        # Should be under 200ms for good performance (Argon2 is intentionally slow for security)
        verification_time_ms = (end_time - start_time) * 1000
        assert verification_time_ms < 200, f"Password verification took {verification_time_ms:.2f}ms"

    def test_jwt_token_operations_performance(self):
        """Test that JWT operations are sub-millisecond."""
        import time
        
        subject = "123:456"
        
        # Test token creation performance
        start_time = time.time()
        token = create_access_token(subject)
        creation_time = (time.time() - start_time) * 1000
        
        # Test token decoding performance
        start_time = time.time()
        result = decode_token(token)
        decoding_time = (time.time() - start_time) * 1000
        
        assert result is not None
        assert creation_time < 10, f"Token creation took {creation_time:.2f}ms"
        assert decoding_time < 10, f"Token decoding took {decoding_time:.2f}ms"


class TestSecurityStandards:
    """Test that authentication meets enterprise security standards."""

    def test_secret_key_configuration(self):
        """Test that secret key is properly configured."""
        # In tests, should have a test secret key
        assert settings.SECRET_KEY is not None
        assert len(settings.SECRET_KEY) > 32  # Minimum length for security

    def test_token_expiration_defaults(self):
        """Test that token expiration times are reasonable."""
        # Access tokens should expire relatively quickly
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES <= 60 * 24  # Max 24 hours
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES >= 15  # Min 15 minutes
        
        # Refresh tokens should expire eventually
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS <= 30  # Max 30 days
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS >= 1  # Min 1 day

    def test_algorithm_security(self):
        """Test that secure JWT algorithm is used."""
        # Should use HMAC with SHA-256 or better
        assert settings.ALGORITHM in ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]