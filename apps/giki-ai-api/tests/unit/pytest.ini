[tool:pytest]
# Pytest configuration for unit tests (more targeted than integration tests)
minversion = 6.0
addopts = 
    -ra
    -q
    --strict-markers
    --disable-warnings
    --tb=short
    --maxfail=5

# Only apply asyncio to explicitly marked tests (not auto mode)
asyncio_mode = strict

# Custom markers
markers =
    asyncio: Explicitly marked async tests

# Test discovery
python_files = test_*.py
python_classes = Test*
python_functions = test_*