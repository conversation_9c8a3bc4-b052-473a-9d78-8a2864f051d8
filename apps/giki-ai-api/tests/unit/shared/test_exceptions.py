"""
Unit tests for exception handling and error management.

Tests exception hierarchy, error context management, serialization,
and enhanced error handling capabilities.
"""


from giki_ai_api.shared.exceptions import (
    AgentError,
    AIServiceError,
    AuthenticationError,
    # Specific service errors
    CategorizationError,
    CircuitBreakerError,
    ConfigurationError,
    DatabaseError,
    DataPreparationError,
    # Processing errors
    DataProcessingError,
    EntityExtractionError,
    # Enhanced exception system
    ErrorSeverity,
    FileProcessingError,
    GCSInteractionError,
    GikiAIBaseError,
    # Base exceptions
    GikiAIError,
    LearningError,
    LLMInteractionError,
    ModelResponseError,
    MultiSheetProcessingError,
    OnboardingError,
    # Other errors
    RAGContextError,
    RAGError,
    RAGProcessingError,
    RecoveryError,
    RetryableError,
    SchemaDetectionError,
    ServiceError,
    ServiceNotInitializedError,
    TransactionProcessingError,
    ValidationError,
    # Vertex AI exceptions
    VertexAIClientError,
    VertexAIGCSError,
    VertexAIGenerationError,
    VertexAIInitializationError,
    VertexAIRAGOperationError,
)


class TestErrorSeverity:
    """Test error severity enumeration."""

    def test_error_severity_values(self):
        """Test that all severity levels are properly defined."""
        assert ErrorSeverity.LOW.value == "low"
        assert ErrorSeverity.MEDIUM.value == "medium"
        assert ErrorSeverity.HIGH.value == "high"
        assert ErrorSeverity.CRITICAL.value == "critical"

    def test_error_severity_comparison(self):
        """Test error severity enum behavior."""
        # Test that enum values can be compared
        assert ErrorSeverity.LOW != ErrorSeverity.HIGH
        assert ErrorSeverity.CRITICAL != ErrorSeverity.MEDIUM
        
        # Test that same values are equal
        assert ErrorSeverity.LOW == ErrorSeverity.LOW
        assert ErrorSeverity.CRITICAL == ErrorSeverity.CRITICAL


class TestBaseExceptions:
    """Test basic exception classes."""

    def test_giki_ai_error_creation(self):
        """Test basic GikiAIError creation."""
        error = GikiAIError("Test error message")
        
        assert str(error) == "Test error message"
        assert isinstance(error, Exception)

    def test_configuration_error_inheritance(self):
        """Test ConfigurationError inheritance."""
        error = ConfigurationError("Configuration issue")
        
        assert isinstance(error, GikiAIError)
        assert isinstance(error, Exception)
        assert str(error) == "Configuration issue"


class TestVertexAIExceptions:
    """Test Vertex AI specific exceptions."""

    def test_vertex_ai_client_error(self):
        """Test VertexAIClientError creation."""
        error = VertexAIClientError("Client error")
        
        assert isinstance(error, GikiAIError)
        assert str(error) == "Client error"

    def test_vertex_ai_initialization_error(self):
        """Test VertexAIInitializationError inheritance."""
        error = VertexAIInitializationError("Initialization failed")
        
        assert isinstance(error, VertexAIClientError)
        assert isinstance(error, GikiAIError)

    def test_vertex_ai_rag_operation_error(self):
        """Test VertexAIRAGOperationError inheritance."""
        error = VertexAIRAGOperationError("RAG operation failed")
        
        assert isinstance(error, VertexAIClientError)
        assert str(error) == "RAG operation failed"

    def test_vertex_ai_generation_error(self):
        """Test VertexAIGenerationError inheritance."""
        error = VertexAIGenerationError("Generation failed")
        
        assert isinstance(error, VertexAIClientError)

    def test_vertex_ai_gcs_error(self):
        """Test VertexAIGCSError inheritance."""
        error = VertexAIGCSError("GCS operation failed")
        
        assert isinstance(error, VertexAIClientError)


class TestEnhancedBaseError:
    """Test enhanced base error class with context and tracking."""

    def test_giki_ai_base_error_basic(self):
        """Test basic GikiAIBaseError creation."""
        error = GikiAIBaseError("Test error")
        
        assert error.message == "Test error"
        assert error.error_code == "GikiAIBaseError"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.context == {}
        assert error.correlation_id is None
        assert error.recovery_suggestions == []

    def test_giki_ai_base_error_full_context(self):
        """Test GikiAIBaseError with full context."""
        context = {"user_id": 123, "operation": "test"}
        recovery_suggestions = ["Try again", "Check configuration"]
        original_error = ValueError("Original error")
        
        error = GikiAIBaseError(
            message="Enhanced error",
            error_code="TEST_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context,
            correlation_id="test-123",
            recovery_suggestions=recovery_suggestions,
            original_error=original_error
        )
        
        assert error.message == "Enhanced error"
        assert error.error_code == "TEST_ERROR"
        assert error.severity == ErrorSeverity.HIGH
        assert error.context == context
        assert error.correlation_id == "test-123"
        assert error.recovery_suggestions == recovery_suggestions
        assert error.original_error == original_error
        assert error.traceback_info is not None

    def test_giki_ai_base_error_to_dict(self):
        """Test error serialization to dictionary."""
        error = GikiAIBaseError(
            message="Test error",
            error_code="TEST_001",
            severity=ErrorSeverity.HIGH,
            context={"key": "value"},
            correlation_id="corr-123",
            recovery_suggestions=["suggestion1", "suggestion2"],
            original_error=ValueError("Original")
        )
        
        error_dict = error.to_dict()
        
        assert error_dict["error_code"] == "TEST_001"
        assert error_dict["message"] == "Test error"
        assert error_dict["severity"] == "high"
        assert error_dict["context"] == {"key": "value"}
        assert error_dict["correlation_id"] == "corr-123"
        assert error_dict["recovery_suggestions"] == ["suggestion1", "suggestion2"]
        assert "Original" in error_dict["original_error"]

    def test_giki_ai_base_error_string_representation(self):
        """Test enhanced string representation."""
        # Basic error
        error1 = GikiAIBaseError("Simple error")
        assert str(error1) == "[GikiAIBaseError] Simple error"
        
        # Error with correlation ID
        error2 = GikiAIBaseError("Error with ID", correlation_id="test-123")
        assert str(error2) == "[GikiAIBaseError] Error with ID (ID: test-123)"
        
        # Error with context
        error3 = GikiAIBaseError("Error with context", context={"user": "test"})
        assert "Context: {'user': 'test'}" in str(error3)
        
        # Error with both
        error4 = GikiAIBaseError(
            "Full error", 
            correlation_id="test-456", 
            context={"operation": "test"}
        )
        error_str = str(error4)
        assert "(ID: test-456)" in error_str
        assert "Context:" in error_str


class TestServiceError:
    """Test service-level error handling."""

    def test_service_error_creation(self):
        """Test ServiceError creation with service context."""
        error = ServiceError(
            message="Service operation failed",
            service_name="TestService",
            operation="test_operation"
        )
        
        assert error.message == "Service operation failed"
        assert error.service_name == "TestService"
        assert error.operation == "test_operation"
        assert error.context["service"] == "TestService"
        assert error.context["operation"] == "test_operation"

    def test_service_error_inheritance(self):
        """Test ServiceError inherits from GikiAIBaseError."""
        error = ServiceError("Test", "Service", "operation")
        
        assert isinstance(error, GikiAIBaseError)
        assert hasattr(error, 'to_dict')
        assert hasattr(error, 'severity')

    def test_service_error_with_additional_context(self):
        """Test ServiceError with additional context."""
        error = ServiceError(
            message="Database error",
            service_name="DatabaseService",
            operation="query",
            severity=ErrorSeverity.HIGH,
            correlation_id="db-123",
            context={"table": "users", "query_type": "SELECT"}
        )
        
        assert error.severity == ErrorSeverity.HIGH
        assert error.correlation_id == "db-123"
        assert error.context["service"] == "DatabaseService"
        assert error.context["operation"] == "query"
        assert error.context["table"] == "users"
        assert error.context["query_type"] == "SELECT"


class TestAgentError:
    """Test ADK agent error handling."""

    def test_agent_error_creation(self):
        """Test AgentError creation with agent context."""
        error = AgentError(
            message="Agent execution failed",
            agent_name="CategorizationAgent",
            tool_name="categorize_transaction"
        )
        
        assert error.message == "Agent execution failed"
        assert error.agent_name == "CategorizationAgent"
        assert error.tool_name == "categorize_transaction"
        assert error.context["agent"] == "CategorizationAgent"
        assert error.context["tool"] == "categorize_transaction"

    def test_agent_error_without_tool(self):
        """Test AgentError creation without tool name."""
        error = AgentError(
            message="Agent initialization failed",
            agent_name="TestAgent"
        )
        
        assert error.agent_name == "TestAgent"
        assert error.tool_name is None
        assert error.context["agent"] == "TestAgent"
        assert error.context["tool"] is None


class TestRetryableError:
    """Test retryable error with backoff configuration."""

    def test_retryable_error_creation(self):
        """Test RetryableError with retry configuration."""
        error = RetryableError(
            message="Temporary failure",
            max_retries=5,
            retry_delay=2.0
        )
        
        assert error.message == "Temporary failure"
        assert error.max_retries == 5
        assert error.retry_delay == 2.0
        assert error.context["max_retries"] == 5
        assert error.context["retry_delay"] == 2.0

    def test_retryable_error_defaults(self):
        """Test RetryableError with default values."""
        error = RetryableError("Default retry error")
        
        assert error.max_retries == 3
        assert error.retry_delay == 1.0


class TestCircuitBreakerError:
    """Test circuit breaker error handling."""

    def test_circuit_breaker_error_creation(self):
        """Test CircuitBreakerError creation."""
        error = CircuitBreakerError(service_name="DatabaseService")
        
        assert "Circuit breaker open for service: DatabaseService" in error.message
        assert error.service_name == "DatabaseService"
        assert error.severity == ErrorSeverity.HIGH
        assert error.context["service"] == "DatabaseService"

    def test_circuit_breaker_error_with_context(self):
        """Test CircuitBreakerError with additional context."""
        error = CircuitBreakerError(
            service_name="AIService",
            correlation_id="circuit-123",
            context={"failure_count": 5}
        )
        
        assert error.correlation_id == "circuit-123"
        assert error.context["service"] == "AIService"
        assert error.context["failure_count"] == 5


class TestSpecificServiceErrors:
    """Test specific service error classes."""

    def test_categorization_error(self):
        """Test CategorizationError with service context."""
        error = CategorizationError("Categorization failed")
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "CategoryService"
        assert error.operation == "categorization"

    def test_validation_error(self):
        """Test ValidationError with field context."""
        error = ValidationError(
            message="Invalid email format",
            field_name="email",
            field_value="invalid-email"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "ValidationService"
        assert error.operation == "validation"
        assert error.context["field_name"] == "email"
        assert error.context["field_value"] == "invalid-email"

    def test_ai_service_error(self):
        """Test AIServiceError with model context."""
        error = AIServiceError(
            message="Model inference failed",
            model_name="gemini-2.0-flash",
            operation_type="inference"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "AIService"
        assert error.operation == "inference"
        assert error.context["model"] == "gemini-2.0-flash"

    def test_database_error(self):
        """Test DatabaseError with query context."""
        error = DatabaseError(
            message="Query timeout",
            query_type="SELECT",
            table_name="transactions"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "DatabaseService"
        assert error.operation == "SELECT"
        assert error.context["table"] == "transactions"

    def test_authentication_error(self):
        """Test AuthenticationError with user context."""
        error = AuthenticationError(
            message="Invalid credentials",
            user_id="user-123"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "AuthService"
        assert error.operation == "authentication"
        assert error.severity == ErrorSeverity.HIGH
        assert error.context["user_id"] == "user-123"

    def test_file_processing_error(self):
        """Test FileProcessingError with file context."""
        error = FileProcessingError(
            message="File corrupted",
            filename="transactions.xlsx",
            file_type="excel"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "FileProcessingService"
        assert error.operation == "file_processing"
        assert error.context["filename"] == "transactions.xlsx"
        assert error.context["file_type"] == "excel"

    def test_onboarding_error(self):
        """Test OnboardingError with tenant context."""
        error = OnboardingError(
            message="Onboarding step failed",
            tenant_id=123,
            step="category_setup"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "OnboardingService"
        assert error.operation == "category_setup"
        assert error.context["tenant_id"] == 123
        assert error.context["onboarding_step"] == "category_setup"

    def test_rag_error(self):
        """Test RAGError with corpus context."""
        error = RAGError(
            message="RAG query failed",
            corpus_id="corpus-456",
            query_type="similarity_search"
        )
        
        assert isinstance(error, ServiceError)
        assert error.service_name == "RAGService"
        assert error.operation == "similarity_search"
        assert error.context["corpus_id"] == "corpus-456"


class TestDataProcessingErrors:
    """Test data processing exception hierarchy."""

    def test_data_processing_error_base(self):
        """Test base DataProcessingError."""
        details = {"row": 10, "column": "amount"}
        error = DataProcessingError(
            message="Processing failed",
            error_code="PROC_001",
            details=details
        )
        
        assert isinstance(error, GikiAIError)
        assert error.error_code == "PROC_001"
        assert error.details == details

    def test_schema_detection_error(self):
        """Test SchemaDetectionError inheritance."""
        error = SchemaDetectionError("Schema not detected")
        
        assert isinstance(error, DataProcessingError)
        assert isinstance(error, GikiAIError)

    def test_transaction_processing_error(self):
        """Test TransactionProcessingError inheritance."""
        error = TransactionProcessingError("Transaction processing failed")
        
        assert isinstance(error, DataProcessingError)

    def test_entity_extraction_error(self):
        """Test EntityExtractionError inheritance."""
        error = EntityExtractionError("Entity extraction failed")
        
        assert isinstance(error, DataProcessingError)

    def test_multi_sheet_processing_error(self):
        """Test MultiSheetProcessingError inheritance."""
        error = MultiSheetProcessingError("Multi-sheet processing failed")
        
        assert isinstance(error, DataProcessingError)

    def test_recovery_error(self):
        """Test RecoveryError inheritance."""
        error = RecoveryError("Recovery operation failed")
        
        assert isinstance(error, DataProcessingError)

    def test_learning_error(self):
        """Test LearningError inheritance."""
        error = LearningError("Learning operation failed")
        
        assert isinstance(error, GikiAIError)


class TestOtherSpecificErrors:
    """Test other specific error classes."""

    def test_rag_context_error(self):
        """Test RAGContextError inheritance."""
        error = RAGContextError("RAG context error")
        
        assert isinstance(error, CategorizationError)

    def test_rag_processing_error(self):
        """Test RAGProcessingError inheritance."""
        error = RAGProcessingError("RAG processing error")
        
        assert isinstance(error, GikiAIError)

    def test_model_response_error(self):
        """Test ModelResponseError inheritance."""
        error = ModelResponseError("Model response error")
        
        assert isinstance(error, CategorizationError)

    def test_gcs_interaction_error(self):
        """Test GCSInteractionError inheritance."""
        error = GCSInteractionError("GCS interaction error")
        
        assert isinstance(error, CategorizationError)

    def test_data_preparation_error(self):
        """Test DataPreparationError inheritance."""
        error = DataPreparationError("Data preparation error")
        
        assert isinstance(error, CategorizationError)

    def test_service_not_initialized_error(self):
        """Test ServiceNotInitializedError inheritance."""
        error = ServiceNotInitializedError("Service not initialized")
        
        assert isinstance(error, GikiAIError)

    def test_llm_interaction_error(self):
        """Test LLMInteractionError inheritance."""
        error = LLMInteractionError("LLM interaction error")
        
        assert isinstance(error, GikiAIError)


class TestErrorInheritanceChain:
    """Test that error inheritance chains are correct."""

    def test_categorization_error_chain(self):
        """Test CategorizationError inheritance chain."""
        error = CategorizationError("Test error")
        
        assert isinstance(error, ServiceError)
        assert isinstance(error, GikiAIBaseError)
        assert isinstance(error, Exception)

    def test_validation_error_chain(self):
        """Test ValidationError inheritance chain."""
        error = ValidationError("Test validation")
        
        assert isinstance(error, ServiceError)
        assert isinstance(error, GikiAIBaseError)
        assert isinstance(error, Exception)

    def test_data_processing_error_chain(self):
        """Test DataProcessingError inheritance chain."""
        error = SchemaDetectionError("Schema error")
        
        assert isinstance(error, DataProcessingError)
        assert isinstance(error, GikiAIError)
        assert isinstance(error, Exception)

    def test_vertex_ai_error_chain(self):
        """Test Vertex AI error inheritance chain."""
        error = VertexAIInitializationError("Init error")
        
        assert isinstance(error, VertexAIClientError)
        assert isinstance(error, GikiAIError)
        assert isinstance(error, Exception)


class TestErrorSerialization:
    """Test error serialization and JSON conversion."""

    def test_basic_error_serialization(self):
        """Test basic error to_dict conversion."""
        error = GikiAIBaseError("Test error")
        error_dict = error.to_dict()
        
        expected_keys = [
            "error_code", "message", "severity", "context",
            "correlation_id", "recovery_suggestions", "original_error"
        ]
        
        for key in expected_keys:
            assert key in error_dict

    def test_service_error_serialization(self):
        """Test service error serialization includes service context."""
        error = ServiceError(
            message="Service error",
            service_name="TestService",
            operation="test_op"
        )
        error_dict = error.to_dict()
        
        assert error_dict["context"]["service"] == "TestService"
        assert error_dict["context"]["operation"] == "test_op"

    def test_error_with_original_exception_serialization(self):
        """Test error serialization with original exception."""
        original = ValueError("Original error")
        error = GikiAIBaseError("Wrapped error", original_error=original)
        error_dict = error.to_dict()
        
        assert "Original error" in error_dict["original_error"]

    def test_error_json_serializable(self):
        """Test that error dictionary is JSON serializable."""
        import json
        
        error = ServiceError(
            message="Test error",
            service_name="TestService",
            operation="test",
            context={"user_id": 123, "action": "test"}
        )
        
        error_dict = error.to_dict()
        
        # Should not raise exception
        json_str = json.dumps(error_dict)
        assert isinstance(json_str, str)
        
        # Should be able to parse back
        parsed = json.loads(json_str)
        assert parsed["message"] == "Test error"


class TestErrorContextManagement:
    """Test error context management and updates."""

    def test_context_initialization(self):
        """Test that context is properly initialized."""
        error = GikiAIBaseError("Test", context={"key": "value"})
        
        assert error.context["key"] == "value"

    def test_context_updates_in_service_error(self):
        """Test that service errors update context correctly."""
        error = ServiceError(
            message="Test",
            service_name="TestService",
            operation="test_op",
            context={"initial": "value"}
        )
        
        assert error.context["initial"] == "value"
        assert error.context["service"] == "TestService"
        assert error.context["operation"] == "test_op"

    def test_context_preservation(self):
        """Test that original context is preserved when updating."""
        original_context = {"user_id": 123, "session": "abc"}
        error = AgentError(
            message="Agent error",
            agent_name="TestAgent",
            context=original_context.copy()
        )
        
        # Original context should be preserved
        assert error.context["user_id"] == 123
        assert error.context["session"] == "abc"
        
        # New context should be added
        assert error.context["agent"] == "TestAgent"


class TestErrorHandlingBestPractices:
    """Test error handling follows best practices."""

    def test_error_messages_are_descriptive(self):
        """Test that error messages provide meaningful information."""
        errors_to_test = [
            ValidationError("Invalid email format", field_name="email"),
            DatabaseError("Connection timeout", query_type="SELECT", table_name="users"),
            AIServiceError("Model inference failed", model_name="gemini-2.0"),
            FileProcessingError("File corrupted", filename="data.xlsx"),
        ]
        
        for error in errors_to_test:
            error_str = str(error)
            # Error string should contain key information
            assert len(error_str) > 10  # Non-trivial message
            assert error.error_code in error_str  # Contains error code

    def test_errors_include_correlation_tracking(self):
        """Test that errors support correlation ID tracking."""
        correlation_id = "test-correlation-123"
        error = GikiAIBaseError("Test error", correlation_id=correlation_id)
        
        assert error.correlation_id == correlation_id
        assert correlation_id in str(error)

    def test_errors_support_recovery_suggestions(self):
        """Test that errors can include recovery suggestions."""
        suggestions = ["Check configuration", "Retry operation", "Contact support"]
        error = GikiAIBaseError("Operation failed", recovery_suggestions=suggestions)
        
        assert error.recovery_suggestions == suggestions
        
        error_dict = error.to_dict()
        assert error_dict["recovery_suggestions"] == suggestions

    def test_critical_errors_have_high_severity(self):
        """Test that critical operations use appropriate severity."""
        auth_error = AuthenticationError("Login failed")
        circuit_error = CircuitBreakerError("DatabaseService")
        
        assert auth_error.severity == ErrorSeverity.HIGH
        assert circuit_error.severity == ErrorSeverity.HIGH

    def test_errors_preserve_original_exception_info(self):
        """Test that errors preserve original exception information."""
        original = ValueError("Original problem")
        
        try:
            raise original
        except ValueError as e:
            wrapped = GikiAIBaseError("Wrapped error", original_error=e)
        
        assert wrapped.original_error == original
        assert wrapped.traceback_info is not None
        assert "ValueError" in wrapped.traceback_info