"""
Test authentication endpoints to ensure proper login functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.core.database import engine
from giki_ai_api.core.main import app
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_password_hash


@pytest.fixture
async def setup_test_user():
    """Create a test user in the database."""
    async with AsyncSession(engine) as session:
        # Check if test user already exists
        result = await session.execute(
            select(User).where(User.email == "<EMAIL>")
        )
        existing_user = result.scalar_one_or_none()
        
        if not existing_user:
            # Create test user with proper password hash
            test_user = User(
                email="<EMAIL>",
                hashed_password=get_password_hash("password123"),
                is_active=True,
                is_admin=False,
                tenant_id=1,  # Default tenant
            )
            session.add(test_user)
            await session.commit()
            await session.refresh(test_user)
            yield test_user
        else:
            # Update existing user's password to ensure it's correct
            existing_user.hashed_password = get_password_hash("password123")
            existing_user.is_active = True
            await session.commit()
            yield existing_user


@pytest.mark.asyncio
async def test_login_with_email_as_username(setup_test_user):
    """Test that login works when using email in the username field."""
    with TestClient(app) as client:
        # Test login with form data (OAuth2 standard)
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": "<EMAIL>",  # OAuth2 spec requires 'username' field
                "password": "password123",
                "grant_type": "password",
            },
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data


@pytest.mark.asyncio
async def test_login_with_wrong_password(setup_test_user):
    """Test that login fails with incorrect password."""
    with TestClient(app) as client:
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": "<EMAIL>",
                "password": "wrongpassword",
                "grant_type": "password",
            },
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Incorrect username or password"


@pytest.mark.asyncio
async def test_login_with_nonexistent_user():
    """Test that login fails for non-existent user."""
    with TestClient(app) as client:
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": "<EMAIL>",
                "password": "password123",
                "grant_type": "password",
            },
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Incorrect username or password"


@pytest.mark.asyncio
async def test_get_current_user(setup_test_user):
    """Test getting current user with valid token."""
    with TestClient(app) as client:
        # First login to get token
        login_response = client.post(
            "/api/v1/auth/token",
            data={
                "username": "<EMAIL>",
                "password": "password123",
                "grant_type": "password",
            },
        )
        
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        
        # Use token to get current user
        response = client.get(
            "/api/v1/auth/me",
            headers={
                "Authorization": f"Bearer {token}",
            },
        )
        
        assert response.status_code == 200
        user_data = response.json()
        assert user_data["email"] == "<EMAIL>"
        assert user_data["is_active"] is True


def test_login_form_urlencoded_content_type():
    """Test that login endpoint accepts form-urlencoded data."""
    with TestClient(app) as client:
        response = client.post(
            "/api/v1/auth/token",
            data="username=<EMAIL>&password=password123&grant_type=password",
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )
        
        # Even if auth fails, we should not get a 422 validation error
        assert response.status_code in [200, 401]  # Success or auth failure, not validation error