{"name": "giki-ai-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/giki-ai-api/src", "tags": ["type:app", "lang:python", "scope:giki-ai"], "targets": {"db": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/ensure-postgres.sh", "cwd": "{workspaceRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && uv run ruff check src --fix && uv run ruff format src", "cwd": "{workspaceRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && uv run pytest tests/ 2>&1 | ../../scripts/nx/log-with-limit.sh ../../logs/api-test.log 1000", "cwd": "{workspaceRoot}"}}, "serve": {"executor": "nx:run-commands", "configurations": {"development": {"command": "./scripts/nx/serve-api.sh", "cwd": "{workspaceRoot}"}, "production": {"command": "./scripts/nx/serve-api.sh production", "cwd": "{workspaceRoot}"}}, "defaultConfiguration": "development", "dependsOn": ["db"]}, "check-logs": {"executor": "nx:run-commands", "configurations": {"development": {"command": "./scripts/nx/check-development-logs.sh api", "cwd": "{workspaceRoot}"}, "production": {"command": "./scripts/nx/check-production-logs.sh api", "cwd": "{workspaceRoot}"}}, "defaultConfiguration": "development"}, "deploy": {"executor": "nx:run-commands", "options": {"command": "../../scripts/nx/deploy-all.sh --skip-frontend", "cwd": "{workspaceRoot}"}, "configurations": {"production": {"command": "../../scripts/nx/deploy-all.sh --skip-frontend", "cwd": "{workspaceRoot}"}}}}}