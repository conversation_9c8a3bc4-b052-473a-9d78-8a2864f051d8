[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "giki-ai-api"
version = "0.1.0"
description = "Backend API for the giki-ai application"
authors = [
    { name = "Giki AI", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
readme = "README.md"
requires-python = ">=3.9,<3.13"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi",
    "pydantic",
    "pydantic-settings",
    "python-jose[cryptography]",
    "passlib[bcrypt]",
    "python-dotenv",
    "asyncpg",  # Raw PostgreSQL async driver
    "psutil", # System and process monitoring
    "httpx", # For general HTTP requests
    "uvicorn[standard]", # Added for running the API
    "email-validator", # Required by Pydantic for email validation
    "uvloop", # Fix for asyncpg SCRAM authentication in Cloud Run
    # Google Cloud dependencies
    "google-cloud-aiplatform",
    "google-cloud-storage",
    "google-auth",
    "google-auth-oauthlib",
    "google-auth-httplib2",
    "protobuf",
    # Vertex AI and Generative AI dependencies
    "vertexai",
    "google-adk==1.2.1",
    # Data processing dependencies
    "pandas",
    "numpy",
    # CLI dependencies
    "typer",
    # Configuration dependencies
    "pyyaml",
    # Other dependencies
    "reportlab>=4.4.1",
    # System monitoring dependencies
    "psutil",
    "argon2-cffi>=25.1.0",
    "aiofiles>=24.1.0",
    "bandit>=1.8.3",
    "mypy>=1.16.0",
    # Redis dependencies
    "redis>=5.0.0",
    "aioredis>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "pytest>=8.2.2",
    "pytest-asyncio>=0.23.7",
    "pytest-json-report",
    "pytest-cov",
    "pytest-mock",
    "httpx", # Already in main dependencies, but often also in dev
    # Add other dev dependencies if needed
]

[tool.hatch.build.targets.wheel]
packages = ["src/giki_ai_api"]

[tool.ruff]
# Same as Black's default
line-length = 88

# Target Python 3.12
target-version = "py312"

# Exclude files
exclude = [
    ".git",
    "__pycache__",
    "dist",
    "build",
    "migrations",
    ".venv",
    "htmlcov",
    "test-results",
    "coverage_html_report"
]

[tool.ruff.lint]
# Enable pycodestyle (E), Pyflakes (F), flake8-bugbear (B), and imports (I)
select = ["E", "F", "B", "I"]
ignore = [
    "E501",  # Line too long - let ruff format handle this
    "E402",  # Module level import not at top - already fixed
    "B008",  # Do not perform function call in argument defaults - legitimate for FastAPI Depends()
    "B904",  # Within an except clause - sometimes chaining isn't needed
]

[tool.ruff.format]
# Use Black-compatible formatting
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[dependency-groups]
dev = [
    "openpyxl>=3.1.5",
    "pytest-asyncio>=0.23.7",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.14.0",
    "ruff>=0.11.9",
]

[tool.ruff.lint.isort]
# Import sorting compatible with Black
known-first-party = ["giki_ai_api"]
force-single-line = false
combine-as-imports = true
