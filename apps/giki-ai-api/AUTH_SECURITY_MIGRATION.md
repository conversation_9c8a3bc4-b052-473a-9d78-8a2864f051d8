# Authentication Security Migration Plan

## Critical Security Issues Found

1. **Authentication Success Caching (60 seconds)**: 
   - Caches successful logins by email+password hash
   - Bypasses password verification for 60 seconds
   - Allows replay attacks within time window

2. **User Data Caching (5 minutes)**:
   - Caches user objects by email alone
   - Doesn't check if user was deactivated/deleted
   - Stale permission data

3. **JWT-Only Authentication**:
   - "Optimized" functions trust JWT content without DB verification
   - No way to revoke tokens before expiry
   - Deactivated users can still access system

4. **User ID Caching**:
   - `get_current_user` caches by user ID
   - No verification of current user state

## New Secure Implementation

Created three new files that implement secure authentication:

1. **`secure_auth.py`**: Core authentication without caching
   - Always verifies against database
   - No authentication or user data caching
   - Minimal JWT payload (just IDs)

2. **`secure_router.py`**: Authentication endpoints
   - Login always queries database
   - No cached authentication results

3. **`secure_dependencies.py`**: FastAPI dependencies
   - Always verifies user exists and is active
   - Always checks tenant association

## Migration Steps

### Phase 1: Update Core Dependencies (URGENT)
```python
# In core/dependencies.py
# REPLACE:
from ..domains.auth.auth import get_current_active_user_optimized
# WITH:
from ..domains.auth.secure_auth import get_current_active_user

# Update all dependency functions to use secure versions
```

### Phase 2: Update All Router Imports
All routers currently use the insecure optimized functions. Update:

```python
# REPLACE in all routers:
from ...core.dependencies import (
    get_current_tenant_id_optimized,
    get_current_active_user_optimized,
)

# WITH:
from ...core.secure_dependencies import (
    get_current_tenant_id,
    get_current_active_user,
)
```

### Phase 3: Update Auth Router
```python
# In main.py, replace auth router import:
# REPLACE:
from .domains.auth import router as auth_router
# WITH:
from .domains.auth.secure_router import router as auth_router
```

### Phase 4: Remove Dangerous Code
After migration is complete:
1. Delete the entire user_cache system
2. Delete all "optimized" authentication functions
3. Delete the old auth.py file (it's 600+ lines of dangerous code)

## Testing Plan

1. **Security Tests**:
   - Verify deactivated users can't access system
   - Verify deleted users can't use old tokens
   - Verify tenant changes are immediately effective
   - Verify no authentication caching occurs

2. **Performance Impact**:
   - Measure login time (currently ~2.4s with caching)
   - Measure authenticated request time
   - Optimize database queries if needed (indexes, not caching)

## Rollback Plan

If performance becomes unacceptable:
1. Add database indexes on frequently queried columns
2. Use connection pooling effectively
3. Consider read replicas for auth queries
4. DO NOT reintroduce caching

## Security Principles

1. **Never cache authentication results**
2. **Never cache user data**
3. **Always verify against database**
4. **Minimal JWT payload**
5. **Fail secure (deny by default)**