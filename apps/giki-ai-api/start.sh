#!/bin/bash
set -e

echo "Starting Giki AI API..."
echo "Python version: $(python --version)"
echo "Current directory: $(pwd)"
echo "PYTHONPATH: $PYTHONPATH"
echo "PORT: $PORT"

# Check if service account exists
if [ -f "/app/dev-service-account.json" ]; then
    echo "Service account found at /app/dev-service-account.json"
    export GOOGLE_APPLICATION_CREDENTIALS="/app/dev-service-account.json"
else
    echo "Warning: Service account not found at /app/dev-service-account.json"
fi

# Test import
echo "Testing import..."
python -c "import giki_ai_api.main; print('Import successful')" || {
    echo "Import failed, checking module structure..."
    ls -la /app/src/
    exit 1
}

echo "Starting uvicorn server..."
exec python -m uvicorn giki_ai_api.main:app --host 0.0.0.0 --port 8080 --workers 1 --timeout-keep-alive 30