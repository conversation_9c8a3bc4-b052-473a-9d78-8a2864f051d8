{"overall_summary": {"validation_duration_seconds": 0.0, "total_validations": 5, "successful_validations": 2, "failed_validations": 3, "success_rate_percent": 40.0, "optimization_status": "NEEDS_IMPROVEMENT"}, "validation_results": {"Database Optimizations": {"status": "error", "error": "No module named 'giki_ai_api.database'"}, "Middleware Optimizations": {"status": "error", "error": "No module named 'giki_ai_api.middleware'"}, "Connection Retry Logic": {"status": "error", "error": "No module named 'giki_ai_api.database_validator'"}, "Performance Monitoring": {"status": "success", "performance_middleware_available": true, "database_monitoring_available": false, "auth_fast_endpoints_available": false, "monitoring_complete": false}, "Configuration Files": {"status": "success", "test_files": {"test_db_connection.py": false, "test_api_performance.py": false, "validate_performance_optimizations.py": true}, "evidence_directory_exists": false, "all_test_files_exist": false}}}