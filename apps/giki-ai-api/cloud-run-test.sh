#!/bin/bash
set -e

echo "🧪 Testing Giki AI API for Cloud Run deployment readiness"

# Test local startup with Cloud Run environment simulation
echo "1. Testing optimized startup locally..."
PYTHONPATH=/Users/<USER>/giki-ai-workspace/apps/giki-ai-api/src \
PORT=8080 \
DISABLE_DATABASE_WARMUP=true \
VERTEX_TIMEOUT_SECONDS=10 \
ENVIRONMENT=production \
python -c "
import asyncio
import time
from giki_ai_api.main import app

async def test_startup():
    async with app.router.lifespan_context(app):
        return True

start = time.time()
result = asyncio.run(test_startup())
end = time.time()
print(f'✅ Startup completed in {end - start:.2f} seconds')
if end - start > 5:
    print('⚠️  WARNING: Startup time > 5 seconds may cause Cloud Run timeout')
else:
    print('✅ Startup time is acceptable for Cloud Run')
"

echo "2. Testing health endpoints..."
PYTHONPATH=/Users/<USER>/giki-ai-workspace/apps/giki-ai-api/src \
python -c "
from fastapi.testclient import TestClient
from giki_ai_api.main import app

client = TestClient(app)

# Test health
response = client.get('/health')
assert response.status_code == 200, f'Health check failed: {response.status_code}'
print('✅ /health endpoint working')

# Test environment endpoint
response = client.get('/health/env')
assert response.status_code == 200, f'Environment check failed: {response.status_code}'
print('✅ /health/env endpoint working')

print('✅ All health endpoints working correctly')
"

echo "3. Testing container build process..."
if [ ! -f "Dockerfile.cloudrun" ]; then
    echo "❌ Dockerfile.cloudrun not found"
    exit 1
fi

if [ ! -f "start-cloud-run.sh" ]; then
    echo "❌ start-cloud-run.sh not found"
    exit 1
fi

if [ ! -x "start-cloud-run.sh" ]; then
    echo "❌ start-cloud-run.sh not executable"
    exit 1
fi

echo "✅ Container build files present and valid"

echo "4. Checking required environment variables..."
required_vars=(
    "PORT"
    "ENVIRONMENT"
    "DATABASE_URL"
    "GOOGLE_APPLICATION_CREDENTIALS"
    "VERTEX_PROJECT_ID"
    "VERTEX_LOCATION"
)

echo "Required environment variables for Cloud Run:"
for var in "${required_vars[@]}"; do
    echo "  - $var"
done

echo ""
echo "🎉 All tests passed! Ready for Cloud Run deployment."
echo ""
echo "Next steps:"
echo "1. Build container: docker build -f Dockerfile.cloudrun -t giki-ai-api ."
echo "2. Test container locally: docker run -p 8080:8080 --env-file .env.production giki-ai-api"
echo "3. Push to Container Registry: docker tag giki-ai-api gcr.io/YOUR-PROJECT/giki-ai-api:latest"
echo "4. Deploy to Cloud Run: gcloud run services replace cloud-run-deploy.yaml"