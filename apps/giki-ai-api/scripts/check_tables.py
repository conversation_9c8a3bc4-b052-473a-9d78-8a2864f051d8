#!/usr/bin/env python3
"""
Check database table names to understand the schema
"""

import asyncio
import logging
import os
import sys

from sqlalchemy import text

# Add the source directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from giki_ai_api.database import get_database_config


async def check_tables():
    """Check what tables exist in the database."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        # Get database configuration
        get_database_config()

        # Get the engine from the database configuration
        from giki_ai_api.database import engine

        logger.info("Checking database tables...")

        async with engine.begin() as conn:
            # Check what tables exist
            tables_query = """
            SELECT 
                schemaname,
                tablename
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY tablename;
            """

            result = await conn.execute(text(tables_query))
            tables = result.fetchall()

            logger.info("Found tables:")
            for table in tables:
                logger.info(f"  {table.schemaname}.{table.tablename}")

            # Check for categories table specifically
            category_tables_query = """
            SELECT 
                schemaname,
                tablename
            FROM pg_tables 
            WHERE tablename LIKE '%categor%'
            ORDER BY tablename;
            """

            result = await conn.execute(text(category_tables_query))
            category_tables = result.fetchall()

            logger.info("Category-related tables:")
            for table in category_tables:
                logger.info(f"  {table.schemaname}.{table.tablename}")

            await conn.commit()
            logger.info("Table check completed successfully")

    except Exception as e:
        logger.error(f"Table check failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(check_tables())
