#!/usr/bin/env python3
"""
Complete AI Schema Interpretation for Capital One File
======================================================

This script completes CATEGORY-001 next step: "Complete AI schema interpretation for Capital One file"
by processing the uploaded Capital One.xlsx file and learning multilevel categories.

Task: CATEGORY-001 (Implement Multilevel GL Category System)
Step: Complete AI schema interpretation and multilevel category learning
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List

import pandas as pd

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from giki_ai_api.adk_agents.interpretation_agent import (
    suggest_schema_mapping_tool_function,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def analyze_capital_one_file():
    """Analyze Capital One file and complete AI schema interpretation."""

    # Find the most recent Capital One file in uploads
    uploads_dir = Path(__file__).parent.parent / "uploads"
    capital_one_files = list(uploads_dir.glob("*Capital One.xlsx"))

    if not capital_one_files:
        # Check data/input_files as fallback
        input_files_dir = Path(__file__).parent.parent / "data" / "input_files"
        capital_one_file = input_files_dir / "Capital One.xlsx"
        if not capital_one_file.exists():
            logger.error("No Capital One.xlsx file found in uploads or input_files")
            return
    else:
        # Use most recent uploaded file
        capital_one_file = max(capital_one_files, key=lambda f: f.stat().st_mtime)

    logger.info(f"Processing Capital One file: {capital_one_file}")

    # Read Excel file and analyze structure
    try:
        # Read all sheets to find the main transaction data
        excel_file = pd.ExcelFile(capital_one_file)
        logger.info(f"Excel sheets found: {excel_file.sheet_names}")

        # Process each sheet to find transaction data
        for sheet_name in excel_file.sheet_names:
            logger.info(f"\n🔍 Analyzing sheet: {sheet_name}")

            # Read sheet
            df = pd.read_excel(capital_one_file, sheet_name=sheet_name)
            logger.info(f"Sheet dimensions: {df.shape}")
            logger.info(f"Columns: {list(df.columns)}")

            # Prepare data for AI analysis
            file_headers = list(df.columns)
            sample_data = []

            # Get first 10 rows for analysis
            for _, row in df.head(10).iterrows():
                sample_data.append([str(val) if pd.notna(val) else "" for val in row])

            # Run AI schema interpretation
            logger.info(f"🤖 Running AI schema interpretation for {sheet_name}...")
            interpretation_result = await suggest_schema_mapping_tool_function(
                file_name=f"Capital One.xlsx - {sheet_name}",
                file_headers=file_headers,
                sample_data=sample_data,
            )

            # Save interpretation results
            output_dir = Path(__file__).parent.parent / "data" / "production_output"
            output_dir.mkdir(exist_ok=True)

            output_file = (
                output_dir
                / f"capital_one_{sheet_name.lower().replace(' ', '_')}_interpretation.json"
            )

            with open(output_file, "w") as f:
                json.dump(
                    {
                        "file_name": f"Capital One.xlsx - {sheet_name}",
                        "sheet_name": sheet_name,
                        "analysis_timestamp": pd.Timestamp.now().isoformat(),
                        "interpretation_result": interpretation_result,
                        "source_columns": file_headers,
                        "sample_data_rows": len(sample_data),
                    },
                    f,
                    indent=2,
                )

            logger.info(f"✅ AI interpretation saved to: {output_file}")

            # Extract category information for multilevel learning
            await extract_and_learn_categories(df, sheet_name, interpretation_result)

    except Exception as e:
        logger.error(f"Error processing Capital One file: {e}")
        import traceback

        traceback.print_exc()


async def extract_and_learn_categories(
    df: pd.DataFrame, sheet_name: str, interpretation: Dict[str, Any]
):
    """Extract and learn multilevel categories from Capital One data."""

    logger.info(f"🏷️ Extracting categories from {sheet_name}...")

    # Find category columns from interpretation
    category_columns = []
    for mapping in interpretation.get("column_mappings", []):
        mapped_field = mapping.get("mapped_field", "")
        if mapped_field.startswith("category_"):
            category_columns.append(
                {
                    "original_name": mapping["original_name"],
                    "level": mapped_field.replace("category_", ""),
                    "confidence": mapping.get("confidence", 0.0),
                }
            )

    if not category_columns:
        # Look for obvious category columns in the data
        for col in df.columns:
            col_lower = str(col).lower()
            if any(
                keyword in col_lower
                for keyword in ["category", "type", "class", "group", "merchant"]
            ):
                category_columns.append(
                    {
                        "original_name": col,
                        "level": "l1",  # Default to level 1
                        "confidence": 0.8,
                    }
                )

    logger.info(
        f"Found {len(category_columns)} potential category columns: {[c['original_name'] for c in category_columns]}"
    )

    # Extract unique categories and build hierarchy
    multilevel_categories = {}

    for cat_col in category_columns:
        col_name = cat_col["original_name"]
        if col_name in df.columns:
            unique_values = df[col_name].dropna().unique()
            logger.info(f"Column '{col_name}' has {len(unique_values)} unique values")

            # Analyze for hierarchical patterns
            for value in unique_values:
                if pd.isna(value) or str(value).strip() == "":
                    continue

                value_str = str(value).strip()

                # Look for hierarchical separators
                if ">" in value_str:
                    # Split on > separator
                    levels = [level.strip() for level in value_str.split(">")]
                    current_dict = multilevel_categories
                    for level in levels:
                        if level not in current_dict:
                            current_dict[level] = {}
                        current_dict = current_dict[level]
                elif "/" in value_str:
                    # Split on / separator
                    levels = [level.strip() for level in value_str.split("/")]
                    current_dict = multilevel_categories
                    for level in levels:
                        if level not in current_dict:
                            current_dict[level] = {}
                        current_dict = current_dict[level]
                else:
                    # Single level category
                    if value_str not in multilevel_categories:
                        multilevel_categories[value_str] = {}

    # Save learned categories
    output_dir = Path(__file__).parent.parent / "data" / "production_output"
    output_dir.mkdir(exist_ok=True)

    categories_file = (
        output_dir
        / f"capital_one_{sheet_name.lower().replace(' ', '_')}_categories.json"
    )

    category_analysis = {
        "source_file": f"Capital One.xlsx - {sheet_name}",
        "analysis_timestamp": pd.Timestamp.now().isoformat(),
        "category_columns_found": category_columns,
        "multilevel_categories": multilevel_categories,
        "total_categories": count_categories_recursive(multilevel_categories),
        "max_depth": get_max_depth(multilevel_categories),
        "category_tree_sample": get_tree_sample(multilevel_categories, max_items=20),
    }

    with open(categories_file, "w") as f:
        json.dump(category_analysis, f, indent=2)

    logger.info(f"✅ Category analysis saved to: {categories_file}")
    logger.info(f"📊 Total categories found: {category_analysis['total_categories']}")
    logger.info(f"📊 Max hierarchy depth: {category_analysis['max_depth']}")

    return category_analysis


def count_categories_recursive(tree: dict, depth: int = 0) -> int:
    """Count total categories in tree."""
    count = len(tree)
    for subtree in tree.values():
        if isinstance(subtree, dict):
            count += count_categories_recursive(subtree, depth + 1)
    return count


def get_max_depth(tree: dict, current_depth: int = 1) -> int:
    """Get maximum depth of category tree."""
    if not tree:
        return current_depth - 1

    max_child_depth = current_depth
    for subtree in tree.values():
        if isinstance(subtree, dict) and subtree:
            child_depth = get_max_depth(subtree, current_depth + 1)
            max_child_depth = max(max_child_depth, child_depth)

    return max_child_depth


def get_tree_sample(tree: dict, max_items: int = 20, prefix: str = "") -> List[str]:
    """Get sample of category tree for display."""
    sample = []
    count = 0

    for name, subtree in tree.items():
        if count >= max_items:
            sample.append(f"{prefix}... ({len(tree) - count} more)")
            break

        if isinstance(subtree, dict) and subtree:
            sample.append(f"{prefix}{name}/")
            child_sample = get_tree_sample(
                subtree, max_items - count - 1, prefix + "  "
            )
            sample.extend(child_sample)
            count += len(child_sample) + 1
        else:
            sample.append(f"{prefix}{name}")
            count += 1

    return sample


async def main():
    """Main execution function."""
    logger.info("🚀 Starting Capital One AI Schema Interpretation")
    logger.info(
        "Task: CATEGORY-001 - Complete AI schema interpretation for Capital One file"
    )

    await analyze_capital_one_file()

    logger.info("✅ Capital One AI schema interpretation completed")
    logger.info("Next steps:")
    logger.info("  1. Review interpretation results in data/production_output/")
    logger.info("  2. Process confirmed interpretation to learn multilevel categories")
    logger.info(
        "  3. Verify learned categories are multilevel and stored with GL fields"
    )


if __name__ == "__main__":
    asyncio.run(main())
