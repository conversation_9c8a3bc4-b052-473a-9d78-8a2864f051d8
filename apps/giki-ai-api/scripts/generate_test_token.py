import datetime
import os
import sys

from jose import jwt

# Add the project root to the Python path to allow importing from src
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
giki_ai_API_SRC_DIR = os.path.abspath(
    os.path.join(SCRIPT_DIR, "..", "src")
)  # Path to apps/giki-ai-api/src
sys.path.insert(0, giki_ai_API_SRC_DIR)

# Now we can import from the project
try:
    from giki_ai_api.config import settings
except ImportError:
    sys.exit(1)


def create_access_token(data: dict, expires_delta: datetime.timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.datetime.now(datetime.UTC) + expires_delta
    else:
        # Default to a reasonable expiry for a test token, e.g., 1 day
        expire = datetime.datetime.now(datetime.UTC) + datetime.timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.AUTH_SECRET_KEY, algorithm=settings.AUTH_ALGORITHM
    )
    return encoded_jwt


if __name__ == "__main__":
    # Payload for the test token
    # Based on TokenData in auth.py: user_id (from sub), username (from sub), tenant_id (from tid), scopes
    test_user_id = 1  # Changed to integer
    test_tenant_id = 1  # Assuming tenant_id is an integer as per TokenData
    test_scopes = [
        "test_scope:read",
        "test_scope:write",
        "ingestion:upload",
    ]  # Added ingestion:upload scope

    token_payload = {
        "sub": str(test_user_id),  # Ensure 'sub' is a string
        "tid": test_tenant_id,  # 'tid' for tenant_id
        "scopes": test_scopes,
        # Add any other claims your application might expect for a test user
    }

    # Set token expiry (e.g., 7 days for a long-lived test token)
    # For E2E tests, a longer expiry might be convenient
    access_token_expires = datetime.timedelta(
        minutes=settings.AUTH_ACCESS_TOKEN_EXPIRE_MINUTES * 24 * 7
    )  # 7 days

    access_token = create_access_token(
        data=token_payload, expires_delta=access_token_expires
    )
