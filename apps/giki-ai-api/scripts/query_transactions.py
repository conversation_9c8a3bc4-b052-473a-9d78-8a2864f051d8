#!/usr/bin/env python3
import asyncio
import os

import asyncpg
from dotenv import load_dotenv


async def query_transactions():
    """Query transaction data for tenant_id = 1"""
    load_dotenv('.env.development')
    
    # Get database connection from environment or config
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        # Remove the +asyncpg part if present
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    else:
        # Default to local development database
        database_url = "postgresql://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db"
    
    conn = await asyncpg.connect(database_url)
    
    try:
        # Query 1: Get earliest and latest transaction dates
        date_range = await conn.fetchrow("""
            SELECT 
                MIN(date) as earliest_date,
                MAX(date) as latest_date,
                COUNT(*) as total_transactions
            FROM transactions
            WHERE tenant_id = 1
        """)
        
        # Query 2: Count transactions with original_category_label
        category_count = await conn.fetchrow("""
            SELECT 
                COUNT(*) as with_category,
                COUNT(*) FILTER (WHERE original_category_label IS NULL) as without_category
            FROM transactions
            WHERE tenant_id = 1
        """)
        
        # Query 3: Get sample transactions to understand date format
        sample_transactions = await conn.fetch("""
            SELECT 
                id,
                date,
                description,
                amount,
                original_category_label,
                category_path,
                ai_category_confidence
            FROM transactions
            WHERE tenant_id = 1
            ORDER BY date DESC
            LIMIT 5
        """)
        
        # Print results
        print("=== Transaction Data Analysis for Tenant ID 1 ===\n")
        
        print("Date Range:")
        print(f"  Earliest transaction: {date_range['earliest_date']}")
        print(f"  Latest transaction: {date_range['latest_date']}")
        print(f"  Total transactions: {date_range['total_transactions']}")
        
        print("\nCategory Label Statistics:")
        print(f"  Transactions with original_category_label: {category_count['with_category']}")
        print(f"  Transactions without original_category_label: {category_count['without_category']}")
        print(f"  Percentage with categories: {(category_count['with_category'] / date_range['total_transactions'] * 100):.2f}%")
        
        print("\nSample Transactions (Latest 5):")
        print("-" * 100)
        for txn in sample_transactions:
            print(f"ID: {txn['id']}")
            print(f"  Date: {txn['date']} (type: {type(txn['date']).__name__})")
            print(f"  Description: {txn['description'][:80]}...")
            print(f"  Amount: ${txn['amount']}")
            print(f"  Original Category: {txn['original_category_label']}")
            print(f"  Category Path: {txn['category_path']}")
            print(f"  AI Confidence: {txn['ai_category_confidence']}")
            print("-" * 100)
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(query_transactions())