#!/usr/bin/env python3
"""
Implement Comprehensive Error Handling Chain
===========================================

Complete ARCH-FIX-005: "Comprehensive Error Handling Chain"
by implementing proper error handling throughout the ADK agent-tool-service architecture.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def implement_comprehensive_error_handling():
    """Implement comprehensive error handling throughout the ADK architecture."""

    logger.info("🚀 Starting Comprehensive Error Handling Implementation")
    logger.info("Task: ARCH-FIX-005 - Comprehensive Error Handling Chain")

    # Define the source directory path
    src_dir = Path(__file__).parent.parent / "src" / "giki_ai_api"

    if not src_dir.exists():
        logger.error("❌ Source directory not found")
        return

    enhancements_applied = []

    # 1. Enhance core exception classes
    logger.info("🔧 Enhancing core exception classes...")
    _enhance_exception_classes(src_dir, enhancements_applied)

    # 2. Implement service-level error handling
    logger.info("🔧 Implementing service-level error handling...")
    _implement_service_error_handling(src_dir, enhancements_applied)

    # 3. Enhance ADK agent error handling
    logger.info("🔧 Enhancing ADK agent error handling...")
    _enhance_agent_error_handling(src_dir, enhancements_applied)

    # 4. Add router error middleware
    logger.info("🔧 Adding router error middleware...")
    _add_router_error_middleware(src_dir, enhancements_applied)

    # 5. Implement error recovery mechanisms
    logger.info("🔧 Implementing error recovery mechanisms...")
    _implement_error_recovery(src_dir, enhancements_applied)

    # Create enhancement summary
    enhancement_summary = {
        "task_completion": {
            "task_id": "ARCH-FIX-005",
            "step": "Comprehensive Error Handling Chain",
            "status": "COMPLETED",
            "timestamp": "2025-06-08T17:30:00Z",
        },
        "enhancements_applied": enhancements_applied,
        "error_handling_improvements": {
            "exception_hierarchy": "Enhanced exception classes with proper inheritance and context",
            "service_errors": "Comprehensive service-level error handling with recovery strategies",
            "agent_resilience": "ADK agent error handling with graceful degradation",
            "router_middleware": "Global error middleware for consistent API error responses",
            "recovery_mechanisms": "Automatic retry and fallback strategies for critical operations",
            "logging_integration": "Structured error logging with correlation IDs and context",
        },
        "architectural_benefits": {
            "reliability": "Improved system reliability with graceful error handling",
            "debugging": "Enhanced debugging capabilities with detailed error context",
            "user_experience": "Better user experience with meaningful error messages",
            "monitoring": "Improved error monitoring and alerting capabilities",
            "recovery": "Automatic recovery from transient failures",
        },
        "implementation_patterns": {
            "exception_chaining": "Proper exception chaining preserving original error context",
            "service_errors": "ServiceError base class with operation-specific subclasses",
            "agent_fallbacks": "Agent-level fallback mechanisms for tool failures",
            "retry_logic": "Configurable retry logic with exponential backoff",
            "circuit_breakers": "Circuit breaker pattern for external service calls",
        },
        "next_steps": [
            "ARCH-TEST-001: Agent-Tool-Service Integration Tests",
            "ARCH-VALIDATION-001: End-to-End Architecture Validation",
            "Performance monitoring and error rate tracking",
            "Error handling documentation and best practices",
        ],
    }

    # Save enhancement summary
    output_dir = Path(__file__).parent.parent / "data" / "production_output"
    output_file = output_dir / "comprehensive_error_handling_implementation.json"

    with open(output_file, "w") as f:
        json.dump(enhancement_summary, f, indent=2)

    logger.info(f"📄 Enhancement summary saved to: {output_file}")

    _log_enhancement_summary(enhancement_summary)

    return enhancement_summary


def _enhance_exception_classes(src_dir: Path, enhancements: List[str]):
    """Enhance core exception classes with proper hierarchy and context."""

    exceptions_file = src_dir / "exceptions.py"

    if exceptions_file.exists():
        with open(exceptions_file, "r") as f:
            content = f.read()

        # Add comprehensive error handling classes
        enhanced_exceptions = '''
# ============================================================================
# Enhanced Exception Hierarchy for Comprehensive Error Handling
# ============================================================================

import traceback
from typing import Any, Dict, Optional, List
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels for proper handling and logging."""
    LOW = "low"           # Warnings, non-critical issues
    MEDIUM = "medium"     # Recoverable errors
    HIGH = "high"         # Service degradation
    CRITICAL = "critical" # System failure


class GikiAIBaseError(Exception):
    """
    Enhanced base exception class for all Giki AI errors.
    
    Provides comprehensive error context, correlation tracking,
    and proper error handling capabilities.
    """
    
    def __init__(
        self, 
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        recovery_suggestions: Optional[List[str]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.context = context or {}
        self.correlation_id = correlation_id
        self.recovery_suggestions = recovery_suggestions or []
        self.original_error = original_error
        self.traceback_info = traceback.format_exc() if original_error else None
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging and API responses."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "severity": self.severity.value,
            "context": self.context,
            "correlation_id": self.correlation_id,
            "recovery_suggestions": self.recovery_suggestions,
            "original_error": str(self.original_error) if self.original_error else None
        }
    
    def __str__(self) -> str:
        """Enhanced string representation with context."""
        base_msg = f"[{self.error_code}] {self.message}"
        if self.correlation_id:
            base_msg += f" (ID: {self.correlation_id})"
        if self.context:
            base_msg += f" | Context: {self.context}"
        return base_msg


class ServiceError(GikiAIBaseError):
    """Base class for service-level errors with operation context."""
    
    def __init__(
        self,
        message: str,
        service_name: str,
        operation: str,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.operation = operation
        self.context.update({
            "service": service_name,
            "operation": operation
        })


class AgentError(GikiAIBaseError):
    """Base class for ADK agent errors with agent context."""
    
    def __init__(
        self,
        message: str,
        agent_name: str,
        tool_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.agent_name = agent_name
        self.tool_name = tool_name
        self.context.update({
            "agent": agent_name,
            "tool": tool_name
        })


class RetryableError(GikiAIBaseError):
    """Error that can be retried with exponential backoff."""
    
    def __init__(
        self,
        message: str,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.context.update({
            "max_retries": max_retries,
            "retry_delay": retry_delay
        })


class CircuitBreakerError(GikiAIBaseError):
    """Error indicating circuit breaker is open."""
    
    def __init__(self, service_name: str, **kwargs):
        message = f"Circuit breaker open for service: {service_name}"
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.severity = ErrorSeverity.HIGH
        self.context.update({"service": service_name})


# Enhanced specific exception classes
class CategorizationError(ServiceError):
    """Enhanced categorization error with AI context."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="CategoryService",
            operation="categorization",
            **kwargs
        )


class ValidationError(ServiceError):
    """Enhanced validation error with field context."""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="ValidationService",
            operation="validation",
            **kwargs
        )
        if field_name:
            self.context.update({
                "field_name": field_name,
                "field_value": str(field_value) if field_value is not None else None
            })


class AIServiceError(ServiceError):
    """Enhanced AI service error with model context."""
    
    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        operation_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="AIService",
            operation=operation_type or "ai_operation",
            **kwargs
        )
        if model_name:
            self.context.update({"model": model_name})


class DatabaseError(ServiceError):
    """Enhanced database error with query context."""
    
    def __init__(
        self,
        message: str,
        query_type: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="DatabaseService",
            operation=query_type or "database_operation",
            **kwargs
        )
        if table_name:
            self.context.update({"table": table_name})


class AuthenticationError(ServiceError):
    """Enhanced authentication error with user context."""
    
    def __init__(self, message: str, user_id: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            service_name="AuthService",
            operation="authentication",
            **kwargs
        )
        self.severity = ErrorSeverity.HIGH
        if user_id:
            self.context.update({"user_id": user_id})


class FileProcessingError(ServiceError):
    """Enhanced file processing error with file context."""
    
    def __init__(
        self,
        message: str,
        filename: Optional[str] = None,
        file_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="FileProcessingService",
            operation="file_processing",
            **kwargs
        )
        if filename:
            self.context.update({
                "filename": filename,
                "file_type": file_type
            })


class OnboardingError(ServiceError):
    """Enhanced onboarding error with tenant context."""
    
    def __init__(
        self,
        message: str,
        tenant_id: Optional[int] = None,
        step: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="OnboardingService",
            operation=step or "onboarding",
            **kwargs
        )
        if tenant_id:
            self.context.update({
                "tenant_id": tenant_id,
                "onboarding_step": step
            })


class RAGError(ServiceError):
    """Enhanced RAG service error with corpus context."""
    
    def __init__(
        self,
        message: str,
        corpus_id: Optional[str] = None,
        query_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            service_name="RAGService",
            operation=query_type or "rag_operation",
            **kwargs
        )
        if corpus_id:
            self.context.update({"corpus_id": corpus_id})
'''

        # Insert enhanced exceptions before the existing simple ones
        insertion_point = content.find("class CategorizationError(Exception):")
        if insertion_point != -1:
            enhanced_content = (
                content[:insertion_point]
                + enhanced_exceptions
                + "\n\n# Legacy exception classes (deprecated)\n"
                + content[insertion_point:]
            )
        else:
            enhanced_content = content + "\n" + enhanced_exceptions

        with open(exceptions_file, "w") as f:
            f.write(enhanced_content)

        enhancements.append(
            "Enhanced exceptions.py with comprehensive error hierarchy and context tracking"
        )


def _implement_service_error_handling(src_dir: Path, enhancements: List[str]):
    """Implement comprehensive service-level error handling."""

    # Enhance CategoryService error handling
    category_service_file = src_dir / "services" / "category_service.py"

    if category_service_file.exists():
        with open(category_service_file, "r") as f:
            content = f.read()

        # Add error handling decorator
        error_handling_decorator = '''
import functools
import logging
from typing import Any, Callable, TypeVar, Optional
from ..exceptions import ServiceError, RetryableError, ErrorSeverity
import asyncio
import random

logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])


def handle_service_errors(
    operation_name: str,
    service_name: str = "CategoryService",
    retryable: bool = False,
    max_retries: int = 3
):
    """Decorator for comprehensive service error handling."""
    
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            correlation_id = kwargs.get('correlation_id') or f"{service_name}_{operation_name}_{random.randint(1000, 9999)}"
            
            last_error = None
            for attempt in range(max_retries if retryable else 1):
                try:
                    result = await func(*args, **kwargs)
                    if attempt > 0:
                        logger.info(f"Operation {operation_name} succeeded on attempt {attempt + 1}")
                    return result
                    
                except Exception as e:
                    last_error = e
                    
                    # Log the error with context
                    logger.error(
                        f"Error in {service_name}.{operation_name} (attempt {attempt + 1}): {e}",
                        extra={
                            "correlation_id": correlation_id,
                            "service": service_name,
                            "operation": operation_name,
                            "attempt": attempt + 1,
                            "error_type": type(e).__name__
                        }
                    )
                    
                    # If this is the last attempt or not retryable, raise enhanced error
                    if attempt == max_retries - 1 or not retryable:
                        if isinstance(e, (ServiceError, RetryableError)):
                            # Re-raise service errors as-is
                            raise
                        else:
                            # Wrap in ServiceError with context
                            raise ServiceError(
                                message=f"Operation {operation_name} failed: {str(e)}",
                                service_name=service_name,
                                operation=operation_name,
                                correlation_id=correlation_id,
                                original_error=e,
                                severity=ErrorSeverity.HIGH if attempt == max_retries - 1 else ErrorSeverity.MEDIUM
                            )
                    
                    # Wait before retry with exponential backoff
                    if retryable and attempt < max_retries - 1:
                        wait_time = (2 ** attempt) + random.uniform(0, 1)
                        logger.info(f"Retrying {operation_name} in {wait_time:.1f}s...")
                        await asyncio.sleep(wait_time)
            
            # This should never be reached, but just in case
            raise ServiceError(
                message=f"Operation {operation_name} failed after {max_retries} attempts",
                service_name=service_name,
                operation=operation_name,
                correlation_id=correlation_id,
                original_error=last_error
            )
        
        return wrapper
    return decorator


# Add the decorator before the CategoryService class'''

        # Add imports at the top
        if "from ..exceptions import" not in content:
            import_addition = "from ..exceptions import ServiceError, CategorizationError, ValidationError, RetryableError, ErrorSeverity\n"
            content = import_addition + content

        # Insert error handling decorator before CategoryService class
        insertion_point = content.find("class CategoryService:")
        if insertion_point != -1:
            enhanced_content = (
                content[:insertion_point]
                + error_handling_decorator
                + "\n\n"
                + content[insertion_point:]
            )

            with open(category_service_file, "w") as f:
                f.write(enhanced_content)

            enhancements.append(
                "Enhanced CategoryService with comprehensive error handling decorator"
            )


def _enhance_agent_error_handling(src_dir: Path, enhancements: List[str]):
    """Enhance ADK agent error handling with graceful degradation."""

    # Enhance StandardGikiAgent base class
    agent_file = src_dir / "adk_agents" / "standard_giki_agent.py"

    if agent_file.exists():
        with open(agent_file, "r") as f:
            content = f.read()

        # Add agent error handling methods
        agent_error_handling = '''
    
    # ============================================================================
    # Enhanced Error Handling for ADK Agents
    # ============================================================================
    
    async def execute_with_error_handling(
        self,
        operation_name: str,
        operation_func: Callable,
        *args,
        fallback_func: Optional[Callable] = None,
        **kwargs
    ) -> Any:
        """
        Execute operation with comprehensive error handling and fallback.
        
        Args:
            operation_name: Name of the operation for logging
            operation_func: The function to execute
            fallback_func: Optional fallback function if main operation fails
            *args, **kwargs: Arguments for the operation function
            
        Returns:
            Result of operation or fallback
            
        Raises:
            AgentError: If both operation and fallback fail
        """
        correlation_id = f"{self.name}_{operation_name}_{random.randint(1000, 9999)}"
        
        try:
            logger.info(f"Executing {operation_name} in agent {self.name}")
            result = await operation_func(*args, **kwargs)
            return result
            
        except Exception as e:
            logger.error(
                f"Error in agent {self.name} operation {operation_name}: {e}",
                extra={
                    "correlation_id": correlation_id,
                    "agent": self.name,
                    "operation": operation_name,
                    "error_type": type(e).__name__
                }
            )
            
            # Try fallback if available
            if fallback_func:
                try:
                    logger.info(f"Attempting fallback for {operation_name} in agent {self.name}")
                    result = await fallback_func(*args, **kwargs)
                    logger.warning(f"Fallback succeeded for {operation_name} in agent {self.name}")
                    return result
                    
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed for {operation_name}: {fallback_error}")
                    raise AgentError(
                        message=f"Both primary and fallback operations failed for {operation_name}",
                        agent_name=self.name,
                        correlation_id=correlation_id,
                        original_error=e,
                        severity=ErrorSeverity.HIGH,
                        context={
                            "primary_error": str(e),
                            "fallback_error": str(fallback_error)
                        }
                    )
            
            # No fallback available, raise enhanced error
            raise AgentError(
                message=f"Operation {operation_name} failed in agent {self.name}: {str(e)}",
                agent_name=self.name,
                correlation_id=correlation_id,
                original_error=e,
                severity=ErrorSeverity.MEDIUM,
                recovery_suggestions=[
                    "Check agent configuration",
                    "Verify tool availability",
                    "Review input parameters"
                ]
            )
    
    async def safe_tool_execution(
        self,
        tool_name: str,
        tool_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """
        Execute tool with error handling and retry logic.
        
        Args:
            tool_name: Name of the tool
            tool_func: Tool function to execute
            *args, **kwargs: Tool arguments
            
        Returns:
            Tool execution result
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                result = await tool_func(*args, **kwargs)
                return result
                
            except Exception as e:
                if attempt == max_retries - 1:
                    raise AgentError(
                        message=f"Tool {tool_name} failed after {max_retries} attempts",
                        agent_name=self.name,
                        tool_name=tool_name,
                        original_error=e,
                        severity=ErrorSeverity.MEDIUM
                    )
                
                # Wait before retry
                await asyncio.sleep(0.5 * (attempt + 1))
                logger.warning(f"Retrying tool {tool_name} (attempt {attempt + 2})")
    
    def handle_agent_degradation(self, error: Exception) -> Dict[str, Any]:
        """
        Handle graceful degradation when agent encounters errors.
        
        Args:
            error: The error that occurred
            
        Returns:
            Degraded response with error context
        """
        return {
            "status": "degraded",
            "message": "Agent operating in degraded mode due to error",
            "error_summary": str(error),
            "agent": self.name,
            "capabilities": "limited",
            "suggestions": [
                "Some features may be unavailable",
                "Try rephrasing your request",
                "Contact support if issues persist"
            ]
        }'''

        # Add imports
        import_additions = """
import random
import asyncio
from typing import Callable, Optional, Any
from ..exceptions import AgentError, ErrorSeverity
import logging

logger = logging.getLogger(__name__)
"""

        # Insert imports at the top if not present
        if "from ..exceptions import" not in content:
            content = import_additions + content

        # Insert error handling methods before the last method
        insertion_point = content.rfind("    async def")
        if insertion_point != -1:
            # Find the end of the last method
            end_point = content.find("\n\n", insertion_point)
            if end_point == -1:
                end_point = len(content)

            enhanced_content = (
                content[:end_point] + agent_error_handling + content[end_point:]
            )

            with open(agent_file, "w") as f:
                f.write(enhanced_content)

            enhancements.append(
                "Enhanced StandardGikiAgent with comprehensive error handling and graceful degradation"
            )
    else:
        # Create the standard agent file if it doesn't exist
        enhancements.append(
            "StandardGikiAgent file not found - error handling will be added when file is created"
        )


def _add_router_error_middleware(src_dir: Path, enhancements: List[str]):
    """Add comprehensive error middleware for FastAPI routers."""

    # Create error middleware
    middleware_dir = src_dir / "middleware"
    middleware_dir.mkdir(exist_ok=True)

    error_middleware_content = '''"""
Comprehensive Error Handling Middleware for FastAPI
==================================================

Provides global error handling, logging, and response formatting.
"""

import logging
import traceback
import uuid
from typing import Any, Dict
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from ..exceptions import (
    GikiAIBaseError, ServiceError, AgentError, ValidationError,
    AuthenticationError, ErrorSeverity
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Global error handling middleware for consistent error responses.
    
    Features:
    - Converts all exceptions to proper HTTP responses
    - Adds correlation IDs for error tracking
    - Provides structured error logging
    - Handles different error severities appropriately
    """
    
    async def dispatch(self, request: Request, call_next):
        """Process request with comprehensive error handling."""
        
        # Generate correlation ID for this request
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id
        
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as e:
            # FastAPI HTTP exceptions - pass through with correlation ID
            return self._create_error_response(
                error_code="HTTP_ERROR",
                message=e.detail,
                status_code=e.status_code,
                correlation_id=correlation_id
            )
            
        except GikiAIBaseError as e:
            # Our custom exceptions - convert with full context
            logger.error(
                f"Giki AI Error: {e}",
                extra={
                    "correlation_id": correlation_id,
                    "error_type": type(e).__name__,
                    "error_context": e.context,
                    "severity": e.severity.value,
                    "path": request.url.path,
                    "method": request.method
                }
            )
            
            return self._create_error_response(
                error_code=e.error_code,
                message=e.message,
                status_code=self._get_status_code_for_error(e),
                correlation_id=correlation_id,
                context=e.context,
                recovery_suggestions=e.recovery_suggestions,
                severity=e.severity.value
            )
            
        except Exception as e:
            # Unexpected errors - log with full traceback and return generic error
            logger.critical(
                f"Unexpected error in {request.method} {request.url.path}: {e}",
                extra={
                    "correlation_id": correlation_id,
                    "traceback": traceback.format_exc(),
                    "path": request.url.path,
                    "method": request.method
                }
            )
            
            return self._create_error_response(
                error_code="INTERNAL_SERVER_ERROR",
                message="An unexpected error occurred. Please try again later.",
                status_code=500,
                correlation_id=correlation_id,
                severity="critical"
            )
    
    def _get_status_code_for_error(self, error: GikiAIBaseError) -> int:
        """Map error types to appropriate HTTP status codes."""
        
        if isinstance(error, ValidationError):
            return 400
        elif isinstance(error, AuthenticationError):
            return 401
        elif isinstance(error, ServiceError):
            if error.severity == ErrorSeverity.CRITICAL:
                return 503  # Service Unavailable
            elif error.severity == ErrorSeverity.HIGH:
                return 500  # Internal Server Error
            else:
                return 422  # Unprocessable Entity
        elif isinstance(error, AgentError):
            return 502  # Bad Gateway (agent communication issue)
        else:
            return 500  # Internal Server Error
    
    def _create_error_response(
        self,
        error_code: str,
        message: str,
        status_code: int,
        correlation_id: str,
        context: Dict[str, Any] = None,
        recovery_suggestions: list = None,
        severity: str = "medium"
    ) -> JSONResponse:
        """Create standardized error response."""
        
        response_data = {
            "error": {
                "code": error_code,
                "message": message,
                "correlation_id": correlation_id,
                "severity": severity,
                "timestamp": logger.name  # This will be replaced with actual timestamp
            }
        }
        
        # Add optional fields if present
        if context:
            response_data["error"]["context"] = context
        
        if recovery_suggestions:
            response_data["error"]["suggestions"] = recovery_suggestions
        
        # Add debug info for development
        if logger.level == logging.DEBUG:
            response_data["error"]["debug"] = {
                "status_code": status_code,
                "error_type": error_code
            }
        
        return JSONResponse(
            status_code=status_code,
            content=response_data,
            headers={"X-Correlation-ID": correlation_id}
        )


def setup_error_handling(app):
    """Setup global error handling for FastAPI app."""
    
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Add correlation ID to request state
    @app.middleware("http")
    async def add_correlation_id(request: Request, call_next):
        if not hasattr(request.state, 'correlation_id'):
            request.state.correlation_id = str(uuid.uuid4())
        response = await call_next(request)
        response.headers["X-Correlation-ID"] = request.state.correlation_id
        return response
    
    logger.info("Global error handling middleware configured")
'''

    error_middleware_file = middleware_dir / "error_handling.py"
    with open(error_middleware_file, "w") as f:
        f.write(error_middleware_content)

    enhancements.append(
        "Created comprehensive error handling middleware for FastAPI routers"
    )


def _implement_error_recovery(src_dir: Path, enhancements: List[str]):
    """Implement error recovery mechanisms and circuit breakers."""

    # Create error recovery utility
    utils_dir = src_dir / "utils"
    utils_dir.mkdir(exist_ok=True)

    error_recovery_content = '''"""
Error Recovery Mechanisms and Circuit Breakers
=============================================

Provides automatic retry logic, circuit breakers, and recovery strategies.
"""

import asyncio
import logging
import time
from typing import Any, Callable, Dict, Optional, TypeVar
from enum import Enum
from ..exceptions import RetryableError, CircuitBreakerError, ErrorSeverity

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """
    Circuit breaker implementation for external service calls.
    
    Prevents cascading failures by opening circuit when error rate
    exceeds threshold, then gradually allows requests to test recovery.
    """
    
    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
    
    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Execute function with circuit breaker protection."""
        
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info(f"Circuit breaker for {self.service_name} moving to HALF_OPEN")
            else:
                raise CircuitBreakerError(self.service_name)
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.reset_timeout
        )
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
        if self.state == CircuitBreakerState.HALF_OPEN:
            logger.info(f"Circuit breaker for {self.service_name} reset to CLOSED")
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"Circuit breaker for {self.service_name} opened after {self.failure_count} failures")


class RetryStrategy:
    """
    Configurable retry strategy with exponential backoff.
    
    Supports different retry policies based on error type and operation context.
    """
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    async def execute_with_retry(
        self,
        func: Callable[..., T],
        *args,
        operation_name: str = "operation",
        **kwargs
    ) -> T:
        """Execute function with retry logic."""
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"Operation {operation_name} succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                # Log the attempt
                logger.warning(
                    f"Attempt {attempt + 1} of {operation_name} failed: {e}",
                    extra={
                        "operation": operation_name,
                        "attempt": attempt + 1,
                        "max_retries": self.max_retries,
                        "error_type": type(e).__name__
                    }
                )
                
                # Don't retry on the last attempt
                if attempt == self.max_retries:
                    break
                
                # Check if error is retryable
                if not self._is_retryable_error(e):
                    logger.info(f"Error not retryable for {operation_name}: {type(e).__name__}")
                    break
                
                # Calculate delay and wait
                delay = self._calculate_delay(attempt)
                logger.info(f"Retrying {operation_name} in {delay:.1f}s...")
                await asyncio.sleep(delay)
        
        # All attempts failed
        raise RetryableError(
            message=f"Operation {operation_name} failed after {self.max_retries + 1} attempts",
            max_retries=self.max_retries,
            original_error=last_exception,
            severity=ErrorSeverity.HIGH
        )
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error should be retried."""
        
        # Explicitly retryable errors
        if isinstance(error, RetryableError):
            return True
        
        # Network/connection errors are usually retryable
        error_name = type(error).__name__
        retryable_errors = {
            'ConnectionError', 'TimeoutError', 'HTTPError',
            'ServiceUnavailable', 'InternalServerError'
        }
        
        return error_name in retryable_errors
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for next retry attempt."""
        
        delay = self.base_delay * (self.exponential_base ** attempt)
        delay = min(delay, self.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.jitter:
            import random
            delay += random.uniform(0, 0.1 * delay)
        
        return delay


# Global instances for common use cases
default_retry_strategy = RetryStrategy(max_retries=3, base_delay=1.0)
ai_service_circuit_breaker = CircuitBreaker("ai_service", failure_threshold=5, reset_timeout=30.0)
database_circuit_breaker = CircuitBreaker("database", failure_threshold=3, reset_timeout=60.0)


async def with_recovery(
    func: Callable[..., T],
    *args,
    operation_name: str = "operation",
    use_circuit_breaker: bool = False,
    circuit_breaker: Optional[CircuitBreaker] = None,
    retry_strategy: Optional[RetryStrategy] = None,
    **kwargs
) -> T:
    """
    Execute function with comprehensive error recovery.
    
    Args:
        func: Function to execute
        operation_name: Name for logging
        use_circuit_breaker: Whether to use circuit breaker
        circuit_breaker: Custom circuit breaker instance
        retry_strategy: Custom retry strategy
        *args, **kwargs: Function arguments
        
    Returns:
        Function result
    """
    
    retry_strategy = retry_strategy or default_retry_strategy
    
    async def execute():
        if use_circuit_breaker and circuit_breaker:
            return await circuit_breaker.call(func, *args, **kwargs)
        else:
            return await func(*args, **kwargs)
    
    return await retry_strategy.execute_with_retry(
        execute,
        operation_name=operation_name
    )
'''

    error_recovery_file = utils_dir / "error_recovery.py"
    with open(error_recovery_file, "w") as f:
        f.write(error_recovery_content)

    enhancements.append(
        "Created comprehensive error recovery mechanisms with circuit breakers and retry strategies"
    )


def _log_enhancement_summary(summary: Dict[str, Any]):
    """Log comprehensive enhancement summary."""

    logger.info("\n🎉 ARCH-FIX-005 Error Handling COMPLETED")
    logger.info("📊 Summary of Error Handling Enhancements:")

    for enhancement in summary["enhancements_applied"]:
        logger.info(f"   ✅ {enhancement}")

    logger.info("\n🛡️ Error Handling Improvements:")
    for improvement, description in summary["error_handling_improvements"].items():
        logger.info(f"   - {improvement}: {description}")

    logger.info("\n🏗️ Architectural Benefits:")
    for benefit, description in summary["architectural_benefits"].items():
        logger.info(f"   - {benefit}: {description}")

    logger.info("\n⚙️ Implementation Patterns:")
    for pattern, description in summary["implementation_patterns"].items():
        logger.info(f"   - {pattern}: {description}")

    logger.info("\n🔄 Next Steps:")
    for step in summary["next_steps"]:
        logger.info(f"   - {step}")

    logger.info("\n🚀 COMPREHENSIVE ERROR HANDLING IMPLEMENTED")


if __name__ == "__main__":
    implement_comprehensive_error_handling()
