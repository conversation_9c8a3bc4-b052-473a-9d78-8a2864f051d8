#!/usr/bin/env python3
"""
Verify GL Category Migration
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from sqlalchemy import text

from giki_ai_api.database import get_database_config


async def verify_migration():
    """Verify that the GL category migration was applied correctly."""
    try:
        # Get database configuration
        get_database_config()

        # Import the engine after configuration
        from giki_ai_api.database import engine

        async with engine.begin() as conn:
            print("🔍 Verifying GL category migration...")
            print("=" * 50)

            # Check the dev_category table structure
            result = await conn.execute(
                text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'dev_category' AND table_schema = 'public'
                ORDER BY ordinal_position;
            """)
            )

            columns = result.fetchall()
            print(f"📋 dev_category table has {len(columns)} columns:")
            print("-" * 70)

            gl_fields = [
                "gl_code",
                "gl_account_name",
                "gl_account_type",
                "level",
                "learned_from_onboarding",
                "frequency_score",
                "confidence_score",
                "path",
            ]

            found_gl_fields = []

            for col in columns:
                nullable = "✅ NULL" if col.is_nullable == "YES" else "❌ NOT NULL"
                default = (
                    f" (default: {col.column_default})" if col.column_default else ""
                )
                marker = "🆕" if col.column_name in gl_fields else "  "

                print(
                    f"{marker} {col.column_name:25} {col.data_type:20} {nullable}{default}"
                )

                if col.column_name in gl_fields:
                    found_gl_fields.append(col.column_name)

            print("-" * 70)
            print(
                f"✅ Found {len(found_gl_fields)}/{len(gl_fields)} GL category fields:"
            )
            for field in gl_fields:
                status = "✅" if field in found_gl_fields else "❌"
                print(f"   {status} {field}")

            # Check indexes
            print("\n🔗 Checking indexes for GL fields...")
            index_result = await conn.execute(
                text("""
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'dev_category' 
                AND (indexname LIKE '%gl_%' OR indexname LIKE '%level%' OR indexname LIKE '%path%')
                ORDER BY indexname;
            """)
            )

            indexes = index_result.fetchall()
            print(f"📊 Found {len(indexes)} GL-related indexes:")
            for idx in indexes:
                print(f"   📌 {idx.indexname}")

            if len(found_gl_fields) == len(gl_fields):
                print("\n🎉 GL category migration successfully applied!")
                return True
            else:
                print(
                    f"\n⚠️  Migration incomplete: {len(found_gl_fields)}/{len(gl_fields)} fields found"
                )
                return False

    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(verify_migration())
    sys.exit(0 if success else 1)
