"""
Production user setup script.

This script should be run via Cloud Run Jobs or manually after deployment
to create initial users in the production database.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from src.giki_ai_api.domains.auth.models import User
from src.giki_ai_api.domains.auth.secure_auth import get_password_hash


async def create_production_users():
    """Create initial users for production."""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        sys.exit(1)
    
    # Create engine
    engine = create_async_engine(database_url, echo=True)
    
    # Define users to create
    production_users = [
        {
            "email": "<EMAIL>",
            "password": "password123",  # Change this in production!
            "is_admin": False,
            "tenant_id": 1,
        },
        {
            "email": "<EMAIL>",
            "password": "admin123",  # Change this in production!
            "is_admin": True,
            "tenant_id": 1,
        },
    ]
    
    async with AsyncSession(engine) as session:
        for user_data in production_users:
            # Check if user exists
            result = await session.execute(
                select(User).where(User.email == user_data["email"])
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print(f"User already exists: {user_data['email']}")
            else:
                # Create new user
                print(f"Creating user: {user_data['email']}")
                new_user = User(
                    email=user_data["email"],
                    hashed_password=get_password_hash(user_data["password"]),
                    is_active=True,
                    is_admin=user_data["is_admin"],
                    tenant_id=user_data["tenant_id"],
                )
                session.add(new_user)
        
        await session.commit()
        print("Production users created successfully!")
        
        # List all users
        result = await session.execute(select(User))
        all_users = result.scalars().all()
        print(f"\nTotal users in database: {len(all_users)}")
        for user in all_users:
            print(f"  - {user.email} (admin: {user.is_admin}, active: {user.is_active})")
    
    await engine.dispose()


if __name__ == "__main__":
    print("Production User Setup Script")
    print("=" * 50)
    print("WARNING: This script will create users in the production database!")
    print("Make sure DATABASE_URL is set correctly.")
    print()
    
    confirm = input("Continue? (yes/no): ")
    if confirm.lower() != "yes":
        print("Aborted.")
        sys.exit(0)
    
    asyncio.run(create_production_users())