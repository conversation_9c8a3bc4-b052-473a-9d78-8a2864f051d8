#!/usr/bin/env python3
"""
Apply GL Code Category Migration

This script applies the database migration for the multilevel GL category system.
It adds the necessary fields to support the core business requirement that categories
are always multilevel and map to GL codes for accounting integration.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

import logging

from sqlalchemy import text

from giki_ai_api.config import settings
from giki_ai_api.database import get_database_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def apply_gl_migration():
    """Apply the GL category migration."""
    try:
        # Get database configuration
        get_database_config()

        # Import the necessary database components
        from giki_ai_api.database import engine

        async with engine.begin() as conn:
            logger.info("Starting GL category migration...")

            # Read migration SQL
            migration_file = (
                project_root / "migrations" / "add_gl_code_fields_to_categories.sql"
            )
            with open(migration_file, "r") as f:
                migration_sql = f.read()

            # Split migration into individual statements
            statements = [
                stmt.strip() for stmt in migration_sql.split(";") if stmt.strip()
            ]

            # Execute each statement
            for i, statement in enumerate(statements):
                if statement.strip():
                    try:
                        logger.info(
                            f"Executing migration statement {i + 1}/{len(statements)}"
                        )
                        logger.debug(f"Statement: {statement[:100]}...")

                        await conn.execute(text(statement))

                    except Exception as e:
                        # Some statements might fail if columns already exist
                        if (
                            "already exists" in str(e).lower()
                            or "duplicate column" in str(e).lower()
                        ):
                            logger.info(
                                f"Column already exists, skipping statement {i + 1}"
                            )
                            continue
                        else:
                            logger.error(f"Error executing statement {i + 1}: {e}")
                            raise

            logger.info("GL category migration completed successfully!")

            # Verify migration by checking for new columns
            verification_sql = """
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'categories' 
            AND column_name IN ('gl_code', 'gl_account_name', 'gl_account_type', 'level', 'learned_from_onboarding', 'frequency_score', 'confidence_score')
            ORDER BY column_name;
            """

            result = await conn.execute(text(verification_sql))
            columns = result.fetchall()

            logger.info("Migration verification - New columns added:")
            for column in columns:
                logger.info(
                    f"  - {column.column_name}: {column.data_type} (nullable: {column.is_nullable})"
                )

        # Engine will be disposed automatically when exiting the context

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        logger.exception("Full exception traceback:")
        raise


async def main():
    """Main function to run the migration."""
    logger.info("GL Category Migration Script")
    logger.info("============================")
    logger.info(f"Database URL: {settings.DATABASE_URL[:50]}...")

    try:
        await apply_gl_migration()
        logger.info("Migration completed successfully!")
        return 0

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
