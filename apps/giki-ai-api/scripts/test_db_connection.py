"""
Test database connection with various methods to diagnose Cloud Run issue.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import asyncpg
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine


async def test_direct_asyncpg():
    """Test direct asyncpg connection."""
    db_url = os.environ.get("DATABASE_URL", "")
    if not db_url:
        print("DATABASE_URL not set")
        return
    
    print(f"DATABASE_URL length: {len(db_url)}")
    
    # Parse the URL manually
    from urllib.parse import urlparse
    parsed = urlparse(db_url)
    
    print(f"Host: {parsed.hostname}")
    print(f"Port: {parsed.port}")
    print(f"User: {parsed.username}")
    print(f"Database: {parsed.path.lstrip('/')}")
    
    try:
        # Try direct connection with parsed components
        conn = await asyncpg.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path.lstrip('/'),
            ssl='prefer'
        )
        
        result = await conn.fetchval("SELECT 1")
        print(f"Direct asyncpg connection successful: {result}")
        await conn.close()
        
    except Exception as e:
        print(f"Direct asyncpg connection failed: {e}")
        import traceback
        traceback.print_exc()


async def test_sqlalchemy_connection():
    """Test SQLAlchemy connection."""
    db_url = os.environ.get("DATABASE_URL", "")
    if not db_url:
        print("DATABASE_URL not set")
        return
    
    # Convert to async URL
    if db_url.startswith("postgresql://"):
        async_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        async_url = db_url
    
    try:
        engine = create_async_engine(async_url, echo=True)
        
        async with AsyncSession(engine) as session:
            result = await session.execute(text("SELECT 1"))
            print(f"SQLAlchemy connection successful: {result.scalar()}")
        
        await engine.dispose()
        
    except Exception as e:
        print(f"SQLAlchemy connection failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Load environment
    from dotenv import load_dotenv
    env_file = Path(__file__).parent.parent / ".env.production"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"Loaded {env_file}")
    
    print("Testing database connections...")
    print("=" * 50)
    
    asyncio.run(test_direct_asyncpg())
    print("\n" + "=" * 50 + "\n")
    asyncio.run(test_sqlalchemy_connection())