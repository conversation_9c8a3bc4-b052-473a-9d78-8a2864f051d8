#!/usr/bin/env python3
"""
Clean up existing Rezolve AI tenant data before fresh onboarding
"""

import asyncio
import logging
import os
import sys

# Add the source directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from sqlalchemy import delete, select

from giki_ai_api.database import get_db
from giki_ai_api.models.core import (
    Category,
    InterpretationCategorizationColumnStorage,
    InterpretationColumnMappingStorage,
    InterpretationResultStorage,
    Transaction,
    Upload,
    User,
)
from giki_ai_api.models.onboarding import OnboardingCompletion


async def cleanup_rezolve_data():
    """Clean up all existing data for Rezolve AI tenant (ID: 2)."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    REZOLVE_TENANT_ID = 2

    try:
        # Get database session
        async for db in get_db():
            logger.info(
                f"Starting cleanup for Rezolve AI tenant (ID: {REZOLVE_TENANT_ID})"
            )

            # Clean up in dependency order (child tables first)

            # 1. Clean up interpretation data
            logger.info("Cleaning interpretation categorization columns...")
            await db.execute(
                delete(InterpretationCategorizationColumnStorage).where(
                    InterpretationCategorizationColumnStorage.interpretation_result_id.in_(
                        select(InterpretationResultStorage.id).where(
                            InterpretationResultStorage.upload_id.in_(
                                select(Upload.id).where(
                                    Upload.tenant_id == REZOLVE_TENANT_ID
                                )
                            )
                        )
                    )
                )
            )

            logger.info("Cleaning interpretation column mappings...")
            await db.execute(
                delete(InterpretationColumnMappingStorage).where(
                    InterpretationColumnMappingStorage.interpretation_result_id.in_(
                        select(InterpretationResultStorage.id).where(
                            InterpretationResultStorage.upload_id.in_(
                                select(Upload.id).where(
                                    Upload.tenant_id == REZOLVE_TENANT_ID
                                )
                            )
                        )
                    )
                )
            )

            logger.info("Cleaning interpretation results...")
            await db.execute(
                delete(InterpretationResultStorage).where(
                    InterpretationResultStorage.upload_id.in_(
                        select(Upload.id).where(Upload.tenant_id == REZOLVE_TENANT_ID)
                    )
                )
            )

            # 2. Clean up transactions
            logger.info("Cleaning transactions...")
            transactions_result = await db.execute(
                select(Transaction).where(Transaction.tenant_id == REZOLVE_TENANT_ID)
            )
            transactions = transactions_result.scalars().all()
            logger.info(f"Found {len(transactions)} transactions to delete")

            await db.execute(
                delete(Transaction).where(Transaction.tenant_id == REZOLVE_TENANT_ID)
            )

            # 3. Clean up uploads
            logger.info("Cleaning uploads...")
            uploads_result = await db.execute(
                select(Upload).where(Upload.tenant_id == REZOLVE_TENANT_ID)
            )
            uploads = uploads_result.scalars().all()
            logger.info(f"Found {len(uploads)} uploads to delete")

            await db.execute(
                delete(Upload).where(Upload.tenant_id == REZOLVE_TENANT_ID)
            )

            # 4. Clean up categories (keep only default categories, remove learned ones)
            logger.info("Cleaning learned categories...")
            categories_result = await db.execute(
                select(Category).where(
                    Category.tenant_id == REZOLVE_TENANT_ID,
                    Category.learned_from_onboarding,
                )
            )
            learned_categories = categories_result.scalars().all()
            logger.info(f"Found {len(learned_categories)} learned categories to delete")

            await db.execute(
                delete(Category).where(
                    Category.tenant_id == REZOLVE_TENANT_ID,
                    Category.learned_from_onboarding,
                )
            )

            # 5. Clean up onboarding completion records
            logger.info("Cleaning onboarding completion records...")
            onboarding_result = await db.execute(
                select(OnboardingCompletion).where(
                    OnboardingCompletion.tenant_id == REZOLVE_TENANT_ID
                )
            )
            onboarding_records = onboarding_result.scalars().all()
            logger.info(f"Found {len(onboarding_records)} onboarding records to delete")

            await db.execute(
                delete(OnboardingCompletion).where(
                    OnboardingCompletion.tenant_id == REZOLVE_TENANT_ID
                )
            )

            # Commit all changes
            await db.commit()
            logger.info("All cleanup completed successfully!")

            # Show what remains for Rezolve AI
            logger.info("Remaining data for Rezolve AI:")

            remaining_transactions = await db.execute(
                select(Transaction).where(Transaction.tenant_id == REZOLVE_TENANT_ID)
            )
            logger.info(
                f"  Transactions: {len(remaining_transactions.scalars().all())}"
            )

            remaining_uploads = await db.execute(
                select(Upload).where(Upload.tenant_id == REZOLVE_TENANT_ID)
            )
            logger.info(f"  Uploads: {len(remaining_uploads.scalars().all())}")

            remaining_categories = await db.execute(
                select(Category).where(Category.tenant_id == REZOLVE_TENANT_ID)
            )
            logger.info(f"  Categories: {len(remaining_categories.scalars().all())}")

            users_result = await db.execute(
                select(User).where(User.tenant_id == REZOLVE_TENANT_ID)
            )
            users = users_result.scalars().all()
            logger.info(f"  Users: {len(users)} (kept for authentication)")

            break  # Exit the generator

    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(cleanup_rezolve_data())
