#!/usr/bin/env python3
"""
Script to create a simple SQLite database with a user.
"""

import hashlib
import os
import sqlite3
from pathlib import Path

# Create the database directory if it doesn't exist
db_path = Path(__file__).parent.parent / "test.db"

# Connect to the database
conn = sqlite3.connect(str(db_path))
cursor = conn.cursor()

# Create tables
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    client_type TEXT,
    industry TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
"""
)

cursor.execute(
    """
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL UNIQUE,
    hashed_password TEXT NOT NULL,
    full_name TEXT,
    is_active INTEGER DEFAULT 1,
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants (id)
)
"""
)

# Create a tenant
cursor.execute("SELECT id FROM tenants WHERE name = ?", ("Giki AI",))
tenant = cursor.fetchone()

if not tenant:
    cursor.execute("INSERT INTO tenants (name) VALUES (?)", ("Giki AI",))
    tenant_id = cursor.lastrowid
else:
    tenant_id = tenant[0]

# Create a user
email = "<EMAIL>"
password = "gikiai-dev-password"
# Simple password hashing for demonstration
salt = os.urandom(32)
hashed_password = hashlib.pbkdf2_hmac("sha256", password.encode("utf-8"), salt, 100000)
# Store as salt:hash
password_storage = salt.hex() + ":" + hashed_password.hex()

cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
user = cursor.fetchone()

if not user:
    cursor.execute(
        "INSERT INTO users (email, hashed_password, tenant_id, is_active) VALUES (?, ?, ?, 1)",
        (email, password_storage, tenant_id),
    )
    user_id = cursor.lastrowid
else:
    user_id = user[0]
    cursor.execute(
        "UPDATE users SET hashed_password = ? WHERE id = ?", (password_storage, user_id)
    )

# Commit changes and close connection
conn.commit()
conn.close()
