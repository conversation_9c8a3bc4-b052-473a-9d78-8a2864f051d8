#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a local SQLite database with a user for development.
"""

import sqlite3
from pathlib import Path

import bcrypt

# Create the database in the current directory
db_path = Path(__file__).parent.parent / "giki_ai_local.db"

# Connect to the database
conn = sqlite3.connect(str(db_path))
cursor = conn.cursor()

# Create tables
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    client_type TEXT,
    industry TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
"""
)

cursor.execute(
    """
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL UNIQUE,
    hashed_password TEXT NOT NULL,
    full_name TEXT,
    is_active INTEGER DEFAULT 1,
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants (id)
)
"""
)

# Create a tenant
cursor.execute("SELECT id FROM tenants WHERE name = ?", ("Giki AI",))
tenant = cursor.fetchone()

if not tenant:
    cursor.execute("INSERT INTO tenants (name) VALUES (?)", ("Giki AI",))
    tenant_id = cursor.lastrowid
else:
    tenant_id = tenant[0]

# Create a user with bcrypt-compatible password hashing
email = "<EMAIL>"
password = "gikiai-dev-password"

# Use bcrypt-style hashing for compatibility with the API
hashed_password = bcrypt.hashpw(password.encode("utf-8"), bcrypt.gensalt()).decode(
    "utf-8"
)

cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
user = cursor.fetchone()

if not user:
    cursor.execute(
        "INSERT INTO users (email, hashed_password, full_name, tenant_id, is_active) VALUES (?, ?, ?, ?, 1)",
        (email, hashed_password, "Nikhil Singh", tenant_id),
    )
    user_id = cursor.lastrowid
    print(f"Created user: {email} with ID: {user_id}")
else:
    user_id = user[0]
    cursor.execute(
        "UPDATE users SET hashed_password = ? WHERE id = ?", (hashed_password, user_id)
    )
    print(f"Updated user: {email} with ID: {user_id}")

# Commit changes and close connection
conn.commit()
conn.close()

print(f"Database created successfully at: {db_path}")
print(f"User credentials: {email} / {password}")
