"""
Prompt Registry - Centralized prompt management for giki.ai

This module provides a centralized system for managing, versioning, and tracking
AI prompts across the application. It supports:
- Version control for prompts
- Performance tracking
- A/B testing capabilities
- Easy review and modification
- Integration preparation for Langfuse

Future: This will integrate with Langfuse for production prompt management.
"""

import hashlib
import json
import logging
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class PromptCategory(Enum):
    """Categories for organizing prompts."""

    SCHEMA_INTERPRETATION = "schema_interpretation"
    CATEGORIZATION = "categorization"
    DEBIT_CREDIT_INFERENCE = "debit_credit_inference"
    REGIONAL_DETECTION = "regional_detection"
    HIERARCHY_DETECTION = "hierarchy_detection"
    CORRECTION_SUGGESTION = "correction_suggestion"
    TRANSACTION_EXTRACTION = "transaction_extraction"
    AI_JUDGE = "ai_judge"
    BUSINESS_APPROPRIATENESS = "business_appropriateness"


class PromptVersion:
    """Represents a versioned prompt with metadata."""

    def __init__(
        self,
        id: str,
        category: PromptCategory,
        name: str,
        template: str,
        version: str,
        variables: List[str],
        model_config: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.id = id
        self.category = category
        self.name = name
        self.template = template
        self.version = version
        self.variables = variables
        self.model_config = model_config
        self.metadata = metadata or {}
        self.created_at = datetime.utcnow()
        self.hash = self._calculate_hash()

    def _calculate_hash(self) -> str:
        """Calculate hash of prompt content for change detection."""
        content = f"{self.template}{json.dumps(self.model_config, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()[:12]

    def format(self, **kwargs) -> str:
        """Format prompt with provided variables."""
        missing_vars = set(self.variables) - set(kwargs.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")

        return self.template.format(**kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization."""
        return {
            "id": self.id,
            "category": self.category.value,
            "name": self.name,
            "template": self.template,
            "version": self.version,
            "variables": self.variables,
            "model_config": self.model_config,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "hash": self.hash,
        }


class PromptRegistry:
    """
    Centralized registry for all AI prompts in giki.ai.

    This provides a single source of truth for all prompts, making it easy to:
    - Review and modify prompts
    - Track performance metrics
    - Version control prompts
    - Prepare for Langfuse integration
    """

    def __init__(self):
        self._prompts: Dict[str, Dict[str, PromptVersion]] = {}
        self._performance_metrics: Dict[str, List[Dict[str, Any]]] = {}
        self._initialize_prompts()

    def _initialize_prompts(self):
        """Initialize all prompts used in the system."""

        # Schema Interpretation Prompts
        self.register(
            PromptVersion(
                id="schema_interpretation_main",
                category=PromptCategory.SCHEMA_INTERPRETATION,
                name="Schema Interpretation Main",
                template="""You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 5 rows):
{sample_data}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

ANALYSIS INSTRUCTIONS:
1. Analyze each column header and sample data intelligently
2. Look for semantic meaning, not just exact matches
3. Consider common variations (e.g., "Trans Date" = "date", "Memo" = "description")
4. For amount columns, check if they contain parentheses or negative values
5. Identify if the file uses separate debit/credit columns or a single amount column
6. Provide confidence scores (0.0-1.0) for each mapping
7. Include reasoning for each mapping decision

SPECIAL CONSIDERATIONS:
- Indian formats: Look for "Cr" and "Dr" indicators
- US formats: Look for parentheses for negative amounts
- European formats: Check for comma as decimal separator
- If you see "Withdrawal Amt." and "Deposit Amt.", these are debit/credit columns

Return JSON format:
{{
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Clear explanation of why this mapping was chosen"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative"
    }},
    "summary": "Brief analysis summary"
}}

JSON:""",
                version="1.2.0",
                variables=["file_name", "file_headers", "sample_data"],
                model_config={
                    "temperature": 0.1,
                    "max_output_tokens": 2000,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                    "performance_notes": "Works well with Gemini 2.0 Flash",
                },
            )
        )

        # Enhanced Schema Interpretation with Context
        self.register(
            PromptVersion(
                id="schema_interpretation_enhanced",
                category=PromptCategory.SCHEMA_INTERPRETATION,
                name="Schema Interpretation Enhanced",
                template="""You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 20 rows):
{sample_data}

Column Statistics:
{column_stats}

Previous Successful Patterns for this Customer:
{previous_patterns}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

ENHANCED ANALYSIS INSTRUCTIONS:
1. Use column statistics to validate your mappings:
   - Date columns should have date-like patterns
   - Amount columns should be mostly numeric
   - Description columns should have high unique value ratios
2. Learn from previous successful patterns if available
3. Pay special attention to columns that were successfully mapped before
4. For amount detection, use these enhanced rules:
   - Check "all_positive", "all_negative", or "mixed_signs" statistics
   - If two columns are all positive, they're likely debit/credit columns
   - If one column has mixed signs, it's likely a single amount column
5. Validate your confidence based on pattern matching in statistics

Return JSON format:
{{
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Explanation including statistical validation"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative",
        "statistical_validation": "explanation of how stats support this"
    }},
    "learned_patterns_applied": ["list of patterns used from history"],
    "summary": "Brief analysis summary with confidence factors"
}}

JSON:""",
                version="2.0.0",
                variables=[
                    "file_name",
                    "file_headers",
                    "sample_data",
                    "column_stats",
                    "previous_patterns",
                ],
                model_config={
                    "temperature": 0.1,
                    "max_output_tokens": 2500,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                    "improvements": "Added statistical validation and learning from history",
                },
            )
        )

        # Debit/Credit Inference Prompt
        self.register(
            PromptVersion(
                id="debit_credit_inference",
                category=PromptCategory.DEBIT_CREDIT_INFERENCE,
                name="Debit/Credit Logic Inference",
                template="""You are an expert in accounting principles and banking conventions.
Analyze these transaction samples to determine debit/credit direction.

Column Mappings: {column_mappings}
Transaction Samples (first 10):
{transaction_samples}

ANALYSIS REQUIREMENTS:
1. Identify if transactions have separate debit/credit columns
2. If single amount column, determine sign convention (negative for debits or credits)
3. Look for transaction type indicators (DR/CR, Debit/Credit, Withdrawal/Deposit)
4. Consider regional banking conventions
5. Analyze description patterns for transaction direction clues

ACCOUNTING PRINCIPLES:
- Debits: Expenses, withdrawals, payments out, purchases
- Credits: Income, deposits, payments in, refunds, reversals
- Sign conventions vary by bank and region

REGIONAL VARIATIONS:
- US: Often uses parentheses for negative values
- India: May use "Dr" and "Cr" suffixes
- Europe: May use different decimal separators

Return JSON format:
{{
    "debit_credit_structure": "separate_columns|single_column_signed|single_column_with_type",
    "sign_convention": "negative_is_debit|negative_is_credit|absolute_with_type",
    "debit_indicators": ["list of terms/patterns indicating debits"],
    "credit_indicators": ["list of terms/patterns indicating credits"],
    "confidence": 0.95,
    "reasoning": "Detailed explanation of detected pattern",
    "examples": [
        {{
            "description": "sample transaction",
            "classification": "debit|credit",
            "evidence": "what indicated this classification"
        }}
    ],
    "recommendations": {{
        "amount_handling": "use_signed_values|split_by_type|use_absolute_with_indicator",
        "type_detection": "column_based|description_based|sign_based"
    }}
}}

JSON:""",
                version="1.1.0",
                variables=["column_mappings", "transaction_samples"],
                model_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1500,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                },
            )
        )

        # Regional Detection Prompt
        self.register(
            PromptVersion(
                id="regional_detection",
                category=PromptCategory.REGIONAL_DETECTION,
                name="Regional Format Detection",
                template="""You are an expert in global banking formats and regional variations.
Analyze this financial data to detect and handle regional conventions.

File Data Sample: {file_data}
Detected Region: {detected_region}

ANALYSIS REQUIREMENTS:
1. Detect date format (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, DD-MMM-YYYY, etc.)
2. Identify currency symbols and formatting
3. Recognize regional transaction description patterns
4. Identify regional banking terminology
5. Detect decimal/thousand separators (1,234.56 vs 1.234,56 vs 1 234.56)

REGIONAL PATTERNS TO DETECT:
- US: MM/DD/YYYY dates, $ currency, decimal point, comma thousands
- Europe: DD/MM/YYYY dates, € currency, comma decimal, period thousands
- India: DD-MM-YYYY dates, ₹ currency, lakhs/crores notation
- UK: DD/MM/YYYY dates, £ currency, varied formats
- International: ISO dates (YYYY-MM-DD), varied currencies

TERMINOLOGY PATTERNS:
- US: "Check #", "ATM Withdrawal", "Direct Deposit"
- India: "NEFT", "IMPS", "UPI", "Cheque"
- Europe: "SEPA", "IBAN", "Standing Order"
- UK: "BACS", "CHAPS", "Standing Order"

Return JSON format:
{{
    "detected_region": "US|Europe|India|UK|International|Other",
    "confidence": 0.95,
    "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD|DD-MM-YYYY|DD-MMM-YYYY",
    "date_examples": ["examples from data"],
    "currency": {{
        "symbol": "$|€|₹|£|Other",
        "position": "prefix|suffix",
        "decimal_separator": ".|,",
        "thousand_separator": ",|.|space|none"
    }},
    "regional_terms": {{
        "payment_methods": ["detected payment method terms"],
        "transaction_types": ["detected transaction type terms"],
        "banking_terms": ["region-specific banking terms found"]
    }},
    "parsing_instructions": {{
        "date_parser": "strptime format string",
        "amount_parser": {{
            "regex": "pattern to extract amounts",
            "conversion": "steps to convert to float"
        }},
        "description_cleaner": ["list of cleaning steps needed"]
    }},
    "validation_rules": {{
        "date_range": "expected date range for transactions",
        "amount_range": "typical amount ranges",
        "common_merchants": ["frequently seen merchant patterns"]
    }}
}}

JSON:""",
                version="1.2.0",
                variables=["file_data", "detected_region"],
                model_config={
                    "temperature": 0.1,
                    "max_output_tokens": 2000,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                },
            )
        )

        # Hierarchy Detection Prompt
        self.register(
            PromptVersion(
                id="hierarchy_detection",
                category=PromptCategory.HIERARCHY_DETECTION,
                name="Category Hierarchy Detection",
                template="""You are an expert at analyzing financial categorization patterns and detecting hierarchical structures.

Category Samples: {category_samples}

Analyze these categories and determine:
1. Are they hierarchical (contain parent > child relationships)?
2. What separator is used (if any)?
3. What type of categorization pattern do they follow?
4. How many levels deep is the hierarchy?

COMMON HIERARCHY PATTERNS:
- Separator-based: "Food > Restaurants > Fast Food"
- Nested: "Food:Dining:Restaurants"
- Path-based: "Expenses/Food/Restaurants"
- Code-based: "***********" (account codes)
- Mixed: "EXPENSE-Food-Restaurant"

ANALYSIS FOCUS:
- Consistency of separators across samples
- Number of hierarchy levels
- Parent-child relationship logic
- Any accompanying codes or IDs

Return JSON format:
{{
    "is_hierarchical": true|false,
    "hierarchy_details": {{
        "separator": ">|:|/|-|_|.",
        "max_depth": 3,
        "consistent_depth": true|false,
        "has_codes": true|false
    }},
    "pattern_type": "hierarchical|flat|mixed|coded",
    "examples": [
        {{
            "original": "sample category",
            "parsed_levels": ["Level1", "Level2", "Level3"],
            "depth": 3
        }}
    ],
    "confidence": 0.95,
    "reasoning": "Detailed explanation of detected pattern",
    "recommendations": {{
        "storage_method": "hierarchical_tree|flat_with_path|separate_levels",
        "query_strategy": "exact_match|prefix_match|level_based"
    }}
}}

JSON:""",
                version="1.0.0",
                variables=["category_samples"],
                model_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1000,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                },
            )
        )

        # Correction Suggestion Prompt
        self.register(
            PromptVersion(
                id="correction_suggestion",
                category=PromptCategory.CORRECTION_SUGGESTION,
                name="Schema Correction Suggestions",
                template="""You are an expert at helping users correct schema mapping issues.
Generate helpful suggestions to fix the mapping problems.

Original Mappings: {original_mappings}
Validation Errors: {validation_errors}
User Feedback: {user_feedback}
Available Columns: {available_columns}

SUGGESTION REQUIREMENTS:
1. Provide clear, actionable correction suggestions
2. Explain why each correction is needed
3. Offer alternative mapping options
4. Prioritize user-friendly explanations
5. Include examples where helpful
6. Consider the user's feedback if provided

COMMON MAPPING ISSUES:
- Missing required fields (date, description, amount)
- Incorrect field types (mapping text column to amount)
- Ambiguous column names needing clarification
- Regional format mismatches
- Debit/credit confusion

Return JSON format:
{{
    "correction_suggestions": [
        {{
            "issue": "Specific problem identified",
            "severity": "critical|warning|info",
            "suggestion": "Clear action to fix the issue",
            "explanation": "Why this correction is needed",
            "alternatives": ["Other possible solutions"],
            "example": "Example of correct mapping",
            "confidence": 0.9
        }}
    ],
    "quick_fixes": [
        {{
            "field": "unmapped field name",
            "recommended_column": "best matching column",
            "confidence": 0.9,
            "reasoning": "why this match is recommended"
        }}
    ],
    "general_advice": "Overall tips for successful mapping",
    "next_steps": ["Ordered list of actions to resolve all issues"]
}}

JSON:""",
                version="1.1.0",
                variables=[
                    "original_mappings",
                    "validation_errors",
                    "user_feedback",
                    "available_columns",
                ],
                model_config={
                    "temperature": 0.3,
                    "max_output_tokens": 2000,
                    "top_p": 0.9,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                },
            )
        )

        # Business Appropriateness Evaluation Prompt for M1 Zero-Onboarding
        self.register(
            PromptVersion(
                id="business_appropriateness_evaluation",
                category=PromptCategory.BUSINESS_APPROPRIATENESS,
                name="Business Appropriateness Evaluation for Zero-Onboarding",
                template="""You are an expert financial categorization evaluator. Evaluate whether an AI-generated category is "business-appropriate" for a transaction using these weighted criteria:

TRANSACTION DETAILS:
Description: {transaction_description}
Amount: ${transaction_amount}
AI Category: {ai_category}
Merchant: {merchant_name}
Date: {transaction_date}

EVALUATION CRITERIA (WEIGHTED):
1. CONTEXT APPROPRIATENESS ({context_weight}%): Does the category make sense for this transaction context?
2. INDUSTRY STANDARDS ({industry_weight}%): Does the category align with standard business categorization practices?
3. MERCHANT ALIGNMENT ({merchant_weight}%): Does the category match what this merchant typically provides?
4. AMOUNT REASONABLENESS ({amount_weight}%): Does the amount make sense for this category?

SCORING INSTRUCTIONS:
- Score each component from 0.0 to 1.0 (0% to 100%)
- 0.85+ is considered "business-appropriate" for M1 validation
- Consider common business scenarios and practices
- Focus on practical business value, not perfect categorization

CONTEXT APPROPRIATENESS (40%):
- Does the category logically fit the transaction description?
- Would a business owner understand why this transaction is in this category?
- Are there obvious context clues that support or contradict the categorization?

INDUSTRY STANDARDS (30%):
- Is this how most businesses would categorize this type of transaction?
- Does it follow standard accounting or business categorization practices?
- Would this categorization be acceptable in business reporting?

MERCHANT ALIGNMENT (20%):
- Does the category match what this merchant typically sells/provides?
- If merchant is unknown, does the category fit typical merchants for this transaction type?
- Are there clear merchant-category mismatches?

AMOUNT REASONABLENESS (10%):
- Is the amount typical for this type of category?
- Are there obvious amount-category mismatches (e.g., $5000 for "Coffee")?
- Does the amount scale appropriately for the category?

ADDITIONAL CONTEXT:
{additional_context}

Return JSON format:
{{
    "context_score": 0.85,
    "context_reasoning": "Detailed explanation of context appropriateness score",
    "industry_score": 0.90,
    "industry_reasoning": "Detailed explanation of industry standards alignment",
    "merchant_score": 0.80,
    "merchant_reasoning": "Detailed explanation of merchant alignment",
    "amount_score": 0.95,
    "amount_reasoning": "Detailed explanation of amount reasonableness",
    "overall_reasoning": "Comprehensive assessment of business appropriateness",
    "improvement_suggestions": ["Specific suggestions if score < 0.85"],
    "business_scenarios": ["Common business situations where this categorization would be appropriate"]
}}

Focus on practical business value and appropriateness rather than perfect categorization. The goal is zero-onboarding categorization that business owners would find useful and reasonable.

JSON:""",
                version="1.0.0",
                variables=[
                    "transaction_description",
                    "transaction_amount",
                    "ai_category",
                    "merchant_name",
                    "transaction_date",
                    "context_weight",
                    "industry_weight",
                    "merchant_weight",
                    "amount_weight",
                    "additional_context",
                ],
                model_config={
                    "temperature": 0.2,
                    "max_output_tokens": 2000,
                    "top_p": 0.8,
                },
                metadata={
                    "author": "giki.ai team",
                    "last_updated": "2025-06-27",
                    "purpose": "M1 zero-onboarding business appropriateness evaluation",
                    "target_threshold": 0.85,
                    "weighting": "40% context, 30% industry, 20% merchant, 10% amount",
                },
            )
        )

        logger.info(f"Initialized {len(self._prompts)} prompts in registry")

    def register(self, prompt: PromptVersion) -> None:
        """Register a new prompt version."""
        if prompt.id not in self._prompts:
            self._prompts[prompt.id] = {}

        self._prompts[prompt.id][prompt.version] = prompt
        logger.info(f"Registered prompt: {prompt.id} v{prompt.version}")

    def get(self, prompt_id: str, version: Optional[str] = None) -> PromptVersion:
        """Get a prompt by ID and optional version."""
        if prompt_id not in self._prompts:
            raise ValueError(f"Prompt '{prompt_id}' not found in registry")

        versions = self._prompts[prompt_id]

        if version:
            if version not in versions:
                raise ValueError(
                    f"Version '{version}' not found for prompt '{prompt_id}'"
                )
            return versions[version]

        # Return latest version
        latest_version = max(versions.keys())
        return versions[latest_version]

    def list_prompts(
        self, category: Optional[PromptCategory] = None
    ) -> List[Dict[str, Any]]:
        """List all prompts, optionally filtered by category."""
        prompts = []

        for prompt_id, versions in self._prompts.items():
            for version, prompt in versions.items():
                if category is None or prompt.category == category:
                    prompts.append(
                        {
                            "id": prompt_id,
                            "name": prompt.name,
                            "category": prompt.category.value,
                            "version": version,
                            "variables": prompt.variables,
                            "created_at": prompt.created_at.isoformat(),
                        }
                    )

        return prompts

    def track_performance(
        self,
        prompt_id: str,
        version: str,
        success: bool,
        latency_ms: float,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Track performance metrics for a prompt."""
        if prompt_id not in self._performance_metrics:
            self._performance_metrics[prompt_id] = []

        metric = {
            "version": version,
            "timestamp": datetime.utcnow().isoformat(),
            "success": success,
            "latency_ms": latency_ms,
            "metadata": metadata or {},
        }

        self._performance_metrics[prompt_id].append(metric)

    def get_performance_summary(self, prompt_id: str) -> Dict[str, Any]:
        """Get performance summary for a prompt."""
        if prompt_id not in self._performance_metrics:
            return {"error": "No performance data available"}

        metrics = self._performance_metrics[prompt_id]

        # Calculate summary statistics
        success_count = sum(1 for m in metrics if m["success"])
        total_count = len(metrics)
        avg_latency = (
            sum(m["latency_ms"] for m in metrics) / total_count
            if total_count > 0
            else 0
        )

        # Group by version
        version_stats = {}
        for metric in metrics:
            version = metric["version"]
            if version not in version_stats:
                version_stats[version] = {"success": 0, "total": 0, "latencies": []}

            version_stats[version]["total"] += 1
            if metric["success"]:
                version_stats[version]["success"] += 1
            version_stats[version]["latencies"].append(metric["latency_ms"])

        # Calculate per-version stats
        for _version, stats in version_stats.items():
            stats["success_rate"] = (
                stats["success"] / stats["total"] if stats["total"] > 0 else 0
            )
            stats["avg_latency"] = sum(stats["latencies"]) / len(stats["latencies"])
            del stats["latencies"]  # Remove raw data from summary

        return {
            "prompt_id": prompt_id,
            "total_calls": total_count,
            "overall_success_rate": success_count / total_count
            if total_count > 0
            else 0,
            "avg_latency_ms": avg_latency,
            "version_performance": version_stats,
        }

    def export_for_review(self, output_path: Optional[Path] = None) -> Dict[str, Any]:
        """Export all prompts for review in a readable format."""
        export_data = {
            "export_timestamp": datetime.utcnow().isoformat(),
            "total_prompts": sum(len(versions) for versions in self._prompts.values()),
            "categories": {},
        }

        # Organize by category
        for category in PromptCategory:
            export_data["categories"][category.value] = []

        # Add prompts to their categories
        for prompt_id, versions in self._prompts.items():
            for version, prompt in versions.items():
                prompt_data = {
                    "id": prompt_id,
                    "name": prompt.name,
                    "version": version,
                    "created_at": prompt.created_at.isoformat(),
                    "hash": prompt.hash,
                    "variables": prompt.variables,
                    "model_config": prompt.model_config,
                    "template": prompt.template,
                    "metadata": prompt.metadata,
                }

                # Add performance data if available
                if prompt_id in self._performance_metrics:
                    summary = self.get_performance_summary(prompt_id)
                    if version in summary.get("version_performance", {}):
                        prompt_data["performance"] = summary["version_performance"][
                            version
                        ]

                export_data["categories"][prompt.category.value].append(prompt_data)

        # Save to file if path provided
        if output_path:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, "w") as f:
                json.dump(export_data, f, indent=2)
            logger.info(f"Exported prompts to {output_path}")

        return export_data

    def prepare_langfuse_migration(self) -> List[Dict[str, Any]]:
        """Prepare prompts for migration to Langfuse."""
        migration_data = []

        for prompt_id, versions in self._prompts.items():
            for version, prompt in versions.items():
                migration_data.append(
                    {
                        "name": prompt_id,
                        "prompt": prompt.template,
                        "version": version,
                        "config": {
                            "model": "gemini-2.0-flash-001",  # Default model
                            **prompt.model_config,
                        },
                        "labels": [prompt.category.value],
                        "tags": prompt.metadata.get("tags", []),
                    }
                )

        return migration_data


# Singleton instance
_prompt_registry = None


def get_prompt_registry() -> PromptRegistry:
    """Get the singleton prompt registry instance."""
    global _prompt_registry
    if _prompt_registry is None:
        _prompt_registry = PromptRegistry()
    return _prompt_registry
