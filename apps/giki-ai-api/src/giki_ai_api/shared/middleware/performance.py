"""
Performance monitoring middleware for tracking API response times and bottlenecks.
"""

import logging
import time
from typing import Callable

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor API performance and log slow requests.
    """

    def __init__(self, app, slow_request_threshold: float = 200.0):
        """
        Initialize performance monitoring middleware.

        Args:
            app: FastAPI application
            slow_request_threshold: Threshold in milliseconds for logging slow requests
        """
        super().__init__(app)
        self.slow_request_threshold = (
            slow_request_threshold / 1000.0
        )  # Convert to seconds

    async def dispatch(self, request: Request, call_next: Callable):
        """
        Process request and measure performance.
        """
        start_time = time.time()

        # Add timing info to request state
        request.state.start_time = start_time

        try:
            # Process the request
            response = await call_next(request)

            # Calculate response time
            end_time = time.time()
            response_time = end_time - start_time
            response_time_ms = response_time * 1000

            # Add performance headers
            response.headers["X-Response-Time"] = f"{response_time_ms:.2f}ms"
            response.headers["X-Process-Time"] = f"{response_time:.6f}"

            # Log performance metrics
            method = request.method
            url = str(request.url)
            status_code = response.status_code

            # Determine log level based on response time
            if response_time > self.slow_request_threshold:
                logger.warning(
                    f"SLOW REQUEST: {method} {url} - {response_time_ms:.2f}ms "
                    f"(status: {status_code}) - EXCEEDS {self.slow_request_threshold * 1000:.0f}ms threshold"
                )
            elif response_time > 0.1:  # Log requests over 100ms as info
                logger.info(
                    f"REQUEST: {method} {url} - {response_time_ms:.2f}ms (status: {status_code})"
                )
            else:
                # Only log fast requests in debug mode
                logger.debug(
                    f"FAST REQUEST: {method} {url} - {response_time_ms:.2f}ms (status: {status_code})"
                )

            # Log specific endpoint performance
            if "/auth/token" in url or "/auth/login" in url:
                if response_time_ms > 2000:
                    logger.error(
                        f"🚨 AUTH PERFORMANCE ISSUE: Login took {response_time_ms:.2f}ms "
                        f"(target: <2s) - {response_time_ms / 2000:.1f}x over target"
                    )
                else:
                    logger.info(
                        f"✅ AUTH PERFORMANCE: Login took {response_time_ms:.2f}ms "
                        f"(target: <2s) - MEETS REQUIREMENT"
                    )

            return response

        except Exception as e:
            # Calculate response time even for errors
            end_time = time.time()
            response_time = end_time - start_time
            response_time_ms = response_time * 1000

            logger.error(
                f"ERROR REQUEST: {request.method} {request.url} - "
                f"{response_time_ms:.2f}ms - Exception: {str(e)}"
            )
            raise


class DatabasePerformanceTracker:
    """
    Track database operation performance.
    """

    @staticmethod
    def track_query(operation_name: str):
        """
        Decorator to track database query performance.
        """

        def decorator(func):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    end_time = time.time()
                    query_time = (end_time - start_time) * 1000

                    if query_time > 500:  # Log queries over 500ms
                        logger.warning(
                            f"SLOW DB QUERY: {operation_name} took {query_time:.2f}ms "
                            f"(target: <500ms)"
                        )
                    else:
                        logger.debug(
                            f"DB QUERY: {operation_name} took {query_time:.2f}ms"
                        )

                    return result
                except Exception as e:
                    end_time = time.time()
                    query_time = (end_time - start_time) * 1000
                    logger.error(
                        f"FAILED DB QUERY: {operation_name} failed after {query_time:.2f}ms - {str(e)}"
                    )
                    raise

            return wrapper

        return decorator


# Performance metrics collection
class PerformanceMetrics:
    """
    Collect and store performance metrics.
    """

    def __init__(self):
        self.request_times = []
        self.slow_requests = []
        self.auth_requests = []

    def add_request(
        self, method: str, url: str, response_time_ms: float, status_code: int
    ):
        """Add a request to metrics."""
        request_data = {
            "method": method,
            "url": url,
            "response_time_ms": response_time_ms,
            "status_code": status_code,
            "timestamp": time.time(),
        }

        self.request_times.append(request_data)

        if response_time_ms > 200:
            self.slow_requests.append(request_data)

        if "/auth/" in url:
            self.auth_requests.append(request_data)

    def get_summary(self):
        """Get performance summary."""
        if not self.request_times:
            return {"message": "No requests recorded"}

        response_times = [r["response_time_ms"] for r in self.request_times]

        return {
            "total_requests": len(self.request_times),
            "average_response_time_ms": sum(response_times) / len(response_times),
            "max_response_time_ms": max(response_times),
            "min_response_time_ms": min(response_times),
            "slow_requests_count": len(self.slow_requests),
            "auth_requests_count": len(self.auth_requests),
            "auth_average_ms": sum(r["response_time_ms"] for r in self.auth_requests)
            / len(self.auth_requests)
            if self.auth_requests
            else 0,
        }


# Global metrics instance
performance_metrics = PerformanceMetrics()


# Performance monitoring utilities (consolidated from performance_middleware.py)
class PerformanceUtils:
    """Utility functions for performance monitoring."""

    @staticmethod
    def get_current_metrics():
        """Get current performance metrics."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return performance_monitor.get_performance_summary(minutes=5)
        except ImportError as e:
            # No fallback metrics - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_performance_summary",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def get_detailed_metrics(minutes: int = 15):
        """Get detailed performance metrics for specified time period."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return performance_monitor.get_performance_summary(minutes=minutes)
        except ImportError as e:
            # No fallback metrics - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message=f"Performance monitoring service not available for {minutes} minute analysis. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_detailed_metrics",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def get_endpoint_stats():
        """Get per-endpoint performance statistics."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return dict(performance_monitor.endpoint_stats)
        except ImportError as e:
            # No fallback endpoint stats - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available for endpoint statistics. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_endpoint_stats",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def reset_metrics():
        """Reset all performance metrics (use with caution)."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            performance_monitor.api_metrics.clear()
            performance_monitor.db_metrics.clear()
            performance_monitor.system_metrics.clear()
            performance_monitor.endpoint_stats.clear()
            logger.info("Performance metrics reset")
        except ImportError as e:
            # No fallback metrics reset - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available for metrics reset. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="reset_metrics",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )
