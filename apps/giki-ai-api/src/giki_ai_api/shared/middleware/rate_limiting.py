"""
Rate limiting middleware to prevent DDoS attacks and API abuse.
Supports both Redis (production) and in-memory (development) storage.
"""

import json
import logging
import os
import time
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from typing import Dict, Optional, Tuple

from fastapi import Request
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from starlette.middleware.base import BaseHTTPMiddleware

try:
    import redis.asyncio as aioredis

    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    aioredis = None

logger = logging.getLogger(__name__)


@dataclass
class RateLimitBucket:
    """Token bucket for rate limiting."""

    tokens: float
    last_refill: float
    max_tokens: int
    refill_rate: float  # tokens per second

    def consume(self, tokens: int = 1) -> bool:
        """Attempt to consume tokens from the bucket."""
        now = time.time()

        # Calculate tokens to add based on time elapsed
        time_passed = now - self.last_refill
        tokens_to_add = time_passed * self.refill_rate

        # Refill bucket (up to max capacity)
        self.tokens = min(self.max_tokens, self.tokens + tokens_to_add)
        self.last_refill = now

        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting rules."""

    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    burst_size: int = 10

    # Endpoint-specific overrides
    endpoint_limits: Dict[str, Tuple[int, int]] = field(
        default_factory=lambda: {
            "/api/v1/auth/login": (5, 30),  # (per_minute, per_hour) - stricter for auth
            "/api/v1/auth/register": (3, 10),  # Very strict for registration
            "/api/v1/upload": (10, 50),  # Moderate for uploads
            "/api/v1/agent/message": (30, 300),  # Higher for chat
        }
    )

    # IP whitelist (for internal services)
    whitelist_ips: set = field(
        default_factory=lambda: {
            "127.0.0.1",
            "::1",
            "10.0.0.0/8",  # Private networks
            "**********/12",
            "***********/16",
        }
    )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using token bucket algorithm."""

    def __init__(self, app, config: Optional[RateLimitConfig] = None):
        super().__init__(app)
        self.config = config or RateLimitConfig()

        # Redis configuration
        self.redis_client: Optional[aioredis.Redis] = None
        self.redis_enabled = False
        self._init_redis()

        # In-memory storage (fallback)
        self.minute_buckets: Dict[str, RateLimitBucket] = {}
        self.hour_buckets: Dict[str, RateLimitBucket] = {}

        # Track request counts for monitoring
        self.request_counts = defaultdict(int)
        self.blocked_counts = defaultdict(int)

        # Cleanup old buckets periodically
        self.last_cleanup = time.time()

        storage_type = "Redis" if self.redis_enabled else "in-memory"
        logger.info(f"Rate limiting middleware initialized with {storage_type} storage")

    def _init_redis(self):
        """Initialize Redis connection if available and enabled."""
        if not REDIS_AVAILABLE:
            logger.info("Redis not available - using in-memory rate limiting")
            return

        try:
            redis_enabled = os.getenv("REDIS_ENABLED", "false").lower() == "true"
            if not redis_enabled:
                logger.info("Redis disabled via REDIS_ENABLED=false")
                return

            redis_url = os.getenv("REDIS_URL")
            redis_host = os.getenv("REDIS_HOST")
            redis_port = os.getenv("REDIS_PORT", "6379")

            if redis_url:
                self.redis_client = aioredis.from_url(redis_url, decode_responses=True)
                self.redis_enabled = True
                logger.info("Redis rate limiting initialized with URL")
            elif redis_host:
                self.redis_client = aioredis.Redis(
                    host=redis_host, port=int(redis_port), decode_responses=True
                )
                self.redis_enabled = True
                logger.info(
                    f"Redis rate limiting initialized with host: {redis_host}:{redis_port}"
                )
            else:
                logger.info(
                    "No Redis configuration found - using in-memory rate limiting"
                )

        except Exception as e:
            logger.warning(f"Failed to initialize Redis for rate limiting: {e}")
            logger.info("Falling back to in-memory rate limiting")
            self.redis_client = None
            self.redis_enabled = False

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address with proxy support."""
        # Check for forwarded headers (common in production behind load balancers)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP (client) from the comma-separated list
            return forwarded_for.split(",")[0].strip()

        forwarded = request.headers.get("X-Forwarded")
        if forwarded:
            return forwarded.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()

        # Fallback to direct connection IP
        if hasattr(request.client, "host"):
            return request.client.host

        return "unknown"

    def _is_whitelisted(self, ip: str) -> bool:
        """Check if IP is whitelisted with proper CIDR matching."""
        import ipaddress

        if ip in self.config.whitelist_ips:
            return True

        # Proper CIDR matching for network ranges
        try:
            ip_addr = ipaddress.ip_address(ip)

            for whitelist_entry in self.config.whitelist_ips:
                try:
                    # Handle both single IPs and CIDR ranges
                    if "/" in whitelist_entry:
                        # CIDR range
                        network = ipaddress.ip_network(whitelist_entry, strict=False)
                        if ip_addr in network:
                            return True
                    else:
                        # Single IP
                        whitelist_addr = ipaddress.ip_address(whitelist_entry)
                        if ip_addr == whitelist_addr:
                            return True

                except (ipaddress.AddressValueError, ValueError):
                    # If parsing fails, try prefix matching for networks
                    try:
                        if "/" in whitelist_entry:
                            # Handle CIDR notation properly
                            network = ipaddress.ip_network(
                                whitelist_entry, strict=False
                            )
                            if ip_addr in network:
                                return True
                    except (ipaddress.AddressValueError, ValueError):
                        # Final fallback: exact string match only
                        if ip == whitelist_entry:
                            return True

        except (ipaddress.AddressValueError, ValueError):
            # If IP parsing fails, try proper network parsing
            for whitelist_ip in self.config.whitelist_ips:
                try:
                    if "/" in whitelist_ip:
                        # Handle CIDR notation properly
                        network = ipaddress.ip_network(whitelist_ip, strict=False)
                        # Try to parse the incoming IP again
                        try:
                            ip_addr = ipaddress.ip_address(ip)
                            if ip_addr in network:
                                return True
                        except ipaddress.AddressValueError:
                            continue
                    else:
                        # Exact match for single IPs
                        if ip == whitelist_ip:
                            return True
                except (ipaddress.AddressValueError, ValueError):
                    # Final fallback: exact string match only
                    if ip == whitelist_ip:
                        return True

        return False

    async def _get_redis_bucket(
        self, key: str, max_tokens: int, refill_rate: float
    ) -> Optional[RateLimitBucket]:
        """Get or create bucket from Redis."""
        if not self.redis_enabled or not self.redis_client:
            return None

        try:
            bucket_data = await self.redis_client.get(key)
            if bucket_data:
                data = json.loads(bucket_data)
                return RateLimitBucket(**data)
            else:
                # Create new bucket
                bucket = RateLimitBucket(
                    tokens=max_tokens,
                    last_refill=time.time(),
                    max_tokens=max_tokens,
                    refill_rate=refill_rate,
                )
                # Store in Redis with 1 hour expiration
                await self.redis_client.setex(key, 3600, json.dumps(asdict(bucket)))
                return bucket
        except Exception as e:
            logger.warning(f"Redis bucket operation failed for {key}: {e}")
            return None

    async def _update_redis_bucket(self, key: str, bucket: RateLimitBucket) -> bool:
        """Update bucket in Redis."""
        if not self.redis_enabled or not self.redis_client:
            return False

        try:
            # Store in Redis with 1 hour expiration
            await self.redis_client.setex(key, 3600, json.dumps(asdict(bucket)))
            return True
        except Exception as e:
            logger.warning(f"Failed to update Redis bucket {key}: {e}")
            return False

    def _get_rate_limits(self, path: str) -> Tuple[int, int]:
        """Get rate limits for specific endpoint."""
        # Check for exact path match
        if path in self.config.endpoint_limits:
            return self.config.endpoint_limits[path]

        # Check for pattern matches
        for endpoint_pattern, limits in self.config.endpoint_limits.items():
            if path.startswith(endpoint_pattern):
                return limits

        # Default limits
        return (self.config.requests_per_minute, self.config.requests_per_hour)

    def _get_or_create_bucket(
        self,
        buckets: Dict[str, RateLimitBucket],
        key: str,
        max_tokens: int,
        refill_rate: float,
    ) -> RateLimitBucket:
        """Get existing bucket or create new one."""
        if key not in buckets:
            buckets[key] = RateLimitBucket(
                tokens=max_tokens,
                last_refill=time.time(),
                max_tokens=max_tokens,
                refill_rate=refill_rate,
            )
        return buckets[key]

    def _cleanup_old_buckets(self):
        """Remove old, unused buckets to prevent memory leaks."""
        now = time.time()

        # Only cleanup every 5 minutes
        if now - self.last_cleanup < 300:
            return

        cutoff_time = now - 3600  # Remove buckets older than 1 hour

        # Cleanup minute buckets
        old_keys = [
            key
            for key, bucket in self.minute_buckets.items()
            if bucket.last_refill < cutoff_time
        ]
        for key in old_keys:
            del self.minute_buckets[key]

        # Cleanup hour buckets
        old_keys = [
            key
            for key, bucket in self.hour_buckets.items()
            if bucket.last_refill < cutoff_time
        ]
        for key in old_keys:
            del self.hour_buckets[key]

        self.last_cleanup = now

        if old_keys:
            logger.debug(f"Cleaned up {len(old_keys)} old rate limit buckets")

    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        # Get client information
        client_ip = self._get_client_ip(request)
        path = request.url.path
        method = request.method

        # Skip rate limiting for whitelisted IPs
        if self._is_whitelisted(client_ip):
            return await call_next(request)

        # Skip rate limiting for health checks and static assets
        if path in ["/health", "/docs", "/redoc", "/openapi.json"] or path.startswith(
            "/static"
        ):
            return await call_next(request)

        # Get rate limits for this endpoint
        per_minute_limit, per_hour_limit = self._get_rate_limits(path)

        # Create bucket keys
        minute_key = f"{client_ip}:{path}:minute"
        hour_key = f"{client_ip}:{path}:hour"

        # Get or create buckets (try Redis first, fallback to in-memory)
        if self.redis_enabled:
            minute_bucket = await self._get_redis_bucket(
                f"rate_limit:{minute_key}", per_minute_limit, per_minute_limit / 60.0
            )
            hour_bucket = await self._get_redis_bucket(
                f"rate_limit:{hour_key}", per_hour_limit, per_hour_limit / 3600.0
            )
        else:
            minute_bucket = None
            hour_bucket = None

        # Fallback to in-memory if Redis failed
        if minute_bucket is None:
            minute_bucket = self._get_or_create_bucket(
                self.minute_buckets,
                minute_key,
                per_minute_limit,
                per_minute_limit / 60.0,
            )
        if hour_bucket is None:
            hour_bucket = self._get_or_create_bucket(
                self.hour_buckets, hour_key, per_hour_limit, per_hour_limit / 3600.0
            )

        # Track request
        self.request_counts[client_ip] += 1

        # Check rate limits
        if not minute_bucket.consume(1):
            self.blocked_counts[client_ip] += 1
            logger.warning(
                f"Rate limit exceeded (per-minute) for {client_ip} on {method} {path}"
            )

            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {per_minute_limit} per minute",
                    "retry_after": 60,
                    "type": "rate_limit_per_minute",
                },
                headers={
                    "Retry-After": "60",
                    "X-RateLimit-Limit-Minute": str(per_minute_limit),
                    "X-RateLimit-Remaining-Minute": "0",
                    "X-RateLimit-Reset-Minute": str(int(time.time() + 60)),
                },
            )

        if not hour_bucket.consume(1):
            self.blocked_counts[client_ip] += 1
            logger.warning(
                f"Rate limit exceeded (per-hour) for {client_ip} on {method} {path}"
            )

            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {per_hour_limit} per hour",
                    "retry_after": 3600,
                    "type": "rate_limit_per_hour",
                },
                headers={
                    "Retry-After": "3600",
                    "X-RateLimit-Limit-Hour": str(per_hour_limit),
                    "X-RateLimit-Remaining-Hour": "0",
                    "X-RateLimit-Reset-Hour": str(int(time.time() + 3600)),
                },
            )

        # Update Redis buckets after successful token consumption
        if self.redis_enabled:
            await self._update_redis_bucket(f"rate_limit:{minute_key}", minute_bucket)
            await self._update_redis_bucket(f"rate_limit:{hour_key}", hour_bucket)

        # Add rate limit headers to successful responses
        response = await call_next(request)

        response.headers["X-RateLimit-Limit-Minute"] = str(per_minute_limit)
        response.headers["X-RateLimit-Remaining-Minute"] = str(
            int(minute_bucket.tokens)
        )
        response.headers["X-RateLimit-Limit-Hour"] = str(per_hour_limit)
        response.headers["X-RateLimit-Remaining-Hour"] = str(int(hour_bucket.tokens))

        # Periodic cleanup
        self._cleanup_old_buckets()

        return response

    def get_stats(self) -> Dict[str, any]:
        """Get rate limiting statistics for monitoring."""
        return {
            "total_buckets": len(self.minute_buckets) + len(self.hour_buckets),
            "minute_buckets": len(self.minute_buckets),
            "hour_buckets": len(self.hour_buckets),
            "request_counts": dict(self.request_counts),
            "blocked_counts": dict(self.blocked_counts),
            "config": {
                "requests_per_minute": self.config.requests_per_minute,
                "requests_per_hour": self.config.requests_per_hour,
                "endpoint_limits": self.config.endpoint_limits,
                "whitelist_count": len(self.config.whitelist_ips),
            },
        }


# Create default rate limiting middleware instance
default_rate_limit_config = RateLimitConfig()
rate_limit_middleware = RateLimitMiddleware
