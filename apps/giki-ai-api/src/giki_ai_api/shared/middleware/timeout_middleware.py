"""
Request Timeout Middleware
=========================

This middleware adds request-level timeouts to prevent API endpoints from hanging
indefinitely and causing frontend timeouts. Particularly important for reports
endpoints that perform database aggregations.

Features:
- Configurable timeout per endpoint
- Graceful timeout handling with proper error responses
- Performance logging for slow requests
- Special handling for long-running operations
"""

import asyncio
import logging
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

logger = logging.getLogger(__name__)


class TimeoutMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle request timeouts and prevent hanging requests.
    """

    def __init__(
        self,
        app,
        default_timeout: int = 30,  # 30 seconds default
        endpoint_timeouts: dict = None,
    ):
        """
        Initialize the timeout middleware.

        Args:
            app: FastAPI application instance
            default_timeout: Default timeout in seconds
            endpoint_timeouts: Dictionary mapping endpoint patterns to custom timeouts
        """
        super().__init__(app)
        self.default_timeout = default_timeout
        self.endpoint_timeouts = endpoint_timeouts or {
            # Reports endpoints need longer timeouts due to database aggregations
            "/api/v1/reports/spending-by-category": 120,
            "/api/v1/reports/spending-by-entity": 120,
            "/api/v1/reports/income-expense-summary": 120,
            "/api/v1/reports/monthly-trends": 120,
            # Upload endpoints can take longer for file processing
            "/api/v1/upload/": 300,  # 5 minutes for large file uploads
            "/api/v1/upload/process": 300,
            # Transaction categorization can take time
            "/api/v1/transactions/categorize": 180,  # 3 minutes for AI processing
            # Health checks - relaxed timeouts for development
            "/health": 60,  # Relaxed from 15s for development
            "/health/db": 120,  # Relaxed from 30s for development
            "/health/performance": 120,  # Relaxed for development
            # Authentication endpoints - relaxed for development
            "/api/v1/auth/login": 60,  # Relaxed login timeout
            "/api/v1/auth/token": 60,  # Relaxed auth timeout
            "/api/v1/auth/me": 30,  # Relaxed user info timeout
            "/api/v1/auth/logout": 30,  # Relaxed logout timeout
            "/api/v1/auth/verify": 30,  # Relaxed token verification
            "/api/v1/auth-fast/token-fast": 30,  # Relaxed fast auth
            "/api/v1/auth-fast/performance-test": 30,  # Relaxed performance test
            # Database monitoring endpoints
            "/api/v1/database/": 120,  # Relaxed database monitoring
        }

    def get_timeout_for_endpoint(self, path: str) -> int:
        """
        Get the appropriate timeout for a given endpoint path.

        Args:
            path: The request path

        Returns:
            Timeout in seconds
        """
        # Check for exact matches first
        if path in self.endpoint_timeouts:
            return self.endpoint_timeouts[path]

        # Check for pattern matches
        for pattern, timeout in self.endpoint_timeouts.items():
            if path.startswith(pattern.rstrip("*")):
                return timeout

        return self.default_timeout

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request with timeout handling.

        Args:
            request: The incoming request
            call_next: The next middleware/endpoint in the chain

        Returns:
            Response from the endpoint or timeout error
        """
        path = request.url.path
        method = request.method
        timeout = self.get_timeout_for_endpoint(path)

        start_time = time.time()

        logger.debug(f"Processing {method} {path} with {timeout}s timeout")

        try:
            # Execute the request with timeout
            response = await asyncio.wait_for(call_next(request), timeout=timeout)

            # Log request performance
            execution_time = (time.time() - start_time) * 1000

            if execution_time > 1000:  # Log requests taking > 1 second
                logger.warning(
                    f"Slow request: {method} {path} took {execution_time:.2f}ms "
                    f"(timeout: {timeout}s)"
                )
            else:
                logger.debug(
                    f"Request completed: {method} {path} in {execution_time:.2f}ms"
                )

            return response

        except asyncio.TimeoutError:
            execution_time = (time.time() - start_time) * 1000

            logger.error(
                f"Request timeout: {method} {path} exceeded {timeout}s timeout "
                f"(executed for {execution_time:.2f}ms)"
            )

            # Return structured timeout error
            return JSONResponse(
                status_code=504,  # Gateway Timeout
                content={
                    "detail": f"Request timeout: Operation exceeded {timeout} seconds",
                    "error_type": "REQUEST_TIMEOUT",
                    "timeout_seconds": timeout,
                    "endpoint": path,
                    "method": method,
                    "execution_time_ms": execution_time,
                    "suggestion": (
                        "Try reducing the date range or applying more specific filters. "
                        "If the issue persists, please contact support."
                    ),
                },
            )

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000

            logger.error(
                f"Request error: {method} {path} failed after {execution_time:.2f}ms: {e}",
                exc_info=True,
            )

            # Let other exceptions bubble up to be handled by global error handlers
            raise
