"""
Server-Sent Events (SSE) endpoints for real-time progress tracking.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException
from sse_starlette.sse import EventSourceResponse

from ...domains.auth.dependencies import get_current_user_with_tenant
from ..services.progress_tracker import ProgressUpdate, progress_tracker

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Progress"])


async def progress_event_generator(
    task_id: str, user_id: int, tenant_id: int
) -> AsyncGenerator[str, None]:
    """
    Generate SSE events for task progress.

    Yields:
        SSE formatted progress updates
    """
    # Subscribe to task updates
    queue = await progress_tracker.subscribe(task_id)

    try:
        # Send initial status
        status = await progress_tracker.get_status(task_id)
        if status:
            data = {
                "type": "status",
                "task_id": task_id,
                "status": status.status,
                "progress": status.progress,
                "total_stages": status.total_stages,
                "current_stage": status.current_stage,
            }
            yield f"data: {json.dumps(data)}\n\n"

        # Stream updates
        while True:
            try:
                # Wait for update with timeout
                update: ProgressUpdate = await asyncio.wait_for(
                    queue.get(),
                    timeout=30.0,  # Send keepalive every 30 seconds
                )

                # Format update as SSE event
                event_data = {
                    "type": "progress",
                    "task_id": task_id,
                    "stage": update.stage,
                    "message": update.message,
                    "progress": update.progress,
                    "timestamp": update.timestamp,
                    "metadata": update.metadata,
                }

                if update.error:
                    event_data["error"] = update.error

                yield f"data: {json.dumps(event_data)}\n\n"

                # Check if task is complete
                if update.stage in ["completed", "failed"]:
                    # Send final status
                    final_status = await progress_tracker.get_status(task_id)
                    if final_status:
                        final_data = {
                            "type": "final",
                            "task_id": task_id,
                            "status": final_status.status,
                            "progress": final_status.progress,
                            "result": final_status.result,
                            "error": final_status.error,
                        }
                        yield f"data: {json.dumps(final_data)}\n\n"
                    break

            except asyncio.TimeoutError:
                # Send keepalive
                yield ": keepalive\n\n"

                # Check if task still exists
                status = await progress_tracker.get_status(task_id)
                if not status or status.status in ["completed", "failed"]:
                    break

    except asyncio.CancelledError:
        logger.info(f"Progress stream cancelled for task {task_id}")
        raise
    except Exception as e:
        logger.error(f"Error in progress stream for task {task_id}: {e}")
        error_data = {"type": "error", "task_id": task_id, "error": str(e)}
        yield f"data: {json.dumps(error_data)}\n\n"
    finally:
        # Unsubscribe from updates
        await progress_tracker.unsubscribe(task_id, queue)


@router.get("/progress/{task_id}")
async def stream_progress(
    task_id: str, current_user=Depends(get_current_user_with_tenant)
) -> EventSourceResponse:
    """
    Stream real-time progress updates for a task via Server-Sent Events.

    Args:
        task_id: The ID of the task to track
        current_user: Current authenticated user

    Returns:
        EventSourceResponse streaming progress updates
    """
    user_id, tenant_id = current_user

    # Verify task exists
    status = await progress_tracker.get_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    # TODO: Verify task belongs to user's tenant
    # For now, we'll include tenant_id in task_id to ensure isolation

    logger.info(f"Starting progress stream for task {task_id} (user: {user_id})")

    # Return SSE response
    return EventSourceResponse(
        progress_event_generator(task_id, user_id, tenant_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable Nginx buffering
        },
    )


@router.get("/status/{task_id}")
async def get_task_status(
    task_id: str, current_user=Depends(get_current_user_with_tenant)
):
    """
    Get current status of a task.

    Args:
        task_id: The ID of the task
        current_user: Current authenticated user

    Returns:
        Current task status
    """
    user_id, tenant_id = current_user

    status = await progress_tracker.get_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    # TODO: Verify task belongs to user's tenant

    return {
        "task_id": status.task_id,
        "status": status.status,
        "progress": status.progress,
        "total_stages": status.total_stages,
        "current_stage": status.current_stage,
        "created_at": status.created_at,
        "completed_at": status.completed_at,
        "error": status.error,
        "result": status.result,
        "updates": [
            {
                "stage": update.stage,
                "message": update.message,
                "progress": update.progress,
                "timestamp": update.timestamp,
                "metadata": update.metadata,
                "error": update.error,
            }
            for update in status.updates[-10:]  # Last 10 updates
        ],
    }
