"""
WebSocket Service for Real-time Agent Updates

This service provides WebSocket functionality for real-time communication
between the backend agents and frontend UI, enabling:
- Agent processing state updates
- Progress notifications for long-running operations
- Bidirectional communication for interactive workflows
"""

import asyncio
import logging
from typing import Any, Dict, Optional, Set

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manages WebSocket connections and message broadcasting."""

    def __init__(self):
        self.active_connections: Dict[
            int, Set[WebSocket]
        ] = {}  # tenant_id -> connections
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, tenant_id: int, user_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()

        # Store connection by tenant for isolation
        if tenant_id not in self.active_connections:
            self.active_connections[tenant_id] = set()
        self.active_connections[tenant_id].add(websocket)

        # Store metadata
        self.connection_metadata[websocket] = {
            "tenant_id": tenant_id,
            "user_id": user_id,
            "connected_at": asyncio.get_event_loop().time(),
        }

        logger.info(f"WebSocket connected for tenant {tenant_id}, user {user_id}")

        # Send initial connection confirmation
        await self.send_personal_message(
            websocket,
            {
                "type": "connection.established",
                "data": {
                    "message": "Connected to Giki real-time updates",
                    "capabilities": [
                        "agent.processing_started",
                        "agent.processing_progress",
                        "agent.processing_completed",
                        "agent.processing_error",
                        "ui.state_update",
                        "data.refresh_required",
                    ],
                },
            },
        )

    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        metadata = self.connection_metadata.get(websocket)
        if metadata:
            tenant_id = metadata["tenant_id"]
            if tenant_id in self.active_connections:
                self.active_connections[tenant_id].discard(websocket)
                if not self.active_connections[tenant_id]:
                    del self.active_connections[tenant_id]
            del self.connection_metadata[websocket]
            logger.info(f"WebSocket disconnected for tenant {tenant_id}")

    async def send_personal_message(
        self, websocket: WebSocket, message: Dict[str, Any]
    ):
        """Send a message to a specific WebSocket connection."""
        if websocket.application_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.error(f"Error sending personal message: {e}")
                self.disconnect(websocket)

    async def broadcast_to_tenant(self, tenant_id: int, message: Dict[str, Any]):
        """Broadcast a message to all connections for a tenant."""
        if tenant_id in self.active_connections:
            disconnected = []
            for connection in self.active_connections[tenant_id]:
                try:
                    if connection.application_state == WebSocketState.CONNECTED:
                        await connection.send_json(message)
                    else:
                        disconnected.append(connection)
                except Exception as e:
                    logger.error(f"Error broadcasting to connection: {e}")
                    disconnected.append(connection)

            # Clean up disconnected connections
            for conn in disconnected:
                self.disconnect(conn)

    async def send_agent_update(
        self,
        tenant_id: int,
        event_type: str,
        data: Dict[str, Any],
        user_id: Optional[str] = None,
    ):
        """Send agent-related updates to connected clients."""
        message = {
            "type": event_type,
            "timestamp": asyncio.get_event_loop().time(),
            "data": data,
        }

        if user_id:
            # Send to specific user's connections
            for conn, metadata in self.connection_metadata.items():
                if (
                    metadata["tenant_id"] == tenant_id
                    and metadata["user_id"] == user_id
                ):
                    await self.send_personal_message(conn, message)
        else:
            # Broadcast to all tenant connections
            await self.broadcast_to_tenant(tenant_id, message)

    async def handle_client_message(
        self, websocket: WebSocket, message: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Handle messages from client and return response if needed."""
        metadata = self.connection_metadata.get(websocket)
        if not metadata:
            return None

        message_type = message.get("type")

        # Handle different message types
        if message_type == "ping":
            return {"type": "pong", "timestamp": asyncio.get_event_loop().time()}

        elif message_type == "agent.capability_request":
            # Client requesting agent capabilities
            return {
                "type": "agent.capabilities",
                "data": {
                    "agent": "Giki",
                    "capabilities": [
                        "Transaction Analysis & Categorization",
                        "Financial Report Generation",
                        "File Upload & Processing",
                        "Spending Pattern Analysis",
                        "Data Export & Download",
                        "Real-time Chart Visualization",
                        "Custom Query Handling",
                    ],
                    "status": "healthy",
                },
            }

        elif message_type == "ui.state_sync":
            # UI requesting state synchronization
            logger.info(f"State sync requested by tenant {metadata['tenant_id']}")
            # In production, this would fetch actual state from database
            return {
                "type": "ui.state_update",
                "data": {
                    "processing_active": False,
                    "last_update": asyncio.get_event_loop().time(),
                },
            }

        else:
            logger.warning(f"Unknown message type: {message_type}")
            return None


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


class WebSocketService:
    """Service class for WebSocket operations."""

    def __init__(self):
        self.manager = websocket_manager

    async def emit_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        tenant_id: Optional[int] = None,
        user_id: Optional[str] = None,
    ):
        """Emit an event to connected clients."""
        # Extract tenant_id from data if not provided
        if tenant_id is None and "tenant_id" in data:
            tenant_id = data["tenant_id"]

        if tenant_id is None:
            logger.warning(f"Cannot emit event {event_type} without tenant_id")
            return

        await self.manager.send_agent_update(
            tenant_id=tenant_id, event_type=event_type, data=data, user_id=user_id
        )

    async def emit_progress(
        self,
        tenant_id: int,
        operation: str,
        progress: float,
        message: Optional[str] = None,
    ):
        """Emit progress update for long-running operations."""
        await self.emit_event(
            event_type="agent.processing_progress",
            data={
                "operation": operation,
                "progress": progress,
                "message": message or f"{operation}: {progress:.0%} complete",
            },
            tenant_id=tenant_id,
        )

    async def handle_websocket(
        self, websocket: WebSocket, tenant_id: int, user_id: str
    ):
        """Handle a WebSocket connection lifecycle."""
        await self.manager.connect(websocket, tenant_id, user_id)

        try:
            while True:
                # Receive message from client
                data = await websocket.receive_json()

                # Process message and get response
                response = await self.manager.handle_client_message(websocket, data)

                # Send response if any
                if response:
                    await self.manager.send_personal_message(websocket, response)

        except WebSocketDisconnect:
            self.manager.disconnect(websocket)
            logger.info(f"Client disconnected: tenant {tenant_id}, user {user_id}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            self.manager.disconnect(websocket)


# Convenience function for emitting events
async def emit_agent_event(event_type: str, data: Dict[str, Any], tenant_id: int):
    """Convenience function to emit agent events."""
    service = WebSocketService()
    await service.emit_event(event_type, data, tenant_id)
