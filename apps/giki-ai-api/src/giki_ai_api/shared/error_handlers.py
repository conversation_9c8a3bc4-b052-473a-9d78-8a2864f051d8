"""
Centralized error handling for the giki-ai-api.

This module provides consistent error handling across all API endpoints.
"""

import logging

from fastapi import FastAP<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from .exceptions import (
    DataPreparationError,
    RAGContextError,
    ServiceNotInitializedError,
    VertexAIGenerationError,
    VertexAIInitializationError,
)

logger = logging.getLogger(__name__)


def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle validation errors from FastAPI's request validation.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    # Cast to RequestValidationError for proper access to errors
    validation_exc = exc if isinstance(exc, RequestValidationError) else None
    if validation_exc is None:
        logger.error(f"Expected RequestValidationError, got {type(exc)}")
        return JSONResponse(
            status_code=400, content={"error": "Validation error", "detail": str(exc)}
        )

    logger.warning(f"Validation error: {validation_exc.errors()}")

    # Process the errors to ensure they're JSON serializable
    processed_errors = []
    for error in validation_exc.errors():
        processed_error = dict(error)
        # Handle ValueError in ctx if present
        if "ctx" in processed_error and isinstance(processed_error["ctx"], dict):
            ctx = processed_error["ctx"]
            if "error" in ctx and isinstance(ctx["error"], ValueError):
                ctx["error"] = str(ctx["error"])
        processed_errors.append(processed_error)

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "detail": processed_errors,
            "message": "Validation error. Please check your request data.",
        },
    )


def pydantic_validation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """
    Handle validation errors from Pydantic models.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    # Cast to ValidationError for proper access to errors
    validation_exc = exc if isinstance(exc, ValidationError) else None
    if validation_exc is None:
        logger.error(f"Expected ValidationError, got {type(exc)}")
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={"detail": "Validation error", "message": str(exc)},
        )

    import json
    from datetime import datetime
    
    def serialize_error_item(item):
        """Convert validation error items to JSON-serializable format."""
        if isinstance(item, datetime):
            return item.isoformat()
        elif isinstance(item, dict):
            return {k: serialize_error_item(v) for k, v in item.items()}
        elif isinstance(item, list):
            return [serialize_error_item(v) for v in item]
        else:
            return item
    
    # Convert validation errors to JSON-serializable format
    serializable_errors = [serialize_error_item(error) for error in validation_exc.errors()]
    
    logger.warning(f"Pydantic validation error: {serializable_errors}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "detail": serializable_errors,
            "message": "Validation error. Please check your request data.",
        },
    )


def sqlalchemy_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle SQLAlchemy database errors.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Database error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": "Database error occurred",
            "message": "An error occurred while accessing the database. Please try again later.",
        },
    )


def service_not_initialized_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """
    Handle errors when a service is not properly initialized.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Service not initialized: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "detail": str(exc),
            "message": "Service is not available. Please try again later.",
        },
    )


def vertex_ai_initialization_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """
    Handle errors during Vertex AI initialization.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Vertex AI initialization error: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "detail": str(exc),
            "message": "AI service is not available. Please try again later.",
        },
    )


def vertex_ai_generation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """
    Handle errors during Vertex AI content generation.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Vertex AI generation error: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": str(exc),
            "message": "Error generating AI content. Please try again later.",
        },
    )


def data_preparation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """
    Handle errors during data preparation.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Data preparation error: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "detail": str(exc),
            "message": "Error preparing data. Please check your input and try again.",
        },
    )


def rag_context_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle errors related to RAG context.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"RAG context error: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": str(exc),
            "message": "Error retrieving context information. Please try again later.",
        },
    )


def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle any unhandled exceptions.
    """
    # Request parameter is required by FastAPI but not used in this handler
    _ = request

    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "detail": "Internal server error",
            "message": "An unexpected error occurred. Please try again later.",
        },
    )


def register_exception_handlers(app: FastAPI) -> None:
    """
    Register all exception handlers with the FastAPI application.
    """
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, pydantic_validation_exception_handler)
    app.add_exception_handler(
        ServiceNotInitializedError, service_not_initialized_exception_handler
    )
    app.add_exception_handler(
        VertexAIInitializationError, vertex_ai_initialization_exception_handler
    )
    app.add_exception_handler(
        VertexAIGenerationError, vertex_ai_generation_exception_handler
    )
    app.add_exception_handler(DataPreparationError, data_preparation_exception_handler)
    app.add_exception_handler(RAGContextError, rag_context_exception_handler)
    app.add_exception_handler(Exception, generic_exception_handler)


def handle_service_errors(func):
    """
    Decorator to handle service-level errors consistently.

    This decorator catches common exceptions and converts them to appropriate
    FastAPI HTTP responses.
    """
    import functools

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValidationError as e:
            logger.error(f"Validation error in {func.__name__}: {e}")
            raise
        # SQLAlchemy removed - database errors now handled as generic exceptions
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
            raise

    return wrapper
