import logging
from typing import TYPE_CHECKING, Optional

from asyncpg import Connection
from fastapi import Depends, HTTPException, Request, status

from ..domains.auth.models import Tenant as TenantModel, User
from ..domains.auth.secure_auth import get_current_active_user
from .config import settings  # Added settings import
from .database import get_db_session

if TYPE_CHECKING:
    from ..shared.ai.vertex_client import VertexAIClient

logger = logging.getLogger(__name__)

# Global instance of the service, to be initialized at startup
categorization_service_instance = None  # Type hint removed to satisfy Ruff for now


async def get_tenant_by_id(tenant_id: int, conn: Connection) -> TenantModel | None:
    """
    Fetch tenant details from database by ID.

    Args:
        tenant_id: The tenant ID to look up
        conn: Async database connection

    Returns:
        TenantModel instance if found, None otherwise
    """
    try:
        # Query the tenant by ID
        query = """
            SELECT id, name, domain, settings, subscription_status, 
                   subscription_plan, created_at, updated_at
            FROM tenant
            WHERE id = $1
        """
        row = await conn.fetchrow(query, tenant_id)

        if row:
            # Convert row to dict and handle JSONB fields
            tenant_data = dict(row)

            # Ensure settings is a dict (JSONB fields might be returned as strings)
            if isinstance(tenant_data.get("settings"), str):
                import json

                try:
                    tenant_data["settings"] = json.loads(tenant_data["settings"])
                except (json.JSONDecodeError, TypeError):
                    tenant_data["settings"] = {}

            tenant = TenantModel(**tenant_data)
            logger.info(f"Found tenant {tenant.id}: {tenant.name}")
            return tenant
        else:
            logger.warning(f"Tenant with ID {tenant_id} not found in database")
            return None

    except Exception as e:
        logger.error(f"Error fetching tenant {tenant_id}: {e}")
        return None


async def get_current_user_with_tenant(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
) -> tuple[User, TenantModel]:
    # User model has tenant_id as required field
    tenant_id = current_user.tenant_id

    tenant = await get_tenant_by_id(tenant_id=tenant_id, conn=conn)
    if not tenant:
        logger.error(
            f"Tenant not found for tenant_id: {tenant_id} for user {current_user.email}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Tenant '{tenant_id}' not found or access denied.",
        )
    return current_user, tenant


async def get_current_tenant_id(
    current_user: User = Depends(get_current_active_user),
) -> int:
    """
    Get tenant ID from current authenticated user.

    Since get_current_active_user always queries the database,
    we know the tenant_id is current and valid.
    """
    return current_user.tenant_id


async def get_categorization_service(
    conn: Connection = Depends(get_db_session),
):  # Return type hint removed
    """
    Dependency function to get the initialized CategorizationService.
    """
    from ..shared.ai.unified_ai import AIServiceConfig, UnifiedAIService

    # Use real AI service instead of mock
    config = AIServiceConfig(
        project=getattr(settings, "VERTEX_PROJECT_ID", None),
        location=getattr(settings, "VERTEX_LOCATION", None),
        model_name=getattr(
            settings, "VERTEX_AI_GEMINI_MODEL_ID", "gemini-2.0-flash-001"
        ),
    )

    # Return real service with database connection
    return UnifiedAIService(db=conn, config=config)


# Service Dependencies
def get_vertex_ai_client(request: Request) -> Optional["VertexAIClient"]:
    """Get the Vertex AI client from app state."""
    client = getattr(request.app.state, "vertex_ai_client", None)
    return client


async def get_vertex_client():
    """Get the Vertex AI client for RAG operations."""
    from ..shared.ai.vertex_client import VertexAIClient

    # Initialize the client if not already done
    client = VertexAIClient(
        project_id=settings.VERTEX_PROJECT_ID,
        location=settings.VERTEX_LOCATION,
        service_account_key_path=settings.VERTEX_SERVICE_ACCOUNT_KEY_PATH,
    )

    # Ensure the client is set up
    if not client.is_ready():
        await client.setup_clients()

    return client


# def get_rag_management_service(db: asyncpg.Connection = Depends(get_db_session)) -> "RAGManagementService":
#     """Dependency to get RAGManagementService instance."""
#     return RAGManagementService(db)


# Temporarily disabled due to missing dependencies
# def get_gemini_service() -> None:
#     """Gemini service dependency - temporarily disabled."""
#     return None


# Temporarily disabled due to missing service classes
# async def get_taxonomy_service(db=Depends(get_db_session)):
#     from .services.core.category_management import CategoryService
#     return CategoryService(db=db)

# async def get_hierarchy_service(db=Depends(get_db_session)):
#     from .services.core.category_management import HierarchyService
#     return HierarchyService(db=db)


# Temporarily disabled due to missing agent classes
# def get_categorization_adk_agent():
#     from .adk_agents.categorization_agent import AgentConfig, CategorizationAgent
#     config = AgentConfig(model_name=settings.VERTEX_AI_GEMINI_MODEL_ID, rag_enabled=True)
#     return CategorizationAgent(config=config)

# async def get_intelligent_data_interpretation_service():
#     return None


async def get_unified_ai_service(conn=Depends(get_db_session)):
    """Dependency to get UnifiedAIService instance."""
    from ..shared.ai.unified_ai import AIServiceConfig, UnifiedAIService

    config = AIServiceConfig(
        project=getattr(settings, "VERTEX_PROJECT_ID", None),
        location=getattr(settings, "VERTEX_LOCATION", None),
        model_name=getattr(
            settings, "VERTEX_AI_GEMINI_MODEL_ID", "gemini-2.0-flash-001"
        ),
    )

    service = UnifiedAIService(db=conn, config=config)
    await service.initialize()
    return service


async def get_intelligence_service():
    """Dependency to get IntelligenceService instance."""
    from ..domains.intelligence.service import get_intelligence_service

    return get_intelligence_service()


# FILE PROCESSING AND TRANSACTION SERVICES NOW HANDLED BY ADK AGENTS
# No primitive service dependencies needed - all through AI-powered agents
