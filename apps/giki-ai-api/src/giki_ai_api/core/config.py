import os
import textwrap
from pathlib import Path
from urllib.parse import urlparse

from pydantic import ValidationInfo, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# Google Cloud Secret Manager integration
try:
    from ..shared.services.google_cloud_secrets import get_secrets_manager

    SECRETS_MANAGER_AVAILABLE = True
except ImportError:
    SECRETS_MANAGER_AVAILABLE = False
    get_secrets_manager = None


class Settings(BaseSettings):
    """
    Application configuration settings loaded from environment variables.
    """

    model_config = SettingsConfigDict(
        # Load environment-specific .env file for configuration
        env_file=".env.development" if os.path.exists(".env.development") else None,
        env_file_encoding="utf-8",
        extra="ignore",  # Ignore extra fields from environment
    )

    # Core Application Settings
    APP_NAME: str = "giki-ai API"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    MAX_UPLOAD_FILE_SIZE_MB: int = 10

    # Database Configuration (Environment-aware)
    # SECURITY: MUST be set via environment variable - no default with credentials
    DATABASE_URL: str = ""  # Will be validated and populated from environment/secrets

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Get environment information
        environment = os.getenv("ENVIRONMENT", "development").lower()
        is_local_dev = environment == "development" and not os.getenv(
            "K_SERVICE"
        )  # Not in Cloud Run

        # Set environment-aware DATABASE_URL if not provided
        if not self.DATABASE_URL:
            if is_local_dev:
                # Local development: Use local PostgreSQL
                self.DATABASE_URL = os.getenv(
                    "LOCAL_DATABASE_URL",
                    "postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db",
                )
            else:
                # Cloud environments: Try to get from Secret Manager
                if SECRETS_MANAGER_AVAILABLE and get_secrets_manager:
                    try:
                        secrets_manager = get_secrets_manager()
                        secret_url = secrets_manager.get_database_url()
                        if secret_url:
                            self.DATABASE_URL = secret_url
                        else:
                            # Fallback for cloud development
                            if environment in ("production", "prod"):
                                self.DATABASE_URL = "postgresql+asyncpg://giki_ai_user:password@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres-prod"
                            else:
                                self.DATABASE_URL = "postgresql+asyncpg://giki_ai_user:password@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres-dev"
                    except Exception as e:
                        print(
                            f"Warning: Could not retrieve database URL from Secret Manager: {e}"
                        )
                        # Use environment variable fallback
                        self.DATABASE_URL = os.getenv("DATABASE_URL", "")

        # Set environment-aware CORS origins
        if not hasattr(self, "_cors_origins_set"):
            if is_local_dev:
                # Local development: Allow localhost and production for testing
                self.CORS_ALLOWED_ORIGINS = "http://localhost:4200,http://127.0.0.1:4200,https://app-giki-ai.web.app"
            else:
                # Cloud environments: Try to get from Secret Manager
                if SECRETS_MANAGER_AVAILABLE and get_secrets_manager:
                    try:
                        secrets_manager = get_secrets_manager()
                        secret_cors = secrets_manager.get_cors_origins()
                        if secret_cors:
                            self.CORS_ALLOWED_ORIGINS = secret_cors
                    except Exception as e:
                        print(
                            f"Warning: Could not retrieve CORS origins from Secret Manager: {e}"
                        )
            self._cors_origins_set = True

    # Database URL parsing properties for backward compatibility
    @property
    def database_host(self) -> str:
        """Parse database host from DATABASE_URL."""
        if not self.DATABASE_URL:
            return "localhost"
        parsed = urlparse(self.DATABASE_URL)
        return parsed.hostname or "localhost"

    @property
    def database_port(self) -> int:
        """Parse database port from DATABASE_URL."""
        if not self.DATABASE_URL:
            return 5432
        parsed = urlparse(self.DATABASE_URL)
        return parsed.port or 5432

    @property
    def database_user(self) -> str:
        """Parse database user from DATABASE_URL."""
        if not self.DATABASE_URL:
            return "giki_ai_user"
        parsed = urlparse(self.DATABASE_URL)
        return parsed.username or "giki_ai_user"

    @property
    def database_password(self) -> str:
        """Parse database password from DATABASE_URL."""
        if not self.DATABASE_URL:
            return ""
        parsed = urlparse(self.DATABASE_URL)
        return parsed.password or ""

    @property
    def database_name(self) -> str:
        """Parse database name from DATABASE_URL."""
        if not self.DATABASE_URL:
            return "giki_ai_db"
        parsed = urlparse(self.DATABASE_URL)
        return parsed.path.lstrip("/") or "giki_ai_db"

    # CORS Configuration (Environment-aware) - Default to production-safe values
    CORS_ALLOWED_ORIGINS: str = (
        "https://app-giki-ai.web.app"  # Default to production only
    )
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: str = "GET,POST,PUT,DELETE,OPTIONS"
    CORS_ALLOW_HEADERS: str = "Authorization,Content-Type"

    # Authentication (using giki-ai-auth)
    # SECURITY: These MUST be set via environment variables - no defaults in production
    AUTH_SECRET_KEY: str = ""
    AUTH_ALGORITHM: str = "HS256"
    AUTH_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    # This is for compatibility with giki-ai-auth which expects this name
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    AUTH_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7  # For giki-ai-auth compatibility
    # For giki-ai-auth compatibility
    # SECURITY: MUST be set via environment variable - no default
    SECRET_KEY: str = ""
    ALGORITHM: str = "HS256"

    # Admin Configuration
    ADMIN_API_KEY: str = ""  # Admin API key for administrative endpoints

    # File Storage Configuration
    UPLOAD_STORAGE_PATH: str = "uploads"  # Base path for file uploads

    @property
    def upload_base_path(self) -> Path:
        """Get the base upload path based on environment."""
        # Cloud Run sets K_SERVICE environment variable
        if os.environ.get("K_SERVICE"):
            # In Cloud Run, use /tmp which is the only writable directory
            return Path("/tmp") / self.UPLOAD_STORAGE_PATH
        else:
            # Local development - use current directory
            return Path(self.UPLOAD_STORAGE_PATH)

    def get_upload_directory(self, tenant_id: int) -> Path:
        """Get upload directory for a specific tenant."""
        upload_dir = self.upload_base_path / str(tenant_id)
        upload_dir.mkdir(parents=True, exist_ok=True)
        return upload_dir

    # GCP Configuration
    # SECURITY: These MUST be set via environment variables in production
    VERTEX_PROJECT_ID: str | None = "rezolve-poc"  # Default for development
    VERTEX_LOCATION: str | None = "us-central1"

    # This field will be populated directly by Pydantic from the GOOGLE_APPLICATION_CREDENTIALS environment variable
    GOOGLE_APPLICATION_CREDENTIALS: str | None = None

    @property
    def VERTEX_SERVICE_ACCOUNT_KEY_PATH(self) -> str | None:
        return self.GOOGLE_APPLICATION_CREDENTIALS

    GCS_BUCKET_NAME_RAG: str | None = "giki-ai-gcs"
    FILE_INGESTION_AI_MODEL_NAME: str = "gemini-2.0-flash-001"
    VERTEX_CHAT_MODEL_NAME: str = "gemini-2.0-flash-001"
    HIERARCHY_INFERENCE_AI_MODEL_NAME: str = (
        "gemini-2.0-flash-001"  # Or another suitable model
    )
    VERTEX_AI_GEMINI_MODEL_ID: str = (
        "gemini-2.0-flash-001"  # Default model for general Gemini interactions
    )

    # Categorization Service Configuration
    # Updated to match the POC model that achieved 71.90% accuracy
    CATEGORIZATION_MODEL_ID: str = "gemini-2.0-flash-001"
    # Optimized RAG parameters based on POC results
    RAG_CHUNK_SIZE: int = (
        512  # Default chunk size, works well for transaction descriptions
    )
    RAG_CHUNK_OVERLAP: int = 100  # Default overlap between chunks
    RAG_RETRIEVAL_COUNT: int = 5  # Number of similar transactions to retrieve from RAG
    POLL_INTERVAL_SECONDS: int = 10
    MAX_POLL_DURATION_SECONDS: int = 600
    RAG_CORPUS_CREATE_TIMEOUT_SECONDS: int = 300
    RAG_FILE_IMPORT_TIMEOUT_SECONDS: int = 1800
    RAG_CORPUS_DELETE_TIMEOUT_SECONDS: int = 300
    RECORD_CATEGORY_FUNCTION_NAME: str = "record_transaction_category"
    RAG_CONTEXT_SNIPPET_SEPARATOR: str = "\n---\n"
    PROMPT_RETRIEVED_CONTEXT_PREFIX: str = (
        "Retrieved Context (Similar Transactions with Categories):\n---\n"
    )
    PROMPT_RETRIEVED_CONTEXT_SUFFIX: str = "\n---\n"
    PROMPT_NO_CONTEXT_MESSAGE: str = "No relevant context retrieved. Categorize based on general knowledge if possible, otherwise use 'Unknown'."
    PROMPT_TARGET_TRANSACTION_FORMAT_STRING: str = "Categorize this single target transaction using the function call:\n```json\n{}\n```"
    RECORD_CATEGORY_FUNCTION_DESCRIPTION: str = "Records the determined category and confidence score for a given financial transaction."
    CATEGORIZATION_TEMPERATURE: float = 0.1
    PROMPT_HIERARCHY_CONTEXT_PREFIX: str = "Customer-Specific Account Hierarchy (use this to guide category selection):\n---\n"
    PROMPT_HIERARCHY_CONTEXT_SUFFIX: str = "\n---\n"
    CATEGORIZATION_SYSTEM_INSTRUCTION: str = textwrap.dedent(
        """
            You are an expert financial transaction categorizer. Your task is to categorize the single target transaction provided.
            You will be given:
            1.  A CUSTOMER-SPECIFIC ACCOUNT HIERARCHY: This lists valid account codes and names for the customer. Prioritize these if applicable.
            2.  RETRIEVED CONTEXT: Examples of similar transaction descriptions with their categories from past transactions.
                These may include a specific "Category" field, or "Account Head" and "Department" fields. Use these examples to infer categorization patterns.
            3.  A single TARGET transaction.

            Instructions:
            1.  Review the CUSTOMER-SPECIFIC ACCOUNT HIERARCHY. This is your primary reference for valid categories for this customer.
            2.  Analyze the RETRIEVED CONTEXT (past examples) to understand common categorization patterns.
               *   Pay special attention to any explicit "Category" field in the retrieved examples, as these represent ground truth categorizations.
               *   If no explicit "Category" field exists, use the "Account Head" and "Department" fields to understand categorization patterns.
            3.  Analyze the single TARGET transaction provided, focusing primarily on its description.
            4.  Determine the most appropriate category for the target transaction based on the following prioritization:
               a. If transaction descriptions in RETRIEVED CONTEXT are very similar to the target and have consistent categories, use that category.
               b. If similar descriptions have a specific "Category" field, prioritize using that exact category.
               c. If only "Account Head" and "Department" fields are available, construct the category as described in step 6.
               d. Match against the CUSTOMER-SPECIFIC ACCOUNT HIERARCHY for standardized naming.
            5.  If using "Account Head" and "Department" fields (when "Category" is not available):
               *   The CUSTOMER-SPECIFIC ACCOUNT HIERARCHY might imply structure (e.g., sub-accounts under a parent could be considered departments).
               *   If no specific department seems relevant, use 'Unknown' or omit it if the Account Head typically stands alone.
            6.  Construct the final category string:
               *   If using an explicit "Category" field from similar transactions, use that exact category name.
               *   Otherwise, if a relevant Department (not 'Unknown') was determined, use the format 'Department - Account Head'.
               *   If only Account Head is relevant, use just the 'Account Head'.
               *   If no clear category can be inferred even with the hierarchy and context, use 'Unknown'.
            7.  Estimate your confidence in this categorization as a float between 0.0 (not confident) and 1.0 (very confident).
               *   Higher confidence (0.8-1.0) should be assigned when the target description closely matches examples with consistent categorization.
               *   Medium confidence (0.5-0.7) when there are similar but not exact matches with consistent categories.
               *   Lower confidence (0.0-0.4) when there are no close matches or inconsistent categorization among similar descriptions.
            8.  Call the '{record_transaction_category}' function with the transaction_id of the TARGET transaction, the final constructed category string, and your confidence score.
        """
    ).strip()

    # Vector DB Configuration
    VECTOR_DB_TYPE: str | None = None
    VECTOR_DB_URL: str | None = None

    # Async Task Queue
    TASK_QUEUE_BROKER_URL: str | None = None
    TASK_QUEUE_BACKEND_URL: str | None = None

    @field_validator("AUTH_SECRET_KEY", "SECRET_KEY", "DATABASE_URL")
    @classmethod
    def validate_critical_secrets(cls, v: str, info: ValidationInfo) -> str:
        """Ensure critical secrets are properly configured and secure."""
        import os
        import secrets
        import string

        # Check environment to determine if we're in production
        environment = os.getenv("ENVIRONMENT", "development").lower()
        is_production = environment in ("production", "prod")
        debug_mode = (
            os.getenv("DEBUG", "True").lower() in ("true", "1", "yes")
            and not is_production
        )
        field_name = info.field_name

        # Try environment variables first (faster), then Google Cloud Secret Manager
        if not v:
            # First, try environment variable (much faster and more reliable)
            env_value = os.getenv(field_name)
            if env_value:
                print(f"INFO: Retrieved {field_name} from environment variable")
                return env_value

            # Only try Google Cloud Secret Manager if environment variable not available
            if SECRETS_MANAGER_AVAILABLE and get_secrets_manager:
                try:
                    print(
                        f"INFO: Attempting to retrieve {field_name} from Google Cloud Secret Manager..."
                    )
                    secrets_manager = get_secrets_manager()
                    secret_value = secrets_manager.get_secret(field_name)
                    if secret_value:
                        print(
                            f"INFO: Retrieved {field_name} from Google Cloud Secret Manager"
                        )
                        return secret_value
                except Exception as e:
                    print(
                        f"WARNING: Failed to retrieve {field_name} from Secret Manager: {e}"
                    )

        # In development, provide secure defaults if not set
        if debug_mode and not v:
            if field_name in ("AUTH_SECRET_KEY", "SECRET_KEY"):
                # Generate a secure random key for development
                alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
                secure_key = "".join(secrets.choice(alphabet) for _ in range(64))
                print(f"INFO: Generated secure {field_name} for development")
                return secure_key
            elif field_name == "DATABASE_URL":
                # In development, DATABASE_URL is required
                if debug_mode:
                    raise ValueError(
                        f"{field_name} must be set via Google Cloud Secret Manager or environment variable. "
                        f"No database credentials should be hardcoded."
                    )

        # In production, require all critical secrets to be set
        if not debug_mode:
            if not v:
                # DATABASE_URL in production can be set via Secret Manager, which happens after initialization
                if field_name == "DATABASE_URL":
                    # Don't return empty - wait for Secret Manager
                    pass
                else:
                    raise ValueError(
                        f"PRODUCTION ERROR: {field_name} is required but not set. "
                        f"Set {field_name} environment variable."
                    )

            # Validate secret strength for auth keys
            if field_name in ("AUTH_SECRET_KEY", "SECRET_KEY"):
                if len(v) < 32:
                    raise ValueError(
                        f"{field_name} must be at least 32 characters long for security."
                    )
                if v.startswith("development-only"):
                    raise ValueError(
                        f"SECURITY VIOLATION: {field_name} still uses development default in production. "
                        f"Generate a secure random value with: python -c 'import secrets; print(secrets.token_urlsafe(64))'"
                    )

            # Validate database URL doesn't contain obvious test values
            if field_name == "DATABASE_URL":
                dangerous_patterns = ["password123", "test", "localhost", "development"]
                v_lower = v.lower()
                for pattern in dangerous_patterns:
                    if pattern in v_lower:
                        raise ValueError(
                            f"SECURITY WARNING: {field_name} contains potentially unsafe pattern: '{pattern}'. "
                            f"Ensure production database credentials are secure."
                        )

        return v

    @field_validator("VERTEX_PROJECT_ID")
    @classmethod
    def validate_vertex_project(cls, v: str | None, info: ValidationInfo) -> str | None:
        """Ensure production Vertex AI project is properly configured."""
        # Get DEBUG from environment since cls.DEBUG may not be available during validation
        import os

        debug_mode = os.getenv("DEBUG", "True").lower() in ("true", "1", "yes")

        if not debug_mode and v == "development-vertex-project":
            raise ValueError(
                "Production deployment detected but VERTEX_PROJECT_ID still uses development default. "
                "Set VERTEX_PROJECT_ID environment variable with your actual GCP project ID."
            )
        return v

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return not self.DEBUG and os.getenv("ENVIRONMENT", "").lower() in (
            "production",
            "prod",
        )

    def get_cors_origins(self) -> list[str]:
        """Parse CORS allowed origins from comma or semicolon separated string."""
        if not self.CORS_ALLOWED_ORIGINS:
            return []

        # Support both comma and semicolon separators for compatibility with gcloud CLI
        if ";" in self.CORS_ALLOWED_ORIGINS:
            # Split by semicolon if present
            origins = [
                origin.strip() for origin in self.CORS_ALLOWED_ORIGINS.split(";")
            ]
        else:
            # Otherwise split by comma
            origins = [
                origin.strip() for origin in self.CORS_ALLOWED_ORIGINS.split(",")
            ]

        # Filter out empty strings
        origins = [origin for origin in origins if origin]

        return origins

    def get_cors_methods(self) -> list[str]:
        """Parse CORS allowed methods from comma-separated string."""
        if not self.CORS_ALLOW_METHODS:
            return ["GET", "POST", "PUT", "DELETE", "OPTIONS"]

        # Split by comma and strip whitespace
        methods = [method.strip() for method in self.CORS_ALLOW_METHODS.split(",")]
        # Filter out empty strings
        methods = [method for method in methods if method]

        return methods

    def get_cors_headers(self) -> list[str]:
        """Parse CORS allowed headers from comma-separated string."""
        if not self.CORS_ALLOW_HEADERS:
            return ["Authorization", "Content-Type"]

        # Split by comma and strip whitespace
        headers = [header.strip() for header in self.CORS_ALLOW_HEADERS.split(",")]
        # Filter out empty strings
        headers = [header for header in headers if header]

        return headers

    # Temporal validation settings
    TEMPORAL_VALIDATION_MAX_TRANSACTIONS_PER_MONTH: int = 20
    TEMPORAL_VALIDATION_BATCH_SIZE: int = 5
    TEMPORAL_VALIDATION_RATE_LIMIT_DELAY: int = 30  # seconds to wait on rate limit

    def validate_production_config(self) -> None:
        """Validate all production-critical configuration with enhanced security checks."""
        if self.is_production():
            issues = []
            security_issues = []

            # Check critical authentication secrets
            if not self.AUTH_SECRET_KEY or len(self.AUTH_SECRET_KEY) < 32:
                security_issues.append(
                    "AUTH_SECRET_KEY must be set with at least 32 secure characters"
                )

            if not self.SECRET_KEY or len(self.SECRET_KEY) < 32:
                security_issues.append(
                    "SECRET_KEY must be set with at least 32 secure characters"
                )

            if not self.DATABASE_URL:
                security_issues.append(
                    "DATABASE_URL must be set via environment variable"
                )

            # Check for dangerous patterns in sensitive fields
            dangerous_patterns = ["development", "test", "password123", "localhost"]
            for pattern in dangerous_patterns:
                if pattern in self.DATABASE_URL.lower():
                    security_issues.append(
                        f"DATABASE_URL contains unsafe pattern: '{pattern}'"
                    )

            # Check optional but recommended production settings
            if self.VERTEX_PROJECT_ID == "development-vertex-project":
                issues.append(
                    "VERTEX_PROJECT_ID should be set to production GCP project"
                )

            # Fail hard on security issues
            if security_issues:
                raise RuntimeError(
                    "🚨 PRODUCTION SECURITY VALIDATION FAILED 🚨\n"
                    + "\nCRITICAL SECURITY ISSUES:\n"
                    + "\n".join(f"- {issue}" for issue in security_issues)
                    + "\n\nTO FIX:\n"
                    + "1. Generate secure keys: python -c 'import secrets; print(secrets.token_urlsafe(64))'\n"
                    + "2. Set environment variables with generated values\n"
                    + "3. Never commit secrets to version control\n"
                    + "4. Use proper secret management in production\n"
                )

            # Log warnings for other issues but don't fail
            if issues:
                print(
                    "⚠️ PRODUCTION CONFIGURATION WARNINGS:\n"
                    + "\n".join(f"- {issue}" for issue in issues)
                    + "\n\nRecommended: Set these environment variables for full production readiness."
                )


# Instantiate the settings object for global access
settings = Settings()

# Validate production configuration on import with enhanced security
try:
    settings.validate_production_config()
except RuntimeError as e:
    # Log the error but don't crash during import (allows for development)
    if settings.is_production():
        print("\n" + "=" * 80)
        print("🚨 CRITICAL SECURITY ERROR - SERVER STARTUP BLOCKED 🚨")
        print("=" * 80)
        print(str(e))
        print("=" * 80 + "\n")
        raise e
    else:
        print(f"INFO: Development mode - {str(e)[:100]}...")

# Additional security check: ensure no secrets in logs
if settings.is_production():
    import logging

    # Configure logging to never log sensitive configuration
    class SensitiveInfoFilter(logging.Filter):
        def filter(self, record):
            sensitive_patterns = [
                settings.AUTH_SECRET_KEY[:8] if settings.AUTH_SECRET_KEY else "",
                settings.SECRET_KEY[:8] if settings.SECRET_KEY else "",
                "password",
                "secret",
                "key",
                "token",
            ]

            message = record.getMessage().lower()
            for pattern in sensitive_patterns:
                if pattern and pattern in message:
                    record.msg = "[REDACTED: Sensitive information filtered from logs]"
                    break
            return True

    # Add filter to root logger
    logging.getLogger().addFilter(SensitiveInfoFilter())

    print("✅ Production security logging filter activated")
