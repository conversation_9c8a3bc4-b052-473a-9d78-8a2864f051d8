"""
Database connection management using asyncpg.

This module provides direct PostgreSQL connection pooling and query execution
without SQLAlchemy ORM overhead.
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

import asyncpg
from asyncpg import Pool

from .config import settings

logger = logging.getLogger(__name__)


def get_database_url() -> str:
    """Get the database URL from environment or settings."""
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL
    if not db_url:
        raise ValueError("DATABASE_URL not configured")

    # Convert SQLAlchemy-style URLs to asyncpg format
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")

    return db_url


# Global connection pool
_pool: Optional[Pool] = None
_pool_lock = asyncio.Lock()


def parse_database_url(url: str) -> Dict[str, Any]:
    """Parse database URL into connection parameters."""
    parsed = urlparse(url)

    # Handle special characters in password
    password = parsed.password
    if password:
        password = password.replace("%40", "@")

    return {
        "host": parsed.hostname,
        "port": parsed.port or 5432,
        "user": parsed.username,
        "password": password,
        "database": parsed.path.lstrip("/"),
        "ssl": "prefer" if "sslmode=disable" not in url else None,
    }


async def create_pool(
    min_size: int = 5,
    max_size: int = 15,
    max_queries: int = 50000,
    max_inactive_connection_lifetime: float = 300.0,
    command_timeout: float = 30.0,
    server_settings: Optional[Dict[str, str]] = None,
) -> Pool:
    """Create and return a connection pool."""
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL

    if not db_url:
        raise ValueError("DATABASE_URL not configured")

    # Parse connection parameters
    conn_params = parse_database_url(db_url)

    logger.info(
        f"Creating connection pool: host={conn_params['host']}, "
        f"database={conn_params['database']}, pool_size={min_size}-{max_size}"
    )

    # Default server settings for better compatibility
    default_server_settings = {
        "application_name": "giki_ai_api",
        "statement_timeout": "30s",
        "timezone": "UTC",
    }
    if server_settings:
        default_server_settings.update(server_settings)

    try:
        pool = await asyncpg.create_pool(
            **conn_params,
            min_size=min_size,
            max_size=max_size,
            max_queries=max_queries,
            max_inactive_connection_lifetime=max_inactive_connection_lifetime,
            command_timeout=command_timeout,
            statement_cache_size=0,  # Disable statement caching for dynamic queries
            server_settings=default_server_settings,
        )

        # Test the pool
        async with pool.acquire() as conn:
            await conn.fetchval("SELECT 1")

        logger.info("Database pool created successfully")
        return pool

    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise


async def get_pool() -> Pool:
    """Get or create the global connection pool."""
    global _pool

    if _pool is None:
        async with _pool_lock:
            if _pool is None:
                _pool = await create_pool()

    return _pool


@asynccontextmanager
async def get_database_connection():
    """Get a database connection from the pool."""
    pool = await get_pool()
    async with pool.acquire() as connection:
        yield connection


async def close_pool():
    """Close the global connection pool."""
    global _pool

    if _pool is not None:
        await _pool.close()
        _pool = None
        logger.info("Database pool closed")


@asynccontextmanager
async def get_db():
    """
    Get a database connection from the pool.

    Usage:
        async with get_db() as conn:
            result = await conn.fetch("SELECT * FROM users")
    """
    pool = await get_pool()

    async with pool.acquire() as conn:
        try:
            yield conn
        except Exception as e:
            logger.error(f"Database error: {e}")
            raise


@asynccontextmanager
async def transaction():
    """
    Create a database transaction context.

    Usage:
        async with transaction() as conn:
            await conn.execute("INSERT INTO ...")
            await conn.execute("UPDATE ...")
    """
    pool = await get_pool()

    async with pool.acquire() as conn:
        async with conn.transaction():
            try:
                yield conn
            except Exception as e:
                logger.error(f"Transaction error: {e}")
                raise


class Database:
    """Database helper class for common operations."""

    @staticmethod
    async def fetch_one(
        query: str, *args, timeout: float = 10.0
    ) -> Optional[asyncpg.Record]:
        """Fetch a single row."""
        async with get_db() as conn:
            return await conn.fetchrow(query, *args, timeout=timeout)

    @staticmethod
    async def fetch_all(
        query: str, *args, timeout: float = 15.0
    ) -> List[asyncpg.Record]:
        """Fetch all rows."""
        async with get_db() as conn:
            return await conn.fetch(query, *args, timeout=timeout)

    @staticmethod
    async def fetch_val(
        query: str, *args, column: int = 0, timeout: float = 10.0
    ) -> Any:
        """Fetch a single value."""
        async with get_db() as conn:
            return await conn.fetchval(query, *args, column=column, timeout=timeout)

    @staticmethod
    async def execute(query: str, *args, timeout: float = 10.0) -> str:
        """Execute a query and return status."""
        async with get_db() as conn:
            return await conn.execute(query, *args, timeout=timeout)

    @staticmethod
    async def execute_many(
        query: str, args_list: List[tuple], timeout: float = 30.0
    ) -> None:
        """Execute a query multiple times with different arguments."""
        async with get_db() as conn:
            await conn.executemany(query, args_list, timeout=timeout)

    @staticmethod
    async def insert_returning(
        table: str, data: Dict[str, Any], returning: str = "*"
    ) -> asyncpg.Record:
        """Insert a row and return specified columns."""
        columns = ", ".join(data.keys())
        placeholders = ", ".join(f"${i + 1}" for i in range(len(data)))
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) RETURNING {returning}"

        async with get_db() as conn:
            return await conn.fetchrow(query, *data.values())

    @staticmethod
    async def update_returning(
        table: str, data: Dict[str, Any], where: Dict[str, Any], returning: str = "*"
    ) -> List[asyncpg.Record]:
        """Update rows and return specified columns."""
        set_clause = ", ".join(f"{k} = ${i + 1}" for i, k in enumerate(data.keys()))
        where_clause = " AND ".join(
            f"{k} = ${i + 1 + len(data)}" for i, k in enumerate(where.keys())
        )
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause} RETURNING {returning}"

        async with get_db() as conn:
            return await conn.fetch(query, *data.values(), *where.values())


# Maintain backward compatibility for FastAPI dependency injection
async def get_db_session():
    """FastAPI dependency for database connections."""
    async with get_db() as conn:
        yield conn


# Alias for backward compatibility
db = Database()
