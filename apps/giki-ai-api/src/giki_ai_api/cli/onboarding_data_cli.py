"""
CLI for orchestrating Customer Onboarding Data Preparation.
"""

import asyncio
import csv
import datetime
import io
import json
import logging
import os
from typing import Annotated, Any

import asyncpg
import pandas as pd
import typer
import vertexai
from vertexai.generative_models import (
    Content,
    GenerationConfig,
    GenerativeModel,
    Part,
)

from ..auth import TokenData, get_password_hash
from ..config import settings
from ..models.base import Base
from ..models.core import Tenant as TenantModel, User as UserModel
from ..services.customer.onboarding import CustomerOnboardingDataService

# Constants for CLI defaults
DEFAULT_TENANT_ID = 1
DEFAULT_TENANT_NAME = "New Customer Tenant"
DEFAULT_TRANSACTION_FILES_DIR = "test-files"
DEFAULT_RAG_CORPUS_NAME_TEMPLATE = "Customer_Onboarding_RAG_Corpus_Tenant_{tenant_id}"
DEFAULT_CUSTOMER_DATA_OUTPUT_DIR_TEMPLATE = (
    "apps/giki-ai-api/data/onboarding_output_data/tenant_{tenant_id}"
)

# Constants for status values
STATUS_SUCCESS = "success"
STATUS_PROCESSING = "processing"

# Constants for AI model configuration
AI_TEMPERATURE = 0.1
AI_SAMPLE_DATA_LIMIT = 5

# Generate secure random password for onboarding user
import secrets
import string


def generate_secure_onboarding_password() -> str:
    """Generate cryptographically secure random password for onboarding."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return "".join(secrets.choice(alphabet) for _ in range(16))


DEFAULT_ONBOARDING_USER_EMAIL_TEMPLATE = (
    "onboarding_user_tenant_{tenant_id}@giki-ai.app"
)

# Configure logging for the CLI
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Define the main CLI logic function
async def cli_main_async_logic(  # Renamed to be the main app callback
    ctx: typer.Context,  # ctx is still useful for Typer's context if needed
    tenant_id: Annotated[
        int, typer.Option(help="Target Tenant ID for onboarding.")
    ] = DEFAULT_TENANT_ID,
    tenant_name: Annotated[
        str,
        typer.Option(help="Target Tenant Name (used if tenant needs to be created)."),
    ] = DEFAULT_TENANT_NAME,
    transaction_files_dir: Annotated[
        str,
        typer.Option(
            help="Directory containing the customer's transaction files (e.g., CSV, XLSX). Default: 'apps/giki-ai-api/data/input_files'.",
            exists=True,
            file_okay=False,
            dir_okay=True,
            readable=True,
        ),
    ] = DEFAULT_TRANSACTION_FILES_DIR,  # Default path as per original task
    rag_corpus_display_name_template: Annotated[
        str,
        typer.Option(
            help="Template for the RAG corpus display name. '{tenant_id}' will be replaced."
        ),
    ] = DEFAULT_RAG_CORPUS_NAME_TEMPLATE,
    rag_gcs_bucket_name: Annotated[
        str,
        typer.Option(
            help="GCS bucket name for RAG data. If not set, uses value from settings.GCS_BUCKET_NAME_RAG."
        ),
    ] = None,  # type: ignore # Will use settings if None, type Optional[str] is better
    customer_data_output_dir_template: Annotated[
        str,
        typer.Option(
            help="Template for the output directory for monthly customer data exports (e.g., for verification). '{tenant_id}' will be replaced."
        ),
    ] = DEFAULT_CUSTOMER_DATA_OUTPUT_DIR_TEMPLATE,  # Renamed from test_data
):
    """
    Orchestrates the entire customer data onboarding flow:
    1. Ingests customer transaction files.
    2. Creates a RAG corpus from an initial period of this data (e.g., first 6 months).
    3. Optionally exports monthly data sets for subsequent periods (e.g., for verification or phased processing).
    """
    # Suppress unused parameter warning
    _ = ctx
    logger.info(
        f"Starting customer data onboarding preparation for Tenant ID: {tenant_id}, Name: {tenant_name}"
    )

    actual_rag_gcs_bucket_name = rag_gcs_bucket_name or settings.GCS_BUCKET_NAME_RAG
    if not actual_rag_gcs_bucket_name:
        logger.error(
            "RAG GCS bucket name is not configured via --rag-gcs-bucket-name or settings.GCS_BUCKET_NAME_RAG."
        )
        raise typer.Exit(code=1)

    final_rag_corpus_display_name = rag_corpus_display_name_template.format(
        tenant_id=tenant_id
    )
    final_customer_data_output_dir = customer_data_output_dir_template.format(
        tenant_id=tenant_id
    )

    os.makedirs(final_customer_data_output_dir, exist_ok=True)

    db_session = DBSession()
    async with db_session as db:
        try:
            # Get or create user token data for the orchestrator service
            # This user represents the onboarding process itself or an admin performing it.
            await get_or_create_onboarding_user_token(  # Changed to await
                db, tenant_id, tenant_name
            )

            orchestrator = CustomerOnboardingDataService(db=db)

            logger.info(
                f"Running customer onboarding workflow for tenant {tenant_id}..."
            )

            # Step 1: Create tenant if needed (already done by get_or_create_onboarding_user_token)
            logger.info("Step 1: Tenant setup completed")

            # Step 2: Seed initial data
            logger.info("Step 2: Seeding initial data...")
            seed_result = await orchestrator.seed_initial_data(
                tenant_id=tenant_id, data_type="default_categories"
            )

            # Step 3: Complete onboarding
            logger.info("Step 3: Completing onboarding...")
            onboarding_data = {
                "transaction_files_dir": transaction_files_dir,
                "rag_corpus_display_name": final_rag_corpus_display_name,
                "rag_gcs_bucket_name": actual_rag_gcs_bucket_name,
                "customer_data_output_dir": final_customer_data_output_dir,
                "seed_result": seed_result,
            }

            result = await orchestrator.complete_onboarding(
                tenant_id=tenant_id, onboarding_data=onboarding_data
            )

            logger.info("Customer onboarding data preparation process finished.")
            if isinstance(result, dict) and result.get("status") == "success":
                typer.secho(
                    "Onboarding data preparation completed successfully.",
                    fg=typer.colors.GREEN,
                )
                typer.echo("Summary:")
                typer.echo(f"  Tenant ID: {tenant_id}")
                ingestion_summary_list = result.get("ingestion_summary", [])
                typer.echo(
                    f"  Ingestion: {len(ingestion_summary_list) if isinstance(ingestion_summary_list, list) else 0} files processed."
                )
                rag_summary = result.get("rag_corpus_summary", {})
                typer.echo(
                    f"  RAG Corpus: {rag_summary.get('corpus_name', 'N/A') if isinstance(rag_summary, dict) else 'N/A'}"
                )
                customer_data_summary = result.get("customer_data_summary", {})
                typer.echo(
                    f"  Data Output Dir: {customer_data_summary.get('output_directory', 'N/A') if isinstance(customer_data_summary, dict) else 'N/A'}"
                )
                typer.echo("\nDocumentation/Access Info:")
                typer.echo(
                    result.get(
                        "documentation_for_onboarding",
                        "No specific documentation generated by the process.",
                    )
                )
            elif isinstance(result, dict):
                typer.secho(
                    f"Onboarding data preparation failed: {result.get('message', 'Unknown error')}",
                    fg=typer.colors.RED,
                )
                ingestion_summary = result.get("ingestion_summary")
                if isinstance(ingestion_summary, list):
                    typer.echo("Ingestion Summary (Failures/Errors):")
                    for item in ingestion_summary:
                        if isinstance(item, dict) and item.get("status") != "success":
                            typer.echo(
                                f"  - File: {item.get('file', 'N/A')}, Status: {item.get('status', 'N/A')}, Message: {item.get('message', 'N/A')}"
                            )
                rag_result_summary = result.get("rag_result")
                if (
                    isinstance(rag_result_summary, dict)
                    and rag_result_summary.get("status") != "success"
                ):
                    typer.echo(
                        f"  RAG Corpus Error: {rag_result_summary.get('message', 'N/A')}"
                    )
            else:
                typer.secho(
                    f"Onboarding data preparation failed with an unexpected result structure: {result}",
                    fg=typer.colors.RED,
                )

        except Exception as e:
            logger.error(f"Unhandled exception in CLI command: {e}", exc_info=True)
            typer.secho(f"An critical error occurred: {e}", fg=typer.colors.RED)
            raise typer.Exit(code=1)


# This redundant/placeholder Typer app definition and callback are removed.
# The main logic is in `cli_main_async_logic`.
# The `app` instance defined later will be the one used.

app = typer.Typer(
    name="giki-ai-onboard",
    help="CLI for Customer Onboarding Data Preparation. Default action is to prepare customer data.",
    invoke_without_command=True,
)


@app.callback()  # This will be the main entry point
def main_cli_runner_sync_wrapper(  # Changed to a synchronous wrapper
    ctx: typer.Context,
    tenant_id: Annotated[
        int, typer.Option(help="Target Tenant ID for onboarding.")
    ] = DEFAULT_TENANT_ID,
    tenant_name: Annotated[
        str,
        typer.Option(help="Target Tenant Name (used if tenant needs to be created)."),
    ] = DEFAULT_TENANT_NAME,
    transaction_files_dir: Annotated[
        str,
        typer.Option(
            help="Directory containing the customer's transaction files (e.g., CSV, XLSX). Default: 'apps/giki-ai-api/data/input_files'.",
            exists=True,
            file_okay=False,
            dir_okay=True,
            readable=True,
        ),
    ] = DEFAULT_TRANSACTION_FILES_DIR,
    rag_corpus_display_name_template: Annotated[
        str,
        typer.Option(
            help="Template for the RAG corpus display name. '{tenant_id}' will be replaced."
        ),
    ] = DEFAULT_RAG_CORPUS_NAME_TEMPLATE,
    rag_gcs_bucket_name: Annotated[
        str,
        typer.Option(
            help="GCS bucket name for RAG data. If not set, uses value from settings.GCS_BUCKET_NAME_RAG."
        ),
    ] = None,  # type: ignore # type Optional[str] is better
    customer_data_output_dir_template: Annotated[
        str,
        typer.Option(
            help="Template for the output directory for monthly customer data exports (e.g., for verification). '{tenant_id}' will be replaced."
        ),
    ] = DEFAULT_CUSTOMER_DATA_OUTPUT_DIR_TEMPLATE,
):
    # This synchronous wrapper will call the main async logic.
    asyncio.run(
        cli_main_async_logic(
            ctx=ctx,
            tenant_id=tenant_id,
            tenant_name=tenant_name,
            transaction_files_dir=transaction_files_dir,
            rag_corpus_display_name_template=rag_corpus_display_name_template,
            rag_gcs_bucket_name=rag_gcs_bucket_name,
            customer_data_output_dir_template=customer_data_output_dir_template,
        )
    )


# Helper to get DB session using context manager
class DBSession:  # Changed to async context manager
    async def __aenter__(self):
        # Import here to avoid circular imports
        from .. import database as db_module

        # Ensure database is configured and get session factory
        async_session_factory = db_module.get_database_config()

        if async_session_factory is None:
            raise RuntimeError("Failed to initialize database session factory")

        # Get the engine after configuration
        engine = db_module.engine

        if engine is None:
            raise RuntimeError("Database engine not initialized")

        # Create tables if they don't exist using run_sync for the async engine.
        async with engine.connect() as conn:
            await conn.run_sync(Base.metadata.create_all)
            await conn.commit()  # Ensure tables are committed

        # Use the async session factory correctly with async context manager
        self.db = async_session_factory()
        return self.db

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb
        await self.db.close()


async def get_or_create_onboarding_user_token(
    db: asyncpg.Connection,
    tenant_id: int,
    tenant_name: str,  # Changed Session to asyncpg.Connection
) -> TokenData:
    """
    Creates or retrieves a tenant and a dedicated user for onboarding tasks for that tenant.
    Returns TokenData for the user.
    """
    # Fetch tenant using asyncpg
    sql = "SELECT * FROM tenants WHERE id = $1"
    row = await db.fetchrow(sql, tenant_id)
    tenant = TenantModel.model_validate(dict(row)) if row else None

    if not tenant:
        logger.info(
            f"Tenant with ID {tenant_id} not found. Creating tenant '{tenant_name}'."
        )
        # Insert tenant using asyncpg
        sql = "INSERT INTO tenants (id, name) VALUES ($1, $2)"
        await db.execute(sql, tenant_id, tenant_name)

        # Fetch the created tenant
        sql = "SELECT * FROM tenants WHERE id = $1"
        row = await db.fetchrow(sql, tenant_id)
        tenant = TenantModel.model_validate(dict(row))
        logger.info(f"Tenant '{tenant_name}' created/retrieved with ID {tenant.id}.")

    onboarding_user_email = DEFAULT_ONBOARDING_USER_EMAIL_TEMPLATE.format(
        tenant_id=tenant_id
    )
    # Fetch user using asyncpg
    sql = "SELECT * FROM users WHERE email = $1 AND tenant_id = $2"
    row = await db.fetchrow(sql, onboarding_user_email, tenant_id)
    user = UserModel.model_validate(dict(row)) if row else None

    if not user:
        logger.info(
            f"Onboarding user {onboarding_user_email} not found for tenant {tenant_id}. Creating..."
        )
        hashed_password = get_password_hash(
            "DefaultOnboardingP@ssw0rd!"
        )  # Use a secure, configurable default

        # Insert user using asyncpg
        import uuid

        user_id = str(uuid.uuid4())
        sql = """
            INSERT INTO users (id, email, tenant_id, hashed_password, is_active, full_name)
            VALUES ($1, $2, $3, $4, $5, $6)
        """
        await db.execute(
            sql,
            user_id,
            onboarding_user_email,
            tenant_id,
            hashed_password,
            True,
            f"Onboarding User Tenant {tenant_id}",
        )

        # Fetch the created user
        sql = "SELECT * FROM users WHERE id = $1"
        row = await db.fetchrow(sql, user_id)
        user = UserModel.model_validate(dict(row))
        logger.info(
            f"Onboarding user {onboarding_user_email} created with ID {user.id} for tenant {tenant_id}."
        )

    return TokenData(user_id=user.id, tenant_id=tenant_id)


# Removed duplicated command definition

# --- Date Analysis Command Logic ---


class CLIFileProcessingError(Exception):
    """Custom exception for file processing errors within the CLI command."""

    pass


def _cli_parse_file_content(
    file_content: bytes, original_filename: str
) -> tuple[list[dict[str, Any]], list[str] | None]:
    """
    Parses file content (CSV or Excel) into a list of dictionaries and returns headers.
    Adapted for CLI use.
    """
    logger.info(f"CLI: Parsing file: {original_filename}")
    detected_headers: list[str] | None = None
    parsed_rows: list[dict[str, Any]] = []
    try:
        file_type_hint = original_filename.lower()
        if file_type_hint.endswith(".csv"):
            decoded_content = file_content.decode("utf-8-sig")
            file_stream = io.StringIO(decoded_content)
            try:
                dialect = csv.Sniffer().sniff(file_stream.read(2048))
                file_stream.seek(0)
                reader = csv.DictReader(file_stream, dialect=dialect)
            except csv.Error:
                logger.warning(
                    f"CLI: CSV Sniffer failed for {original_filename}, falling back to default dialect."
                )
                file_stream.seek(0)
                reader = csv.DictReader(file_stream)

            raw_fieldnames = reader.fieldnames
            if raw_fieldnames is not None:
                detected_headers = list(raw_fieldnames)
            else:
                detected_headers = None
            if not detected_headers:
                raise CLIFileProcessingError("CSV file is empty or has no headers.")
            for row in reader:
                parsed_rows.append(dict(row))
        elif file_type_hint.endswith((".xlsx", ".xls")):
            file_stream = io.BytesIO(file_content)
            engine = "openpyxl" if file_type_hint.endswith(".xlsx") else "xlrd"
            df = pd.read_excel(file_stream, engine=engine, dtype=str)
            df = df.fillna("")
            detected_headers = [str(col).strip() for col in df.columns.tolist()]
            temp_parsed_rows = df.to_dict(orient="records")
            for row_dict in temp_parsed_rows:
                cleaned_record = {
                    str(key).strip(): str(value).strip()
                    for key, value in row_dict.items()
                }
                if any(cleaned_record.values()):
                    parsed_rows.append(cleaned_record)
        else:
            raise CLIFileProcessingError(
                f"Unsupported file type: '{original_filename}'."
            )
    except Exception as e:
        logger.error(f"CLI: Error parsing file {original_filename}: {e}", exc_info=True)
        raise CLIFileProcessingError(
            f"Could not parse file '{original_filename}': {str(e)}"
        )
    return parsed_rows, detected_headers


async def _cli_get_ai_field_mappings(
    parsed_data: list[dict[str, Any]],
    headers: list[str] | None,
    model_instance: "GenerativeModel",
) -> tuple[dict[str, Any] | None, list[dict[str, Any]] | None]:
    if not parsed_data:
        logger.warning("CLI: No parsed data to send to AI.")
        return None, None

    sample_data_limit = 5
    sample_data_for_prompt = parsed_data[:sample_data_limit]
    sample_data_json_lines = "\n".join(
        [json.dumps(row) for row in sample_data_for_prompt]
    )

    prompt_text = f"""
You are an AI assistant specialized in analyzing sample financial data to create a reusable mapping configuration. This configuration will be used to programmatically process a full dataset.
The file originally had the following headers: {json.dumps(headers)}
Here are the first few rows of data (up to {sample_data_limit}) as JSON objects, representing the structure of the entire file:
{sample_data_json_lines}
Your task is to analyze these headers and sample rows to define a "field_mappings" JSON object. This object will specify how to extract data for our target transaction fields from any row in the dataset.
For each target field below, define its mapping:
- "date":
  - "source_columns": (List of strings) Candidate source header(s) for the transaction date. Prioritize columns explicitly named like 'Date', 'Transaction Date', 'Posting Date'.
  - "date_format_hint": (Optional string) If you can infer a common date format (e.g., "MM/DD/YYYY", "YYYY-MM-DD", "DD Mon YYYY"), provide it. Otherwise, null.
You MUST return a single JSON object as your response, containing "field_mappings". Focus only on the "date" field for this task.
Example for "date" field: {{ "date": {{ "source_columns": ["Transaction Date"], "date_format_hint": "MM/DD/YYYY" }} }}
"""
    request_contents = [Content(parts=[Part.from_text(prompt_text)])]
    generation_config = GenerationConfig(
        temperature=0.1,  # Lower temperature for more deterministic mapping
        response_mime_type="application/json",
    )
    logger.info(
        f"CLI: Sending headers and {sample_data_limit} sample rows to AI model {settings.FILE_INGESTION_AI_MODEL_NAME} for date field mapping generation."
    )

    try:
        ai_response = await model_instance.generate_content_async(  # Use async version
            contents=request_contents,
            generation_config=generation_config,
        )
    except Exception as e:
        logger.error(f"CLI: Vertex AI content generation failed: {e}", exc_info=True)
        raise CLIFileProcessingError(f"Vertex AI API call error: {e}")

    if not ai_response.candidates or not ai_response.candidates[0].content.parts:
        logger.error(f"CLI: AI response is empty or malformed. Response: {ai_response}")
        raise CLIFileProcessingError("AI response is empty or malformed.")

    response_text = ai_response.candidates[0].content.parts[0].text
    logger.info(
        f"CLI: Received AI mapping response for dates. Length: {len(response_text)} chars."
    )

    try:
        ai_mapping_result = json.loads(response_text)
    except json.JSONDecodeError as json_err:
        logger.error(
            f"CLI: Failed to parse AI JSON mapping response. Error: {json_err}. Response snippet: {response_text[:500]}",
            exc_info=True,
        )
        raise CLIFileProcessingError(
            f"AI returned invalid JSON for mapping: {json_err}"
        )

    field_mappings = ai_mapping_result.get("field_mappings")
    # global_clarifications = ai_mapping_result.get("global_clarifications", []) # Not needed for this specific task

    if not field_mappings or "date" not in field_mappings:
        logger.error(
            f"CLI: AI response did not contain 'date' in 'field_mappings'. Response: {response_text[:500]}"
        )
        raise CLIFileProcessingError(
            "AI response is missing 'date' in 'field_mappings'."
        )

    logger.info(
        f"CLI: Received date field_mappings from AI: {json.dumps(field_mappings.get('date'), indent=2)}"
    )
    return field_mappings, None


_COMMON_DATE_FORMATS_FOR_PARSING = [
    "%Y-%m-%d",
    "%m/%d/%Y",
    "%d/%m/%Y",
    "%m-%d-%Y",
    "%d-%m-%Y",
    "%Y/%m/%d",
    "%m/%d/%y",
    "%d/%m/%y",
    "%Y/%m/%y",
    "%d-%b-%Y",
    "%d-%B-%Y",
    "%b %d, %Y",
    "%B %d, %Y",
    "%Y%m%d",
    "%d.%m.%Y",
    "%Y-%m-%dT%H:%M:%S",
    "%Y-%m-%d %H:%M:%S",
    "%m/%d/%Y %H:%M:%S",
    "%d/%m/%Y %H:%M:%S",
    "%Y-%m-%dT%H:%M:%S.%f",  # ISO with microseconds
    "%d %b %Y",  # 01 Jan 2023
    "%d %B %Y",  # 01 January 2023
]


def _cli_parse_date_with_hint_and_common(
    date_val_str: str, date_format_hint: str | None
) -> datetime.date | None:
    if not date_val_str:
        return None
    parsed_date_obj: datetime.date | None = None

    if date_format_hint:
        hint_map = {
            "MM/DD/YYYY": "%m/%d/%Y",
            "M/D/YYYY": "%m/%d/%Y",
            "DD/MM/YYYY": "%d/%m/%Y",
            "D/M/YYYY": "%d/%m/%Y",
            "YYYY-MM-DD": "%Y-%m-%d",
            "YYYY/MM/DD": "%Y/%m/%d",
            "MM-DD-YYYY": "%m-%d-%Y",
            "DD-MM-YYYY": "%d-%m-%Y",
            "MM/DD/YY": "%m/%d/%y",
            "M/D/YY": "%m/%d/%y",
            "DD/MM/YY": "%d/%m/%y",
            "D/M/YY": "%d/%m/%y",
            "YY/MM/DD": "%y/%m/%d",
            "DD MON YYYY": "%d %b %Y",
            "DD-MON-YYYY": "%d-%b-%Y",
            "DD Mmm YYYY": "%d %b %Y",  # Example: 01 Jan 2023
        }
        # Normalize hint: upper, replace common separators if needed
        normalized_hint_key = date_format_hint.upper().replace(" ", "-")
        normalized_hint = hint_map.get(normalized_hint_key)

        if (
            not normalized_hint
        ):  # Try direct hint if not in map or if it's already a strptime format
            normalized_hint = (
                date_format_hint  # Assume it might be a valid strptime format itself
            )

        if normalized_hint:
            try:
                dt_obj = datetime.datetime.strptime(date_val_str, normalized_hint)
                parsed_date_obj = dt_obj.date()
                return parsed_date_obj
            except ValueError:
                logger.debug(
                    f"CLI: Date '{date_val_str}' did not match AI hint '{date_format_hint}' (normalized to '{normalized_hint}')."
                )

    for fmt in _COMMON_DATE_FORMATS_FOR_PARSING:
        try:
            dt_obj = datetime.datetime.strptime(date_val_str, fmt)
            parsed_date_obj = dt_obj.date()
            return parsed_date_obj
        except ValueError:
            continue

    try:  # Pandas as a last resort
        pd_dt = pd.to_datetime(date_val_str, errors="coerce")
        if pd.notna(pd_dt):
            return pd_dt.date()
    except Exception:
        pass
    logger.warning(
        f"CLI: Could not parse date '{date_val_str}' with hint or common formats."
    )
    return None


@app.command(name="analyze-dates")
async def analyze_file_dates_command(
    file_path: Annotated[
        str,
        typer.Argument(
            help="Path to the transaction file (CSV or Excel).",
            exists=True,
            file_okay=True,
            dir_okay=False,
            readable=True,
        ),
    ],
):
    """
    Analyzes a single transaction file to determine the earliest and latest transaction dates
    using AI-driven column interpretation for date fields.
    """
    typer.echo(f"Analyzing dates for file: {file_path}")

    if not settings.VERTEX_PROJECT_ID or not settings.VERTEX_LOCATION:
        typer.secho(
            "Error: VERTEX_PROJECT_ID and VERTEX_LOCATION must be set in settings or environment.",
            fg=typer.colors.RED,
        )
        raise typer.Exit(code=1)

    try:
        # Initialize Vertex AI (should be done once per application, but for CLI command, it's fine here)
        vertexai.init(
            project=settings.VERTEX_PROJECT_ID, location=settings.VERTEX_LOCATION
        )
        model_instance = GenerativeModel(settings.FILE_INGESTION_AI_MODEL_NAME)
    except NameError:  # If vertexai components were not imported
        typer.secho(
            "Error: Vertex AI SDK components could not be imported. Is google-cloud-aiplatform installed?",
            fg=typer.colors.RED,
        )
        raise typer.Exit(code=1)
    except Exception as e:
        typer.secho(f"Error initializing Vertex AI: {e}", fg=typer.colors.RED)
        raise typer.Exit(code=1)

    min_date_obj: datetime.date | None = None
    max_date_obj: datetime.date | None = None

    try:
        with open(file_path, "rb") as f:
            file_content = f.read()

        original_filename = os.path.basename(file_path)
        parsed_data, headers = _cli_parse_file_content(file_content, original_filename)

        if not parsed_data:
            typer.secho(
                f"No data parsed from {original_filename}.", fg=typer.colors.YELLOW
            )
            return

        field_mappings, _ = await _cli_get_ai_field_mappings(
            parsed_data, headers, model_instance
        )

        if not field_mappings or "date" not in field_mappings:
            typer.secho(
                f"AI did not return valid 'date' mappings for {original_filename}.",
                fg=typer.colors.RED,
            )
            return

        date_mapping = field_mappings["date"]
        date_source_columns = date_mapping.get("source_columns", [])
        date_format_hint = date_mapping.get("date_format_hint")

        if not date_source_columns:
            typer.secho(
                f"AI 'date' mapping has no 'source_columns' for {original_filename}.",
                fg=typer.colors.RED,
            )
            return

        typer.echo(
            f"Using AI date mapping for {original_filename}: Source Columns: {date_source_columns}, Format Hint: {date_format_hint or 'None'}"
        )

        found_any_date = False
        for _idx, row_data in enumerate(parsed_data):
            date_val_str: str | None = None
            for col_name in date_source_columns:
                val = row_data.get(col_name)
                if val and isinstance(val, str) and val.strip():
                    date_val_str = val.strip()
                    break

            if date_val_str:
                parsed_date = _cli_parse_date_with_hint_and_common(
                    date_val_str, date_format_hint
                )
                if parsed_date:
                    found_any_date = True
                    if min_date_obj is None or parsed_date < min_date_obj:
                        min_date_obj = parsed_date
                    if max_date_obj is None or parsed_date > max_date_obj:
                        max_date_obj = parsed_date

        if not found_any_date:
            typer.secho(
                f"No valid dates could be parsed from {original_filename} using AI mapping.",
                fg=typer.colors.YELLOW,
            )
        else:
            min_date_str = min_date_obj.strftime("%Y-%m-%d") if min_date_obj else "N/A"
            max_date_str = max_date_obj.strftime("%Y-%m-%d") if max_date_obj else "N/A"
            typer.secho(
                f"Report for {original_filename}: Min Date: {min_date_str}, Max Date: {max_date_str}",
                fg=typer.colors.GREEN,
            )

    except CLIFileProcessingError as e:
        typer.secho(
            f"Error processing file {original_filename}: {e}", fg=typer.colors.RED
        )
    except Exception as e:
        typer.secho(
            f"An unexpected error occurred with file {original_filename}: {e}",
            fg=typer.colors.RED,
        )
        logger.error(
            f"Unexpected CLI error for {original_filename}: {e}", exc_info=True
        )


if __name__ == "__main__":
    # This allows running `uv run python -m giki_ai_api.cli.onboarding_data_cli ...`
    # For example: `uv run python -m giki_ai_api.cli.onboarding_data_cli analyze-dates path/to/your/file.csv`
    app()
