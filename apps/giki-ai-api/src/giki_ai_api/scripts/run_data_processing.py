#!/usr/bin/env python
# apps/giki-ai-api/src/giki_ai_api/scripts/run_data_processing.py

import logging
import sys
from pathlib import Path

# Add the parent directory to sys.path to import from giki_ai_api
sys.path.append(str(Path(__file__).parent.parent.parent))

from giki_ai_api.scripts.process_input_files import main as process_input_files

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)

    logger.info("Starting data processing")

    # Process input files
    process_input_files()

    logger.info("Data processing completed")
