"""
Debug router for authentication issues.
"""

import logging
from typing import Annotated

import asyncpg
from fastapi import APIRouter, Depends

from ...core.database import get_db_session
from .secure_auth import get_password_hash, verify_password

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/debug/test-db")
async def test_database_connection(
    db: Annotated[asyncpg.Connection, Depends(get_db_session)],
):
    """Test basic database connectivity."""
    try:
        # Test basic query
        scalar_result = await db.fetchval("SELECT 1")

        # Test user table access
        user_count = await db.fetchval("SELECT COUNT(*) FROM users")

        return {
            "database_connected": True,
            "test_query_result": scalar_result,
            "user_count": user_count,
        }
    except Exception as e:
        logger.error(f"Database test error: {e}")
        return {
            "database_connected": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


@router.get("/debug/test-password")
async def test_password_hashing():
    """Test password hashing functionality."""
    try:
        test_password = "password123"

        # Generate new hash
        new_hash = get_password_hash(test_password)

        # Verify the hash
        verification_result = verify_password(test_password, new_hash)

        # Test with known bcrypt hash
        known_hash = "$2b$12$test.hash.that.should.fail.verification"
        known_hash_result = False
        try:
            known_hash_result = verify_password(test_password, known_hash)
        except Exception:
            pass

        return {
            "hashing_works": True,
            "new_hash_prefix": new_hash[:20] + "...",
            "verification_result": verification_result,
            "known_hash_test": known_hash_result,
        }
    except Exception as e:
        logger.error(f"Password hashing test error: {e}")
        return {
            "hashing_works": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


@router.get("/debug/test-user-query")
async def test_user_query(
    db: Annotated[asyncpg.Connection, Depends(get_db_session)],
):
    """Test querying the user table."""
    try:
        # Test asyncpg raw SQL query
        row = await db.fetchrow(
            "SELECT email, is_active, hashed_password FROM users WHERE email = $1",
            "<EMAIL>",
        )

        raw_sql_result = {
            "found": row is not None,
            "email": row["email"] if row else None,
            "is_active": row["is_active"] if row else None,
            "has_password": bool(row["hashed_password"]) if row else None,
        }

        # Test table structure query
        table_info = await db.fetch(
            """
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position
            """
        )

        table_structure = [
            {"column": row["column_name"], "type": row["data_type"]}
            for row in table_info
        ]

        return {
            "user_query": raw_sql_result,
            "table_structure": table_structure,
            "query_method": "asyncpg_pure_sql",
        }
    except Exception as e:
        logger.error(f"User query test error: {e}")
        import traceback

        return {
            "query_works": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc(),
        }
