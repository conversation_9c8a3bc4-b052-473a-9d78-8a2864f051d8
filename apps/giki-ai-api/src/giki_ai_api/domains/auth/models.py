"""
Authentication domain models.

Data models for users and authentication without SQLAlchemy.
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, EmailStr


class TenantDB(BaseModel):
    """Tenant model for database records."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    domain: Optional[str] = None
    settings: Optional[dict] = None
    subscription_status: str = "active"
    subscription_plan: str = "free"
    created_at: datetime
    updated_at: datetime


# Alias for backwards compatibility
Tenant = TenantDB


class UserDB(BaseModel):
    """User model for database records."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str
    email: EmailStr
    hashed_password: str
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    tenant_id: int
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    login_count: int = 0

    # Relationship data (loaded separately)
    tenant: Optional[TenantDB] = None

    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser

    @property
    def full_name(self) -> Optional[str]:
        """Get user's full name (username for now)."""
        return self.username


class User(BaseModel):
    """User model for API responses (no password)."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str
    email: EmailStr
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    tenant_id: int
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    login_count: int = 0
    tenant: Optional[TenantDB] = None

    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser

    @property
    def full_name(self) -> Optional[str]:
        """Get user's full name (username for now)."""
        return self.username


class UserCreate(BaseModel):
    """Schema for creating a new user."""

    username: str
    email: EmailStr
    password: str
    tenant_id: Optional[int] = None
    is_superuser: bool = False


class UserUpdate(BaseModel):
    """Schema for updating a user."""

    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    is_superuser: Optional[bool] = None


class Token(BaseModel):
    """OAuth2 token response."""

    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """Data encoded in JWT token."""

    username: Optional[str] = None
    tenant_id: Optional[int] = None
    user_id: Optional[int] = None
