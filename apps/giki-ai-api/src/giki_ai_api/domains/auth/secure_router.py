"""
Secure authentication router without dangerous optimizations.

Every request validates against the database for current user state.
"""

import logging
from typing import Annotated

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from ...core.database import get_db_session
from .models import User
from .schemas import Token


# Simple user response without extra fields
class SimpleUserResponse(BaseModel):
    id: int
    email: str
    tenant_id: int
    is_active: bool
    is_admin: bool
    full_name: str | None = None


from .secure_auth import (
    authenticate_user,
    create_access_token,
    get_current_active_user,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/token", response_model=Token)
@router.post("/login", response_model=Token)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    conn: Annotated[Connection, Depends(get_db_session)],
):
    """
    Secure login endpoint that always validates against the database.

    NO CACHING of authentication results for security.
    """
    # Authenticate user (queries database, verifies password)
    user = await authenticate_user(conn, form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = create_access_token(user)

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 3600,  # 1 hour
    }


@router.get("/me", response_model=SimpleUserResponse)
async def read_users_me(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Get current user information.

    Always returns fresh data from database.
    """
    return current_user  # Return the User model directly, it will be serialized to UserResponse


@router.post("/logout")
async def logout(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Logout endpoint.

    Since we don't cache sessions, logout is effectively a no-op.
    The client should discard the token.
    """
    logger.info(f"User {current_user.email} logged out")
    return {"message": "Successfully logged out"}


@router.get("/verify")
async def verify_token(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Verify if a token is valid.

    Always checks database for current user state.
    """
    return {
        "valid": True,
        "user_id": current_user.id,
        "email": current_user.email,
        "tenant_id": current_user.tenant_id,
    }


@router.get("/quick-actions")
async def get_quick_actions(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Get quick actions for the dashboard.
    Returns frequently used actions based on user role and preferences.
    """
    try:
        # Return common quick actions for financial platform
        quick_actions = [
            {
                "id": "upload_file",
                "title": "Upload File",
                "description": "Import transactions from Excel/CSV",
                "icon": "upload",
                "url": "/upload",
                "category": "data_import",
            },
            {
                "id": "review_transactions",
                "title": "Review Transactions",
                "description": "Check categorization and make corrections",
                "icon": "list-checks",
                "url": "/transactions",
                "category": "review",
            },
            {
                "id": "generate_report",
                "title": "Generate Report",
                "description": "Create financial reports and insights",
                "icon": "chart-bar",
                "url": "/reports",
                "category": "reporting",
            },
            {
                "id": "manage_categories",
                "title": "Manage Categories",
                "description": "Edit categorization rules and hierarchies",
                "icon": "tag",
                "url": "/categories",
                "category": "configuration",
            },
        ]

        # Add admin-specific actions if user is admin
        if hasattr(current_user, "is_admin") and current_user.is_admin:
            quick_actions.extend(
                [
                    {
                        "id": "user_management",
                        "title": "User Management",
                        "description": "Manage users and permissions",
                        "icon": "users",
                        "url": "/settings",
                        "category": "admin",
                    },
                    {
                        "id": "system_health",
                        "title": "System Health",
                        "description": "Monitor API and database performance",
                        "icon": "activity",
                        "url": "/health/performance",
                        "category": "admin",
                    },
                ]
            )

        return {
            "actions": quick_actions,
            "total": len(quick_actions),
            "user_id": current_user.id,
            "user_role": "admin"
            if (hasattr(current_user, "is_admin") and current_user.is_admin)
            else "user",
        }

    except Exception as e:
        logger.error(f"Error fetching quick actions for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching quick actions",
        )
