"""
Accuracy measurement service for categorization quality assessment.

Core business logic for measuring categorization accuracy across three scenarios:
1. Historical data (RAG-based categorization)
2. Category schema only (no historical data)
3. Zero-onboarding (no schema, no historical data)

Provides precision, recall, F1-score calculations and AI judge evaluation.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from asyncpg import Connection

from ...shared.exceptions import ServiceError
from ..categories.agent import AgentConfig, CategorizationAgent
from ..categories.service import CategoryService
from .ai_judge_agent import AIJudgeAgent
from .models import (
    AccuracyTest,
    AccuracyTestScenario,
    AccuracyTestStatus,
    AccuracyTestSummary,
    AIJudgment,
    AIJudgmentResult,
    CategorySchema,
)
from .repository import AccuracyRepository, AccuracyRepositoryError
from .transaction_loader import TransactionLoader

logger = logging.getLogger(__name__)


class AccuracyMeasurementError(ServiceError):
    """Accuracy measurement service error."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="AccuracyMeasurementService",
            operation="accuracy_measurement",
            **kwargs,
        )


class AccuracyMeasurementService:
    """Service for measuring categorization accuracy across different scenarios."""

    def __init__(self, connection: Connection):
        self.connection = connection
        self.repository = AccuracyRepository(connection)
        self.category_service = CategoryService(connection)

    # ==================== Test Management ====================

    async def create_accuracy_test(
        self,
        tenant_id: int,
        name: str,
        scenario: AccuracyTestScenario,
        test_data_source: str,
        description: Optional[str] = None,
        category_schema_id: Optional[int] = None,
        sample_size: int = 100,
    ) -> int:
        """Create a new accuracy test configuration."""
        try:
            # Validate scenario-specific requirements
            if scenario == AccuracyTestScenario.SCHEMA_ONLY and not category_schema_id:
                raise AccuracyMeasurementError(
                    "Category schema ID is required for schema-only scenario"
                )

            if category_schema_id:
                # Verify schema exists and belongs to tenant
                schema = await self.repository.get_category_schema(
                    category_schema_id, tenant_id
                )
                if not schema:
                    raise AccuracyMeasurementError(
                        f"Category schema {category_schema_id} not found for tenant {tenant_id}"
                    )

            test_data = {
                "tenant_id": tenant_id,
                "name": name,
                "description": description,
                "scenario": scenario.value,
                "test_data_source": test_data_source,
                "category_schema_id": category_schema_id,
                "sample_size": sample_size,
            }

            test_id = await self.repository.create_accuracy_test(test_data)
            logger.info(
                f"Created accuracy test {test_id} for scenario {scenario.value}"
            )

            return test_id

        except AccuracyRepositoryError:
            raise
        except Exception as e:
            logger.error(f"Failed to create accuracy test: {e}")
            raise AccuracyMeasurementError(f"Failed to create accuracy test: {e}")

    async def get_accuracy_test(
        self, test_id: int, tenant_id: int
    ) -> Optional[AccuracyTest]:
        """Get accuracy test by ID with tenant isolation."""
        try:
            return await self.repository.get_accuracy_test(test_id, tenant_id)
        except AccuracyRepositoryError:
            raise
        except Exception as e:
            logger.error(f"Failed to get accuracy test {test_id}: {e}")
            raise AccuracyMeasurementError(f"Failed to get accuracy test: {e}")

    async def list_accuracy_tests(
        self,
        tenant_id: int,
        limit: int = 50,
        offset: int = 0,
        scenario: Optional[AccuracyTestScenario] = None,
        status: Optional[AccuracyTestStatus] = None,
    ) -> List[AccuracyTestSummary]:
        """List accuracy tests with optional filtering."""
        try:
            return await self.repository.list_accuracy_tests(
                tenant_id, limit, offset, scenario, status
            )
        except AccuracyRepositoryError:
            raise
        except Exception as e:
            logger.error(f"Failed to list accuracy tests: {e}")
            raise AccuracyMeasurementError(f"Failed to list accuracy tests: {e}")

    # ==================== Test Execution ====================

    async def run_accuracy_test(self, test_id: int, tenant_id: int) -> Dict[str, Any]:
        """Execute an accuracy test and return comprehensive results."""
        try:
            # Get test configuration
            test = await self.repository.get_accuracy_test(test_id, tenant_id)
            if not test:
                raise AccuracyMeasurementError(f"Accuracy test {test_id} not found")

            if test.status != AccuracyTestStatus.PENDING:
                raise AccuracyMeasurementError(
                    f"Test {test_id} is not in pending status (current: {test.status})"
                )

            # Update status to running
            await self.repository.update_accuracy_test_status(
                test_id, tenant_id, AccuracyTestStatus.RUNNING
            )

            logger.info(
                f"Starting accuracy test {test_id} for scenario {test.scenario}"
            )

            try:
                # Execute scenario-specific test
                if test.scenario == AccuracyTestScenario.HISTORICAL_DATA:
                    results = await self._run_historical_data_test(test)
                elif test.scenario == AccuracyTestScenario.SCHEMA_ONLY:
                    results = await self._run_schema_only_test(test)
                elif test.scenario == AccuracyTestScenario.ZERO_ONBOARDING:
                    results = await self._run_zero_onboarding_test(test)
                else:
                    raise AccuracyMeasurementError(f"Unknown scenario: {test.scenario}")

                # Calculate comprehensive metrics
                metrics = await self._calculate_comprehensive_metrics(test_id, results)

                # Update test with final results
                completion_data = {
                    "total_transactions": results["total_transactions"],
                    "successful_categorizations": results["successful_categorizations"],
                    "ai_judge_correct": results["ai_judge_correct"],
                    "ai_judge_incorrect": results["ai_judge_incorrect"],
                    "ai_judge_partially_correct": results["ai_judge_partially_correct"],
                    "precision": metrics.get("overall_precision"),
                    "recall": metrics.get("overall_recall"),
                    "f1_score": metrics.get("overall_f1_score"),
                    "accuracy_percentage": metrics.get("overall_accuracy"),
                }

                await self.repository.update_accuracy_test_status(
                    test_id, tenant_id, AccuracyTestStatus.COMPLETED, completion_data
                )

                logger.info(
                    f"Completed accuracy test {test_id} with {results['successful_categorizations']}/{results['total_transactions']} successful"
                )

                return {
                    "test_id": test_id,
                    "status": "completed",
                    "results": results,
                    "metrics": metrics,
                    "summary": {
                        "total_transactions": results["total_transactions"],
                        "success_rate": results["successful_categorizations"]
                        / results["total_transactions"]
                        * 100,
                        "ai_judge_accuracy": results["ai_judge_correct"]
                        / (results["ai_judge_correct"] + results["ai_judge_incorrect"])
                        * 100
                        if (results["ai_judge_correct"] + results["ai_judge_incorrect"])
                        > 0
                        else 0,
                        "overall_precision": metrics.get("overall_precision"),
                        "overall_recall": metrics.get("overall_recall"),
                        "overall_f1_score": metrics.get("overall_f1_score"),
                    },
                }

            except Exception as e:
                # Update test status to failed
                error_data = {
                    "error_message": str(e),
                    "error_details": {"error_type": type(e).__name__},
                }
                await self.repository.update_accuracy_test_status(
                    test_id, tenant_id, AccuracyTestStatus.FAILED, error_data
                )
                raise

        except AccuracyRepositoryError:
            raise
        except AccuracyMeasurementError:
            raise
        except Exception as e:
            logger.error(f"Failed to run accuracy test {test_id}: {e}")
            raise AccuracyMeasurementError(f"Failed to run accuracy test: {e}")

    async def _run_historical_data_test(self, test: AccuracyTest) -> Dict[str, Any]:
        """Run accuracy test for historical data scenario (RAG-based)."""
        logger.info(f"Running historical data test for test {test.id}")

        # Configure categorization agent with RAG enabled
        config = AgentConfig(
            model_name="gemini-2.0-flash-001",
            rag_enabled=True,  # Use historical data for categorization
            project="rezolve-poc",
            location="us-central1",
        )

        agent = CategorizationAgent(
            config=config,
            name=f"accuracy_test_historical_{test.id}",
            description="AI categorization agent for historical data accuracy testing",
        )

        # Load test transactions (implement based on test_data_source format)
        transactions = await self._load_test_transactions(
            test.test_data_source, test.sample_size
        )

        results = {
            "total_transactions": len(transactions),
            "successful_categorizations": 0,
            "ai_judge_correct": 0,
            "ai_judge_incorrect": 0,
            "ai_judge_partially_correct": 0,
            "categorization_results": [],
        }

        for i, transaction in enumerate(transactions):
            try:
                # Perform AI categorization
                category_result = await agent.categorize_transaction_with_details(
                    {
                        "description": transaction["description"],
                        "amount": transaction["amount"],
                        "transaction_type": transaction["transaction_type"],
                        "tenant_id": test.tenant_id,
                        "date": transaction.get("date", datetime.utcnow().isoformat()),
                    }
                )

                if category_result.get("category"):
                    results["successful_categorizations"] += 1

                # Get detailed categorization info
                details = agent.get_last_categorization_details()

                # Perform AI judge evaluation
                judgment = await self._perform_ai_judgment(
                    test.id, transaction, category_result, details
                )

                # Update judgment counts
                if judgment.judgment_result == AIJudgmentResult.CORRECT:
                    results["ai_judge_correct"] += 1
                elif judgment.judgment_result == AIJudgmentResult.INCORRECT:
                    results["ai_judge_incorrect"] += 1
                elif judgment.judgment_result == AIJudgmentResult.PARTIALLY_CORRECT:
                    results["ai_judge_partially_correct"] += 1

                results["categorization_results"].append(
                    {
                        "transaction_id": transaction["id"],
                        "original_category": transaction.get("original_category"),
                        "ai_category": category_result.get("category"),
                        "ai_confidence": details.get("confidence", 0.0),
                        "judgment_result": judgment.judgment_result.value,
                        "judgment_confidence": judgment.judgment_confidence,
                    }
                )

                logger.info(
                    f"Processed transaction {i + 1}/{len(transactions)}: {judgment.judgment_result.value}"
                )

            except Exception as e:
                logger.warning(f"Failed to process transaction {i + 1}: {e}")
                continue

        return results

    async def _run_schema_only_test(self, test: AccuracyTest) -> Dict[str, Any]:
        """Run accuracy test for schema-only scenario."""
        logger.info(f"Running schema-only test for test {test.id}")

        # Get category schema
        schema = await self.repository.get_category_schema(
            test.category_schema_id, test.tenant_id
        )
        if not schema:
            raise AccuracyMeasurementError(
                f"Category schema {test.category_schema_id} not found"
            )

        # Configure categorization agent without RAG but with schema
        config = AgentConfig(
            model_name="gemini-2.0-flash-001",
            rag_enabled=False,  # No historical data
            project="rezolve-poc",
            location="us-central1",
        )

        agent = CategorizationAgent(
            config=config,
            name=f"accuracy_test_schema_{test.id}",
            description="AI categorization agent for schema-only accuracy testing",
        )

        # Load category schema into agent context (implement schema loading)
        await self._load_category_schema_for_agent(agent, schema)

        # Load test transactions
        transactions = await self._load_test_transactions(
            test.test_data_source, test.sample_size
        )

        # Process similar to historical data test but without RAG
        return await self._process_transactions_with_agent(test, agent, transactions)

    async def _run_zero_onboarding_test(self, test: AccuracyTest) -> Dict[str, Any]:
        """Run accuracy test for zero-onboarding scenario."""
        logger.info(f"Running zero-onboarding test for test {test.id}")

        # Configure categorization agent for zero-onboarding
        config = AgentConfig(
            model_name="gemini-2.0-flash-001",
            rag_enabled=False,  # No historical data or schema
            project="rezolve-poc",
            location="us-central1",
        )

        agent = CategorizationAgent(
            config=config,
            name=f"accuracy_test_zero_{test.id}",
            description="AI categorization agent for zero-onboarding accuracy testing",
        )

        # Load test transactions
        transactions = await self._load_test_transactions(
            test.test_data_source, test.sample_size
        )

        # Process with pure AI-driven categorization
        return await self._process_transactions_with_agent(test, agent, transactions)

    async def _process_transactions_with_agent(
        self,
        test: AccuracyTest,
        agent: CategorizationAgent,
        transactions: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Common transaction processing logic for different scenarios."""
        results = {
            "total_transactions": len(transactions),
            "successful_categorizations": 0,
            "ai_judge_correct": 0,
            "ai_judge_incorrect": 0,
            "ai_judge_partially_correct": 0,
            "categorization_results": [],
        }

        for i, transaction in enumerate(transactions):
            try:
                # Perform AI categorization
                category_result = await agent.categorize_transaction_with_details(
                    {
                        "description": transaction["description"],
                        "amount": transaction["amount"],
                        "transaction_type": transaction["transaction_type"],
                        "tenant_id": test.tenant_id,
                        "date": transaction.get("date", datetime.utcnow().isoformat()),
                    }
                )

                if category_result.get("category"):
                    results["successful_categorizations"] += 1

                # Get detailed categorization info
                details = agent.get_last_categorization_details()

                # Perform AI judge evaluation
                judgment = await self._perform_ai_judgment(
                    test.id, transaction, category_result, details
                )

                # Update judgment counts
                if judgment.judgment_result == AIJudgmentResult.CORRECT:
                    results["ai_judge_correct"] += 1
                elif judgment.judgment_result == AIJudgmentResult.INCORRECT:
                    results["ai_judge_incorrect"] += 1
                elif judgment.judgment_result == AIJudgmentResult.PARTIALLY_CORRECT:
                    results["ai_judge_partially_correct"] += 1

                results["categorization_results"].append(
                    {
                        "transaction_id": transaction["id"],
                        "original_category": transaction.get("original_category"),
                        "ai_category": category_result.get("category"),
                        "ai_confidence": details.get("confidence", 0.0),
                        "judgment_result": judgment.judgment_result.value,
                        "judgment_confidence": judgment.judgment_confidence,
                    }
                )

                logger.info(
                    f"Processed transaction {i + 1}/{len(transactions)}: {judgment.judgment_result.value}"
                )

            except Exception as e:
                logger.warning(f"Failed to process transaction {i + 1}: {e}")
                continue

        return results

    # ==================== Helper Methods ====================

    async def _load_test_transactions(
        self, test_data_source: str, sample_size: int
    ) -> List[Dict[str, Any]]:
        """Load test transactions from data source."""
        try:
            # Load transactions using TransactionLoader
            transactions = await TransactionLoader.load_from_file(
                test_data_source,
                sample_size=sample_size,
                required_original_category=True,  # We need original categories for accuracy measurement
            )

            # Validate transactions
            validation = await TransactionLoader.validate_transactions(transactions)
            logger.info(
                f"Loaded {validation['valid']} valid transactions "
                f"({validation['has_categories']} with categories) from {test_data_source}"
            )

            if validation["valid"] == 0:
                raise AccuracyMeasurementError(
                    f"No valid transactions found in {test_data_source}"
                )

            return transactions

        except Exception as e:
            logger.error(f"Failed to load transactions from {test_data_source}: {e}")
            raise AccuracyMeasurementError(f"Failed to load test transactions: {e}")

    async def _load_category_schema_for_agent(
        self, agent: CategorizationAgent, schema: CategorySchema
    ):
        """Load category schema into agent context for schema-only testing."""
        # This would be implemented to inject schema into agent's categorization context
        logger.warning("Category schema loading for agent not implemented")

    async def _perform_ai_judgment(
        self,
        test_id: int,
        transaction: Dict[str, Any],
        category_result: Dict[str, Any],
        details: Dict[str, Any],
    ) -> AIJudgment:
        """Perform AI judge evaluation of categorization correctness."""
        try:
            # Initialize AI Judge
            ai_judge = AIJudgeAgent()

            # Get AI judgment
            judgment_result = await ai_judge.judge_categorization(
                transaction_description=transaction["description"],
                transaction_amount=transaction["amount"],
                original_category=transaction.get("original_category"),
                ai_category=category_result.get("category", "Unknown"),
                ai_hierarchy=details.get("full_hierarchy"),
                ai_confidence=details.get("confidence", 0.0),
                context={
                    "transaction_type": transaction["transaction_type"],
                    "transaction_date": transaction.get("date"),
                    "test_scenario": "accuracy_measurement",
                },
            )

            # Create AI judgment record
            judgment_data = {
                "test_id": test_id,
                "transaction_id": str(transaction["id"]),
                "original_category": transaction.get("original_category"),
                "ai_category": category_result.get("category", "Unknown"),
                "ai_full_hierarchy": details.get("full_hierarchy", ""),
                "ai_confidence": details.get("confidence", 0.0),
                "judgment_result": judgment_result["judgment_result"],
                "judgment_confidence": judgment_result["judgment_confidence"],
                "judgment_reasoning": judgment_result["judgment_reasoning"],
                "transaction_description": transaction["description"],
                "transaction_amount": transaction["amount"],
                "transaction_type": transaction["transaction_type"],
                "semantic_similarity": judgment_result.get("semantic_similarity", 0.0),
                "hierarchy_alignment": judgment_result.get(
                    "hierarchy_alignment", False
                ),
                "key_factors": json.dumps(judgment_result.get("key_factors", [])),
                "improvement_suggestions": judgment_result.get(
                    "improvement_suggestions", ""
                ),
            }

            # Store in database
            judgment_id = await self.repository.create_ai_judgment(judgment_data)
            judgment_data["id"] = judgment_id
            judgment_data["judged_at"] = datetime.utcnow()
            judgment_data["judge_model"] = "gemini-2.0-flash-001"
            judgment_data["judge_version"] = "2.0"  # Updated version with real AI judge

            return AIJudgment(**judgment_data)

        except Exception as e:
            logger.error(f"AI judgment failed for transaction {transaction['id']}: {e}")
            # Return a conservative judgment on error
            judgment_data = {
                "test_id": test_id,
                "transaction_id": str(transaction["id"]),
                "original_category": transaction.get("original_category"),
                "ai_category": category_result.get("category", "Unknown"),
                "ai_full_hierarchy": details.get("full_hierarchy", ""),
                "ai_confidence": details.get("confidence", 0.0),
                "judgment_result": AIJudgmentResult.INCORRECT.value,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"AI judgment failed: {str(e)}",
                "transaction_description": transaction["description"],
                "transaction_amount": transaction["amount"],
                "transaction_type": transaction["transaction_type"],
            }

            judgment_id = await self.repository.create_ai_judgment(judgment_data)
            judgment_data["id"] = judgment_id
            judgment_data["judged_at"] = datetime.utcnow()
            judgment_data["judge_model"] = "gemini-2.0-flash-001"
            judgment_data["judge_version"] = "2.0"

            return AIJudgment(**judgment_data)

    async def _calculate_comprehensive_metrics(
        self, test_id: int, results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate comprehensive accuracy metrics for the test."""
        total = results["total_transactions"]
        correct = results["ai_judge_correct"]
        incorrect = results["ai_judge_incorrect"]
        partially_correct = results["ai_judge_partially_correct"]

        if total == 0:
            return {
                "overall_precision": 0.0,
                "overall_recall": 0.0,
                "overall_f1_score": 0.0,
                "overall_accuracy": 0.0,
            }

        # Calculate basic metrics
        accuracy = (correct + partially_correct * 0.5) / total * 100
        precision = (
            correct / (correct + incorrect) if (correct + incorrect) > 0 else 0.0
        )
        recall = correct / total if total > 0 else 0.0
        f1_score = (
            2 * (precision * recall) / (precision + recall)
            if (precision + recall) > 0
            else 0.0
        )

        # Store detailed metrics in database
        metrics_to_store = [
            {
                "test_id": test_id,
                "metric_name": "overall",
                "metric_type": "accuracy",
                "value": accuracy / 100,
                "sample_count": total,
                "true_positives": correct,
                "false_positives": incorrect,
                "calculation_method": "ai_judge_evaluation",
            },
            {
                "test_id": test_id,
                "metric_name": "overall",
                "metric_type": "precision",
                "value": precision,
                "sample_count": total,
                "true_positives": correct,
                "false_positives": incorrect,
                "calculation_method": "precision = tp / (tp + fp)",
            },
            {
                "test_id": test_id,
                "metric_name": "overall",
                "metric_type": "recall",
                "value": recall,
                "sample_count": total,
                "true_positives": correct,
                "false_negatives": incorrect,
                "calculation_method": "recall = tp / (tp + fn)",
            },
            {
                "test_id": test_id,
                "metric_name": "overall",
                "metric_type": "f1_score",
                "value": f1_score,
                "sample_count": total,
                "calculation_method": "f1 = 2 * (precision * recall) / (precision + recall)",
            },
        ]

        for metric_data in metrics_to_store:
            await self.repository.create_accuracy_metric(metric_data)

        return {
            "overall_accuracy": accuracy,
            "overall_precision": precision,
            "overall_recall": recall,
            "overall_f1_score": f1_score,
        }
