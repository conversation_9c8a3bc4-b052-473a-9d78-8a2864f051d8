"""
AI Judge Agent for evaluating categorization accuracy.

This agent uses Vertex AI to judge whether AI-generated categories are correct
compared to original/expected categories, providing nuanced evaluation beyond
simple string matching.
"""

import asyncio
import logging
from enum import Enum
from typing import Any, Dict, List, Optional

import vertexai
from vertexai.generative_models import GenerativeModel

from ...shared.ai.standard_giki_agent import StandardGikiAgent
from .models import AIJudgmentResult

logger = logging.getLogger(__name__)


class JudgmentConfidence(Enum):
    """Confidence levels for AI judgments."""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class AIJudgeAgent(StandardGikiAgent):
    """AI agent for judging categorization correctness using semantic understanding."""

    def __init__(self, name: str = "ai_judge", model: str = "gemini-2.0-flash-001"):
        """Initialize AI Judge agent."""
        super().__init__(
            name=name,
            description="AI judge for evaluating categorization correctness",
            model_name=model,
            project_id="rezolve-poc",
            location="us-central1",
            enable_standard_tools=False,  # Don't need standard tools for judging
        )

        # Initialize Vertex AI
        self._model_name = model
        vertexai.init(project="rezolve-poc", location="us-central1")
        logger.info("✅ Vertex AI initialized for AI Judge")

    async def judge_categorization(
        self,
        transaction_description: str,
        transaction_amount: float,
        original_category: Optional[str],
        ai_category: str,
        ai_hierarchy: Optional[str] = None,
        ai_confidence: float = 0.0,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Judge whether an AI categorization is correct compared to the original.

        Returns:
            Dict containing:
            - judgment_result: correct/incorrect/partially_correct
            - judgment_confidence: 0.0-1.0
            - judgment_reasoning: explanation of the judgment
            - semantic_similarity: how similar the categories are semantically
            - hierarchy_alignment: whether hierarchies align properly
        """

        # Build comprehensive prompt for judgment
        prompt = self._build_judgment_prompt(
            transaction_description,
            transaction_amount,
            original_category,
            ai_category,
            ai_hierarchy,
            ai_confidence,
            context,
        )

        try:
            # Use Vertex AI directly for judgment
            model = GenerativeModel(
                model_name=self._model_name,
                system_instruction="You are an expert financial categorization judge.",
            )

            response = await model.generate_content_async(
                prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1024,
                    "response_mime_type": "text/plain",
                },
            )

            response_text = (
                response.text if hasattr(response, "text") else str(response)
            )

            # Parse judgment response
            judgment = self._parse_judgment_response(response_text)

            logger.info(
                f"AI Judge result: {judgment['judgment_result']} "
                f"(confidence: {judgment['judgment_confidence']:.2f}) "
                f"for '{transaction_description[:50]}...'"
            )

            return judgment

        except Exception as e:
            logger.error(f"AI Judge failed: {e}")
            # Return conservative judgment on error
            return {
                "judgment_result": AIJudgmentResult.INCORRECT.value,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Judgment failed due to error: {str(e)}",
                "semantic_similarity": 0.0,
                "hierarchy_alignment": False,
                "error": str(e),
            }

    def _build_judgment_prompt(
        self,
        transaction_description: str,
        transaction_amount: float,
        original_category: Optional[str],
        ai_category: str,
        ai_hierarchy: Optional[str],
        ai_confidence: float,
        context: Optional[Dict[str, Any]],
    ) -> str:
        """Build comprehensive prompt for AI judgment."""

        prompt = f"""You are an expert financial categorization judge. Your task is to evaluate whether an AI-generated category is correct compared to the original/expected category.

Transaction Details:
- Description: {transaction_description}
- Amount: ${transaction_amount:.2f}
- Original Category: {original_category or "Not provided"}
- AI Category: {ai_category}
- AI Hierarchy: {ai_hierarchy or "Not provided"}
- AI Confidence: {ai_confidence:.2%}

Evaluation Criteria:
1. Semantic Correctness: Does the AI category capture the same meaning as the original?
2. Business Context: Is the categorization appropriate for business expense tracking?
3. Hierarchy Alignment: If hierarchical, does it follow logical parent-child relationships?
4. Specificity: Is the AI category appropriately specific (not too generic)?

Consider that categories may be expressed differently but still be correct:
- "Software" and "Technology > Software & Licenses" are semantically equivalent
- "Marketing" and "Marketing & Advertising" are close enough to be correct
- "Salary" and "Employee Compensation > Salaries and Wages" are the same concept

Provide your judgment in the following JSON format:
{{
    "judgment_result": "correct" | "incorrect" | "partially_correct",
    "judgment_confidence": 0.0-1.0,
    "judgment_reasoning": "Detailed explanation of your judgment",
    "semantic_similarity": 0.0-1.0,
    "hierarchy_alignment": true | false,
    "key_factors": ["list", "of", "key", "factors", "in", "decision"],
    "improvement_suggestions": "How the AI categorization could be improved (if applicable)"
}}

Important:
- Be fair and consider semantic equivalence, not just exact string matching
- "partially_correct" means the category is in the right general area but not quite specific enough
- Consider common variations and abbreviations
- Focus on business utility - would a CFO consider these categories equivalent?"""

        return prompt

    def _parse_judgment_response(self, response: str) -> Dict[str, Any]:
        """Parse AI judge response into structured format."""
        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                judgment_data = json.loads(json_match.group())

                # Validate and normalize judgment result
                judgment_result = judgment_data.get(
                    "judgment_result", "incorrect"
                ).lower()
                if judgment_result not in ["correct", "incorrect", "partially_correct"]:
                    judgment_result = "incorrect"

                # Map to enum value
                result_map = {
                    "correct": AIJudgmentResult.CORRECT.value,
                    "incorrect": AIJudgmentResult.INCORRECT.value,
                    "partially_correct": AIJudgmentResult.PARTIALLY_CORRECT.value,
                }

                return {
                    "judgment_result": result_map[judgment_result],
                    "judgment_confidence": float(
                        judgment_data.get("judgment_confidence", 0.5)
                    ),
                    "judgment_reasoning": judgment_data.get(
                        "judgment_reasoning", "No reasoning provided"
                    ),
                    "semantic_similarity": float(
                        judgment_data.get("semantic_similarity", 0.0)
                    ),
                    "hierarchy_alignment": bool(
                        judgment_data.get("hierarchy_alignment", False)
                    ),
                    "key_factors": judgment_data.get("key_factors", []),
                    "improvement_suggestions": judgment_data.get(
                        "improvement_suggestions", ""
                    ),
                }
            else:
                raise ValueError("No JSON found in response")

        except Exception as e:
            logger.error(f"Failed to parse judgment response: {e}")
            # Return conservative judgment
            return {
                "judgment_result": AIJudgmentResult.INCORRECT.value,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Failed to parse judgment: {str(e)}",
                "semantic_similarity": 0.0,
                "hierarchy_alignment": False,
                "parse_error": str(e),
            }

    async def batch_judge_categorizations(
        self, categorizations: List[Dict[str, Any]], batch_size: int = 10
    ) -> List[Dict[str, Any]]:
        """Judge multiple categorizations in batches for efficiency."""
        results = []

        for i in range(0, len(categorizations), batch_size):
            batch = categorizations[i : i + batch_size]
            batch_results = await asyncio.gather(
                *[
                    self.judge_categorization(
                        transaction_description=cat["transaction_description"],
                        transaction_amount=cat["transaction_amount"],
                        original_category=cat.get("original_category"),
                        ai_category=cat["ai_category"],
                        ai_hierarchy=cat.get("ai_hierarchy"),
                        ai_confidence=cat.get("ai_confidence", 0.0),
                    )
                    for cat in batch
                ]
            )
            results.extend(batch_results)

        return results
