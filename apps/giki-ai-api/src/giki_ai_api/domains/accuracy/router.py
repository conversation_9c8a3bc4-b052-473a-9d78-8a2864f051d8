"""
Accuracy measurement API router.

FastAPI router for accuracy testing endpoints including test management,
execution, metrics, and reporting.
"""

import logging
from datetime import datetime

from asyncpg import Connection
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, status

from ...core.dependencies import get_db_session
from ...domains.auth.dependencies import get_current_tenant_id
from .models import AccuracyTestScenario, AccuracyTestStatus
from .schemas import (
    AccuracyMetricListResponse,
    AccuracyMetricResponse,
    AccuracyReportResponse,
    # Request schemas
    AccuracyTestCreate,
    AccuracyTestExecutionResponse,
    AccuracyTestListResponse,
    # Response schemas
    AccuracyTestResponse,
    AIJudgmentListResponse,
    AIJudgmentResponse,
    CategorySchemaCreate,
    CategorySchemaImport,
    CategorySchemaImportResponse,
    CategorySchemaListResponse,
    CategorySchemaResponse,
)
from .service import AccuracyMeasurementError, AccuracyMeasurementService

logger = logging.getLogger(__name__)

router = APIRouter(tags=["accuracy"])


# ==================== Accuracy Tests ====================


@router.post(
    "/tests", response_model=AccuracyTestResponse, status_code=status.HTTP_201_CREATED
)
async def create_accuracy_test(
    test_data: AccuracyTestCreate,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Create a new accuracy test configuration."""
    try:
        service = AccuracyMeasurementService(connection)

        test_id = await service.create_accuracy_test(
            tenant_id=tenant_id,
            name=test_data.name,
            scenario=test_data.scenario,
            test_data_source=test_data.test_data_source,
            description=test_data.description,
            category_schema_id=test_data.category_schema_id,
            sample_size=test_data.sample_size,
        )

        # Get the created test
        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve created test",
            )

        return AccuracyTestResponse(**test.dict())

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to create accuracy test: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating accuracy test: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/tests", response_model=AccuracyTestListResponse)
async def list_accuracy_tests(
    scenario: AccuracyTestScenario = None,
    status: AccuracyTestStatus = None,
    limit: int = 50,
    offset: int = 0,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """List accuracy tests with optional filtering."""
    try:
        service = AccuracyMeasurementService(connection)

        tests = await service.list_accuracy_tests(
            tenant_id=tenant_id,
            limit=limit,
            offset=offset,
            scenario=scenario,
            status=status,
        )

        # Calculate total count (simplified - would need separate query in real implementation)
        total_count = len(tests)
        has_more = len(tests) == limit

        return AccuracyTestListResponse(
            tests=tests,
            total_count=total_count,
            page_size=limit,
            page_offset=offset,
            has_more=has_more,
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to list accuracy tests: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing accuracy tests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/tests/{test_id}", response_model=AccuracyTestResponse)
async def get_accuracy_test(
    test_id: int,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Get accuracy test by ID."""
    try:
        service = AccuracyMeasurementService(connection)

        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        return AccuracyTestResponse(**test.dict())

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to get accuracy test {test_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting accuracy test {test_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/tests/{test_id}/run", response_model=AccuracyTestExecutionResponse)
async def run_accuracy_test(
    test_id: int,
    background_tasks: BackgroundTasks,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Execute an accuracy test."""
    try:
        service = AccuracyMeasurementService(connection)

        # Verify test exists and is in correct status
        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        if test.status != AccuracyTestStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Test {test_id} is not in pending status (current: {test.status})",
            )

        # For long-running tests, we would typically use background tasks
        # For now, run synchronously for simplicity
        results = await service.run_accuracy_test(test_id, tenant_id)

        return AccuracyTestExecutionResponse(
            test_id=test_id,
            status="completed",
            message="Accuracy test completed successfully",
            results=results.get("results"),
            metrics=results.get("metrics"),
            summary=results.get("summary"),
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to run accuracy test {test_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error running accuracy test {test_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ==================== Accuracy Metrics ====================


@router.get("/tests/{test_id}/metrics", response_model=AccuracyMetricListResponse)
async def get_accuracy_metrics(
    test_id: int,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Get accuracy metrics for a specific test."""
    try:
        service = AccuracyMeasurementService(connection)

        # Verify test exists
        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        metrics = await service.repository.get_accuracy_metrics_for_test(test_id)

        return AccuracyMetricListResponse(
            metrics=[AccuracyMetricResponse(**metric.dict()) for metric in metrics],
            test_id=test_id,
            total_metrics=len(metrics),
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to get accuracy metrics for test {test_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(
            f"Unexpected error getting accuracy metrics for test {test_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ==================== AI Judgments ====================


@router.get("/tests/{test_id}/judgments", response_model=AIJudgmentListResponse)
async def get_ai_judgments(
    test_id: int,
    limit: int = 1000,
    judgment_result: str = None,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Get AI judgments for a specific test."""
    try:
        service = AccuracyMeasurementService(connection)

        # Verify test exists
        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        # Parse judgment result filter
        judgment_filter = None
        if judgment_result:
            try:
                from .models import AIJudgmentResult

                judgment_filter = AIJudgmentResult(judgment_result)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid judgment result: {judgment_result}",
                )

        judgments = await service.repository.get_ai_judgments_for_test(
            test_id, limit, judgment_filter
        )

        # Calculate summary
        total = len(judgments)
        correct = sum(1 for j in judgments if j.judgment_result.value == "correct")
        incorrect = sum(1 for j in judgments if j.judgment_result.value == "incorrect")
        partially_correct = sum(
            1 for j in judgments if j.judgment_result.value == "partially_correct"
        )

        summary = {
            "total": total,
            "correct": correct,
            "incorrect": incorrect,
            "partially_correct": partially_correct,
            "accuracy_rate": (correct + partially_correct * 0.5) / total * 100
            if total > 0
            else 0.0,
            "average_confidence": sum(j.judgment_confidence for j in judgments) / total
            if total > 0
            else 0.0,
        }

        return AIJudgmentListResponse(
            judgments=[AIJudgmentResponse(**judgment.dict()) for judgment in judgments],
            test_id=test_id,
            total_judgments=total,
            summary=summary,
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to get AI judgments for test {test_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting AI judgments for test {test_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ==================== Category Schemas ====================


@router.post(
    "/schemas",
    response_model=CategorySchemaResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_category_schema(
    schema_data: CategorySchemaCreate,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Create a new category schema."""
    try:
        service = AccuracyMeasurementService(connection)

        # Calculate schema metadata
        category_count = len(schema_data.schema_data.get("categories", []))
        max_depth = _calculate_hierarchy_depth(schema_data.schema_data)
        has_gl_codes = bool(schema_data.schema_data.get("gl_codes"))

        schema_create_data = {
            "tenant_id": tenant_id,
            "name": schema_data.name,
            "description": schema_data.description,
            "schema_format": schema_data.schema_format,
            "schema_data": schema_data.schema_data,
            "category_count": category_count,
            "max_hierarchy_depth": max_depth,
            "has_gl_codes": has_gl_codes,
            "gl_code_mapping": schema_data.schema_data.get("gl_codes"),
            "imported_from": schema_data.imported_from,
            "imported_by": schema_data.imported_by,
        }

        schema_id = await service.repository.create_category_schema(schema_create_data)

        # Get the created schema
        schema = await service.repository.get_category_schema(schema_id, tenant_id)
        if not schema:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve created schema",
            )

        return CategorySchemaResponse(**schema.dict())

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to create category schema: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating category schema: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post(
    "/schemas/import",
    response_model=CategorySchemaImportResponse,
    status_code=status.HTTP_201_CREATED,
)
async def import_category_schema(
    import_data: CategorySchemaImport,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Import category schema from file."""
    try:
        # This would implement file parsing and schema creation
        # For now, return a placeholder response

        return CategorySchemaImportResponse(
            schema_id=0,  # Placeholder
            message="Schema import not yet implemented",
            categories_imported=0,
            hierarchy_levels_detected=0,
            gl_codes_generated=0,
            validation_warnings=["Import functionality pending implementation"],
        )

    except Exception as e:
        logger.error(f"Unexpected error importing category schema: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/schemas", response_model=CategorySchemaListResponse)
async def list_category_schemas(
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """List category schemas for the tenant."""
    try:
        service = AccuracyMeasurementService(connection)

        schemas = await service.repository.list_category_schemas(tenant_id)

        return CategorySchemaListResponse(
            schemas=[CategorySchemaResponse(**schema.dict()) for schema in schemas],
            total_count=len(schemas),
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to list category schemas: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing category schemas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/schemas/{schema_id}", response_model=CategorySchemaResponse)
async def get_category_schema(
    schema_id: int,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Get category schema by ID."""
    try:
        service = AccuracyMeasurementService(connection)

        schema = await service.repository.get_category_schema(schema_id, tenant_id)
        if not schema:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category schema {schema_id} not found",
            )

        return CategorySchemaResponse(**schema.dict())

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to get category schema {schema_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting category schema {schema_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ==================== Reports ====================


@router.get("/tests/{test_id}/report", response_model=AccuracyReportResponse)
async def get_accuracy_report(
    test_id: int,
    tenant_id: int = Depends(get_current_tenant_id),
    connection: Connection = Depends(get_db_session),
):
    """Get comprehensive accuracy report for a test."""
    try:
        service = AccuracyMeasurementService(connection)

        # Verify test exists
        test = await service.get_accuracy_test(test_id, tenant_id)
        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        # Get comprehensive data
        metrics = await service.repository.get_accuracy_metrics_for_test(test_id)
        judgments = await service.repository.get_ai_judgments_for_test(test_id)

        # Build comprehensive report
        overall_metrics = {}
        for metric in metrics:
            if metric.metric_name == "overall":
                overall_metrics[metric.metric_type] = metric.value

        # Judgment distribution
        judgment_counts = {}
        for judgment in judgments:
            result = judgment.judgment_result.value
            judgment_counts[result] = judgment_counts.get(result, 0) + 1

        # AI judge summary
        total_judgments = len(judgments)
        ai_judge_summary = {
            "total_evaluations": total_judgments,
            "average_confidence": sum(j.judgment_confidence for j in judgments)
            / total_judgments
            if total_judgments > 0
            else 0.0,
            "exact_matches": sum(1 for j in judgments if j.category_exact_match),
            "semantic_matches": sum(1 for j in judgments if j.category_semantic_match),
        }

        return AccuracyReportResponse(
            test_id=test_id,
            test_name=test.name,
            scenario=test.scenario,
            overall_metrics=overall_metrics,
            category_breakdown=[],  # Would implement detailed breakdowns
            confidence_breakdown=[],
            hierarchy_breakdown=[],
            ai_judge_summary=ai_judge_summary,
            judgment_distribution=judgment_counts,
            quality_metrics={},  # Would implement quality analysis
            improvement_suggestions=[],
            excel_report_url=None,  # Would implement Excel export
            generated_at=datetime.utcnow(),
        )

    except AccuracyMeasurementError as e:
        logger.error(f"Failed to get accuracy report for test {test_id}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(
            f"Unexpected error getting accuracy report for test {test_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ==================== M1 Zero-Onboarding Quick Test ====================


# M1 quick test endpoint removed - was returning fake validation
# Real M1 testing will be done through proper file upload and categorization flow


# ==================== Helper Functions ====================


def _calculate_hierarchy_depth(schema_data: dict) -> int:
    """Calculate maximum hierarchy depth from schema data."""
    try:
        categories = schema_data.get("categories", [])
        if not categories:
            return 1

        max_depth = 1
        for category in categories:
            if isinstance(category, dict):
                # Count hierarchy separators (e.g., "Level1 > Level2 > Level3")
                category_name = category.get("name", "")
                if " > " in category_name:
                    depth = len(category_name.split(" > "))
                    max_depth = max(max_depth, depth)

        return max_depth

    except Exception:
        return 1
