# apps/giki-ai-api/src/giki_ai_api/routers/transactions.py
"""
Transaction Router - Customer-Facing Transaction Management

Provides real transaction endpoints for customers to view, filter, and manage
their financial data using direct SQL queries for optimal performance.
"""

import logging
import time
from typing import List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, ConfigDict

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Transactions"],
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Not found"},
        status.HTTP_401_UNAUTHORIZED: {"description": "Not authenticated"},
        status.HTTP_403_FORBIDDEN: {"description": "Not authorized"},
    },
)


class TransactionResponse(BaseModel):
    """Transaction response schema."""

    model_config = ConfigDict(from_attributes=True)

    id: str
    date: str
    description: str
    amount: float
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    category_id: Optional[int] = None
    category_path: Optional[str] = None
    ai_suggested_category_id: Optional[int] = None
    ai_suggested_category_path: Optional[str] = None
    ai_category_confidence: Optional[float] = None
    is_categorized: bool = False
    is_user_modified: bool = False
    user_corrected: bool = False
    entity_id: Optional[int] = None
    upload_id: Optional[str] = None
    status: str = "uncategorized"
    vendor_name: Optional[str] = None
    original_category: Optional[str] = None
    ai_category: Optional[str] = None


class PaginatedTransactionResponse(BaseModel):
    """Paginated transaction response."""

    items: List[TransactionResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    next_cursor: Optional[str] = None
    has_more: bool = False


def derive_transaction_status(row: dict) -> str:
    """Derive transaction status from row data."""
    if row.get("is_user_modified"):
        return "user_modified"
    elif row.get("ai_category") or row.get("ai_suggested_category_id"):
        return "ai_suggested"
    elif row.get("category_id") or row.get("original_category"):
        return "categorized"
    return "uncategorized"


def row_to_transaction_response(row: dict) -> TransactionResponse:
    """Convert database row to TransactionResponse."""
    # Determine transaction status
    status = derive_transaction_status(row)

    # Determine categorization flags
    is_categorized = bool(row.get("category_id") or row.get("original_category"))

    return TransactionResponse(
        id=row["id"],
        date=row["date"].isoformat()
        if hasattr(row["date"], "isoformat")
        else row["date"],
        description=row["description"],
        amount=float(row["amount"]),
        account=row.get("account"),
        transaction_type=row.get("transaction_type"),
        category_id=row.get("category_id"),
        category_path=row.get("category_path"),
        ai_suggested_category_id=row.get("ai_suggested_category_id"),
        ai_suggested_category_path=row.get("ai_category"),
        ai_category_confidence=float(row["ai_confidence"])
        if row.get("ai_confidence")
        else None,
        is_categorized=is_categorized,
        is_user_modified=row.get("is_user_modified", False),
        user_corrected=row.get("user_corrected", False),
        entity_id=row.get("entity_id"),
        upload_id=row.get("upload_id"),
        status=status,
        vendor_name=row.get("vendor_name"),
        original_category=row.get("original_category"),
        ai_category=row.get("ai_category"),
    )


@router.get("/fast", response_model=PaginatedTransactionResponse)
async def list_transactions_fast(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    cursor: Optional[str] = Query(None, description="Cursor for pagination"),
    limit: int = Query(100, ge=1, le=1000, description="Number of items to return"),
) -> PaginatedTransactionResponse:
    """
    Fast transaction listing using cursor-based pagination.
    Optimized for dashboard performance with <100ms response time.
    """
    query_start = time.time()

    try:
        # Build base query
        base_query = """
            SELECT 
                id,
                date,
                description,
                amount,
                account,
                transaction_type,
                category_id,
                original_category,
                ai_category,
                ai_confidence,
                vendor_name,
                entity_id,
                upload_id,
                CASE 
                    WHEN category_id IS NOT NULL THEN true 
                    ELSE false 
                END as is_user_modified,
                false as user_corrected
            FROM transactions
            WHERE tenant_id = $1
        """

        # Add cursor condition if provided
        query_params = [tenant_id]
        if cursor:
            try:
                cursor_date, cursor_id = cursor.split(":")
                base_query += " AND (date < $2 OR (date = $2 AND id < $3))"
                query_params.extend([cursor_date, cursor_id])
            except ValueError:
                logger.warning(f"Invalid cursor format: {cursor}")

        # Add ordering and limit
        base_query += " ORDER BY date DESC, id DESC LIMIT $" + str(
            len(query_params) + 1
        )
        query_params.append(limit + 1)  # Fetch one extra to determine has_more

        # Execute query
        rows = await conn.fetch(base_query, *query_params)

        # Process results
        has_more = len(rows) > limit
        if has_more:
            rows = rows[:limit]

        # Determine next cursor
        next_cursor = None
        if has_more and rows:
            last_row = rows[-1]
            next_cursor = f"{last_row['date'].isoformat()}:{last_row['id']}"

        # Convert rows to response objects
        items = [row_to_transaction_response(dict(row)) for row in rows]

        # Log performance metrics
        total_time = (time.time() - query_start) * 1000
        logger.info(
            f"Fast retrieved {len(items)} transactions for tenant {tenant_id} in {total_time:.2f}ms"
        )

        if total_time > 200:
            logger.warning(f"🚨 SLOW FAST QUERY: {total_time:.2f}ms (target: <200ms)")
        else:
            logger.info(f"✅ FAST QUERY SUCCESS: {total_time:.2f}ms (target: <200ms)")

        return PaginatedTransactionResponse(
            items=items,
            total_count=-1,  # Not available in cursor-based pagination
            page=1,
            page_size=limit,
            total_pages=-1,
            next_cursor=next_cursor,
            has_more=has_more,
        )

    except Exception as e:
        logger.error(
            f"Error in fast transactions fetch for tenant {tenant_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch transactions",
        )


@router.get("/", response_model=PaginatedTransactionResponse)
async def list_transactions(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    upload_id: Optional[str] = Query(None, description="Filter by upload ID"),
    start_date: Optional[str] = Query(
        None, description="Start date filter (YYYY-MM-DD)"
    ),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    status: Optional[str] = Query(None, description="Filter by status"),
    category_id: Optional[str] = Query(None, description="Filter by category ID"),
    search_term: Optional[str] = Query(None, description="Search in description"),
    min_amount: Optional[float] = Query(None, description="Minimum amount"),
    max_amount: Optional[float] = Query(None, description="Maximum amount"),
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=1000),
    sort_by: str = Query("date", description="Field to sort by"),
    sort_direction: str = Query("desc", regex="^(asc|desc)$"),
    skip_count: bool = Query(False, description="Skip total count for performance"),
) -> PaginatedTransactionResponse:
    """
    List transactions with comprehensive filtering and pagination.

    Supports filtering by:
    - Date range (start_date, end_date)
    - Transaction status (uncategorized, categorized, ai_suggested, user_modified)
    - Category
    - Upload batch
    - Amount range
    - Description search

    Performance optimization:
    - Set skip_count=true to skip total count calculation
    - Use smaller page_size for faster response
    """
    start_time = time.time()

    try:
        # Build WHERE clause
        where_conditions = ["tenant_id = $1"]
        query_params = [tenant_id]
        param_count = 1

        # Add filters
        if upload_id:
            param_count += 1
            where_conditions.append(f"upload_id = ${param_count}")
            query_params.append(upload_id)

        if start_date:
            param_count += 1
            where_conditions.append(f"date >= ${param_count}::date")
            query_params.append(start_date)

        if end_date:
            param_count += 1
            where_conditions.append(f"date <= ${param_count}::date")
            query_params.append(end_date)

        if min_amount is not None:
            param_count += 1
            where_conditions.append(f"amount >= ${param_count}")
            query_params.append(min_amount)

        if max_amount is not None:
            param_count += 1
            where_conditions.append(f"amount <= ${param_count}")
            query_params.append(max_amount)

        if search_term:
            param_count += 1
            where_conditions.append(f"description ILIKE ${param_count}")
            query_params.append(f"%{search_term}%")

        if category_id:
            param_count += 1
            where_conditions.append(f"category_id = ${param_count}")
            query_params.append(int(category_id))

        # Status filter
        if status and status != "all":
            if status == "uncategorized":
                where_conditions.append(
                    "category_id IS NULL AND original_category IS NULL"
                )
            elif status == "user_modified":
                where_conditions.append("category_id IS NOT NULL")
            elif status == "ai_suggested":
                where_conditions.append("ai_category IS NOT NULL")

        where_clause = " AND ".join(where_conditions)

        # Get total count if not skipped
        total_count = 0
        if not skip_count:
            count_query = f"SELECT COUNT(*) FROM transactions WHERE {where_clause}"
            total_count = await conn.fetchval(count_query, *query_params)

        # Build main query
        offset = (page - 1) * page_size

        # Map sort fields
        sort_field_map = {
            "date": "date",
            "amount": "amount",
            "description": "description",
        }
        sort_field = sort_field_map.get(sort_by, "date")

        main_query = f"""
            SELECT 
                id,
                date,
                description,
                amount,
                account,
                transaction_type,
                category_id,
                original_category,
                ai_category,
                ai_confidence,
                vendor_name,
                entity_id,
                upload_id,
                CASE 
                    WHEN category_id IS NOT NULL THEN true 
                    ELSE false 
                END as is_user_modified,
                false as user_corrected
            FROM transactions
            WHERE {where_clause}
            ORDER BY {sort_field} {sort_direction.upper()}, id DESC
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """

        query_params.extend([page_size, offset])

        # Execute query
        rows = await conn.fetch(main_query, *query_params)

        # Convert to response objects
        items = [row_to_transaction_response(dict(row)) for row in rows]

        # Calculate pagination
        total_pages = (
            (total_count + page_size - 1) // page_size if total_count > 0 else 0
        )

        # Log performance
        elapsed_time = (time.time() - start_time) * 1000
        logger.info(
            f"Retrieved {len(items)} transactions for tenant {tenant_id} "
            f"(page {page}/{total_pages}) in {elapsed_time:.2f}ms"
        )

        return PaginatedTransactionResponse(
            items=items,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        logger.error(f"Error fetching transactions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch transactions",
        )


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: str,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> TransactionResponse:
    """Get a single transaction by ID."""
    query = """
        SELECT 
            id,
            date,
            description,
            amount,
            account,
            transaction_type,
            category_id,
            original_category,
            ai_category,
            ai_confidence,
            vendor_name,
            entity_id,
            upload_id,
            CASE 
                WHEN category_id IS NOT NULL THEN true 
                ELSE false 
            END as is_user_modified,
            false as user_corrected
        FROM transactions
        WHERE id = $1 AND tenant_id = $2
    """

    row = await conn.fetchrow(query, transaction_id, tenant_id)

    if not row:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found",
        )

    return row_to_transaction_response(dict(row))


@router.put("/{transaction_id}/category")
async def update_transaction_category(
    transaction_id: str,
    category_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Update transaction category (user modification)."""
    try:
        # Verify transaction exists and belongs to tenant
        check_query = "SELECT id FROM transactions WHERE id = $1 AND tenant_id = $2"
        exists = await conn.fetchval(check_query, transaction_id, tenant_id)

        if not exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found",
            )

        # Update category
        update_query = """
            UPDATE transactions 
            SET category_id = $1, updated_at = NOW()
            WHERE id = $2 AND tenant_id = $3
            RETURNING id
        """

        result = await conn.fetchval(
            update_query, category_id, transaction_id, tenant_id
        )

        if result:
            logger.info(
                f"Updated category for transaction {transaction_id} to {category_id}"
            )
            return {"success": True, "transaction_id": transaction_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update transaction",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating transaction category: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update transaction category",
        )


@router.post("/{transaction_id}/approve-ai-suggestion")
async def approve_ai_suggestion(
    transaction_id: str,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Approve AI-suggested category for a transaction."""
    try:
        # Get AI suggestion
        query = """
            SELECT ai_category, ai_confidence 
            FROM transactions 
            WHERE id = $1 AND tenant_id = $2
        """
        row = await conn.fetchrow(query, transaction_id, tenant_id)

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found",
            )

        if not row["ai_category"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No AI suggestion available for this transaction",
            )

        # For now, just mark that we have the AI category
        # In a full implementation, you'd map this to a category_id
        logger.info(
            f"Approved AI suggestion '{row['ai_category']}' "
            f"(confidence: {row['ai_confidence']}) for transaction {transaction_id}"
        )

        return {
            "success": True,
            "transaction_id": transaction_id,
            "ai_category": row["ai_category"],
            "confidence": float(row["ai_confidence"]) if row["ai_confidence"] else None,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving AI suggestion: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to approve AI suggestion",
        )
