"""
Transaction domain ADK tools.

This module provides ADK tool functions for transaction operations that can be used
by agents to process and analyze financial transactions.
"""

import logging
from datetime import date
from typing import Any, Dict, Optional

import asyncpg

from ...shared.exceptions import ServiceError
from ..categories.models import Category
from .models import Entity, Transaction
from .schemas import TransactionUpdate
from .service import TransactionService

logger = logging.getLogger(__name__)


async def get_transaction_by_id_tool(
    db: asyncpg.Connection, tenant_id: int, transaction_id: str
) -> Optional[Dict[str, Any]]:
    """
    ADK tool function to get a transaction by ID.

    Args:
        db: Database session
        tenant_id: Tenant ID
        transaction_id: Transaction ID

    Returns:
        Transaction data if found, None otherwise
    """
    try:
        sql = "SELECT * FROM transactions WHERE id = $1 AND tenant_id = $2"
        row = await db.fetchrow(sql, transaction_id, tenant_id)

        if not row:
            return None

        transaction = Transaction.model_validate(dict(row))
        return {
            "id": transaction.id,
            "date": transaction.date.isoformat(),
            "description": transaction.description,
            "amount": float(transaction.amount),
            "account": transaction.account,
            "category": transaction.get_display_category(),
            "entity_id": transaction.entity_id,
            "is_categorized": transaction.is_categorized,
            "confidence_score": float(transaction.confidence_score)
            if transaction.confidence_score
            else None,
        }

    except Exception as e:
        logger.error(f"Error getting transaction: {e}")
        return None


async def search_transactions_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    search_query: str,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    limit: int = 20,
) -> Dict[str, Any]:
    """
    ADK tool function to search transactions by description.

    Args:
        db: Database session
        tenant_id: Tenant ID
        search_query: Search query for description
        start_date: Optional start date filter
        end_date: Optional end date filter
        limit: Maximum number of results

    Returns:
        Dictionary containing search results
    """
    try:
        # Build search query
        # Build dynamic SQL query with optional date filters
        date_filter = ""
        params = [tenant_id, f"%{search_query}%"]
        param_count = 2

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT * FROM transactions 
            WHERE tenant_id = $1 
              AND description ILIKE $2
              {date_filter}
            ORDER BY date DESC
            LIMIT {limit}
        """

        rows = await db.fetch(sql, *params)
        transactions = [Transaction.model_validate(dict(row)) for row in rows]

        return {
            "query": search_query,
            "count": len(transactions),
            "transactions": [
                {
                    "id": t.id,
                    "date": t.date.isoformat(),
                    "description": t.description,
                    "amount": float(t.amount),
                    "category": t.get_display_category(),
                }
                for t in transactions
            ],
        }

    except Exception as e:
        logger.error(f"Error searching transactions: {e}")
        raise ServiceError(
            message=f"Failed to search transactions: {str(e)}",
            error_code="TRANSACTION_SEARCH_ERROR",
            tenant_id=tenant_id,
        )


async def categorize_transaction_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    transaction_id: str,
    category_name: str,
    confidence_score: float = 1.0,
) -> Dict[str, Any]:
    """
    ADK tool function to categorize a transaction.

    Args:
        db: Database session
        tenant_id: Tenant ID
        transaction_id: Transaction ID
        category_name: Category name to assign
        confidence_score: Confidence score for categorization

    Returns:
        Categorization result
    """
    try:
        service = TransactionService(db)

        # Find category by name
        sql = "SELECT * FROM categories WHERE tenant_id = $1 AND name = $2"
        row = await db.fetchrow(sql, tenant_id, category_name)

        if not row:
            raise ServiceError(
                message=f"Category '{category_name}' not found",
                error_code="CATEGORY_NOT_FOUND",
                tenant_id=tenant_id,
            )

        category = Category.model_validate(dict(row))

        # Update transaction
        update_data = TransactionUpdate(
            category_id=category.id,
            category_path=category.get_full_path(),
            is_categorized=True,
            confidence_score=confidence_score,
        )

        await service.update_transaction(transaction_id, update_data, tenant_id)

        return {
            "transaction_id": transaction_id,
            "category": category.get_full_path(),
            "category_id": category.id,
            "confidence_score": confidence_score,
            "success": True,
        }

    except ServiceError:
        raise
    except Exception as e:
        logger.error(f"Error categorizing transaction: {e}")
        raise ServiceError(
            message=f"Failed to categorize transaction: {str(e)}",
            error_code="CATEGORIZATION_ERROR",
            tenant_id=tenant_id,
        )


async def extract_entity_from_transaction_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    transaction_id: str,
    entity_name: Optional[str] = None,
    confidence_score: float = 0.8,
) -> Dict[str, Any]:
    """
    ADK tool function to extract or assign entity to a transaction.

    Args:
        db: Database session
        tenant_id: Tenant ID
        transaction_id: Transaction ID
        entity_name: Optional entity name (if None, will attempt extraction)
        confidence_score: Confidence score for extraction

    Returns:
        Entity extraction result
    """
    try:
        # Get transaction
        sql = "SELECT * FROM transactions WHERE id = $1 AND tenant_id = $2"
        row = await db.fetchrow(sql, transaction_id, tenant_id)

        if not row:
            raise ServiceError(
                message=f"Transaction '{transaction_id}' not found",
                error_code="TRANSACTION_NOT_FOUND",
                tenant_id=tenant_id,
            )

        transaction = Transaction.model_validate(dict(row))

        # Extract entity name if not provided
        if not entity_name:
            # Simple extraction logic - can be enhanced
            # Try to extract merchant/vendor name from description
            # This is a placeholder - should use NLP/regex patterns
            words = transaction.description.split()
            if words:
                entity_name = words[0]  # Simple heuristic

        if not entity_name:
            return {
                "transaction_id": transaction_id,
                "entity_extracted": False,
                "message": "Could not extract entity from description",
            }

        # Check if entity exists
        sql = "SELECT * FROM entities WHERE tenant_id = $1 AND name = $2"
        row = await db.fetchrow(sql, tenant_id, entity_name)
        entity = Entity.model_validate(dict(row)) if row else None

        # Create entity if doesn't exist
        if not entity:
            import uuid

            entity_id = str(uuid.uuid4())
            # Insert entity using asyncpg
            sql = """
                INSERT INTO entities (id, name, normalized_name, entity_type, confidence_score, extraction_method, tenant_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            await db.execute(
                sql,
                entity_id,
                entity_name,
                entity_name.lower(),
                "vendor",
                confidence_score,
                "ai",
                tenant_id,
            )

            # Create entity object for return data
            entity = Entity(
                id=entity_id,
                name=entity_name,
                normalized_name=entity_name.lower(),
                entity_type="vendor",
                confidence_score=confidence_score,
                extraction_method="ai",
                tenant_id=tenant_id,
            )

        # Update transaction with entity
        update_sql = (
            "UPDATE transactions SET entity_id = $1 WHERE id = $2 AND tenant_id = $3"
        )
        await db.execute(update_sql, entity.id, transaction_id, tenant_id)

        return {
            "transaction_id": transaction_id,
            "entity_id": entity.id,
            "entity_name": entity.name,
            "entity_type": entity.entity_type,
            "confidence_score": float(entity.confidence_score)
            if entity.confidence_score
            else confidence_score,
            "entity_extracted": True,
        }

    except ServiceError:
        raise
    except Exception as e:
        logger.error(f"Error extracting entity: {e}")
        raise ServiceError(
            message=f"Failed to extract entity: {str(e)}",
            error_code="ENTITY_EXTRACTION_ERROR",
            tenant_id=tenant_id,
        )


async def get_transaction_analytics_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to get transaction analytics.

    Args:
        db: Database session
        tenant_id: Tenant ID
        start_date: Optional start date filter
        end_date: Optional end date filter

    Returns:
        Transaction analytics data
    """
    try:
        # Build date filter for SQL query
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        # Get all analytics in a single query
        analytics_sql = f"""
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN is_categorized = true THEN 1 END) as categorized_count,
                COUNT(CASE WHEN entity_id IS NOT NULL THEN 1 END) as entity_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                MIN(amount) as min_amount,
                MAX(amount) as max_amount
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
        """

        row = await db.fetchrow(analytics_sql, *params)

        total_transactions = row["total_transactions"]
        categorized_count = row["categorized_count"]
        entity_count = row["entity_count"]

        # Calculate rates
        categorization_rate = (
            (categorized_count / total_transactions * 100)
            if total_transactions > 0
            else 0
        )
        entity_extraction_rate = (
            (entity_count / total_transactions * 100) if total_transactions > 0 else 0
        )

        return {
            "tenant_id": tenant_id,
            "date_range": {
                "start": start_date.isoformat() if start_date else None,
                "end": end_date.isoformat() if end_date else None,
            },
            "total_transactions": total_transactions,
            "categorized_transactions": categorized_count,
            "transactions_with_entities": entity_count,
            "categorization_rate": round(categorization_rate, 2),
            "entity_extraction_rate": round(entity_extraction_rate, 2),
            "amount_statistics": {
                "total": float(row["total_amount"]) if row["total_amount"] else 0,
                "average": float(row["avg_amount"]) if row["avg_amount"] else 0,
                "minimum": float(row["min_amount"]) if row["min_amount"] else 0,
                "maximum": float(row["max_amount"]) if row["max_amount"] else 0,
            },
        }

    except Exception as e:
        logger.error(f"Error getting transaction analytics: {e}")
        raise ServiceError(
            message=f"Failed to get transaction analytics: {str(e)}",
            error_code="ANALYTICS_ERROR",
            tenant_id=tenant_id,
        )


async def detect_duplicate_transactions_tool(
    db: asyncpg.Connection, tenant_id: int, threshold_days: int = 1
) -> Dict[str, Any]:
    """
    ADK tool function to detect potential duplicate transactions.

    Args:
        db: Database session
        tenant_id: Tenant ID
        threshold_days: Number of days to check for duplicates

    Returns:
        Duplicate detection results
    """
    try:
        # Self-join to find potential duplicates using asyncpg
        sql = """
            SELECT 
                t1.id as id1, t1.date as date1, t1.description as desc1, t1.amount as amount1,
                t2.id as id2, t2.date as date2, t2.description as desc2, t2.amount as amount2
            FROM transactions t1
            JOIN transactions t2 ON (
                t1.tenant_id = t2.tenant_id 
                AND t1.id != t2.id
                AND t1.amount = t2.amount
                AND t1.description = t2.description
                AND ABS(EXTRACT(DAY FROM t1.date - t2.date)) <= $2
            )
            WHERE t1.tenant_id = $1
            ORDER BY t1.date DESC
        """

        duplicates = await db.fetch(sql, tenant_id, threshold_days)

        # Group duplicates
        duplicate_groups = {}
        for row in duplicates:
            key = f"{row['amount1']}_{row['desc1']}"
            if key not in duplicate_groups:
                duplicate_groups[key] = []

            # Add both transactions to the group
            for suffix in ["1", "2"]:
                t_data = {
                    "id": row[f"id{suffix}"],
                    "date": row[f"date{suffix}"].isoformat(),
                    "description": row[f"desc{suffix}"],
                    "amount": float(row[f"amount{suffix}"]),
                }
                if t_data not in duplicate_groups[key]:
                    duplicate_groups[key].append(t_data)

        return {
            "tenant_id": tenant_id,
            "duplicate_groups": len(duplicate_groups),
            "total_duplicates": sum(len(group) for group in duplicate_groups.values()),
            "threshold_days": threshold_days,
            "duplicates": list(duplicate_groups.values()),
        }

    except Exception as e:
        logger.error(f"Error detecting duplicates: {e}")
        raise ServiceError(
            message=f"Failed to detect duplicates: {str(e)}",
            error_code="DUPLICATE_DETECTION_ERROR",
            tenant_id=tenant_id,
        )
