"""
Database models for onboarding domain.

Pydantic models for temporal validation results and onboarding status.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class TemporalValidation(BaseModel):
    """
    Stores temporal validation results for each tenant.

    Tracks the accuracy of categorization over time periods to ensure
    the AI model maintains performance with historical data.
    """

    # Primary key
    id: uuid.UUID
    validation_id: str = Field(..., description="Unique validation identifier")

    # Tenant relationship
    tenant_id: int

    # Validation parameters
    start_month: str = Field(..., description="YYYY-MM format")
    end_month: str = Field(..., description="YYYY-MM format")
    accuracy_threshold: float = Field(default=0.85, ge=0.0, le=1.0)

    # Execution status
    status: str = Field(
        default="pending", description="pending, running, completed, failed"
    )
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Results
    average_accuracy: Optional[float] = Field(None, ge=0.0, le=1.0)
    meets_threshold: Optional[bool] = None
    monthly_results: Optional[List[Dict[str, Any]]] = Field(
        None, description="Stores List[MonthlyAccuracyResult] as JSON"
    )

    # Error tracking
    error_message: Optional[str] = None

    # Additional metadata
    total_transactions_tested: Optional[int] = Field(None, ge=0)
    total_training_transactions: Optional[int] = Field(None, ge=0)

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RAGCorpus(BaseModel):
    """
    Tracks RAG corpus status for each tenant.

    Stores information about the Vertex AI RAG corpus created from
    the tenant's historical transaction data, including customer schema discovery metadata.
    """

    # Primary key
    id: int

    # Tenant relationship (one-to-one)
    tenant_id: int

    # Corpus information
    corpus_resource_name: str = Field(..., description="Full Vertex AI resource name")
    corpus_display_name: str = Field(..., description="Human-readable display name")

    # Corpus statistics
    total_patterns: int = Field(default=0, ge=0)
    unique_categories: int = Field(default=0, ge=0)

    # GCS storage information
    gcs_uri: Optional[str] = Field(None, description="gs://bucket/path/to/corpus.jsonl")

    # Status tracking
    is_active: bool = Field(default=True)
    last_rebuild_at: Optional[datetime] = None

    # Schema discovery metadata (NEW - for dynamic schema discovery)
    customer_category_schemas: Optional[Dict[str, Any]] = Field(
        None, description="Discovered category schemas from all files"
    )
    unified_category_mapping: Optional[Dict[str, Any]] = Field(
        None, description="Original -> Unified category mappings"
    )
    reverse_category_mapping: Optional[Dict[str, Any]] = Field(
        None, description="Unified -> Original(s) category mappings"
    )
    source_file_schemas: Optional[Dict[str, Any]] = Field(
        None, description="Per-file schema information"
    )
    schema_discovery_session_id: Optional[str] = Field(
        None, max_length=100, description="Schema discovery session reference"
    )

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OnboardingStatus(BaseModel):
    """
    Tracks overall onboarding status and approval for each tenant.

    Maintains the current state of the onboarding workflow and
    production readiness approval.
    """

    # Primary key
    id: int

    # Tenant relationship (one-to-one)
    tenant_id: int

    # Onboarding type (M1/M2/M3 scenarios)
    onboarding_type: str = Field(
        default="historical_data",
        description="Types: zero_onboarding (M1), historical_data (M2), schema_only (M3)",
    )

    # Onboarding stages
    stage: str = Field(
        default="not_started",
        description="Stages: not_started, data_uploaded, corpus_building, validating, completed, zero_ready, schema_imported, schema_ready",
    )

    # Production approval
    approved_for_production: bool = Field(default=False)
    approved_at: Optional[datetime] = None
    approved_by_user_id: Optional[int] = None
    approval_notes: Optional[str] = None

    # Validation tracking
    last_validation_id: Optional[str] = None
    last_validation_accuracy: Optional[float] = Field(None, ge=0.0, le=1.0)

    # Transaction statistics
    total_transactions: int = Field(default=0, ge=0)
    transactions_with_labels: int = Field(default=0, ge=0)
    date_range_start: Optional[datetime] = None
    date_range_end: Optional[datetime] = None

    # Activity tracking
    last_activity: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
