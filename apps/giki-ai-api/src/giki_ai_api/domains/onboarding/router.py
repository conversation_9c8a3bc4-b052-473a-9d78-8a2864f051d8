"""
API router for onboarding domain.

Provides endpoints for temporal accuracy validation workflow.
"""

import logging
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Optional

from asyncpg import Connection
from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)

from ...core.config import settings
from ...core.database import get_db_session
from ...core.dependencies import (
    get_current_tenant_id,
    get_current_user_with_tenant,
    get_vertex_ai_client,
)
from ...shared.ai.vertex_client import VertexAIClient
from .processing_schemas import (
    ColumnStatisticResponse,
    FileProcessingReportResponse,
    ProcessingReportSummary,
    RowDetailsRequest,
    RowDetailsResponse,
    SchemaMappingReportResponse,
)
from .schemas import (
    FileColumnMapping,
    OnboardingApprovalRequest,
    OnboardingStartRequest,
    OnboardingStatus,
    SchemaOnlyStartRequest,
    TemporalValidationProgress,
    TemporalValidationRequest,
    TemporalValidationResult,
    ZeroOnboardingStartRequest,
)
from .service import OnboardingService

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Onboarding"])


@router.get("/status", response_model=OnboardingStatus)
async def get_onboarding_status(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Get current onboarding status for the tenant."""

    service = OnboardingService(conn, vertex_client)
    return await service.get_onboarding_status(tenant_id)


@router.post("/start", response_model=OnboardingStatus)
async def start_onboarding(
    request: OnboardingStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start the onboarding process for a new tenant."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Initialize onboarding record in database
    await service.initialize_onboarding_record(tenant_id)

    return await service.get_onboarding_status(tenant_id)


@router.post("/upload-historical-data")
async def upload_historical_data(
    file: UploadFile = File(...),
    year: int = Form(...),
    has_category_labels: bool = Form(...),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
    # background_tasks removed - all operations are now synchronous
):
    """
    Upload historical transaction data with existing category labels.

    This is the critical first step in onboarding where customers upload
    their full year of labeled transaction data.
    """

    if not file.filename.endswith((".csv", ".xlsx", ".xls")):
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Please upload CSV or Excel file.",
        )

    # Save file temporarily
    current_user, tenant = user_tenant
    upload_dir = settings.get_upload_directory(tenant.id)

    file_path = upload_dir / f"{year}_{file.filename}"

    try:
        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)

        service = OnboardingService(conn, vertex_client)

        # Interpret columns
        column_mapping = await service.interpret_file_columns(str(file_path))

        # Process file and store transactions in database using sophisticated AI flow
        (
            transaction_count,
            report_id,
            upload_id,
        ) = await service.process_uploaded_file_with_reporting(
            tenant_id=tenant.id,
            file_path=str(file_path),
            column_mapping=column_mapping,
            has_category_labels=has_category_labels,
        )

        return {
            "status": "success",
            "message": f"File uploaded and processed successfully. {transaction_count} transactions imported.",
            "file_path": str(file_path),
            "column_mapping": column_mapping.model_dump(),
            "has_category_labels": has_category_labels,
            "transactions_imported": transaction_count,
            "processing_report_id": report_id,
            "upload_id": upload_id,
        }

    except Exception as e:
        logger.error(f"File upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")


@router.post("/batch-upload-files")
async def batch_upload_files(
    files: List[UploadFile] = File(..., description="Financial data files to upload"),
    year: str = Form(..., description="Year of the data (e.g., 2024)"),
    has_category_labels: bool = Form(
        True, description="Whether files contain category labels"
    ),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> dict:
    """
    Batch upload multiple financial data files with enhanced reporting.

    This endpoint accepts multiple file uploads and processes each one:
    - Supports Excel (.xlsx) and CSV files
    - Intelligent column mapping and categorization
    - Stores transactions with original category labels
    - Provides detailed processing reports for each file

    Returns summary of upload results, transaction counts, and report IDs
    for retrieving detailed processing information including row-by-row
    results, column statistics, and data quality metrics.
    """
    current_user, tenant = user_tenant

    logger.info(f"Starting batch upload for tenant {tenant.id} with {len(files)} files")

    # Results tracking
    upload_results = []
    total_transactions = 0
    successful_uploads = 0

    service = OnboardingService(conn, vertex_client)

    for file in files:
        if not file.filename:
            upload_results.append(
                {
                    "filename": "unknown",
                    "status": "error",
                    "error": "No filename provided",
                    "transactions_imported": 0,
                    "report_id": None,
                }
            )
            continue

        try:
            logger.info(f"Processing {file.filename}...")

            # Save uploaded file to tenant directory
            upload_dir = Path("uploads") / str(tenant.id)
            upload_dir.mkdir(parents=True, exist_ok=True)
            tenant_file_path = upload_dir / f"{year}_{file.filename}"

            # Save uploaded file content
            content = await file.read()
            with open(tenant_file_path, "wb") as f:
                f.write(content)

            # Interpret columns with AI
            column_mapping = await service.interpret_file_columns(str(tenant_file_path))

            # Process file with enhanced reporting
            (
                transaction_count,
                report_id,
                upload_id,
            ) = await service.process_uploaded_file_with_reporting(
                tenant_id=tenant.id,
                file_path=str(tenant_file_path),
                column_mapping=column_mapping,
                has_category_labels=has_category_labels,
            )

            upload_results.append(
                {
                    "filename": file.filename,
                    "status": "success",
                    "transactions_imported": transaction_count,
                    "report_id": str(report_id),
                    "upload_id": str(upload_id),
                    "column_mapping": column_mapping.model_dump(),
                    "file_path": str(tenant_file_path),
                }
            )

            total_transactions += transaction_count
            successful_uploads += 1

            logger.info(
                f"✅ {file.filename}: {transaction_count} transactions imported"
            )

        except Exception as e:
            logger.error(f"Failed to process {file.filename}: {e}")
            upload_results.append(
                {
                    "filename": file.filename,
                    "status": "failed",
                    "error": str(e),
                    "transactions_imported": 0,
                    "report_id": None,
                }
            )

    # Optionally build RAG corpus from all uploaded data
    rag_corpus_built = False
    if successful_uploads > 0:
        try:
            logger.info("Building RAG corpus from uploaded data...")
            corpus_result = await service.build_rag_corpus(tenant.id)
            rag_corpus_built = True
            logger.info(
                f"✅ RAG corpus built with {corpus_result.total_patterns} patterns"
            )
        except Exception as e:
            logger.error(f"Failed to build RAG corpus: {e}")

    return {
        "status": "completed",
        "summary": {
            "total_files_processed": len(files),
            "successful_uploads": successful_uploads,
            "failed_uploads": len(files) - successful_uploads,
            "total_transactions_imported": total_transactions,
            "rag_corpus_built": rag_corpus_built,
        },
        "upload_results": upload_results,
        "message": f"Batch upload completed. {successful_uploads}/{len(files)} files processed successfully. Use report_id from upload_results to fetch detailed processing reports via /file-processing-report/{{report_id}}.",
    }


@router.post("/interpret-columns", response_model=FileColumnMapping)
async def interpret_file_columns(
    file_path: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> FileColumnMapping:
    """Use AI to interpret column mappings from an uploaded file."""

    # Validate file path is within tenant's upload directory
    current_user, tenant = user_tenant
    expected_dir = settings.upload_base_path / str(tenant.id)
    file_path_obj = Path(file_path)

    if not file_path_obj.is_relative_to(expected_dir):
        raise HTTPException(status_code=403, detail="Access denied to file")

    if not file_path_obj.exists():
        raise HTTPException(status_code=404, detail="File not found")

    service = OnboardingService(conn, vertex_client)
    return await service.interpret_file_columns(str(file_path_obj))


@router.post("/build-rag-corpus")
async def build_rag_corpus(
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> dict:
    """
    Build RAG corpus from the tenant's labeled transaction data.

    This creates a knowledge base from the customer's historical categorization
    patterns that will be used for AI predictions.

    Now runs synchronously to provide immediate feedback to users.
    """

    current_user, tenant = user_tenant

    try:
        # Initialize service
        service = OnboardingService(conn, vertex_client)

        # Build corpus synchronously
        logger.info(f"Starting synchronous RAG corpus build for tenant {tenant.id}")
        corpus_result = await service.build_rag_corpus(tenant.id)

        # Update onboarding status
        query = """
            UPDATE onboarding_status 
            SET stage = $1, last_activity = $2
            WHERE tenant_id = $3
        """
        await conn.execute(
            query, "corpus_created", datetime.now(timezone.utc), tenant.id
        )

        logger.info(f"✅ Successfully built RAG corpus for tenant {tenant.id}")

        return {
            "corpus_id": corpus_result.corpus_name,
            "status": "completed",
            "message": "RAG corpus successfully built",
            "tenant_id": tenant.id,
            "total_patterns": corpus_result.total_patterns,
            "categories_found": len(corpus_result.categories_found),
            "processing_time_seconds": corpus_result.processing_time_seconds,
        }

    except Exception as e:
        logger.error(f"Failed to build RAG corpus for tenant {tenant.id}: {e}")

        # Update status to show failure
        try:
            query = """
                UPDATE onboarding_status 
                SET stage = $1, last_activity = $2
                WHERE tenant_id = $3
            """
            await conn.execute(
                query, "corpus_failed", datetime.now(timezone.utc), tenant.id
            )
        except Exception:
            pass

        raise HTTPException(
            status_code=500, detail=f"Failed to build RAG corpus: {str(e)}"
        )


@router.post("/validate-temporal-accuracy", response_model=TemporalValidationResult)
async def validate_temporal_accuracy(
    request: TemporalValidationRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> TemporalValidationResult:
    """
    Run temporal accuracy validation for July-December 2024.

    This simulates month-by-month accuracy testing where:
    - Each month uses all previous months as training data
    - Tests accuracy against the customer's original labels
    - Must achieve >85% accuracy for production approval

    Now runs synchronously to provide immediate results.
    """

    current_user, tenant = user_tenant
    if request.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Cannot validate another tenant")

    service = OnboardingService(conn, vertex_client)

    # Generate a validation ID for tracking
    validation_id = str(uuid.uuid4())

    try:
        # Run validation synchronously
        logger.info(f"Starting synchronous temporal validation for tenant {tenant.id}")
        validation_result = await service.run_temporal_validation(
            request, validation_id
        )

        # Persist the validation result
        import json

        query = """
            INSERT INTO temporal_validation (
                validation_id, tenant_id, started_at, completed_at, 
                status, accuracy_threshold, average_accuracy, meets_threshold,
                monthly_results, total_training_transactions, total_transactions_tested, 
                error_message
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """

        await conn.execute(
            query,
            validation_id,
            tenant.id,
            validation_result.started_at,
            validation_result.completed_at,
            validation_result.status,
            validation_result.accuracy_threshold,
            validation_result.average_accuracy,
            validation_result.meets_threshold,
            json.dumps([r.model_dump() for r in validation_result.monthly_results]),
            validation_result.total_training_transactions,
            validation_result.total_test_transactions,
            validation_result.error_message,
        )

        logger.info(
            f"✅ Temporal validation completed for tenant {tenant.id} - Average accuracy: {validation_result.average_accuracy:.1f}%"
        )

        return validation_result

    except Exception as e:
        logger.error(f"Failed temporal validation for tenant {tenant.id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to run temporal validation: {str(e)}"
        )


@router.get(
    "/validation-results/{validation_id}", response_model=TemporalValidationResult
)
async def get_validation_results(
    validation_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
) -> TemporalValidationResult:
    """Get results of a specific validation run."""

    current_user, tenant = user_tenant

    # Fetch validation from database
    query = """
        SELECT validation_id, tenant_id, started_at, completed_at, status, 
               validation_results, error_message, months_tested, overall_accuracy,
               corpus_build_time, testing_time, total_transactions_tested
        FROM temporal_validation
        WHERE validation_id = $1 AND tenant_id = $2
    """
    row = await conn.fetchrow(query, validation_id, tenant.id)

    if not row:
        validation = None
    else:
        from .models import TemporalValidation

        validation = TemporalValidation(**dict(row))

    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")

    # Convert to response model
    from .schemas import MonthlyAccuracyResult

    return TemporalValidationResult(
        tenant_id=validation.tenant_id,
        validation_id=validation.validation_id,
        started_at=validation.started_at,
        completed_at=validation.completed_at,
        status=validation.status,
        accuracy_threshold=validation.accuracy_threshold,
        average_accuracy=validation.average_accuracy or 0.0,
        meets_threshold=validation.meets_threshold or False,
        monthly_results=[
            MonthlyAccuracyResult(**mr) for mr in (validation.monthly_results or [])
        ]
        if validation.monthly_results
        else [],
        total_training_transactions=validation.total_training_transactions,
        total_test_transactions=validation.total_transactions_tested,
        error_message=validation.error_message,
    )


@router.get(
    "/validation-progress/{validation_id}", response_model=TemporalValidationProgress
)
async def get_validation_progress(
    validation_id: str,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> TemporalValidationProgress:
    """
    Get real-time progress for temporal validation with progressive RAG corpus building.

    Returns detailed progress including:
    - Current phase (corpus_building, testing, completed)
    - Per-month corpus building progress
    - Overall validation progress
    - Estimated completion time
    """
    # Get validation record
    query = """
        SELECT validation_id, tenant_id, started_at, completed_at, status,
               validation_results, error_message, months_tested, overall_accuracy,
               corpus_build_time, testing_time, total_transactions_tested,
               monthly_results
        FROM temporal_validation
        WHERE validation_id = $1 AND tenant_id = $2
    """
    row = await conn.fetchrow(query, validation_id, tenant_id)

    if row:
        from .models import TemporalValidation

        validation = TemporalValidation(**dict(row))
    else:
        validation = None

    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")

    # Calculate progress based on status and monthly results
    months_total = (
        len(validation.monthly_results or []) if validation.monthly_results else 4
    )  # Default to 4 months
    months_completed = len(
        [m for m in (validation.monthly_results or []) if m.get("accuracy", 0) > 0]
    )

    # Determine current phase
    if validation.status == "pending":
        current_phase = "corpus_building"
    elif validation.status == "running":
        current_phase = "testing"
    elif validation.status in ["completed", "failed"]:
        current_phase = "completed"
    else:
        current_phase = "corpus_building"

    # Calculate overall progress
    if validation.status == "completed":
        overall_progress = 100.0
    elif validation.status == "failed":
        overall_progress = 0.0
    else:
        overall_progress = (
            (months_completed / months_total) * 100.0 if months_total > 0 else 0.0
        )

    # Get current month being processed
    current_month = None
    if validation.monthly_results and months_completed < months_total:
        # Next month to process
        all_months = [
            "2024-03",
            "2024-04",
            "2024-05",
            "2024-06",
        ]  # Adjust based on date range
        if months_completed < len(all_months):
            current_month = all_months[months_completed]

    # Build corpus progress (simplified - in real implementation, this would track actual corpus building)
    corpus_progress = []
    if validation.monthly_results:
        for i, monthly_result in enumerate(validation.monthly_results):
            month = monthly_result.get("month", f"2024-{3 + i:02d}")
            corpus_progress.append(
                {
                    "month": month,
                    "corpus_name": f"temporal_validation_{tenant_id}_{month}",
                    "training_data_size": monthly_result.get(
                        "training_transactions", 0
                    ),
                    "status": "completed"
                    if monthly_result.get("accuracy", 0) > 0
                    else "building",
                    "progress_percentage": 100.0
                    if monthly_result.get("accuracy", 0) > 0
                    else 0.0,
                    "started_at": validation.started_at,
                    "completed_at": validation.completed_at
                    if monthly_result.get("accuracy", 0) > 0
                    else None,
                }
            )

    return TemporalValidationProgress(
        validation_id=validation_id,
        current_phase=current_phase,
        months_total=months_total,
        months_completed=months_completed,
        current_month=current_month,
        corpus_progress=corpus_progress,
        overall_progress_percentage=overall_progress,
        estimated_completion_time=validation.completed_at,
    )


@router.post("/approve-for-production", response_model=OnboardingStatus)
async def approve_for_production(
    request: OnboardingApprovalRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """
    Approve tenant for production usage after successful validation.

    This is the final step that enables the tenant to start using
    the AI categorization on new transactions.
    """

    current_user, tenant = user_tenant
    if request.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Cannot approve another tenant")

    service = OnboardingService(conn, vertex_client)

    try:
        status = await service.approve_for_production(
            request.tenant_id, request.validation_id, request.approval_notes
        )

        if not status.approved_for_production:
            raise HTTPException(status_code=400, detail="Approval criteria not met")

        return status

    except Exception as e:
        logger.error(f"Production approval failed: {e}")
        raise HTTPException(status_code=500, detail=f"Approval failed: {str(e)}")


@router.get("/sample-data")
async def get_sample_data(user_tenant: tuple = Depends(get_current_user_with_tenant)):
    """Get sample data files for testing the onboarding process."""
    current_user, tenant = user_tenant

    sample_files = ["Capital One.xlsx", "Credit Card.xlsx", "ICICI.xlsx", "SVB.xlsx"]

    # Use absolute path to the test-files directory (the ONLY authorized sample data location)
    data_dir = Path(__file__).parent.parent.parent.parent.parent / "test-files"
    available_files = []

    for filename in sample_files:
        file_path = data_dir / filename
        if file_path.exists():
            available_files.append(
                {
                    "filename": filename,
                    "path": str(file_path),
                    "size_kb": file_path.stat().st_size / 1024,
                    "description": f"Sample {filename.replace('.xlsx', '')} bank transactions with category labels",
                }
            )

    return {
        "sample_files": available_files,
        "instructions": "Use these files to test the temporal accuracy validation workflow",
    }


@router.get("/diagnose-schema/{tenant_id}")
async def diagnose_schema_mappings(
    tenant_id: int,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Diagnose schema mapping issues for accuracy debugging.

    This endpoint helps debug why accuracy measurements may be failing
    by providing detailed information about schema mappings and category comparisons.
    """
    current_user, tenant = user_tenant

    # Only allow access to own tenant data
    if tenant_id != tenant.id:
        raise HTTPException(
            status_code=403, detail="Cannot diagnose schema mappings for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    try:
        diagnostic_info = await service.diagnose_schema_mappings(tenant_id)
        return diagnostic_info
    except Exception as e:
        logger.error(f"Schema diagnosis failed for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Schema diagnosis failed: {str(e)}"
        )


# ===============================
# FILE PROCESSING REPORT ENDPOINTS
# ===============================


@router.post("/upload-with-reporting")
async def upload_file_with_detailed_reporting(
    file: UploadFile = File(...),
    year: int = Form(...),
    has_category_labels: bool = Form(...),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Upload a file with enhanced processing that provides detailed reporting.

    This endpoint uses the new enhanced processing method that tracks:
    - Row-by-row processing results
    - Column statistics and data quality
    - Schema discovery integration
    - Detailed error and warning messages

    Returns the processing report ID that can be used to retrieve detailed reports.
    """
    if not file.filename.endswith((".csv", ".xlsx", ".xls")):
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Please upload CSV or Excel file.",
        )

    current_user, tenant = user_tenant
    upload_dir = settings.get_upload_directory(tenant.id)

    file_path = upload_dir / f"{year}_{file.filename}"

    try:
        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)

        service = OnboardingService(conn, vertex_client)

        # Interpret columns
        column_mapping = await service.interpret_file_columns(str(file_path))

        # Process file with enhanced reporting
        (
            transaction_count,
            report_id,
            upload_id,
        ) = await service.process_uploaded_file_with_reporting(
            tenant_id=tenant.id,
            file_path=str(file_path),
            column_mapping=column_mapping,
            has_category_labels=has_category_labels,
        )

        return {
            "status": "success",
            "message": f"File processed with detailed reporting. {transaction_count} transactions imported.",
            "report_id": report_id,
            "upload_id": upload_id,
            "transactions_imported": transaction_count,
            "file_path": str(file_path),
            "column_mapping": column_mapping.model_dump(),
        }

    except FileNotFoundError as e:
        logger.error(f"Upload directory creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to create upload directory. This may be a permissions issue in the deployment environment.",
        )
    except PermissionError as e:
        logger.error(f"File write permission denied: {e}")
        raise HTTPException(
            status_code=500,
            detail="Permission denied when writing uploaded file. Check deployment file system permissions.",
        )
    except ValueError as e:
        logger.error(f"File processing validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"File upload with reporting failed: {e}", exc_info=True)
        # Log environment details for debugging
        import os

        logger.error(
            f"Environment: K_SERVICE={os.environ.get('K_SERVICE')}, PWD={os.getcwd()}"
        )
        logger.error(f"Upload path attempted: {upload_dir}")
        raise HTTPException(
            status_code=500,
            detail=f"File processing failed: {str(e)}. Check server logs for details.",
        )


@router.get(
    "/file-processing-report/{upload_id}", response_model=FileProcessingReportResponse
)
async def get_file_processing_report(
    upload_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> FileProcessingReportResponse:
    """
    Get detailed processing report for a specific file upload.

    This provides comprehensive information about how the file was processed,
    including success rates, data quality scores, and discovered categories.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    report = await service.get_file_processing_report(upload_id)
    if not report:
        raise HTTPException(status_code=404, detail="Processing report not found")

    # Verify tenant access
    if report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied to this report")

    # Get column statistics for mapping info
    column_stats = await service.get_column_statistics(upload_id)

    # Build response
    mapped_columns = []
    unmapped_columns = []

    for stat in column_stats:
        if stat.mapped_field:
            mapped_columns.append(
                {
                    "original_name": stat.column_name,
                    "mapped_field": stat.mapped_field,
                    "confidence": stat.mapping_confidence or 0.0,
                    "method": stat.mapping_method or "ai",
                }
            )
        else:
            unmapped_columns.append(stat.column_name)

    success_rate = (
        (report.successful_rows / report.total_rows * 100)
        if report.total_rows > 0
        else 0.0
    )

    return FileProcessingReportResponse(
        upload_id=report.upload_id,
        file_name=report.file_name,
        status=report.status,
        total_rows=report.total_rows,
        successful_rows=report.successful_rows,
        failed_rows=report.failed_rows,
        skipped_rows=report.skipped_rows,
        success_rate=success_rate,
        processing_started_at=report.processing_started_at,
        processing_completed_at=report.processing_completed_at,
        processing_duration_seconds=report.processing_duration_seconds,
        date_format_detected=report.date_format_detected,
        total_columns=report.total_columns,
        mapped_columns=mapped_columns,
        unmapped_columns=unmapped_columns,
        data_quality_score=report.data_quality_score or 0.0,
        validation_errors=report.validation_errors or [],
        warnings=report.warnings or [],
        categories_discovered=report.categories_discovered or [],
        category_count=report.category_count or 0,
        schema_confidence=report.schema_confidence,
        error_message=report.error_message,
        created_at=report.created_at,
        updated_at=report.updated_at,
    )


@router.get(
    "/column-statistics/{upload_id}", response_model=List[ColumnStatisticResponse]
)
async def get_column_statistics(
    upload_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> List[ColumnStatisticResponse]:
    """
    Get detailed column statistics for a processed file.

    This provides information about each column including:
    - Data type detection and consistency
    - Null value percentages
    - Value distributions
    - Mapping confidence scores
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    # Verify access
    report = await service.get_file_processing_report(upload_id)
    if not report or report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    column_stats = await service.get_column_statistics(upload_id)

    responses = []
    for stat in column_stats:
        null_percentage = (
            (stat.null_values / stat.total_values * 100)
            if stat.total_values > 0
            else 0.0
        )

        # Calculate data quality score for this column
        quality_score = 1.0
        if stat.null_values > 0:
            quality_score *= 1 - null_percentage / 100
        if stat.type_consistency:
            quality_score *= stat.type_consistency

        responses.append(
            ColumnStatisticResponse(
                column_name=stat.column_name,
                column_index=stat.column_index,
                mapped_field=stat.mapped_field,
                total_values=stat.total_values,
                non_null_values=stat.non_null_values,
                null_percentage=null_percentage,
                unique_values=stat.unique_values,
                detected_type=stat.detected_type or "unknown",
                type_consistency=stat.type_consistency or 0.0,
                min_value=stat.min_value,
                max_value=stat.max_value,
                average_value=stat.average_value,
                most_common_values=stat.most_common_values or [],
                empty_string_count=stat.empty_string_count or 0,
                invalid_format_count=stat.invalid_format_count or 0,
                data_quality_score=quality_score,
                mapping_confidence=stat.mapping_confidence,
                mapping_method=stat.mapping_method,
            )
        )

    return responses


@router.post("/row-details/{upload_id}", response_model=RowDetailsResponse)
async def get_row_processing_details(
    upload_id: str,
    request: RowDetailsRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> RowDetailsResponse:
    """
    Get paginated row-by-row processing details.

    This allows drilling down into individual row processing results,
    including parsing errors, validation issues, and data quality problems.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    # Verify access
    report = await service.get_file_processing_report(upload_id)
    if not report or report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    result = await service.get_row_processing_details(
        upload_id=upload_id,
        page=request.page,
        page_size=request.page_size,
        status_filter=request.status_filter,
        has_errors=request.has_errors,
    )

    # Convert row details to response format
    row_responses = []
    for row in result["rows"]:
        row_responses.append(
            {
                "row_number": row.row_number,
                "status": row.status,
                "parsed_date": row.parsed_date,
                "parsed_amount": row.parsed_amount,
                "parsed_description": row.parsed_description,
                "parsed_category": row.parsed_category,
                "parsed_vendor": row.parsed_vendor,
                "validation_errors": row.validation_errors or [],
                "data_quality_issues": row.data_quality_issues or [],
                "raw_data": row.raw_data,
            }
        )

    return RowDetailsResponse(
        rows=row_responses,
        total_rows=result["total_rows"],
        page=result["page"],
        page_size=result["page_size"],
        total_pages=result["total_pages"],
    )


@router.get(
    "/schema-mapping-report/{tenant_id}", response_model=SchemaMappingReportResponse
)
async def get_schema_mapping_report(
    tenant_id: int,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> SchemaMappingReportResponse:
    """
    Get comprehensive schema mapping report showing discovered categories and mappings.

    This report shows:
    - All categories discovered from uploaded files
    - Unified category mappings
    - Bidirectional mapping relationships
    - Mapping confidence and unmapped categories
    """
    current_user, tenant = user_tenant

    # Verify access
    if tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    service = OnboardingService(conn, vertex_client)
    report_data = await service.get_schema_mapping_report(tenant_id)

    return SchemaMappingReportResponse(**report_data)


@router.get("/recent-processing-reports", response_model=List[ProcessingReportSummary])
async def get_recent_processing_reports(
    limit: int = 10,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> List[ProcessingReportSummary]:
    """
    Get recent file processing reports for the current tenant.

    Returns a summary of recent file uploads with key metrics.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    reports = await service.get_recent_processing_reports(tenant.id, limit)

    summaries = []
    for report in reports:
        success_rate = (
            (report.successful_rows / report.total_rows * 100)
            if report.total_rows > 0
            else 0.0
        )

        summaries.append(
            ProcessingReportSummary(
                upload_id=report.upload_id,
                file_name=report.file_name,
                status=report.status,
                total_rows=report.total_rows,
                success_rate=success_rate,
                data_quality_score=report.data_quality_score or 0.0,
                processing_duration_seconds=report.processing_duration_seconds,
                created_at=report.created_at,
            )
        )

    return summaries


@router.post("/start-zero", response_model=OnboardingStatus)
async def start_zero_onboarding(
    request: ZeroOnboardingStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start M1 zero-onboarding process - immediate production ready."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Initialize M1 zero-onboarding record
    await service.initialize_zero_onboarding(tenant_id)

    return await service.get_onboarding_status(tenant_id)


@router.post("/start-schema-only", response_model=OnboardingStatus)
async def start_schema_only_onboarding(
    request: SchemaOnlyStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start M3 schema-only onboarding process."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Initialize M3 schema-only onboarding record
    await service.initialize_schema_only_onboarding(tenant_id)

    return await service.get_onboarding_status(tenant_id)


@router.post("/import-schema")
async def import_category_schema(
    file: UploadFile = File(...),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """Import category hierarchy for M3 schema-only onboarding."""
    
    # Validate file format
    if not file.filename or not file.filename.endswith(('.xlsx', '.csv', '.json')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Schema file must be Excel (.xlsx), CSV (.csv), or JSON (.json)"
        )

    service = OnboardingService(conn, vertex_client)

    # For now, just mark as schema imported
    # TODO: Implement actual schema parsing and storage
    await service.import_category_schema(tenant_id, file.filename)

    return {"message": f"Category schema imported from {file.filename}"}
