"""
Data models for detailed file processing reports during onboarding.

These models track row-by-row processing details, column statistics,
and schema discovery results to provide comprehensive reporting.

Migrated from SQLAlchemy to Pydantic models with asyncpg support.
"""

from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class FileProcessingReport(BaseModel):
    """Overall processing report for an uploaded file."""

    id: str = Field(..., description="UUID primary key")
    upload_id: Optional[str] = Field(None, description="Associated upload UUID")
    tenant_id: int = Field(..., description="Tenant ID")

    # File metadata
    file_name: str = Field(..., description="Original filename")
    file_size_bytes: Optional[int] = Field(None, description="File size in bytes")
    total_rows: int = Field(..., description="Total rows in file")
    total_columns: int = Field(..., description="Total columns in file")

    # Processing results
    successful_rows: int = Field(default=0, description="Successfully processed rows")
    failed_rows: int = Field(default=0, description="Failed to process rows")
    skipped_rows: int = Field(default=0, description="Skipped rows")

    # Processing metadata
    processing_started_at: datetime = Field(..., description="When processing started")
    processing_completed_at: Optional[datetime] = Field(
        None, description="When processing completed"
    )
    processing_duration_seconds: Optional[float] = Field(
        None, description="Processing duration"
    )
    date_format_detected: Optional[str] = Field(
        None, description="Detected date format"
    )

    # Column mapping results
    column_mapping_confidence: Optional[Dict[str, float]] = Field(
        None, description="Column confidence scores"
    )
    mapped_columns: Optional[List[str]] = Field(
        None, description="Successfully mapped columns"
    )
    unmapped_columns: Optional[List[str]] = Field(None, description="Unmapped columns")

    # Data quality metrics
    data_quality_score: Optional[float] = Field(
        None, description="Quality score 0.0-1.0"
    )
    validation_errors: Optional[List[str]] = Field(
        None, description="Validation error summaries"
    )
    warnings: Optional[List[str]] = Field(None, description="Warning messages")

    # Schema discovery results
    categories_discovered: Optional[List[str]] = Field(
        None, description="Unique categories found"
    )
    category_count: int = Field(default=0, description="Number of categories")
    schema_confidence: Optional[float] = Field(
        None, description="Schema interpretation confidence"
    )

    # Status
    status: str = Field(..., description="processing, completed, failed")
    error_message: Optional[str] = Field(None, description="Error message if failed")

    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class RowProcessingDetail(BaseModel):
    """Detailed processing results for individual rows."""

    id: str = Field(..., description="UUID primary key")
    report_id: str = Field(..., description="Foreign key to FileProcessingReport")

    # Row identification
    row_number: int = Field(..., description="Row number in file")
    original_data: Optional[Dict] = Field(None, description="Original row data as dict")

    # Processing status
    status: str = Field(..., description="success, failed, skipped")

    # Parsed data
    parsed_date: Optional[datetime] = Field(None, description="Parsed date value")
    parsed_amount: Optional[float] = Field(None, description="Parsed amount value")
    parsed_description: Optional[str] = Field(None, description="Parsed description")
    parsed_category: Optional[str] = Field(None, description="Parsed category")
    parsed_vendor: Optional[str] = Field(None, description="Parsed vendor")

    # Validation results
    validation_errors: Optional[List[str]] = Field(
        None, description="Validation errors for this row"
    )
    data_quality_issues: Optional[List[str]] = Field(None, description="Quality issues")

    # Processing metadata
    date_parsing_attempted: bool = Field(default=False)
    date_parsing_format: Optional[str] = Field(None)
    amount_parsing_attempted: bool = Field(default=False)
    category_mapping_confidence: Optional[float] = Field(None)
    processing_time_ms: Optional[int] = Field(
        default=0, description="Processing time in milliseconds"
    )

    created_at: datetime = Field(default_factory=datetime.utcnow)


class ColumnStatistic(BaseModel):
    """Statistics for individual columns in the uploaded file."""

    id: str = Field(..., description="UUID primary key")
    report_id: str = Field(..., description="Foreign key to FileProcessingReport")

    # Column identification
    column_name: str = Field(..., description="Column name from file")
    column_index: int = Field(..., description="Column position")
    mapped_field: Optional[str] = Field(
        None, description="What this column was mapped to"
    )

    # Data statistics
    total_values: int = Field(..., description="Total number of values")
    non_null_values: int = Field(..., description="Non-null values count")
    null_values: int = Field(..., description="Null values count")
    unique_values: int = Field(..., description="Unique values count")

    # Data type analysis
    detected_type: Optional[str] = Field(
        None, description="string, number, date, boolean, mixed"
    )
    type_consistency: Optional[float] = Field(
        None, description="Type consistency 0.0-1.0"
    )

    # Value distribution
    min_value: Optional[str] = Field(None, description="Minimum value")
    max_value: Optional[str] = Field(None, description="Maximum value")
    average_value: Optional[float] = Field(
        None, description="Average for numeric columns"
    )
    most_common_values: Optional[List[tuple]] = Field(
        None, description="(value, count) tuples"
    )

    # Data quality
    empty_string_count: int = Field(default=0)
    invalid_format_count: int = Field(default=0)
    outlier_count: int = Field(default=0)

    # Mapping quality
    mapping_confidence: Optional[float] = Field(
        None, description="Mapping confidence 0.0-1.0"
    )
    mapping_method: Optional[str] = Field(None, description="ai, pattern, exact")

    created_at: datetime = Field(default_factory=datetime.utcnow)


# Helper functions for database operations
async def create_file_processing_report(db, report: FileProcessingReport) -> str:
    """Create a new file processing report in the database."""
    sql = """
        INSERT INTO file_processing_reports (
            id, upload_id, tenant_id, file_name, file_size_bytes, total_rows, total_columns,
            successful_rows, failed_rows, skipped_rows, processing_started_at, 
            processing_completed_at, processing_duration_seconds, date_format_detected,
            column_mapping_confidence, mapped_columns, unmapped_columns,
            data_quality_score, validation_errors, warnings, categories_discovered,
            category_count, schema_confidence, status, error_message, created_at, updated_at
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17,
            $18, $19, $20, $21, $22, $23, $24, $25, $26, $27
        )
    """

    await db.execute(
        sql,
        report.id,
        report.upload_id,
        report.tenant_id,
        report.file_name,
        report.file_size_bytes,
        report.total_rows,
        report.total_columns,
        report.successful_rows,
        report.failed_rows,
        report.skipped_rows,
        report.processing_started_at,
        report.processing_completed_at,
        report.processing_duration_seconds,
        report.date_format_detected,
        report.column_mapping_confidence,
        report.mapped_columns,
        report.unmapped_columns,
        report.data_quality_score,
        report.validation_errors,
        report.warnings,
        report.categories_discovered,
        report.category_count,
        report.schema_confidence,
        report.status,
        report.error_message,
        report.created_at,
        report.updated_at,
    )
    return report.id


async def get_file_processing_report(
    db, report_id: str
) -> Optional[FileProcessingReport]:
    """Get a file processing report by ID."""
    sql = "SELECT * FROM file_processing_reports WHERE id = $1"
    row = await db.fetchrow(sql, report_id)
    return FileProcessingReport.model_validate(dict(row)) if row else None


async def update_file_processing_report(db, report_id: str, updates: Dict) -> bool:
    """Update a file processing report."""
    set_clauses = []
    params = []
    param_count = 0

    for field, value in updates.items():
        param_count += 1
        set_clauses.append(f"{field} = ${param_count}")
        params.append(value)

    if not set_clauses:
        return False

    param_count += 1
    params.append(report_id)

    sql = f"""
        UPDATE file_processing_reports 
        SET {", ".join(set_clauses)}, updated_at = NOW()
        WHERE id = ${param_count}
    """

    result = await db.execute(sql, *params)
    return result == "UPDATE 1"
