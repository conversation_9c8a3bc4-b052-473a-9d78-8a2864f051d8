name: "zero_onboarding_categorization"
description: "Prompt for zero-onboarding categorization with business intelligence"
version: "1.0"
model_params:
  temperature: 0.1
  max_output_tokens: 500

system_instruction: |
  You are an expert business accounting AI with deep knowledge of how real companies categorize expenses.

user_template: |
  TRANSACTION TO CATEGORIZE:
  Description: {description}
  Amount: ${amount}
  Type: {transaction_type}

  ZERO-ONBOARDING CONTEXT:
  - This is a new company with no historical transaction data
  - Create realistic 3-4 level business expense hierarchies 
  - Use professional accounting terminology, NOT oversimplified labels
  - Follow standard business practices and accounting principles

  BUSINESS INTELLIGENCE ANALYSIS:
  1. Merchant/Vendor Analysis: Extract business type from company names
  2. Service Type Detection: Identify consulting, software, marketing, etc.
  3. Amount Pattern Analysis: Consider if this is recurring, advance payment, etc.
  4. Industry Context: Apply business expense categorization best practices

  REALISTIC HIERARCHY EXAMPLES:
  - Consulting services: "Professional Services > Consulting Services > Product Development > Advance Payments"
  - Software subscription: "Technology > Software & Licenses > Productivity Tools > Monthly Subscriptions" 
  - Marketing services: "Marketing & Advertising > Digital Marketing > Brand Development > Design Services"
  - Office expenses: "Operating Expenses > Office & Administrative > Supplies & Equipment"

  CREATE REALISTIC BUSINESS HIERARCHY:
  Generate a 3-4 level category hierarchy that a real business would use.
  Consider the specific nature of this transaction and create appropriate parent-child relationships.

  RESPONSE FORMAT (JSON):
  {{
      "category": "Final specific category name",
      "parent_category": "Immediate parent category",
      "full_hierarchy": "Top Level > Level 2 > Level 3 > Final Category",
      "confidence": 0.85,
      "reasoning": "Business logic explanation including merchant analysis and expense type",
      "business_context": "Type of business expense and why this hierarchy makes sense",
      "hierarchy_levels": 4
  }}

  IMPORTANT: Create professional business categories, not generic labels like "Miscellaneous" or "Other".

required_fields:
  - description
  - amount
  - transaction_type

response_format: "json"