"""
Parallel Categorization Agent - Advanced Concurrent Processing

This module implements a specialized FinancialParallelAgent for high-throughput
transaction categorization using:
- ADK ParallelAgent for concurrent processing
- Proper VertexAI Tool/ChatSession architecture
- Advanced batch processing with error handling
- A2A protocol compliance for agent coordination
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List

# ADK imports for parallel agent patterns
from google.adk.tools import (
    FunctionTool,
    LongRunningFunctionTool,
    load_memory,
    preload_memory,
)

# Advanced VertexAI imports for proper Tool/ChatSession architecture
from vertexai.generative_models import (
    ChatSession,
    FunctionDeclaration,
    GenerativeModel,
    Tool,
    ToolConfig,
)

# Advanced agent patterns
from ...shared.ai.agent_patterns import (
    AgentCapabilities,
    AgentDiscoveryCard,
    FinancialParallelAgent,
    global_agent_orchestrator,
)
from ...shared.utils.rate_limiter import gemini_rate_limiter

logger = logging.getLogger(__name__)


class ParallelCategorizationAgent(FinancialParallelAgent):
    """
    Specialized parallel agent for high-throughput transaction categorization.

    Features:
    - Concurrent processing of transaction batches
    - Proper VertexAI Tool/ChatSession persistence
    - Advanced error handling and recovery
    - A2A protocol agent coordination
    - Memory sharing across parallel tasks
    """

    def __init__(
        self,
        max_concurrent_tasks: int = 25,
        batch_size: int = 10,
        enable_chat_sessions: bool = True,
        **kwargs,
    ):
        """Initialize parallel categorization agent with advanced VertexAI architecture."""

        # Create categorization-specific tools using proper VertexAI Tool architecture
        categorization_tools = self._create_categorization_tools()

        super().__init__(
            name="giki_ai_parallel_categorization_agent",
            description="High-throughput parallel transaction categorization with advanced VertexAI architecture",
            max_concurrent_tasks=max_concurrent_tasks,
            task_timeout_seconds=600,  # 10 minutes for large batches
            custom_tools=categorization_tools,
            **kwargs,
        )

        self.batch_size = batch_size
        self.enable_chat_sessions = enable_chat_sessions
        self._chat_sessions: Dict[str, ChatSession] = {}
        self._categorization_model = GenerativeModel(
            model_name="gemini-2.0-flash-001",
            tools=self._create_vertex_tools(),
            tool_config=self._create_tool_config(),
        )

        # Register with A2A protocol orchestrator
        self._register_with_orchestrator()

        logger.info(
            f"ParallelCategorizationAgent initialized with {max_concurrent_tasks} max concurrent tasks"
        )

    def _create_categorization_tools(self) -> List[FunctionTool]:
        """Create categorization-specific tools for parallel processing."""

        tools = []

        # Parallel batch categorization tool
        async def categorize_batch_parallel_tool_function(
            transactions: List[Dict[str, Any]],
            tenant_id: int,
            use_rag: bool = True,
            **kwargs,
        ) -> Dict[str, Any]:
            """Categorize a batch of transactions in parallel."""
            return await self._categorize_batch_parallel(
                transactions, tenant_id, use_rag
            )

        tools.append(LongRunningFunctionTool(categorize_batch_parallel_tool_function))

        # Parallel RAG context retrieval tool
        async def get_parallel_rag_context_tool_function(
            transaction_descriptions: List[str],
            tenant_id: int,
            top_n: int = 3,
            **kwargs,
        ) -> Dict[str, Any]:
            """Retrieve RAG context for multiple transactions in parallel."""
            return await self._get_parallel_rag_context(
                transaction_descriptions, tenant_id, top_n
            )

        tools.append(LongRunningFunctionTool(get_parallel_rag_context_tool_function))

        # Performance monitoring tool
        async def get_categorization_performance_tool_function(
            session_id: str, **kwargs
        ) -> Dict[str, Any]:
            """Get performance metrics for current categorization session."""
            return await self._get_performance_metrics(session_id)

        tools.append(FunctionTool(get_categorization_performance_tool_function))

        return tools

    def _create_vertex_tools(self) -> List[Tool]:
        """Create proper VertexAI Tool instances for advanced AI capabilities."""

        # Define categorization function with proper schema
        categorize_function = FunctionDeclaration(
            name="categorize_transactions_batch",
            description="Categorize a batch of financial transactions using AI with confidence scoring",
            parameters={
                "type": "object",
                "properties": {
                    "transactions": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "description": {"type": "string"},
                                "amount": {"type": "number"},
                                "type": {"type": "string"},
                            },
                        },
                    },
                    "tenant_categories": {"type": "array", "items": {"type": "string"}},
                },
                "required": ["transactions", "tenant_categories"],
            },
        )

        # Define RAG context function
        rag_context_function = FunctionDeclaration(
            name="get_rag_context_for_transactions",
            description="Retrieve similar historical transactions using vector search",
            parameters={
                "type": "object",
                "properties": {
                    "transaction_descriptions": {
                        "type": "array",
                        "items": {"type": "string"},
                    },
                    "similarity_threshold": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                    },
                },
                "required": ["transaction_descriptions"],
            },
        )

        return [Tool(function_declarations=[categorize_function, rag_context_function])]

    def _create_tool_config(self) -> ToolConfig:
        """Create ToolConfig for advanced tool calling control."""
        return ToolConfig(
            function_calling_config=ToolConfig.FunctionCallingConfig(
                mode=ToolConfig.FunctionCallingConfig.Mode.AUTO,
                allowed_function_names=[
                    "categorize_transactions_batch",
                    "get_rag_context_for_transactions",
                ],
            )
        )

    async def categorize_transactions_batch(
        self,
        transactions: List[Dict[str, Any]],
        tenant_id: int,
        use_chat_session: bool = True,
    ) -> Dict[str, Any]:
        """
        Main entry point for parallel batch categorization.

        Args:
            transactions: List of transaction dictionaries
            tenant_id: Tenant ID for multi-tenant isolation
            use_chat_session: Whether to use persistent ChatSession

        Returns:
            Categorization results with performance metrics
        """

        session_id = f"batch_{tenant_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Initialize shared context for all parallel tasks
        await preload_memory(
            f"categorization_session_{session_id}",
            {
                "session_id": session_id,
                "tenant_id": tenant_id,
                "total_transactions": len(transactions),
                "start_time": datetime.now().isoformat(),
                "use_chat_session": use_chat_session,
            },
        )

        try:
            # Split transactions into optimal batch sizes for parallel processing
            batches = [
                transactions[i : i + self.batch_size]
                for i in range(0, len(transactions), self.batch_size)
            ]

            logger.info(
                f"Processing {len(transactions)} transactions in {len(batches)} parallel batches"
            )

            # Process batches in parallel using ADK ParallelAgent capabilities
            batch_results = await self.process_batch(
                tasks=[
                    {
                        "id": f"batch_{i}",
                        "transactions": batch,
                        "tenant_id": tenant_id,
                        "session_id": session_id,
                        "use_chat_session": use_chat_session,
                    }
                    for i, batch in enumerate(batches)
                ],
                tenant_id=tenant_id,
            )

            # Aggregate results
            all_results = []
            total_errors = 0
            total_processing_time = 0

            for batch_result in batch_results:
                if "error" not in batch_result:
                    all_results.extend(batch_result.get("categorizations", []))
                    total_processing_time += batch_result.get("processing_time_ms", 0)
                else:
                    total_errors += 1
                    logger.error(f"Batch processing error: {batch_result['error']}")

            # Calculate performance metrics
            success_rate = (len(all_results) / len(transactions)) if transactions else 0
            avg_confidence = (
                sum(r.get("confidence", 0) for r in all_results) / len(all_results)
                if all_results
                else 0
            )

            result = {
                "success": True,
                "session_id": session_id,
                "total_transactions": len(transactions),
                "successful_categorizations": len(all_results),
                "failed_batches": total_errors,
                "success_rate": success_rate,
                "average_confidence": avg_confidence,
                "total_processing_time_ms": total_processing_time,
                "categorizations": all_results,
                "performance_metrics": {
                    "batches_processed": len(batches),
                    "parallel_efficiency": success_rate,
                    "avg_batch_time_ms": total_processing_time / len(batches)
                    if batches
                    else 0,
                },
            }

            # Update shared memory with final results
            await load_memory(
                f"categorization_session_{session_id}",
                {"final_results": result, "end_time": datetime.now().isoformat()},
            )

            return result

        except Exception as e:
            logger.error(f"Parallel categorization failed: {e}")
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "partial_results": [],
            }

    async def _categorize_batch_parallel(
        self, transactions: List[Dict[str, Any]], tenant_id: int, use_rag: bool = True
    ) -> Dict[str, Any]:
        """Process a single batch using advanced VertexAI ChatSession."""

        batch_id = (
            f"batch_{tenant_id}_{len(transactions)}_{datetime.now().strftime('%H%M%S')}"
        )
        start_time = datetime.now()

        try:
            # Get or create persistent ChatSession for this tenant
            chat_session = await self._get_chat_session(tenant_id)

            # Prepare batch context
            transaction_descriptions = [t.get("description", "") for t in transactions]

            # Get RAG context if enabled
            rag_context = ""
            if use_rag:
                rag_result = await self._get_parallel_rag_context(
                    transaction_descriptions, tenant_id, 3
                )
                if rag_result.get("success"):
                    rag_context = rag_result.get("context", "")

            # Build sophisticated prompt using ChatSession
            prompt = self._build_batch_categorization_prompt(
                transactions, rag_context, tenant_id
            )

            # Use rate limiter for API calls
            async with gemini_rate_limiter:
                # Send message to ChatSession for context persistence
                response = await chat_session.send_message_async(prompt)

                # Parse structured response
                categorizations = self._parse_categorization_response(
                    response.text, transactions
                )

                processing_time = (datetime.now() - start_time).total_seconds() * 1000

                return {
                    "success": True,
                    "batch_id": batch_id,
                    "categorizations": categorizations,
                    "processing_time_ms": processing_time,
                    "rag_used": use_rag and bool(rag_context),
                    "session_persistent": True,
                }

        except Exception as e:
            logger.error(f"Batch categorization failed for batch {batch_id}: {e}")
            return {
                "success": False,
                "batch_id": batch_id,
                "error": str(e),
                "processing_time_ms": (datetime.now() - start_time).total_seconds()
                * 1000,
            }

    async def _get_chat_session(self, tenant_id: int) -> ChatSession:
        """Get or create persistent ChatSession for tenant."""

        session_key = f"tenant_{tenant_id}"

        if session_key not in self._chat_sessions:
            # Create new ChatSession with proper history initialization
            chat_session = self._categorization_model.start_chat(history=[])

            # Initialize with tenant-specific context
            init_message = f"""
            You are a financial categorization AI for tenant {tenant_id}.
            You will process batches of transactions for categorization.
            Maintain context and learning patterns across all batches.
            Always provide structured JSON responses with category and confidence.
            """

            await chat_session.send_message_async(init_message)
            self._chat_sessions[session_key] = chat_session

            logger.info(f"Created new ChatSession for tenant {tenant_id}")

        return self._chat_sessions[session_key]

    def _build_batch_categorization_prompt(
        self, transactions: List[Dict[str, Any]], rag_context: str, tenant_id: int
    ) -> str:
        """Build sophisticated batch categorization prompt."""

        prompt = f"""
        BATCH CATEGORIZATION REQUEST for Tenant {tenant_id}
        
        Categorize the following {len(transactions)} transactions with high accuracy.
        
        {rag_context}
        
        TRANSACTIONS TO CATEGORIZE:
        """

        for i, txn in enumerate(transactions, 1):
            prompt += f"""
        {i}. Description: {txn.get("description", "N/A")}
           Amount: {txn.get("amount", "N/A")}
           Type: {txn.get("type", "N/A")}
        """

        prompt += """
        
        REQUIREMENTS:
        - Return structured JSON array
        - Each item: {"index": N, "category": "category_name", "confidence": 0.XX, "reasoning": "brief_reason"}
        - Use context from similar transactions when available
        - Confidence scores between 0.0 and 1.0
        - Brief but clear reasoning for each categorization
        
        JSON Response:
        """

        return prompt

    def _parse_categorization_response(
        self, response_text: str, transactions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Parse AI response into structured categorization results."""

        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r"\[.*\]", response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                categorizations = json.loads(json_str)

                # Validate and enhance results
                enhanced_results = []
                for i, cat in enumerate(categorizations):
                    if i < len(transactions):
                        enhanced_results.append(
                            {
                                "transaction_index": i,
                                "transaction_description": transactions[i].get(
                                    "description", ""
                                ),
                                "category": cat.get("category", "Unknown"),
                                "confidence": float(cat.get("confidence", 0.5)),
                                "reasoning": cat.get("reasoning", "AI categorization"),
                                "method": "parallel_ai_categorization",
                                "timestamp": datetime.now().isoformat(),
                            }
                        )

                return enhanced_results

        except Exception as e:
            logger.error(f"Failed to parse categorization response: {e}")

        # Fallback to basic categorization
        return [
            {
                "transaction_index": i,
                "transaction_description": txn.get("description", ""),
                "category": "Unknown",
                "confidence": 0.3,
                "reasoning": "Parse error - basic categorization",
                "method": "fallback",
                "error": "Response parsing failed",
            }
            for i, txn in enumerate(transactions)
        ]

    async def _get_parallel_rag_context(
        self, transaction_descriptions: List[str], tenant_id: int, top_n: int = 3
    ) -> Dict[str, Any]:
        """Retrieve RAG context for multiple transactions in parallel."""

        try:
            # Import RAG function from main categorization agent
            from .agent import get_vector_rag_context_tool_function

            # Process RAG lookups in parallel for efficiency
            rag_tasks = [
                get_vector_rag_context_tool_function(
                    transaction_description=desc,
                    tenant_id=tenant_id,
                    top_n=top_n,
                    similarity_threshold=0.6,
                )
                for desc in transaction_descriptions
            ]

            rag_results = await asyncio.gather(*rag_tasks, return_exceptions=True)

            # Aggregate successful results
            all_context = []
            successful_lookups = 0

            for result in rag_results:
                if isinstance(result, dict) and result.get("retrieved_entries"):
                    all_context.extend(result["retrieved_entries"])
                    successful_lookups += 1

            # Build aggregated context
            if all_context:
                context_text = "SIMILAR HISTORICAL TRANSACTIONS:\n"
                for entry in all_context[: top_n * 3]:  # Limit total context
                    context_text += f"- {entry.get('description', '')} → {entry.get('category', '')} (similarity: {entry.get('similarity_score', 0):.2f})\n"

                return {
                    "success": True,
                    "context": context_text,
                    "total_similar_transactions": len(all_context),
                    "successful_lookups": successful_lookups,
                }

            return {
                "success": False,
                "context": "",
                "message": "No similar transactions found",
            }

        except Exception as e:
            logger.error(f"Parallel RAG context retrieval failed: {e}")
            return {"success": False, "context": "", "error": str(e)}

    async def _get_performance_metrics(self, session_id: str) -> Dict[str, Any]:
        """Get performance metrics for categorization session."""

        try:
            # Load session memory
            session_memory = await load_memory(
                f"categorization_session_{session_id}", {}
            )

            if session_memory:
                return {
                    "session_id": session_id,
                    "metrics": session_memory.get("final_results", {}),
                    "chat_sessions_active": len(self._chat_sessions),
                    "memory_loaded": True,
                }

            return {
                "session_id": session_id,
                "metrics": {},
                "chat_sessions_active": len(self._chat_sessions),
                "memory_loaded": False,
            }

        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return {"session_id": session_id, "error": str(e)}

    def _register_with_orchestrator(self):
        """Register this parallel agent with A2A protocol orchestrator."""

        capabilities = AgentCapabilities(
            streaming=True,
            push_notifications=True,
            multimodal=True,
            financial_processing=True,
            real_time_data=True,
            vector_search=True,
            code_execution=False,
        )

        discovery_card = AgentDiscoveryCard(
            agent_id="giki_ai_parallel_categorization_agent",
            name="GIKI AI Parallel Categorization Agent",
            description="High-throughput parallel transaction categorization with advanced VertexAI architecture",
            version="1.3.0",
            capabilities=capabilities,
            financial_domains=[
                "categorization",
                "batch_processing",
                "parallel_processing",
            ],
            supported_workflows=[
                "batch_categorization",
                "high_throughput_processing",
                "concurrent_categorization",
            ],
            data_requirements={
                "tenant_id": "required",
                "transactions": "required_array",
                "batch_size": "optional",
            },
            avg_response_time_ms=300,  # Slightly higher due to batch processing
            max_concurrent_tasks=25,
            memory_persistence=True,
        )

        # Register with global orchestrator
        global_agent_orchestrator.register_agent(self, discovery_card)

        logger.info(
            "Registered ParallelCategorizationAgent with A2A protocol orchestrator"
        )


# Factory function for creating parallel categorization agents
async def create_parallel_categorization_agent(
    max_concurrent_tasks: int = 25, batch_size: int = 10, **kwargs
) -> ParallelCategorizationAgent:
    """
    Factory function to create and initialize parallel categorization agent.

    Returns:
        Configured ParallelCategorizationAgent ready for batch processing
    """

    agent = ParallelCategorizationAgent(
        max_concurrent_tasks=max_concurrent_tasks, batch_size=batch_size, **kwargs
    )

    logger.info(
        f"Created ParallelCategorizationAgent with {max_concurrent_tasks} max concurrent tasks"
    )
    return agent
