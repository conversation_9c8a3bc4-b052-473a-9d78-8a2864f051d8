"""
Categories router for category management endpoints.
"""

import logging

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .models import Category as CategoryModel
from .schemas import Category, CategoryCreate, CategoryTree, CategoryUpdate
from .service import CategoryService

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Categories"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized"},
    },
)


async def get_category_service(
    conn: Connection = Depends(get_db_session),
) -> CategoryService:
    """Dependency to get CategoryService instance."""
    return CategoryService(conn)


async def get_multilevel_category_service(
    conn: Connection = Depends(get_db_session),
) -> CategoryService:
    """Dependency to get CategoryService instance with GL code support."""
    return CategoryService(conn)


@router.get("", response_model=list[CategoryTree])
async def read_tenant_categories(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    """
    Read all categories for the current tenant.
    OPTIMIZED: Added performance monitoring to track slow queries.
    """
    logger.info(
        f"Reading categories for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        import time

        start_time = time.time()

        categories = await category_service.get_categories(tenant_id_int)

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Categories query executed in {execution_time:.2f}ms, returned {len(categories)} categories"
        )

        # Warn if query is slow (750ms threshold based on logs)
        if execution_time > 200:  # 200ms threshold to meet performance requirements
            logger.warning(
                f"Slow query detected: read_tenant_categories took {execution_time:.2f}ms (target: <200ms)"
            )

        return categories
    except Exception as e:
        logger.error(
            f"Error retrieving categories for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving categories: {str(e)}",
        )


@router.get("/{category_id}", response_model=CategoryTree)
async def read_category(
    category_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Reading category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        category = await category_service.get_category_by_id(category_id, tenant_id_int)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
        return category
    except Exception as e:
        logger.error(f"Error reading category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading category: {str(e)}",
        )


@router.post("", response_model=Category, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_in: CategoryCreate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Creating category '{category_in.name}' for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Pass the CategoryCreate object directly to the service
        created_category = await category_service.create_category(
            category_data=category_in,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
        )
        return created_category
    except Exception as e:
        logger.error(
            f"Error creating category '{category_in.name}' for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating category: {str(e)}",
        )


@router.put("/{category_id}", response_model=Category)
async def update_category(
    category_id: int,
    category_in: CategoryUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Updating category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Pass the CategoryUpdate object directly to the service
        updated_category = await category_service.update_category(
            category_id=category_id,
            update_data=category_in,
            user_id=current_user.id,
        )
        if not updated_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
        return updated_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error updating category {category_id} for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating category: {str(e)}",
        )


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: int,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Deleting category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Delete category without reassigning (force_delete=True for now)
        deleted = await category_service.delete_category(
            category_id=category_id, force_delete=True
        )
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
    except ValueError as e:
        # Handle children constraint error
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error deleting category {category_id} for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting category: {str(e)}",
        )


async def _build_category_tree(
    category_model: CategoryModel,
    conn: Connection,
) -> CategoryTree:
    # Query children using raw SQL
    query = """
        SELECT id, name, path, parent_id, tenant_id, gl_code, gl_account_name, 
               gl_account_type, level, created_at, updated_at
        FROM categories
        WHERE parent_id = $1
        ORDER BY name
    """
    rows = await conn.fetch(query, category_model.id)

    child_trees = []
    for row in rows:
        child_model = CategoryModel(**dict(row))
        child_tree = await _build_category_tree(child_model, conn)
        child_trees.append(child_tree)

    return CategoryTree(
        id=category_model.id,
        name=category_model.name,
        path=category_model.path,
        parent_id=category_model.parent_id,
        tenant_id=category_model.tenant_id or 0,  # Handle nullable tenant_id
        children=child_trees,
    )


async def _update_category_paths(
    category_model: CategoryModel, conn: Connection
) -> None:
    if category_model.parent_id is not None:
        # Get parent using raw SQL
        parent_query = """
            SELECT id, name, path, parent_id, tenant_id 
            FROM categories 
            WHERE id = $1
        """
        parent_row = await conn.fetchrow(parent_query, category_model.parent_id)
        if parent_row:
            parent_path = parent_row["path"]
            category_model.path = f"{parent_path} - {category_model.name}"
        else:
            logger.warning(
                f"Parent category ID {category_model.parent_id} not found for category ID {category_model.id} during path update. Path may be incorrect."
            )
            category_model.path = category_model.name
    else:
        category_model.path = category_model.name

    # Update the category path in database
    update_query = """
        UPDATE categories 
        SET path = $1, updated_at = NOW() 
        WHERE id = $2
    """
    await conn.execute(update_query, category_model.path, category_model.id)

    # Get children using raw SQL
    children_query = """
        SELECT id, name, path, parent_id, tenant_id, gl_code, gl_account_name, 
               gl_account_type, level, created_at, updated_at
        FROM categories 
        WHERE parent_id = $1
    """
    children_rows = await conn.fetch(children_query, category_model.id)

    for child_row in children_rows:
        child_model = CategoryModel(**dict(child_row))
        await _update_category_paths(child_model, conn)


# ============================================================================
# GL CODE MANAGEMENT ENDPOINTS (Multilevel Category System)
# ============================================================================

from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class GLCodeUpdate(BaseModel):
    """Schema for updating GL code mapping."""

    gl_code: Optional[str] = None
    gl_account_name: Optional[str] = None
    gl_account_type: Optional[str] = None


class BulkGLUpdate(BaseModel):
    """Schema for bulk GL code updates."""

    mappings: List[Dict[str, Any]]


class GLMappingExport(BaseModel):
    """Schema for GL mapping export request."""

    format_type: str = "csv"


class GLCodeValidationRequest(BaseModel):
    """Schema for GL code validation request."""

    gl_code: str


class GLCodeSuggestionRequest(BaseModel):
    """Schema for GL code suggestion request."""

    category_name: str
    category_path: str
    account_type: Optional[str] = None


@router.post("/validate-gl-code")
async def validate_gl_code(
    request: GLCodeValidationRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Validate GL code format and check for duplicates.

    Performs format validation and duplicate checking for GL codes
    within the tenant's scope.
    """
    logger.info(
        f"Validating GL code for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        validation_result = await category_service.validate_gl_code(
            request.gl_code, tenant_id_int
        )
        return validation_result
    except Exception as e:
        logger.error(f"Error validating GL code: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating GL code: {str(e)}",
        )


@router.post("/suggest-gl-codes")
async def suggest_gl_codes(
    request: GLCodeSuggestionRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Get AI-powered GL code suggestions for a category.

    Uses AI to suggest appropriate GL codes based on category name,
    path in hierarchy, and account type.
    """
    logger.info(
        f"Getting GL code suggestions for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        suggestions = await category_service.suggest_gl_codes(
            category_name=request.category_name,
            category_path=request.category_path,
            account_type=request.account_type,
            tenant_id=tenant_id_int,
        )
        return suggestions
    except Exception as e:
        logger.error(f"Error getting GL code suggestions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting GL code suggestions: {str(e)}",
        )


@router.put("/{category_id}/gl-mapping", response_model=Category)
async def update_category_gl_mapping(
    category_id: int,
    gl_update: GLCodeUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Update GL code mapping for a category.

    This endpoint supports the core business requirement that categories map to
    GL (General Ledger) codes for accounting integration.
    """
    logger.info(
        f"Updating GL mapping for category {category_id}, tenant: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        updated_category = await category_service.update_gl_code_mapping(
            category_id=category_id,
            gl_code=gl_update.gl_code,
            gl_account_name=gl_update.gl_account_name,
            gl_account_type=gl_update.gl_account_type,
            user_id=current_user.id,
        )
        return updated_category

    except Exception as e:
        logger.error(
            f"Error updating GL mapping for category {category_id}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating GL mapping: {str(e)}",
        )


@router.post("/bulk-gl-update", response_model=Dict[str, Any])
async def bulk_update_gl_mappings(
    bulk_update: BulkGLUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Bulk update GL code mappings for multiple categories.

    Allows customers to update GL codes and category structures via UI.
    """
    logger.info(
        f"Bulk updating GL mappings for {len(bulk_update.mappings)} categories, tenant: {tenant_id_int}"
    )
    try:
        result = await category_service.bulk_update_gl_mappings(
            mappings=bulk_update.mappings,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
        )
        return result

    except Exception as e:
        logger.error(f"Error in bulk GL mapping update: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating GL mappings: {str(e)}",
        )


@router.get("/gl-mappings/export")
async def export_gl_mappings(
    format_type: str = "csv",
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Export GL code mappings for accounting software integration.

    Supports integration with QuickBooks, SAP, Xero, and other accounting systems.
    """
    logger.info(
        f"Exporting GL mappings in {format_type} format for tenant: {tenant_id_int}"
    )
    try:
        export_data = await category_service.export_gl_mappings(
            tenant_id=tenant_id_int, format_type=format_type
        )

        if format_type == "csv":
            from fastapi.responses import Response

            return Response(
                content=export_data,
                media_type="text/csv",
                headers={
                    "Content-Disposition": f"attachment; filename=gl_mappings_tenant_{tenant_id_int}.csv"
                },
            )
        else:
            return {"data": export_data, "format": format_type}

    except Exception as e:
        logger.error(f"Error exporting GL mappings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting GL mappings: {str(e)}",
        )


@router.post("/learn-from-onboarding", response_model=Dict[str, Any])
async def learn_categories_from_onboarding(
    transactions: List[Dict[str, Any]],
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Learn multilevel category structure from onboarding transaction data.

    This implements the core business requirement that categories are ALWAYS
    multilevel and learned from onboarding data for each tenant.
    """
    logger.info(
        f"Learning categories from {len(transactions)} onboarding transactions for tenant: {tenant_id_int}"
    )
    try:
        learning_result = await category_service.learn_categories_from_onboarding_data(
            transactions=transactions, tenant_id=tenant_id_int, user_id=current_user.id
        )
        return learning_result

    except Exception as e:
        logger.error(
            f"Error learning categories from onboarding data: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error learning categories: {str(e)}",
        )


@router.get("/hierarchy", response_model=Dict[str, Any])
async def get_category_hierarchy(
    include_usage_counts: bool = True,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Get complete multilevel category hierarchy for a tenant.

    Returns hierarchical tree structure with GL code information and usage statistics.
    """
    logger.info(f"Getting category hierarchy for tenant: {tenant_id_int}")
    try:
        hierarchy_tree = await category_service.get_category_hierarchy(
            tenant_id=tenant_id_int, include_usage_counts=include_usage_counts
        )

        return {
            "root_categories": hierarchy_tree.root_categories,
            "total_categories": hierarchy_tree.total_categories,
            "max_depth": hierarchy_tree.max_depth,
            "category_counts": hierarchy_tree.category_counts,
        }

    except Exception as e:
        logger.error(f"Error getting category hierarchy: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving category hierarchy: {str(e)}",
        )


@router.post("/create-multilevel-hierarchies")
async def create_multilevel_hierarchies(
    clear_existing: bool = False,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
    conn: Connection = Depends(get_db_session),
):
    """
    Create multilevel category hierarchies from customer transaction data.

    This endpoint:
    1. Loads customer transaction data from input files
    2. Analyzes original categorization patterns
    3. Detects hierarchical relationships
    4. Creates parent-child category structures
    5. Returns statistics on created hierarchies

    Args:
        clear_existing: Whether to clear existing categories first
    """
    try:
        logger.info(f"Creating multilevel hierarchies for tenant {tenant_id}")

        # Load customer transaction data from database
        # Query existing transactions for this tenant with original category labels
        query = """
            SELECT t.description, t.original_category_label as category, t.amount 
            FROM transactions t 
            WHERE t.tenant_id = $1 
            AND t.original_category_label IS NOT NULL
            AND t.original_category_label != ''
        """
        transactions = await conn.fetch(query, tenant_id)

        if not transactions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No customer transaction data found. Please upload data files first.",
            )

        # Convert to the expected format
        customer_transactions = []
        for transaction in transactions:
            if transaction["description"] and transaction["category"]:
                customer_transactions.append(
                    {
                        "description": str(transaction["description"]).strip(),
                        "category": str(transaction["category"]).strip(),
                        "amount": float(transaction["amount"])
                        if transaction["amount"]
                        else 0,
                        "source": "database",
                    }
                )

        logger.info(f"Total customer transactions loaded: {len(customer_transactions)}")

        # Create multilevel hierarchies
        result = (
            await category_service.create_multilevel_hierarchies_from_customer_data(
                tenant_id=tenant_id,
                customer_transactions=customer_transactions,
                clear_existing=clear_existing,
            )
        )

        return {
            "success": True,
            "message": f"Successfully created {result['total_categories_created']} categories",
            "statistics": {
                "total_categories": result["total_categories_created"],
                "parent_categories": result["parent_categories"],
                "child_categories": result["child_categories"],
                "hierarchies_detected": result["hierarchies_detected"],
                "unique_patterns": result["unique_patterns"],
                "data_source": "database_transactions",
                "total_transactions_analyzed": len(customer_transactions),
            },
            "categories_created": result["categories"][:20],  # First 20 for preview
        }

    except Exception as e:
        logger.error(f"Error creating multilevel hierarchies: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create multilevel hierarchies: {str(e)}",
        )
