"""
Vertex RAG Implementation using ADK VertexAiSearchTool

This module provides the get_vector_rag_context function used by RuntimeCategorizationService.
It bridges the gap between the ADK-based categorization agent and the runtime service.
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


async def get_vector_rag_context(
    corpus_name: str,
    query: str,
    amount: Optional[float] = None,
    date: Optional[datetime] = None,
    top_n: int = 5,
    similarity_threshold: float = 0.7,
) -> Optional[Dict[str, Any]]:
    """
    Get vector RAG context using ADK VertexAiSearchTool integration.

    This function is called by RuntimeCategorizationService to get similar
    transactions from the RAG corpus built during onboarding.

    Args:
        corpus_name: The RAG corpus name for the tenant
        query: Transaction description to search for
        amount: Transaction amount (optional)
        date: Transaction date (optional)
        top_n: Number of top results to return
        similarity_threshold: Minimum similarity score

    Returns:
        Dictionary with matches or None if no matches found
    """
    try:
        # Import the ADK-based function from the agent
        # Extract tenant_id from corpus_name
        # Format: projects/{tenant_id}/locations/us-central1/ragCorpora/tenant_{tenant_id}_corpus
        import re

        from .agent import get_vector_rag_context_tool_function

        tenant_match = re.search(r"tenant_(\d+)_corpus", corpus_name)
        if not tenant_match:
            logger.error(f"Could not extract tenant_id from corpus_name: {corpus_name}")
            return None

        tenant_id = int(tenant_match.group(1))

        # Enhance query with amount and date if provided
        enhanced_query = query
        if amount is not None:
            enhanced_query += f" amount:{amount}"
        if date is not None:
            enhanced_query += f" date:{date.strftime('%Y-%m-%d')}"

        logger.info(f"RAG lookup for tenant {tenant_id}: {enhanced_query[:50]}...")

        # Call the ADK-based RAG function
        rag_result = await get_vector_rag_context_tool_function(
            transaction_description=enhanced_query,
            tenant_id=tenant_id,
            top_n=top_n,
            similarity_threshold=similarity_threshold,
        )

        if not rag_result.get("rag_used") or not rag_result.get("retrieved_entries"):
            logger.info("No RAG matches found above threshold")
            return None

        # Convert to format expected by RuntimeCategorizationService
        matches = []
        for entry in rag_result["retrieved_entries"]:
            matches.append(
                {
                    "category": entry["category"],
                    "content": entry["content"],
                    "confidence": entry["similarity_score"],
                    "similarity_score": entry["similarity_score"],
                    "metadata": entry.get("metadata", {}),
                }
            )

        # Sort by confidence/similarity descending
        matches.sort(key=lambda x: x["confidence"], reverse=True)

        result = {
            "matches": matches,
            "total_matches": len(matches),
            "search_method": rag_result.get("search_method", "adk_vertex_search_tool"),
            "average_similarity": rag_result.get("average_similarity", 0.0),
            "corpus_name": corpus_name,
            "query": enhanced_query,
        }

        logger.info(
            f"✅ RAG returned {len(matches)} matches, avg similarity: {result['average_similarity']:.3f}"
        )
        return result

    except Exception as e:
        logger.error(f"❌ Vertex RAG failed: {e}")
        return None
