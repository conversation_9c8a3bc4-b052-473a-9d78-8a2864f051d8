"""
Enhanced Categorization Tools for Zero-Onboarding Support

These tools extend our existing categorization agent with the ability to:
1. Suggest new categories when no historical data exists
2. Create hierarchical category structures automatically
3. Learn from transaction patterns without onboarding data
"""

import logging
from typing import Any, Dict, List

import asyncpg
from google.adk.tools import FunctionTool

logger = logging.getLogger(__name__)


async def lookup_transaction_category_tool(
    db: asyncpg.Connection,
    transaction_description: str,
    transaction_amount: float,
    tenant_id: int = 1,
) -> Dict[str, Any]:
    """
    Primary categorization tool using database lookup for 100% financial reliability.

    This tool checks existing patterns in the database first (lookup-only approach).
    Only falls back to AI bootstrap for zero-onboarding scenarios.
    """

    logger.info(f"Lookup categorization for: {transaction_description[:50]}...")

    try:
        from .service import CategoryService

        category_service = CategoryService(db)

        # Check if tenant has historical data
        tenant_categories = await category_service.get_categories(str(tenant_id))

        if len(tenant_categories) == 0:
            # Zero-onboarding scenario - use main CategorizationAgent in zero-onboarding mode
            from .agent import AgentConfig, CategorizationAgent

            # Configure agent for zero-onboarding mode (no RAG)
            config = AgentConfig(
                model_name="gemini-2.0-flash-001",
                rag_enabled=False,  # No RAG corpus available
                project="rezolve-poc",
                location="us-central1",
            )

            agent = CategorizationAgent(config)

            # Use the main agent's categorization intelligence
            result = await agent.categorize_transaction_with_details(
                {
                    "description": transaction_description,
                    "amount": transaction_amount,
                    "transaction_type": "debit" if transaction_amount > 0 else "credit",
                    "tenant_id": tenant_id,
                    "date": None,
                }
            )

            return {
                "method": "unified_agent_zero_onboarding",
                "financial_grade": False,  # Zero-onboarding mode
                "category_found": True,
                "category_name": result.get("category", "Uncategorized"),
                "confidence": result.get("confidence", 0.0),
                "reasoning": result.get("reasoning", "AI categorization"),
                "zero_onboarding": True,
                "agent_used": True,
            }

        # Use lookup-based categorization (financial-grade)
        # For now, if tenant has categories, return lookup indication
        # The actual RAG lookup will be handled by the runtime service
        return {
            "method": "database_lookup_available",
            "financial_grade": True,
            "category_found": False,  # Will be determined by RAG lookup
            "tenant_has_data": True,
            "total_categories": len(tenant_categories),
        }

    except Exception as e:
        logger.error(f"Lookup categorization failed: {e}")
        return {
            "method": "lookup_failed",
            "financial_grade": False,
            "category_found": False,
            "error": str(e),
        }


# REMOVED: ai_bootstrap_category_tool - replaced with unified CategorizationAgent approach
# Zero-onboarding is now a mode in the main agent, not a separate primitive tool


async def get_category_taxonomy_tool(
    db: asyncpg.Connection, tenant_id: int = 1
) -> Dict[str, Any]:
    """
    Essential tool to retrieve tenant's category structure for lookup operations.

    This tool provides the category taxonomy that enables lookup-based categorization.
    """

    try:
        from .service import CategoryService

        category_service = CategoryService(db)
        categories = await category_service.get_categories(str(tenant_id))

        return {
            "success": True,
            "categories": [
                {
                    "id": cat.id,
                    "name": cat.name,
                    "parent_id": cat.parent_id,
                    "gl_code": cat.gl_code,
                }
                for cat in categories
            ],
            "total_categories": len(categories),
            "has_historical_data": len(categories) > 0,
        }

    except Exception as e:
        logger.error(f"Error getting category taxonomy: {e}")
        return {
            "success": False,
            "categories": [],
            "total_categories": 0,
            "has_historical_data": False,
            "error": str(e),
        }


async def create_hierarchical_category_tool(
    db: asyncpg.Connection,
    category_name: str,
    parent_category: str = None,
    tenant_id: int = 1,
    gl_code: str = None,
    is_expense: bool = True,
) -> Dict[str, Any]:
    """
    Tool to create hierarchical categories with proper parent-child relationships.

    This tool integrates with our existing CategoryService to create well-structured
    category hierarchies that our AI suggests.
    """

    logger.info(
        f"Creating hierarchical category: {category_name} (parent: {parent_category})"
    )

    try:
        from .service import CategoryService

        category_service = CategoryService(db)

        # Create parent category first if needed
        parent_id = None
        if parent_category:
            existing_parent = await category_service.get_category_by_name(
                parent_category, str(tenant_id)
            )

            if not existing_parent:
                from .schemas import CategoryCreate

                parent_data = CategoryCreate(
                    name=parent_category,
                    is_expense=is_expense,
                    metadata={"ai_generated_parent": True},
                )
                parent_category_obj = await category_service.create_category(
                    parent_data,
                    tenant_id=tenant_id,
                    user_id=1,  # AI system user
                )
                parent_id = parent_category_obj.id
                logger.info(f"Created parent category: {parent_category}")
            else:
                parent_id = existing_parent.id

        # Create the main category
        from .schemas import CategoryCreate

        category_data = CategoryCreate(
            name=category_name,
            parent_id=parent_id,
            gl_code=gl_code,
            is_expense=is_expense,
            metadata={"ai_generated": True, "zero_onboarding": True},
        )

        category = await category_service.create_category(
            category_data,
            tenant_id=tenant_id,
            user_id=1,  # AI system user
        )

        return {
            "success": True,
            "category_id": category.id,
            "category_name": category.name,
            "parent_id": parent_id,
            "hierarchy_path": f"{parent_category} > {category_name}"
            if parent_category
            else category_name,
        }

    except Exception as e:
        logger.error(f"Error creating hierarchical category: {e}")
        return {"success": False, "error": str(e)}


# Register tools with the categorization agent
def get_categorization_tools(db: asyncpg.Connection) -> List[FunctionTool]:
    """Get the essential categorization tools using unified CategorizationAgent"""

    return [
        FunctionTool(
            func=lambda **kwargs: lookup_transaction_category_tool(db, **kwargs),
            name="lookup_transaction_category",
            description="Unified categorization using CategorizationAgent (automatic mode detection)",
        ),
        FunctionTool(
            func=lambda **kwargs: create_hierarchical_category_tool(db, **kwargs),
            name="create_hierarchical_category",
            description="Create hierarchical categories with proper parent-child relationships",
        ),
        FunctionTool(
            func=lambda **kwargs: get_category_taxonomy_tool(db, **kwargs),
            name="get_category_taxonomy",
            description="Get tenant's category structure for mode detection",
        ),
    ]
