"""
Runtime Categorization Service

This service handles categorization of NEW transactions during production usage.
It uses the RAG corpus built during onboarding to categorize transactions
without any original labels.

Key Concepts:
- Runtime Categorization: AI categorizing new transactions in production
- RAG Corpus: Knowledge base built from historical patterns
- Confidence Scoring: How certain the AI is about each categorization
- Review Queue: Low-confidence items flagged for user review
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

import asyncpg

from ...shared.exceptions import ServiceError
from ..transactions.models import Transaction
from .vertex_rag import get_vector_rag_context

# Removed: zero_onboarding_service import - now using hybrid tools directly

logger = logging.getLogger(__name__)


class RuntimeCategorizationService:
    """
    Service for categorizing new transactions in production.

    This is ONLY used during production operations to categorize
    new transactions that don't have any labels.
    """

    def __init__(self, db: asyncpg.Connection, vertex_client=None):
        self.db = db
        self.vertex_client = vertex_client

        # Initialize services for categorization
        from .service import CategoryService

        self.category_service = CategoryService(db)

    async def categorize_transactions(
        self,
        tenant_id: int,
        transaction_ids: List[int],
        confidence_threshold: float = 0.85,
    ) -> Dict[str, any]:
        """
        Categorize a batch of new transactions using the trained RAG corpus.

        Args:
            tenant_id: The tenant ID
            transaction_ids: List of transaction IDs to categorize
            confidence_threshold: Minimum confidence to auto-approve (default 85%)

        Returns:
            Categorization results including success count and review queue
        """
        try:
            # Validate that these are production transactions (no original categories)
            sql = """
                SELECT * FROM transactions 
                WHERE tenant_id = $1 AND id = ANY($2)
            """
            rows = await self.db.fetch(sql, tenant_id, transaction_ids)
            transactions = [Transaction.model_validate(dict(row)) for row in rows]

            # Ensure no original categories (production validation)
            for transaction in transactions:
                if transaction.original_category is not None:
                    raise ServiceError(
                        f"Transaction {transaction.id} has original_category. "
                        "Runtime categorization is only for NEW transactions without labels."
                    )

            # Get tenant's RAG corpus
            corpus_name = await self._get_tenant_corpus(tenant_id)
            if not corpus_name:
                raise ServiceError(
                    "No RAG corpus found. Please complete onboarding first."
                )

            # Categorize each transaction
            categorization_results = []
            high_confidence_count = 0
            needs_review_count = 0

            for transaction in transactions:
                try:
                    # Get RAG context for this transaction
                    rag_context = await get_vector_rag_context(
                        corpus_name=corpus_name,
                        query=transaction.description,
                        amount=transaction.amount,
                        date=transaction.date,
                    )

                    if rag_context and rag_context.get("matches"):
                        # Use the best match
                        best_match = rag_context["matches"][0]
                        suggested_category = best_match["category"]
                        confidence = best_match["confidence"]

                        # Get or create category
                        category = await self._get_or_create_category(
                            tenant_id, suggested_category
                        )

                        # Update transaction
                        transaction.ai_suggested_category = suggested_category
                        transaction.ai_suggested_category_id = (
                            category.id if category else None
                        )
                        transaction.ai_category_confidence = confidence
                        transaction.needs_review = confidence < confidence_threshold
                        transaction.is_categorized = confidence >= confidence_threshold

                        if confidence >= confidence_threshold:
                            high_confidence_count += 1
                        else:
                            needs_review_count += 1

                        categorization_results.append(
                            {
                                "transaction_id": transaction.id,
                                "description": transaction.description,
                                "amount": transaction.amount,
                                "suggested_category": suggested_category,
                                "confidence": confidence,
                                "needs_review": confidence < confidence_threshold,
                                "rag_matches": rag_context["matches"][
                                    :3
                                ],  # Top 3 matches
                            }
                        )
                    else:
                        # No RAG matches - use zero-onboarding categorization with tools
                        logger.info(
                            f"No RAG matches for transaction {transaction.id}, using zero-onboarding AI categorization"
                        )

                        from .tools import lookup_transaction_category_tool

                        # Use hybrid categorization tool (lookup-first, AI bootstrap if needed)
                        ai_suggestion = await lookup_transaction_category_tool(
                            db=self.db,
                            transaction_description=transaction.description,
                            transaction_amount=float(transaction.amount),
                            tenant_id=tenant_id,
                        )

                        if ai_suggestion and ai_suggestion.get("category_found", False):
                            # Categorization successful (either lookup or AI bootstrap)
                            suggested_category = ai_suggestion.get("category_name")
                            confidence = ai_suggestion.get("confidence", 0.0)
                            is_financial_grade = ai_suggestion.get(
                                "financial_grade", False
                            )

                            # Create category if suggested
                            category = await self._get_or_create_category(
                                tenant_id, suggested_category, ai_suggestion
                            )

                            # Update transaction with AI suggestion
                            transaction.ai_suggested_category = suggested_category
                            transaction.ai_suggested_category_id = (
                                category.id if category else None
                            )
                            transaction.ai_category_confidence = confidence
                            transaction.needs_review = confidence < confidence_threshold
                            transaction.is_categorized = (
                                confidence >= confidence_threshold
                            )

                            if confidence >= confidence_threshold:
                                high_confidence_count += 1
                            else:
                                needs_review_count += 1

                            categorization_results.append(
                                {
                                    "transaction_id": transaction.id,
                                    "description": transaction.description,
                                    "amount": transaction.amount,
                                    "suggested_category": suggested_category,
                                    "confidence": confidence,
                                    "needs_review": confidence < confidence_threshold,
                                    "rag_matches": [],
                                    "ai_created_category": True,
                                    "method": ai_suggestion.get(
                                        "method", "hybrid_categorization"
                                    ),
                                    "financial_grade": is_financial_grade,
                                    "reasoning": ai_suggestion.get(
                                        "reasoning", "AI analysis"
                                    ),
                                }
                            )
                        else:
                            # Even AI couldn't suggest with confidence - flag for review
                            transaction.needs_review = True
                            transaction.is_categorized = False
                            needs_review_count += 1

                            categorization_results.append(
                                {
                                    "transaction_id": transaction.id,
                                    "description": transaction.description,
                                    "amount": transaction.amount,
                                    "suggested_category": None,
                                    "confidence": 0.0,
                                    "needs_review": True,
                                    "rag_matches": [],
                                    "method": "categorization_failed",
                                    "financial_grade": False,
                                    "error": ai_suggestion.get(
                                        "error", "Unknown error"
                                    ),
                                }
                            )

                except Exception as e:
                    logger.error(
                        f"Error categorizing transaction {transaction.id}: {e}"
                    )
                    # Continue with other transactions
                    needs_review_count += 1

            # Commit all changes
            await self.db.commit()

            # Calculate statistics
            total_processed = len(transactions)
            accuracy_rate = (
                (high_confidence_count / total_processed * 100)
                if total_processed > 0
                else 0
            )

            return {
                "success": True,
                "total_processed": total_processed,
                "high_confidence_count": high_confidence_count,
                "needs_review_count": needs_review_count,
                "auto_categorization_rate": round(accuracy_rate, 2),
                "results": categorization_results,
                "confidence_threshold": confidence_threshold,
            }

        except ServiceError:
            raise
        except Exception as e:
            logger.error(f"Runtime categorization failed: {e}")
            raise ServiceError(f"Failed to categorize transactions: {str(e)}")

    async def get_review_queue(
        self, tenant_id: int, limit: int = 50
    ) -> List[Dict[str, any]]:
        """
        Get transactions that need manual review due to low confidence.

        Args:
            tenant_id: The tenant ID
            limit: Maximum number of items to return

        Returns:
            List of transactions needing review
        """
        try:
            from ..transactions.models import Transaction

            # Get transactions needing review using asyncpg
            sql = """
                SELECT * FROM transactions 
                WHERE tenant_id = $1 
                  AND needs_review = true 
                  AND is_categorized = false 
                  AND original_category IS NULL
                LIMIT $2
            """
            rows = await self.db.fetch(sql, tenant_id, limit)
            transactions = [Transaction.model_validate(dict(row)) for row in rows]

            return [
                {
                    "transaction_id": t.id,
                    "date": t.date.isoformat(),
                    "description": t.description,
                    "amount": float(t.amount),
                    "suggested_category": t.ai_suggested_category,
                    "confidence": float(t.ai_category_confidence)
                    if t.ai_category_confidence
                    else 0.0,
                    "vendor": t.vendor,
                }
                for t in transactions
            ]

        except Exception as e:
            logger.error(f"Error getting review queue: {e}")
            return []

    async def apply_user_correction(
        self,
        tenant_id: int,
        transaction_id: int,
        corrected_category: str,
        update_rag: bool = True,
    ) -> bool:
        """
        Apply a user's category correction and optionally update RAG corpus.

        Args:
            tenant_id: The tenant ID
            transaction_id: The transaction to correct
            corrected_category: The user's chosen category
            update_rag: Whether to add this to the RAG corpus for learning

        Returns:
            Success status
        """
        try:
            from ..transactions.models import Transaction

            # Get transaction using asyncpg
            sql = "SELECT * FROM transactions WHERE tenant_id = $1 AND id = $2"
            row = await self.db.fetchrow(sql, tenant_id, transaction_id)

            if not row:
                raise ServiceError("Transaction not found")

            transaction = Transaction.model_validate(dict(row))

            # Get or create category
            category = await self._get_or_create_category(tenant_id, corrected_category)

            # Update transaction using asyncpg
            from datetime import datetime

            update_sql = """
                UPDATE transactions 
                SET ai_suggested_category = $1,
                    ai_suggested_category_id = $2,
                    user_corrected_category = $3,
                    is_categorized = $4,
                    needs_review = $5,
                    corrected_at = $6
                WHERE id = $7 AND tenant_id = $8
            """
            await self.db.execute(
                update_sql,
                corrected_category,
                category.id if category else None,
                corrected_category,
                True,
                False,
                datetime.utcnow(),
                transaction_id,
                tenant_id,
            )

            # Update RAG corpus with this correction
            if update_rag:
                await self._update_rag_with_correction(
                    tenant_id,
                    transaction.description,
                    transaction.amount,
                    corrected_category,
                )

            return True

        except Exception as e:
            logger.error(f"Error applying user correction: {e}")
            return False

    async def get_categorization_stats(
        self,
        tenant_id: int,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
    ) -> Dict[str, any]:
        """
        Get categorization statistics for runtime performance monitoring.

        Args:
            tenant_id: The tenant ID
            date_from: Optional start date filter
            date_to: Optional end date filter

        Returns:
            Statistics about runtime categorization performance
        """
        try:
            # Build date filter for SQL
            date_filter = ""
            params = [tenant_id]
            param_count = 1

            if date_from:
                param_count += 1
                date_filter += f" AND date >= ${param_count}"
                params.append(date_from)
            if date_to:
                param_count += 1
                date_filter += f" AND date <= ${param_count}"
                params.append(date_to)

            # Get all statistics in a single query
            sql = f"""
                SELECT 
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN is_categorized = true AND needs_review = false THEN 1 END) as auto_categorized,
                    COUNT(CASE WHEN needs_review = true THEN 1 END) as needs_review,
                    COUNT(CASE WHEN user_corrected_category IS NOT NULL THEN 1 END) as user_corrected
                FROM transactions 
                WHERE tenant_id = $1 
                  AND original_category IS NULL{date_filter}
            """

            row = await self.db.fetchrow(sql, *params)
            total_transactions = row["total_transactions"] or 0
            auto_categorized = row["auto_categorized"] or 0
            needs_review = row["needs_review"] or 0
            user_corrected = row["user_corrected"] or 0

            # Calculate rates
            auto_rate = (
                (auto_categorized / total_transactions * 100)
                if total_transactions > 0
                else 0
            )
            review_rate = (
                (needs_review / total_transactions * 100)
                if total_transactions > 0
                else 0
            )
            correction_rate = (
                (user_corrected / total_transactions * 100)
                if total_transactions > 0
                else 0
            )

            return {
                "total_transactions": total_transactions,
                "auto_categorized": auto_categorized,
                "needs_review": needs_review,
                "user_corrected": user_corrected,
                "auto_categorization_rate": round(auto_rate, 2),
                "review_rate": round(review_rate, 2),
                "correction_rate": round(correction_rate, 2),
                "date_range": {
                    "from": date_from.isoformat() if date_from else None,
                    "to": date_to.isoformat() if date_to else None,
                },
            }

        except Exception as e:
            logger.error(f"Error getting categorization stats: {e}")
            return {"error": str(e), "total_transactions": 0}

    async def _get_tenant_corpus(self, tenant_id: int) -> Optional[str]:
        """Get the RAG corpus name for a tenant."""
        # In real implementation, this would query the corpus mapping table
        # For now, return the standard format
        return f"projects/{tenant_id}/locations/us-central1/ragCorpora/tenant_{tenant_id}_corpus"

    async def _get_or_create_category(
        self, tenant_id: int, category_name: str
    ) -> Optional[any]:
        """Get existing category or create new one."""
        from .models import Category

        # Check for existing category using asyncpg
        sql = "SELECT * FROM categories WHERE tenant_id = $1 AND name = $2"
        row = await self.db.fetchrow(sql, tenant_id, category_name)
        category = Category.model_validate(dict(row)) if row else None

        if not category:
            # Create new category using asyncpg
            import uuid

            category_id = str(uuid.uuid4())
            sql = """
                INSERT INTO categories (id, name, tenant_id, level, parent_id, gl_code)
                VALUES ($1, $2, $3, $4, $5, $6)
            """
            await self.db.execute(
                sql,
                category_id,
                category_name,
                tenant_id,
                0,  # Top level for AI-discovered categories
                None,
                None,  # GL codes assigned later by user
            )

            # Fetch the created category
            sql = "SELECT * FROM categories WHERE id = $1"
            row = await self.db.fetchrow(sql, category_id)
            category = Category.model_validate(dict(row))

        return category

    async def _update_rag_with_correction(
        self, tenant_id: int, description: str, amount: float, category: str
    ) -> None:
        """Add user correction to RAG corpus for continuous learning."""
        # In real implementation, this would update the Vertex AI RAG corpus
        # with the new pattern for future categorizations
        logger.info(
            f"Would update RAG corpus for tenant {tenant_id}: "
            f"{description} -> {category}"
        )

    async def _ai_suggest_new_category(
        self, transaction: Transaction, tenant_id: int
    ) -> Optional[Dict[str, any]]:
        """
        Use AI to suggest a new category when no RAG matches are found.
        This enables zero-onboarding categorization.
        """
        try:
            logger.info(
                f"AI suggesting category for transaction: {transaction.description[:50]}..."
            )

            # Get existing categories for context
            existing_categories = await self.category_service.get_tenant_categories(
                tenant_id
            )

            # Prepare transaction data
            transaction_data = {
                "description": transaction.description,
                "amount": float(transaction.amount),
                "transaction_type": "debit" if transaction.amount < 0 else "credit",
                "date": transaction.date.isoformat() if transaction.date else None,
                "vendor": transaction.vendor,
            }

            # Use zero-onboarding service to suggest category
            suggestion = await self.zero_onboarding_service.categorize_transaction(
                transaction_data, str(tenant_id), existing_categories
            )

            if suggestion and suggestion.get("confidence", 0) > 0.5:
                logger.info(
                    f"AI suggested category: {suggestion.get('category_name')} "
                    f"(confidence: {suggestion.get('confidence', 0):.2f})"
                )
                return suggestion

            return None

        except Exception as e:
            logger.error(f"Error in AI category suggestion: {e}")
            return None

    async def _get_or_create_category(
        self, tenant_id: int, category_name: str, ai_suggestion: Optional[Dict] = None
    ) -> Optional[object]:
        """
        Enhanced version that can create hierarchical categories from AI suggestions.
        """
        try:
            # Check if category already exists
            existing_category = await self.category_service.get_category_by_name(
                category_name, tenant_id
            )

            if existing_category:
                return existing_category

            # Create new category with hierarchy if AI suggested it
            parent_id = None
            if ai_suggestion and ai_suggestion.get("parent_category"):
                parent_category = await self.category_service.get_category_by_name(
                    ai_suggestion["parent_category"], tenant_id
                )

                if not parent_category:
                    # Create parent category first
                    parent_data = {
                        "name": ai_suggestion["parent_category"],
                        "tenant_id": tenant_id,
                        "is_expense": ai_suggestion.get("is_expense", True),
                        "gl_code": ai_suggestion.get("suggested_gl_code", "5000"),
                        "created_by": "ai_zero_onboarding",
                    }
                    parent_category = await self.category_service.create_category(
                        parent_data
                    )

                parent_id = parent_category.id if parent_category else None

            # Create the main category
            category_data = {
                "name": category_name,
                "parent_id": parent_id,
                "tenant_id": tenant_id,
                "is_expense": ai_suggestion.get("is_expense", True)
                if ai_suggestion
                else True,
                "gl_code": ai_suggestion.get("suggested_gl_code", "5000")
                if ai_suggestion
                else "5000",
                "created_by": "ai_zero_onboarding"
                if ai_suggestion
                else "runtime_categorization",
                "metadata": {
                    "ai_generated": bool(ai_suggestion),
                    "confidence": ai_suggestion.get("confidence")
                    if ai_suggestion
                    else None,
                    "reasoning": ai_suggestion.get("reasoning")
                    if ai_suggestion
                    else None,
                },
            }

            category = await self.category_service.create_category(category_data)
            logger.info(f"Created new category: {category_name} for tenant {tenant_id}")

            return category

        except Exception as e:
            logger.error(f"Error creating category {category_name}: {e}")
            return None
