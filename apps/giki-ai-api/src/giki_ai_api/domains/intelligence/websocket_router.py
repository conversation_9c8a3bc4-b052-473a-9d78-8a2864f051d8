"""
WebSocket Router for Real-time Agent Communication

Provides WebSocket endpoints for real-time updates between agents and UI.
"""

import logging
from typing import Optional

import asyncpg
from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect, status

from ...core.dependencies import get_db_session
from ...shared.services.websocket_service import WebSocketService
from ..auth.secure_auth import get_current_active_user_ws

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/ws", tags=["WebSocket"])

# WebSocket service instance
ws_service = WebSocketService()


@router.websocket("/agent")
async def websocket_agent_endpoint(
    websocket: WebSocket,
    token: Optional[str] = None,
    db: asyncpg.Connection = Depends(get_db_session),
):
    """
    WebSocket endpoint for real-time agent communication.

    Features:
    - Real-time agent processing updates
    - Progress notifications for long operations
    - Bidirectional communication
    - Multi-tenant isolation
    """
    # Authenticate WebSocket connection
    try:
        if not token:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Validate token and get user
        user = await get_current_active_user_ws(token, db)
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Get tenant_id for the user
        tenant_id = user.tenant_id if hasattr(user, "tenant_id") else 1

        logger.info(
            f"WebSocket connection established for user {user.id}, tenant {tenant_id}"
        )

        # Handle WebSocket connection
        await ws_service.handle_websocket(
            websocket=websocket, tenant_id=tenant_id, user_id=str(user.id)
        )

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
