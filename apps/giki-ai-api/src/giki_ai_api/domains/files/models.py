"""
File Processing Models

Pydantic models for storing file interpretation results, column mappings,
and schema analysis outcomes. Enables persistence of AI-driven schema
interpretation for audit trails and reprocessing capabilities.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class InterpretationResultStorage(BaseModel):
    """
    Stores complete schema interpretation results for uploaded files.

    This model captures the full AI interpretation process outcome,
    including confidence scores, detected patterns, and processing metadata.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    upload_id: str = Field(..., max_length=255, description="Upload identifier")
    tenant_id: int = Field(..., description="Tenant ID")
    user_id: uuid.UUID = Field(..., description="User ID")

    # File information
    filename: str = Field(..., max_length=500, description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    mime_type: Optional[str] = Field(None, max_length=100, description="MIME type")

    # Interpretation results
    overall_confidence: float = Field(default=0.0, ge=0.0, le=1.0)
    interpretation_status: str = Field(
        default="pending", description="Status: pending, completed, failed"
    )

    # Schema analysis results
    detected_headers: Optional[List[str]] = Field(
        None, description="List of detected column headers"
    )
    sample_data: Optional[List[Dict[str, Any]]] = Field(
        None, description="Sample rows for validation"
    )
    row_count: int = Field(default=0, ge=0)

    # AI interpretation metadata
    ai_model_used: Optional[str] = Field(None, max_length=100)
    processing_time_ms: Optional[int] = Field(None, ge=0)
    interpretation_summary: Optional[str] = Field(None)

    # Required field status
    required_fields_mapped: Optional[Dict[str, bool]] = Field(
        None, description='{"date": true, "amount": false, ...}'
    )

    # Processing flags
    is_confirmed: bool = Field(default=False)
    is_processed: bool = Field(default=False)
    has_errors: bool = Field(default=False)
    error_details: Optional[Dict[str, Any]] = Field(None)

    # Timestamps
    created_at: datetime
    updated_at: datetime
    confirmed_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class InterpretationColumnMappingStorage(BaseModel):
    """
    Stores individual column mapping results from AI interpretation.

    Each row represents one column mapping decision with confidence score,
    reasoning, and validation status for detailed audit capabilities.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    interpretation_result_id: uuid.UUID = Field(
        ..., description="Parent interpretation result ID"
    )

    # Column mapping details
    source_column: str = Field(
        ..., max_length=255, description="Original column name from file"
    )
    target_field: str = Field(
        ...,
        max_length=100,
        description="Mapped field (date, amount, description, etc.)",
    )

    # AI analysis
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    reasoning: Optional[str] = Field(None, description="AI explanation for the mapping")

    # Data validation
    sample_values: Optional[List[Any]] = Field(
        None, description="Sample values for validation"
    )
    data_type_detected: Optional[str] = Field(
        None, max_length=50, description="string, number, date, boolean"
    )
    format_pattern: Optional[str] = Field(
        None, max_length=200, description="For date/number formats"
    )

    # Validation status
    is_valid: bool = Field(default=True)
    validation_errors: Optional[List[str]] = Field(
        None, description="List of validation error messages"
    )

    # User modifications
    is_user_modified: bool = Field(default=False)
    original_target_field: Optional[str] = Field(
        None, max_length=100, description="If user changed the mapping"
    )
    user_confidence: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="User-provided confidence override"
    )

    # Processing metadata
    processing_order: Optional[int] = Field(
        None, description="Order in which this mapping was processed"
    )

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InterpretationCategorizationColumnStorage(BaseModel):
    """
    Stores categorization-specific column analysis for transaction categorization.

    Tracks which columns are relevant for AI categorization and their
    importance weights for optimized transaction processing.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    interpretation_result_id: uuid.UUID = Field(
        ..., description="Parent interpretation result ID"
    )

    # Column categorization analysis
    column_name: str = Field(..., max_length=255)
    categorization_relevance: float = Field(
        default=0.0, ge=0.0, le=1.0, description="0.0 to 1.0"
    )
    relevance_reasoning: Optional[str] = Field(None)

    # Column role in categorization
    categorization_role: str = Field(
        ..., max_length=100, description="primary, secondary, metadata, ignored"
    )
    importance_weight: float = Field(default=1.0, ge=0.0)

    # Pattern analysis
    unique_value_count: Optional[int] = Field(None, ge=0)
    sample_patterns: Optional[List[str]] = Field(
        None, description="Common patterns found in this column"
    )
    category_indicators: Optional[List[str]] = Field(
        None, description="Keywords/patterns that suggest categories"
    )

    # AI model preferences
    preferred_for_embedding: bool = Field(
        default=False, description="Should this column be used for RAG embeddings"
    )
    context_priority: Optional[int] = Field(
        None, description="Priority order for context building"
    )

    # Processing optimization
    requires_preprocessing: bool = Field(default=False)
    preprocessing_steps: Optional[List[str]] = Field(
        None, description="List of preprocessing steps needed"
    )

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Additional helper models for future extensibility


class FileProcessingLog(BaseModel):
    """
    Audit log for file processing operations.

    Tracks all processing steps for compliance and debugging purposes.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    upload_id: str = Field(..., max_length=255)
    tenant_id: int

    # Log details
    operation_type: str = Field(
        ..., max_length=100, description="upload, interpret, confirm, process"
    )
    operation_status: str = Field(
        ..., max_length=50, description="started, completed, failed"
    )

    # Processing metrics
    processing_duration_ms: Optional[int] = Field(None, ge=0)
    memory_usage_mb: Optional[float] = Field(None, ge=0)
    cpu_usage_percent: Optional[float] = Field(None, ge=0, le=100)

    # Error tracking
    error_type: Optional[str] = Field(None, max_length=100)
    error_message: Optional[str] = Field(None)
    error_stack_trace: Optional[str] = Field(None)

    # Contextual information
    operation_context: Optional[Dict[str, Any]] = Field(
        None, description="Additional context data"
    )
    user_agent: Optional[str] = Field(None, max_length=500)
    ip_address: Optional[str] = Field(None, max_length=45)

    # Timestamps
    created_at: datetime

    class Config:
        from_attributes = True


class InterpretationTemplate(BaseModel):
    """
    Reusable interpretation templates for common file formats.

    Stores successful interpretation patterns to improve future
    processing accuracy and speed.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    tenant_id: int

    # Template metadata
    template_name: str = Field(..., max_length=200)
    description: Optional[str] = Field(None)
    file_pattern: Optional[str] = Field(
        None, max_length=500, description="Regex pattern for matching filenames"
    )

    # Template configuration
    expected_columns: List[str] = Field(
        ..., description="List of expected column patterns"
    )
    column_mappings: Dict[str, str] = Field(..., description="Default column mappings")
    validation_rules: Optional[Dict[str, Any]] = Field(
        None, description="Validation rules to apply"
    )

    # Usage statistics
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    usage_count: int = Field(default=0, ge=0)
    last_used_at: Optional[datetime] = None

    # Template status
    is_active: bool = Field(default=True)
    is_system_template: bool = Field(
        default=False, description="Created by system vs user"
    )

    # Timestamps
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
