"""
Debit Credit Agent - Advanced Accounting Logic Inference
========================================================

This agent specializes in debit/credit inference beyond basic schema interpretation,
handling complex accounting scenarios using the ADK v1.3.0 pattern.

Key capabilities:
- Advanced debit/credit inference using accounting principles
- Regional banking terminology and cultural pattern recognition
- Complex transaction analysis (splits, transfers, reversals)
- Accounting rule validation and correction
- Multi-currency transaction direction inference
- Banking format analysis and standardization
"""

import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import asyncpg

logger = logging.getLogger(__name__)

# Real ADK imports
from google.adk.tools import FunctionTool
from vertexai.generative_models import GenerativeModel

# Import StandardGikiAgent base class
from ...shared.ai.standard_giki_agent import StandardGikiAgent

# ===== DEBIT/CREDIT INFERENCE TOOLS =====


async def infer_transaction_direction_tool_function(
    transaction_data: Dict[str, Any],
    account_context: Optional[Dict[str, Any]] = None,
    regional_settings: Optional[Dict[str, str]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Advanced debit/credit inference using accounting principles.

    Analyzes transaction characteristics and account context to determine
    the correct debit/credit direction using advanced accounting logic.

    Args:
        transaction_data: Transaction details (amount, description, merchant, etc.)
        account_context: Account type and characteristics
        regional_settings: Regional banking conventions

    Returns:
        Dictionary with inferred transaction direction and reasoning
    """
    logger.info("Inferring transaction direction using advanced accounting principles")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for accounting analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create advanced inference prompt
        inference_prompt = f"""
        You are an expert in advanced accounting principles and banking conventions.
        Analyze this transaction to determine the correct debit/credit direction.
        
        Transaction Data:
        {json.dumps(transaction_data, indent=2)}
        
        Account Context:
        {json.dumps(account_context or {}, indent=2)}
        
        Regional Settings:
        {json.dumps(regional_settings or {"region": "global", "convention": "standard"}, indent=2)}
        
        ADVANCED ACCOUNTING PRINCIPLES:
        
        1. ACCOUNT TYPE RULES:
           - Assets: Debit increases, Credit decreases
           - Liabilities: Credit increases, Debit decreases  
           - Equity: Credit increases, Debit decreases
           - Revenue: Credit increases, Debit decreases
           - Expenses: Debit increases, Credit decreases
        
        2. CASH FLOW PERSPECTIVE (Bank Account View):
           - Money IN: Credit (from bank's perspective, liability increase)
           - Money OUT: Debit (from bank's perspective, liability decrease)
           - But for customer: Money IN = Debit to Cash, Money OUT = Credit from Cash
        
        3. TRANSACTION TYPE ANALYSIS:
           - Purchases/Payments: Usually Debits (expense increases)
           - Deposits/Income: Usually Credits (revenue increases)
           - Transfers: Depends on account relationship
           - Refunds: Opposite of original transaction
           - Fees: Usually Debits (expense increases)
        
        4. DESCRIPTION PATTERNS:
           - "DEBIT", "WITHDRAWAL", "PAYMENT": Likely debit transactions
           - "CREDIT", "DEPOSIT", "REFUND": Likely credit transactions
           - "TRANSFER", "ACH": Need context analysis
           - "FEE", "CHARGE": Usually debit transactions
        
        5. AMOUNT SIGN CONVENTIONS:
           - Negative amounts often indicate outflows (debits from cash)
           - Positive amounts often indicate inflows (credits to cash)
           - But this varies by bank and account type
        
        6. REGIONAL VARIATIONS:
           - US: Standard accounting rules
           - UK: Similar but some terminology differences
           - India: Follows IFRS with local adaptations
           - Other: May have unique conventions
        
        7. MERCHANT/VENDOR ANALYSIS:
           - Known vendors: Likely expense transactions (debits)
           - Government entities: Could be taxes (debits) or refunds (credits)
           - Banks: Could be fees (debits) or interest (credits)
           - Salary/payroll: Usually income (credits)
        
        Return JSON format:
        {{
            "inferred_direction": "debit|credit",
            "confidence": 0.95,
            "account_perspective": "customer|bank",
            "reasoning": {{
                "primary_factors": ["factor1", "factor2"],
                "account_type_analysis": "Based on asset account, outflow should be credit",
                "amount_sign_analysis": "Negative amount suggests outflow",
                "description_analysis": "Contains payment keyword indicating expense",
                "merchant_analysis": "Known vendor suggests purchase transaction",
                "regional_considerations": "Standard US accounting conventions apply"
            }},
            "alternative_possibilities": [
                {{
                    "direction": "credit", 
                    "probability": 0.05,
                    "scenario": "If this were a refund instead of purchase"
                }}
            ],
            "validation_checks": {{
                "amount_direction_consistent": true,
                "description_amount_consistent": true,
                "account_type_logical": true,
                "regional_convention_followed": true
            }},
            "recommended_action": "apply_inference|request_clarification|flag_for_review"
        }}
        
        JSON:"""

        # Generate inference
        response = await model.generate_content_async(
            inference_prompt,
            generation_config={
                "temperature": 0.1,  # Low temperature for consistent accounting logic
                "max_output_tokens": 1500,
                "top_p": 0.8,
            },
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Direction inferred: {result['inferred_direction']} with {result['confidence'] * 100:.1f}% confidence"
        )
        return result

    except Exception as e:
        logger.error(f"Transaction direction inference failed: {e}")
        raise Exception(f"AI direction inference failed: {e}")


async def analyze_banking_patterns_tool_function(
    transactions: List[Dict[str, Any]],
    bank_metadata: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Analyze regional banking terminology and patterns.

    Identifies specific banking conventions, terminology patterns, and
    regional variations in transaction formatting and direction indication.

    Args:
        transactions: List of transactions to analyze
        bank_metadata: Optional bank-specific information

    Returns:
        Dictionary with banking pattern analysis
    """
    logger.info(f"Analyzing banking patterns for {len(transactions)} transactions")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for pattern analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Sample transactions for analysis (limit to prevent token overflow)
        sample_transactions = (
            transactions[:20] if len(transactions) > 20 else transactions
        )

        # Create pattern analysis prompt
        pattern_prompt = f"""
        You are a banking systems expert specializing in regional variations.
        Analyze these transactions to identify banking patterns and conventions.
        
        Sample Transactions:
        {json.dumps(sample_transactions, indent=2)}
        
        Bank Metadata:
        {json.dumps(bank_metadata or {}, indent=2)}
        
        PATTERN ANALYSIS FOCUS:
        
        1. TERMINOLOGY PATTERNS:
           - How are debits/credits indicated? (DR/CR, +/-, words)
           - Common transaction type keywords
           - Regional vocabulary differences
           - Currency formatting conventions
        
        2. AMOUNT SIGN CONVENTIONS:
           - Positive = inflow or outflow?
           - Negative = inflow or outflow?
           - Are there explicit direction indicators?
           - Consistency across transaction types
        
        3. DESCRIPTION FORMATTING:
           - Standard prefixes/suffixes
           - Merchant name formatting
           - Date/time inclusion patterns
           - Reference number patterns
        
        4. REGIONAL BANKING CHARACTERISTICS:
           - US: ACH, Wire, Check patterns
           - UK: BACS, Faster Payments, Standing Order
           - India: NEFT, RTGS, UPI patterns
           - Other: Region-specific patterns
        
        5. BANK-SPECIFIC CONVENTIONS:
           - Large banks vs regional banks
           - Credit unions vs commercial banks
           - Online banks vs traditional banks
        
        6. TRANSACTION CATEGORIZATION HINTS:
           - Automatic categorization indicators
           - Merchant category codes (if present)
           - Internal bank categorization
        
        Return JSON format:
        {{
            "banking_region": "US|UK|India|Other",
            "bank_type": "commercial|credit_union|online|regional",
            "patterns_identified": {{
                "amount_sign_convention": {{
                    "positive_means": "inflow|outflow",
                    "negative_means": "inflow|outflow", 
                    "confidence": 0.95,
                    "evidence": ["pattern1", "pattern2"]
                }},
                "direction_indicators": {{
                    "explicit_markers": ["DR", "CR", "DEBIT", "CREDIT"],
                    "implicit_patterns": ["negative for outflow", "positive for inflow"],
                    "consistency_score": 0.9
                }},
                "terminology": {{
                    "debit_terms": ["debit", "withdrawal", "payment"],
                    "credit_terms": ["credit", "deposit", "refund"],
                    "neutral_terms": ["transfer", "ach", "wire"],
                    "regional_variations": ["specific terms found"]
                }},
                "formatting": {{
                    "description_pattern": "MERCHANT NAME LOCATION",
                    "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD",
                    "currency_symbol": "$|£|₹|€",
                    "decimal_separator": ".|,"
                }}
            }},
            "inference_rules": [
                {{
                    "rule": "If amount < 0 and description contains 'PAYMENT', then debit",
                    "confidence": 0.9,
                    "applicability": "all_transactions|subset"
                }}
            ],
            "anomalies": [
                {{
                    "transaction_index": 5,
                    "anomaly_type": "inconsistent_sign_convention",
                    "description": "Transaction breaks expected pattern"
                }}
            ],
            "recommendations": {{
                "apply_rules_automatically": true,
                "manual_review_needed": false,
                "confidence_threshold": 0.8,
                "special_handling": ["transfers", "refunds"]
            }}
        }}
        
        JSON:"""

        # Generate analysis
        response = await model.generate_content_async(
            pattern_prompt,
            generation_config={
                "temperature": 0.2,  # Slightly higher for pattern recognition
                "max_output_tokens": 2000,
                "top_p": 0.9,
            },
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Banking patterns analyzed: {result['banking_region']} region, {len(result['inference_rules'])} rules identified"
        )
        return result

    except Exception as e:
        logger.error(f"Banking pattern analysis failed: {e}")
        raise Exception(f"AI pattern analysis failed: {e}")


async def handle_complex_transactions_tool_function(
    transaction: Dict[str, Any],
    transaction_type: str,
    context_transactions: Optional[List[Dict[str, Any]]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Handle complex transaction scenarios (splits, transfers, reversals).

    Analyzes complex transaction types that require special handling
    for accurate debit/credit determination.

    Args:
        transaction: Primary transaction to analyze
        transaction_type: Type of complex transaction
        context_transactions: Related transactions for context

    Returns:
        Dictionary with complex transaction analysis
    """
    logger.info(f"Handling complex transaction type: {transaction_type}")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for complex analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create complex transaction prompt
        complex_prompt = f"""
        You are an expert in complex accounting transaction analysis.
        Analyze this complex transaction scenario for proper debit/credit handling.
        
        Primary Transaction:
        {json.dumps(transaction, indent=2)}
        
        Transaction Type: {transaction_type}
        
        Context Transactions:
        {json.dumps(context_transactions or [], indent=2)}
        
        COMPLEX TRANSACTION TYPES:
        
        1. SPLIT TRANSACTIONS:
           - Single payment split across multiple categories
           - Each split should sum to total amount
           - Maintain consistent debit/credit direction
           - Preserve transaction linkage
        
        2. TRANSFER TRANSACTIONS:
           - Money movement between accounts
           - Debit from source, Credit to destination
           - Net effect should be zero across accounts
           - Handle fees as separate transactions
        
        3. REVERSAL TRANSACTIONS:
           - Correction of previous transaction
           - Opposite direction of original
           - May be partial or full reversal
           - Maintain audit trail linkage
        
        4. REFUND TRANSACTIONS:
           - Return of previous payment
           - Usually opposite of original transaction
           - May include processing fees
           - Timeline considerations important
        
        5. FOREIGN EXCHANGE:
           - Currency conversion involved
           - May have exchange rate fees
           - Base currency vs foreign currency
           - Timing of conversion matters
        
        6. RECURRING TRANSACTIONS:
           - Subscription payments
           - Loan payments (principal + interest)
           - Salary deposits
           - Consistent pattern expected
        
        ANALYSIS REQUIREMENTS:
        - Identify all component parts
        - Determine individual debit/credit directions
        - Calculate balancing entries
        - Flag any inconsistencies
        - Suggest proper accounting treatment
        
        Return JSON format:
        {{
            "complex_type": "{transaction_type}",
            "analysis": {{
                "total_amount": 1000.00,
                "currency": "USD",
                "component_count": 2,
                "requires_splitting": true,
                "balancing_required": true
            }},
            "components": [
                {{
                    "description": "Primary transaction component",
                    "amount": 750.00,
                    "direction": "debit",
                    "account_category": "expense",
                    "reasoning": "Main purchase amount"
                }},
                {{
                    "description": "Fee component", 
                    "amount": 250.00,
                    "direction": "debit",
                    "account_category": "fee_expense",
                    "reasoning": "Processing fee"
                }}
            ],
            "balancing_entries": [
                {{
                    "description": "Cash reduction",
                    "amount": 1000.00,
                    "direction": "credit",
                    "account_category": "cash",
                    "reasoning": "Total cash outflow"
                }}
            ],
            "validation": {{
                "total_debits": 1000.00,
                "total_credits": 1000.00,
                "is_balanced": true,
                "consistency_check": true
            }},
            "special_handling": {{
                "requires_approval": false,
                "manual_review": false,
                "link_to_original": "transaction_id_if_reversal",
                "category_override": false
            }},
            "recommendations": [
                "Split into separate line items",
                "Maintain transaction linkage",
                "Apply standard expense categorization"
            ]
        }}
        
        JSON:"""

        # Generate analysis
        response = await model.generate_content_async(
            complex_prompt,
            generation_config={
                "temperature": 0.1,  # Low temperature for accounting accuracy
                "max_output_tokens": 2000,
                "top_p": 0.8,
            },
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Complex transaction analyzed: {len(result['components'])} components, balanced={result['validation']['is_balanced']}"
        )
        return result

    except Exception as e:
        logger.error(f"Complex transaction analysis failed: {e}")
        raise Exception(f"AI complex transaction analysis failed: {e}")


async def validate_accounting_rules_tool_function(
    transactions: List[Dict[str, Any]],
    validation_rules: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Validate and correct accounting rule compliance.

    Checks transaction directions against accounting principles and
    suggests corrections for any violations.

    Args:
        transactions: List of transactions to validate
        validation_rules: Optional custom validation rules

    Returns:
        Dictionary with validation results and corrections
    """
    logger.info(f"Validating accounting rules for {len(transactions)} transactions")

    validation_results = {
        "total_transactions": len(transactions),
        "valid_transactions": 0,
        "invalid_transactions": 0,
        "warnings": [],
        "errors": [],
        "corrections": [],
        "summary": {},
    }

    try:
        # Default validation rules
        default_rules = {
            "require_positive_expenses": True,
            "require_negative_income": False,
            "validate_balance_consistency": True,
            "check_amount_direction_match": True,
            "flag_large_amounts": True,
            "large_amount_threshold": 10000.0,
        }

        if validation_rules:
            default_rules.update(validation_rules)

        # Validate each transaction
        for i, txn in enumerate(transactions):
            transaction_valid = True
            txn_issues = []

            amount = txn.get("amount", 0)
            direction = txn.get("direction", "").lower()
            description = txn.get("description", "")
            category = txn.get("category", "")

            # Rule 1: Amount direction consistency
            if default_rules["check_amount_direction_match"]:
                if direction == "debit" and amount > 0:
                    # For most systems, debits should be negative or explicitly marked
                    if not any(
                        word in description.lower()
                        for word in ["deposit", "refund", "credit"]
                    ):
                        txn_issues.append(
                            {
                                "rule": "amount_direction_mismatch",
                                "severity": "warning",
                                "message": f"Debit transaction with positive amount: {amount}",
                                "suggested_correction": "Verify transaction direction or amount sign",
                            }
                        )

                elif direction == "credit" and amount < 0:
                    # Credits should typically be positive
                    if not any(
                        word in description.lower()
                        for word in ["payment", "withdrawal", "fee"]
                    ):
                        txn_issues.append(
                            {
                                "rule": "amount_direction_mismatch",
                                "severity": "warning",
                                "message": f"Credit transaction with negative amount: {amount}",
                                "suggested_correction": "Verify transaction direction or amount sign",
                            }
                        )

            # Rule 2: Large amount flagging
            if default_rules["flag_large_amounts"]:
                if abs(amount) > default_rules["large_amount_threshold"]:
                    txn_issues.append(
                        {
                            "rule": "large_amount_detected",
                            "severity": "info",
                            "message": f"Large transaction amount: {abs(amount)}",
                            "suggested_correction": "Review for accuracy",
                        }
                    )

            # Rule 3: Category-direction consistency
            expense_categories = [
                "food",
                "dining",
                "transportation",
                "utilities",
                "rent",
                "shopping",
            ]
            income_categories = ["salary", "income", "deposit", "refund"]

            if any(cat in category.lower() for cat in expense_categories):
                if direction == "credit":
                    txn_issues.append(
                        {
                            "rule": "category_direction_mismatch",
                            "severity": "error",
                            "message": f"Expense category '{category}' with credit direction",
                            "suggested_correction": "Change direction to debit for expense transaction",
                        }
                    )
                    transaction_valid = False

            elif any(cat in category.lower() for cat in income_categories):
                if direction == "debit":
                    txn_issues.append(
                        {
                            "rule": "category_direction_mismatch",
                            "severity": "error",
                            "message": f"Income category '{category}' with debit direction",
                            "suggested_correction": "Change direction to credit for income transaction",
                        }
                    )
                    transaction_valid = False

            # Rule 4: Description-amount consistency
            payment_keywords = ["payment", "purchase", "bill", "fee", "charge"]
            income_keywords = ["deposit", "salary", "refund", "credit", "interest"]

            desc_lower = description.lower()
            if any(keyword in desc_lower for keyword in payment_keywords):
                if amount > 0 and direction != "debit":
                    txn_issues.append(
                        {
                            "rule": "description_direction_mismatch",
                            "severity": "warning",
                            "message": "Payment description with non-debit direction",
                            "suggested_correction": "Verify direction matches payment nature",
                        }
                    )

            elif any(keyword in desc_lower for keyword in income_keywords):
                if amount < 0 and direction != "credit":
                    txn_issues.append(
                        {
                            "rule": "description_direction_mismatch",
                            "severity": "warning",
                            "message": "Income description with non-credit direction",
                            "suggested_correction": "Verify direction matches income nature",
                        }
                    )

            # Store results
            if transaction_valid:
                validation_results["valid_transactions"] += 1
            else:
                validation_results["invalid_transactions"] += 1

            # Add issues to appropriate lists
            for issue in txn_issues:
                issue["transaction_index"] = i
                issue["transaction_id"] = txn.get("id", f"txn_{i}")

                if issue["severity"] == "error":
                    validation_results["errors"].append(issue)
                elif issue["severity"] == "warning":
                    validation_results["warnings"].append(issue)

                if "suggested_correction" in issue:
                    validation_results["corrections"].append(issue)

        # Generate summary
        validation_results["summary"] = {
            "overall_validity": validation_results["valid_transactions"]
            / len(transactions)
            * 100,
            "error_rate": validation_results["invalid_transactions"]
            / len(transactions)
            * 100,
            "warning_count": len(validation_results["warnings"]),
            "correction_suggestions": len(validation_results["corrections"]),
            "most_common_issues": {},
            "validation_passed": validation_results["invalid_transactions"] == 0,
        }

        # Count issue frequencies
        issue_counts = {}
        for issue in validation_results["errors"] + validation_results["warnings"]:
            rule = issue["rule"]
            issue_counts[rule] = issue_counts.get(rule, 0) + 1

        validation_results["summary"]["most_common_issues"] = issue_counts

        logger.info(
            f"Validation complete: {validation_results['valid_transactions']}/{len(transactions)} valid, "
            f"{len(validation_results['errors'])} errors, {len(validation_results['warnings'])} warnings"
        )
        return validation_results

    except Exception as e:
        logger.error(f"Accounting rules validation failed: {e}")
        validation_results["errors"].append(
            {
                "rule": "validation_error",
                "severity": "error",
                "message": f"Validation process failed: {str(e)}",
                "transaction_index": -1,
            }
        )
        return validation_results


async def infer_multicurrency_direction_tool_function(
    transaction: Dict[str, Any],
    exchange_rates: Optional[Dict[str, float]] = None,
    base_currency: str = "USD",
    **_kwargs,
) -> Dict[str, Any]:
    """
    Handle multi-currency transaction direction inference.

    Analyzes foreign currency transactions to determine proper
    debit/credit direction considering currency conversion effects.

    Args:
        transaction: Transaction with currency information
        exchange_rates: Current exchange rates
        base_currency: Base accounting currency

    Returns:
        Dictionary with multi-currency direction analysis
    """
    logger.info("Inferring direction for multi-currency transaction")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for currency analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create multi-currency prompt
        currency_prompt = f"""
        You are an expert in international accounting and foreign exchange.
        Analyze this multi-currency transaction for proper debit/credit treatment.
        
        Transaction:
        {json.dumps(transaction, indent=2)}
        
        Exchange Rates:
        {json.dumps(exchange_rates or {}, indent=2)}
        
        Base Currency: {base_currency}
        
        MULTI-CURRENCY ACCOUNTING PRINCIPLES:
        
        1. CURRENCY IDENTIFICATION:
           - Transaction currency vs base currency
           - Exchange rate at transaction date
           - Rate source and reliability
        
        2. CONVERSION RULES:
           - Convert foreign currency to base currency
           - Record both amounts if significant
           - Track exchange rate used
        
        3. DIRECTION DETERMINATION:
           - Apply standard debit/credit rules to base currency amount
           - Consider currency gain/loss implications
           - Handle rounding differences appropriately
        
        4. EXCHANGE RATE EFFECTS:
           - Favorable vs unfavorable rates
           - Rate volatility considerations
           - Timing of conversion
        
        5. DOCUMENTATION REQUIREMENTS:
           - Record original currency and amount
           - Document exchange rate used
           - Note conversion date
           - Flag significant rate differences
        
        Return JSON format:
        {{
            "currency_analysis": {{
                "transaction_currency": "EUR",
                "base_currency": "{base_currency}",
                "original_amount": 100.00,
                "converted_amount": 110.00,
                "exchange_rate": 1.10,
                "rate_source": "transaction_date|current|manual",
                "rate_reliability": "high|medium|low"
            }},
            "direction_inference": {{
                "recommended_direction": "debit|credit",
                "confidence": 0.95,
                "reasoning": "Based on converted amount and transaction nature",
                "currency_impact": "Exchange rate favorable to base currency"
            }},
            "accounting_entries": [
                {{
                    "account": "Foreign Currency Expense",
                    "currency": "EUR", 
                    "original_amount": 100.00,
                    "base_amount": 110.00,
                    "direction": "debit"
                }},
                {{
                    "account": "Cash",
                    "currency": "{base_currency}",
                    "base_amount": 110.00,
                    "direction": "credit"
                }}
            ],
            "exchange_considerations": {{
                "rate_variance_threshold": 0.05,
                "requires_rate_documentation": true,
                "potential_gain_loss": 0.00,
                "rounding_adjustment": 0.00
            }},
            "validation": {{
                "conversion_accurate": true,
                "direction_consistent": true,
                "documentation_complete": true,
                "requires_review": false
            }}
        }}
        
        JSON:"""

        # Generate analysis
        response = await model.generate_content_async(
            currency_prompt,
            generation_config={
                "temperature": 0.1,  # Low temperature for accounting precision
                "max_output_tokens": 1500,
                "top_p": 0.8,
            },
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Multi-currency analysis complete: {result['currency_analysis']['transaction_currency']} → {base_currency}, "
            f"direction: {result['direction_inference']['recommended_direction']}"
        )
        return result

    except Exception as e:
        logger.error(f"Multi-currency direction inference failed: {e}")
        raise Exception(f"AI currency analysis failed: {e}")


# Create FunctionTool instances
infer_transaction_direction_tool = FunctionTool(
    func=infer_transaction_direction_tool_function
)
analyze_banking_patterns_tool = FunctionTool(
    func=analyze_banking_patterns_tool_function
)
handle_complex_transactions_tool = FunctionTool(
    func=handle_complex_transactions_tool_function
)
validate_accounting_rules_tool = FunctionTool(
    func=validate_accounting_rules_tool_function
)
infer_multicurrency_direction_tool = FunctionTool(
    func=infer_multicurrency_direction_tool_function
)


@dataclass
class DebitCreditAgentConfig:
    """Configuration for DebitCreditAgent."""

    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    tools: List[FunctionTool] | None = None
    enable_code_execution: bool = False


class DebitCreditAgent(StandardGikiAgent):
    """
    Advanced debit/credit inference agent for complex accounting scenarios.

    This agent handles:
    - Advanced debit/credit inference using accounting principles
    - Regional banking terminology and cultural pattern recognition
    - Complex transaction analysis (splits, transfers, reversals)
    - Accounting rule validation and correction
    - Multi-currency transaction direction inference
    """

    def __init__(
        self,
        config: DebitCreditAgentConfig | None = None,
        db: asyncpg.Connection | None = None,
        **kwargs,
    ):
        """Initialize the DebitCreditAgent using StandardGikiAgent inheritance."""
        if config is None:
            config = DebitCreditAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
            )

        # Store config and extract values
        self._config = config
        model_name = config.model_name
        project = config.project
        location = config.location

        # Set up debit/credit analysis tools
        custom_tools = [
            infer_transaction_direction_tool,
            analyze_banking_patterns_tool,
            handle_complex_transactions_tool,
            validate_accounting_rules_tool,
            infer_multicurrency_direction_tool,
        ]
        if config.tools:
            custom_tools.extend(config.tools)

        # Initialize StandardGikiAgent with debit/credit specific configuration
        super().__init__(
            name="debit_credit_agent",
            description="Advanced debit/credit inference and accounting logic specialist",
            custom_tools=custom_tools,
            enable_interactive_tools=False,  # Debit/credit operations are analytical
            enable_code_execution=config.enable_code_execution,
            model_name=model_name,
            instruction="""You are an expert in advanced accounting principles and debit/credit inference.
            Handle complex accounting scenarios including splits, transfers, reversals, and multi-currency transactions.
            Apply regional banking conventions and ensure compliance with accounting standards.
            Use lookup-based logic for 100% accuracy in financial categorization.""",
            project_id=project,
            location=location or "global",
            **kwargs,
        )

        # Store attributes after initialization
        self._model_name = model_name
        self._project = project
        self._location = location
        self._db = db

        # Initialize debit/credit specific components
        self._vertex_model = GenerativeModel(model_name=model_name)

        logger.info(f"DebitCreditAgent initialized with model: {model_name}")

    async def infer_transaction_direction(
        self,
        transaction_data: Dict[str, Any],
        account_context: Optional[Dict[str, Any]] = None,
        regional_settings: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Infer debit/credit direction using advanced accounting principles.

        Args:
            transaction_data: Transaction details
            account_context: Account type and characteristics
            regional_settings: Regional banking conventions

        Returns:
            Dictionary with inferred direction and reasoning
        """
        return await infer_transaction_direction_tool_function(
            transaction_data=transaction_data,
            account_context=account_context,
            regional_settings=regional_settings,
        )

    async def analyze_banking_patterns(
        self,
        transactions: List[Dict[str, Any]],
        bank_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Analyze regional banking terminology and patterns.

        Args:
            transactions: List of transactions to analyze
            bank_metadata: Optional bank-specific information

        Returns:
            Dictionary with banking pattern analysis
        """
        return await analyze_banking_patterns_tool_function(
            transactions=transactions,
            bank_metadata=bank_metadata,
        )

    async def handle_complex_transactions(
        self,
        transaction: Dict[str, Any],
        transaction_type: str,
        context_transactions: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Handle complex transaction scenarios.

        Args:
            transaction: Primary transaction to analyze
            transaction_type: Type of complex transaction
            context_transactions: Related transactions for context

        Returns:
            Dictionary with complex transaction analysis
        """
        return await handle_complex_transactions_tool_function(
            transaction=transaction,
            transaction_type=transaction_type,
            context_transactions=context_transactions,
        )

    async def validate_accounting_rules(
        self,
        transactions: List[Dict[str, Any]],
        validation_rules: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Validate and correct accounting rule compliance.

        Args:
            transactions: List of transactions to validate
            validation_rules: Optional custom validation rules

        Returns:
            Dictionary with validation results
        """
        return await validate_accounting_rules_tool_function(
            transactions=transactions,
            validation_rules=validation_rules,
        )

    async def infer_multicurrency_direction(
        self,
        transaction: Dict[str, Any],
        exchange_rates: Optional[Dict[str, float]] = None,
        base_currency: str = "USD",
    ) -> Dict[str, Any]:
        """
        Handle multi-currency transaction direction inference.

        Args:
            transaction: Transaction with currency information
            exchange_rates: Current exchange rates
            base_currency: Base accounting currency

        Returns:
            Dictionary with multi-currency analysis
        """
        return await infer_multicurrency_direction_tool_function(
            transaction=transaction,
            exchange_rates=exchange_rates,
            base_currency=base_currency,
        )
