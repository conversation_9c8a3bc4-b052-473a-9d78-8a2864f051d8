"""
File Service - Handles file upload and processing operations.

Provides functionality for:
- Excel/CSV file processing
- Column mapping and validation
- Data extraction and transformation
- Multi-sheet handling
"""

import io
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
from asyncpg import Connection

from ...shared.exceptions import ServiceError, ValidationError
from .schemas import (
    FileProcessingResult,
    SheetInfo,
)

logger = logging.getLogger(__name__)


class FileService:
    """
    Service for handling file uploads and processing.

    Supports Excel (xlsx, xls) and CSV file formats with:
    - Multi-sheet processing
    - Automatic column detection
    - Data validation
    - Transaction extraction
    """

    def __init__(self, conn: Connection):
        self.conn = conn
        self.supported_extensions = {".xlsx", ".xls", ".csv"}

    async def process_file(
        self,
        file_content: bytes,
        filename: str,
        tenant_id: int,
        upload_id: Optional[str] = None,
    ) -> FileProcessingResult:
        """
        Process uploaded file and extract transaction data.

        Args:
            file_content: Raw file bytes
            filename: Original filename
            tenant_id: Tenant ID for data isolation
            upload_id: Optional upload tracking ID

        Returns:
            FileProcessingResult with extracted data and metadata
        """
        try:
            # Validate file extension
            file_ext = Path(filename).suffix.lower()
            if file_ext not in self.supported_extensions:
                raise ValidationError(
                    f"Unsupported file type: {file_ext}. "
                    f"Supported types: {', '.join(self.supported_extensions)}"
                )

            # Process based on file type
            if file_ext == ".csv":
                sheets_data = await self._process_csv(file_content, filename)
            else:
                sheets_data = await self._process_excel(file_content, filename)

            # Calculate totals
            total_rows = sum(sheet.row_count for sheet in sheets_data)

            return FileProcessingResult(
                success=True,
                filename=filename,
                upload_id=upload_id or self._generate_upload_id(),
                sheets=sheets_data,
                total_rows=total_rows,
                processed_at=datetime.utcnow().isoformat(),
            )

        except Exception as e:
            logger.error(f"File processing failed for {filename}: {e}")
            raise ServiceError(
                f"Failed to process file: {str(e)}",
                service_name="FileService",
                operation="process_file",
                error_code="FILE_PROCESSING_ERROR",
                context={"tenant_id": tenant_id, "filename": filename},
                original_error=e,
            )

    async def _process_csv(self, file_content: bytes, filename: str) -> List[SheetInfo]:
        """Process CSV file and return sheet data."""
        try:
            # Read CSV file
            df = pd.read_csv(io.BytesIO(file_content))

            # Extract metadata
            # Convert numeric column headers to strings
            columns = [str(col) for col in df.columns.tolist()]

            # Also need to convert column names in sample data to strings
            sample_data = []
            for row in df.head(5).to_dict("records"):
                # Convert all keys to strings to handle numeric column headers
                sample_data.append({str(k): v for k, v in row.items()})

            sheet_info = SheetInfo(
                name="Sheet1",
                columns=columns,
                row_count=len(df),
                sample_data=sample_data,
            )

            return [sheet_info]

        except Exception as e:
            logger.error(f"CSV processing error: {e}")
            raise

    async def _process_excel(
        self, file_content: bytes, filename: str
    ) -> List[SheetInfo]:
        """Process Excel file with multiple sheets."""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(io.BytesIO(file_content))
            sheets_data = []

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)

                # Skip empty sheets
                if df.empty:
                    continue

                # Convert numeric column headers to strings
                columns = [str(col) for col in df.columns.tolist()]

                # Also need to convert column names in sample data to strings
                sample_data = []
                for row in df.head(5).to_dict("records"):
                    # Convert all keys to strings to handle numeric column headers
                    sample_data.append({str(k): v for k, v in row.items()})

                sheet_info = SheetInfo(
                    name=sheet_name,
                    columns=columns,
                    row_count=len(df),
                    sample_data=sample_data,
                )
                sheets_data.append(sheet_info)

            return sheets_data

        except Exception as e:
            logger.error(f"Excel processing error: {e}")
            raise

    async def validate_column_mapping(
        self, columns: List[str], mapping: Dict[str, str]
    ) -> bool:
        """
        Validate that column mapping is valid.

        Args:
            columns: Available columns in the file
            mapping: Proposed column mapping

        Returns:
            True if mapping is valid
        """
        # Check that all mapped columns exist
        for source_col in mapping.keys():
            if source_col not in columns:
                raise ValidationError(
                    f"Column '{source_col}' not found in file. "
                    f"Available columns: {', '.join(columns)}"
                )

        # Check required fields are mapped
        required_fields = {"date", "description", "amount"}
        mapped_fields = set(mapping.values())

        missing_fields = required_fields - mapped_fields
        if missing_fields:
            raise ValidationError(
                f"Required fields not mapped: {', '.join(missing_fields)}"
            )

        return True

    async def extract_transactions(
        self,
        sheet_data: List[Dict[str, Any]],
        column_mapping: Dict[str, str],
        tenant_id: int,
    ) -> List[Dict[str, Any]]:
        """
        Extract transactions from sheet data using column mapping.
        Enhanced to support AI-detected debit/credit columns.

        Args:
            sheet_data: Raw data from sheet
            column_mapping: Mapping of columns to transaction fields
            tenant_id: Tenant ID for data isolation

        Returns:
            List of transaction dictionaries
        """
        transactions = []

        logger.info(f"Extracting transactions with column mapping: {column_mapping}")

        for row in sheet_data:
            try:
                # Extract mapped fields
                transaction = {}

                for source_col, target_field in column_mapping.items():
                    value = row.get(source_col)

                    # Handle special field processing
                    if target_field == "date" and value:
                        # Convert to standard date format
                        if isinstance(value, pd.Timestamp):
                            value = value.strftime("%Y-%m-%d")
                        elif isinstance(value, str):
                            # Try to parse date string
                            try:
                                parsed_date = pd.to_datetime(value)
                                value = parsed_date.strftime("%Y-%m-%d")
                            except (ValueError, TypeError, pd.errors.ParserError):
                                pass

                    elif (
                        target_field in ["amount", "debit_amount", "credit_amount"]
                        and value
                    ):
                        # Ensure amount is numeric
                        try:
                            # Handle various currency formats
                            value_str = str(value).strip()
                            # Remove currency symbols and thousands separators
                            value_str = (
                                value_str.replace(",", "")
                                .replace("$", "")
                                .replace("£", "")
                                .replace("€", "")
                            )
                            # Handle parentheses for negative values (accounting format)
                            if value_str.startswith("(") and value_str.endswith(")"):
                                value_str = "-" + value_str[1:-1]
                            value = float(value_str)
                        except (ValueError, TypeError):
                            logger.warning(f"Could not parse amount: {value}")
                            value = 0.0

                    transaction[target_field] = value

                # Add tenant ID
                transaction["tenant_id"] = tenant_id

                # Calculate final amount from debit/credit or use amount directly
                debit_amount = transaction.get("debit_amount", 0)
                credit_amount = transaction.get("credit_amount", 0)
                amount = transaction.get("amount", 0)

                # Handle NaN values from pandas
                import math

                # Handle various NaN representations
                def is_nan_value(val):
                    if val is None:
                        return True
                    if isinstance(val, float) and math.isnan(val):
                        return True
                    if str(val).lower() in ["nan", "null", "none", ""]:
                        return True
                    return False

                if is_nan_value(debit_amount):
                    debit_amount = 0
                if is_nan_value(credit_amount):
                    credit_amount = 0
                if is_nan_value(amount):
                    amount = 0

                # For debit/credit systems: debit is negative, credit is positive
                if debit_amount or credit_amount:
                    final_amount = float(credit_amount or 0) - float(debit_amount or 0)
                    transaction["amount"] = final_amount
                elif amount:
                    transaction["amount"] = float(amount)
                else:
                    transaction["amount"] = 0.0

                # Only add if we have required fields (date, description, and any amount)
                if (
                    transaction.get("date")
                    and transaction.get("description")
                    and transaction.get("amount") != 0
                ):
                    transactions.append(transaction)

            except Exception as e:
                logger.warning(f"Error processing row: {e}")
                continue

        return transactions

    def _generate_upload_id(self) -> str:
        """Generate unique upload ID."""
        import uuid

        return str(uuid.uuid4())

    async def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return list(self.supported_extensions)
