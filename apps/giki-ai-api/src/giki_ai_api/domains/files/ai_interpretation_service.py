"""
AI Interpretation Service - Unified wrapper for schema interpretation

This service provides consistent AI-powered column interpretation across all file processing
scenarios, ensuring the SchemaInterpretationAgent is used properly for all three milestones.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd

from ...core.config import settings
from ...shared.exceptions import ServiceError
from ..onboarding.schemas import FileColumnMapping
from .schema_interpretation_agent import (
    SchemaInterpretationAgent,
    SchemaInterpretationAgentConfig,
)
from .schemas import ColumnMapping, SchemaInterpretationResponse

logger = logging.getLogger(__name__)


class AIInterpretationService:
    """
    Unified service for AI-powered file interpretation.

    This service ensures consistent use of the SchemaInterpretationAgent across:
    - Onboarding flows (M2-Rezolve, M3-giki.ai)
    - Zero-onboarding flows (M1-Nuvie)
    - Production file uploads
    - Batch file processing
    """

    def __init__(self):
        """Initialize the AI interpretation service."""
        self.logger = logging.getLogger(__name__)
        self._agent = None
        self._config = None

    @property
    def agent(self) -> SchemaInterpretationAgent:
        """Lazy initialization of the schema interpretation agent."""
        if self._agent is None:
            self._config = SchemaInterpretationAgentConfig(
                model_name=settings.FILE_INGESTION_AI_MODEL_NAME
                or "gemini-2.0-flash-001",
                project=settings.VERTEX_PROJECT_ID or "rezolve-poc",
                location=settings.VERTEX_LOCATION or "us-central1",
            )
            self._agent = SchemaInterpretationAgent(config=self._config)
        return self._agent

    async def interpret_file(
        self,
        file_path: str,
        upload_id: Optional[str] = None,
        tenant_id: Optional[int] = None,
    ) -> SchemaInterpretationResponse:
        """
        Interpret a file's schema using AI.

        Args:
            file_path: Path to the file to interpret
            upload_id: Optional upload ID for tracking
            tenant_id: Optional tenant ID for multi-tenancy

        Returns:
            SchemaInterpretationResponse with column mappings and confidence scores
        """
        try:
            self.logger.info(f"Starting AI interpretation for file: {file_path}")

            # Read file and extract headers/sample data
            file_path_obj = Path(file_path)

            # Read more rows for better analysis
            if file_path_obj.suffix.lower() == ".csv":
                df = pd.read_csv(file_path, nrows=100)  # Increased from 10
            elif file_path_obj.suffix.lower() in [".xlsx", ".xls"]:
                df = pd.read_excel(file_path, nrows=100)  # Increased from 10
            else:
                raise ServiceError(f"Unsupported file type: {file_path_obj.suffix}")

            # Convert column headers to strings (handles numeric headers)
            headers = [str(col) for col in df.columns.tolist()]

            # Prepare enhanced sample data for AI interpretation
            sample_data = []
            # Use more rows for better pattern detection
            for _, row in df.head(20).iterrows():  # Increased from 5
                # Convert all values to strings, handling NaN and numeric headers
                row_data = []
                for col in df.columns:
                    val = row[col]
                    if pd.isna(val):
                        row_data.append("")
                    else:
                        row_data.append(str(val))
                sample_data.append(row_data)

            # Perform statistical analysis on columns for enhanced interpretation
            column_stats = self._analyze_column_patterns(df)

            # Check for previous interpretations from same tenant for learning
            previous_patterns = None
            if tenant_id and hasattr(self, "_conn"):
                previous_patterns = await self._get_tenant_interpretation_patterns(
                    tenant_id
                )

            # Use the schema interpretation agent with enhanced context
            self.logger.info(
                f"Calling AI agent with {len(headers)} headers and enhanced context"
            )
            interpretation_result = await self.agent.interpret_excel_schema(
                file_name=file_path_obj.name,
                file_headers=headers,
                sample_data=sample_data,
                column_stats=column_stats,
                previous_patterns=previous_patterns,
            )

            # Enhanced debit/credit analysis if dual columns detected
            debit_credit_inference = interpretation_result.get(
                "debit_credit_inference", {}
            )
            if not debit_credit_inference:
                # Check for debit/credit columns in mappings
                has_debit = any(
                    m.get("mapped_field") == "debit_amount"
                    for m in interpretation_result.get("column_mappings", [])
                )
                has_credit = any(
                    m.get("mapped_field") == "credit_amount"
                    for m in interpretation_result.get("column_mappings", [])
                )

                if has_debit and has_credit:
                    # Perform enhanced debit/credit analysis
                    debit_credit_inference = await self._enhanced_debit_credit_analysis(
                        df, interpretation_result
                    )

            # Convert to response format with validation
            column_mappings = []
            for mapping in interpretation_result.get("column_mappings", []):
                # Validate confidence based on data patterns
                validated_confidence = self._validate_mapping_confidence(
                    mapping, column_stats.get(mapping["original_name"], {})
                )

                column_mappings.append(
                    ColumnMapping(
                        original_name=mapping["original_name"],
                        mapped_field=mapping.get("mapped_field", "unmapped"),
                        confidence=validated_confidence,
                        reasoning=mapping.get("reasoning", ""),
                    )
                )

            # Check required fields mapping with enhanced logic
            required_fields = {"date", "description", "amount"}
            mapped_fields = {
                m.mapped_field
                for m in column_mappings
                if m.mapped_field and m.mapped_field != "unmapped"
            }

            # Special handling for debit/credit columns as amount
            if "debit_amount" in mapped_fields and "credit_amount" in mapped_fields:
                mapped_fields.add("amount")  # Dual columns satisfy amount requirement

            required_fields_mapped = {
                field: field in mapped_fields for field in required_fields
            }

            # Calculate enhanced overall confidence
            overall_confidence = self._calculate_overall_confidence(
                column_mappings, required_fields_mapped, debit_credit_inference
            )

            # Create response
            response = SchemaInterpretationResponse(
                upload_id=upload_id or "",
                filename=file_path_obj.name,
                column_mappings=column_mappings,
                overall_confidence=overall_confidence,
                required_fields_mapped=required_fields_mapped,
                interpretation_summary=interpretation_result.get("summary", ""),
                debit_credit_inference=debit_credit_inference,
                regional_variations=interpretation_result.get(
                    "regional_variations", []
                ),
            )

            self.logger.info(
                f"AI interpretation completed with {response.overall_confidence:.1%} confidence"
            )

            return response

        except Exception as e:
            self.logger.error(f"AI interpretation failed: {e}", exc_info=True)
            raise ServiceError(f"Failed to interpret file schema: {str(e)}")

    async def interpret_with_storage(
        self,
        file_path: str,
        upload_id: str,
        tenant_id: int,
        conn: Any,  # asyncpg.Connection
    ) -> SchemaInterpretationResponse:
        """
        Interpret file and store results in database.

        Args:
            file_path: Path to the file
            upload_id: Upload ID for tracking
            tenant_id: Tenant ID
            conn: Database connection

        Returns:
            SchemaInterpretationResponse
        """
        # Check if we already have an interpretation for this upload
        existing_query = """
            SELECT * FROM schema_interpretations 
            WHERE upload_id = $1 AND tenant_id = $2
        """
        existing = await conn.fetchrow(existing_query, upload_id, tenant_id)

        if existing:
            self.logger.info(f"Using cached interpretation for upload {upload_id}")
            # Reconstruct response from stored data
            import json

            return SchemaInterpretationResponse(
                upload_id=upload_id,
                filename=existing["filename"],
                column_mappings=[
                    ColumnMapping(**m) for m in json.loads(existing["column_mappings"])
                ],
                overall_confidence=existing["overall_confidence"],
                required_fields_mapped=json.loads(existing["required_fields_mapped"]),
                interpretation_summary=existing["interpretation_summary"],
                debit_credit_inference=json.loads(existing["debit_credit_inference"]),
                regional_variations=json.loads(existing["regional_variations"]),
            )

        # Perform new interpretation
        response = await self.interpret_file(file_path, upload_id, tenant_id)

        # Store in database
        import json
        import uuid
        from datetime import datetime

        insert_query = """
            INSERT INTO schema_interpretations (
                id, upload_id, tenant_id, filename, column_mappings,
                overall_confidence, required_fields_mapped, interpretation_summary,
                debit_credit_inference, regional_variations, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """

        await conn.execute(
            insert_query,
            str(uuid.uuid4()),
            upload_id,
            tenant_id,
            response.filename,
            json.dumps([m.model_dump() for m in response.column_mappings]),
            response.overall_confidence,
            json.dumps(response.required_fields_mapped),
            response.interpretation_summary,
            json.dumps(response.debit_credit_inference),
            json.dumps(response.regional_variations),
            datetime.utcnow(),
            datetime.utcnow(),
        )

        self.logger.info(f"Stored interpretation for upload {upload_id}")
        return response

    def _analyze_column_patterns(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Analyze patterns in each column for enhanced AI interpretation."""
        column_stats = {}

        for col in df.columns:
            stats = {
                "data_type": str(df[col].dtype),
                "null_count": df[col].isnull().sum(),
                "unique_count": df[col].nunique(),
                "sample_values": df[col].dropna().head(5).tolist(),
            }

            # Numeric analysis
            if pd.api.types.is_numeric_dtype(df[col]):
                stats.update(
                    {
                        "min_value": df[col].min(),
                        "max_value": df[col].max(),
                        "mean_value": df[col].mean(),
                        "has_negatives": (df[col] < 0).any(),
                    }
                )

            # String analysis
            elif pd.api.types.is_string_dtype(df[col]):
                stats.update(
                    {
                        "avg_length": df[col].str.len().mean(),
                        "has_dates": df[col]
                        .str.contains(r"\d{4}-\d{2}-\d{2}|\d{2}/\d{2}/\d{4}")
                        .any(),
                        "has_currency": df[col].str.contains(r"\$|\£|\€").any(),
                    }
                )

            column_stats[str(col)] = stats

        return column_stats

    async def _get_tenant_interpretation_patterns(
        self, tenant_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get previous interpretation patterns for this tenant for learning."""
        # TODO: Implement pattern learning from previous interpretations
        return None

    async def _enhanced_debit_credit_analysis(
        self, df: pd.DataFrame, interpretation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform enhanced debit/credit analysis for dual column systems."""
        # Find debit and credit columns
        debit_col = None
        credit_col = None

        for mapping in interpretation_result.get("column_mappings", []):
            if mapping.get("mapped_field") == "debit_amount":
                debit_col = mapping["original_name"]
            elif mapping.get("mapped_field") == "credit_amount":
                credit_col = mapping["original_name"]

        if not (debit_col and credit_col):
            return {}

        # Analyze the dual column usage patterns
        debit_data = df[debit_col].dropna()
        credit_data = df[credit_col].dropna()

        analysis = {
            "dual_column_system": True,
            "debit_column": debit_col,
            "credit_column": credit_col,
            "debit_entries": len(debit_data),
            "credit_entries": len(credit_data),
            "mutual_exclusivity": not (
                (df[debit_col].notna()) & (df[credit_col].notna())
            ).any(),
            "accounting_principle": "standard_debit_credit",
        }

        # Check for Nuvie-style patterns
        if "withdrawal" in debit_col.lower() and "deposit" in credit_col.lower():
            analysis["bank_style"] = "withdrawal_deposit"
            analysis["accounting_principle"] = "bank_perspective"

        return analysis

    def _validate_mapping_confidence(
        self, mapping: Dict[str, Any], column_stats: Dict[str, Any]
    ) -> float:
        """Validate and adjust confidence score based on data patterns."""
        base_confidence = mapping.get("confidence", 0.5)

        # Boost confidence for clear patterns
        mapped_field = mapping.get("mapped_field", "")

        if mapped_field == "date" and column_stats.get("has_dates"):
            base_confidence = min(1.0, base_confidence + 0.2)
        elif mapped_field in ["amount", "debit_amount", "credit_amount"]:
            if column_stats.get("data_type") in ["float64", "int64"]:
                base_confidence = min(1.0, base_confidence + 0.15)
        elif mapped_field == "description":
            if column_stats.get("avg_length", 0) > 10:
                base_confidence = min(1.0, base_confidence + 0.1)

        return base_confidence

    def _calculate_overall_confidence(
        self,
        column_mappings: List[ColumnMapping],
        required_fields_mapped: Dict[str, bool],
        debit_credit_inference: Dict[str, Any],
    ) -> float:
        """Calculate enhanced overall confidence score."""
        if not column_mappings:
            return 0.0

        # Base confidence from individual mappings
        mapped_confidences = [
            m.confidence for m in column_mappings if m.mapped_field != "unmapped"
        ]
        if not mapped_confidences:
            return 0.0

        base_confidence = sum(mapped_confidences) / len(mapped_confidences)

        # Boost for required fields
        required_boost = sum(required_fields_mapped.values()) / len(
            required_fields_mapped
        )

        # Boost for debit/credit detection
        debit_credit_boost = (
            0.1 if debit_credit_inference.get("dual_column_system") else 0.0
        )

        # Combined confidence
        overall = (base_confidence * 0.7) + (required_boost * 0.2) + debit_credit_boost

        return min(1.0, overall)

    def convert_to_file_column_mapping(
        self, interpretation: SchemaInterpretationResponse
    ) -> FileColumnMapping:
        """
        Convert SchemaInterpretationResponse to FileColumnMapping for compatibility.

        This ensures backward compatibility with existing code that expects
        FileColumnMapping objects.
        """
        # Find mapped columns
        date_col = None
        desc_col = None
        amount_col = None
        debit_col = None
        credit_col = None
        category_col = None
        vendor_col = None

        for mapping in interpretation.column_mappings:
            if mapping.mapped_field == "date":
                date_col = mapping.original_name
            elif mapping.mapped_field == "description":
                desc_col = mapping.original_name
            elif mapping.mapped_field == "amount":
                amount_col = mapping.original_name
            elif mapping.mapped_field == "debit_amount":
                debit_col = mapping.original_name
            elif mapping.mapped_field == "credit_amount":
                credit_col = mapping.original_name
            elif mapping.mapped_field == "category":
                category_col = mapping.original_name
            elif mapping.mapped_field == "vendor":
                vendor_col = mapping.original_name

        return FileColumnMapping(
            date_column=date_col or "",
            description_column=desc_col or "",
            amount_column=amount_col,
            debit_column=debit_col,
            credit_column=credit_col,
            category_column=category_col,
            vendor_column=vendor_col,
        )

    async def validate_interpretation(
        self, interpretation: SchemaInterpretationResponse
    ) -> Dict[str, Any]:
        """
        Validate that the AI interpretation meets minimum requirements.

        Returns:
            Dict with validation results and any issues found
        """
        issues = []
        warnings = []

        # Check required fields
        if not interpretation.required_fields_mapped.get("date"):
            issues.append("No date column identified")
        if not interpretation.required_fields_mapped.get("description"):
            issues.append("No description column identified")
        if not interpretation.required_fields_mapped.get("amount"):
            # Check if we have debit/credit columns instead
            has_debit_credit = any(
                m.mapped_field in ["debit_amount", "credit_amount"]
                for m in interpretation.column_mappings
            )
            if not has_debit_credit:
                issues.append(
                    "No amount column identified (neither single amount nor debit/credit)"
                )

        # Check confidence
        if interpretation.overall_confidence < 0.7:
            warnings.append(
                f"Low confidence interpretation ({interpretation.overall_confidence:.1%})"
            )

        # Check for zero-onboarding scenario
        has_category = any(
            m.mapped_field == "category" for m in interpretation.column_mappings
        )

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "has_category_column": has_category,
            "is_zero_onboarding": not has_category,
            "confidence": interpretation.overall_confidence,
        }


# Singleton instance for consistent usage
_ai_interpretation_service = None


def get_ai_interpretation_service() -> AIInterpretationService:
    """Get the singleton AI interpretation service instance."""
    global _ai_interpretation_service
    if _ai_interpretation_service is None:
        _ai_interpretation_service = AIInterpretationService()
    return _ai_interpretation_service
