import logging
import time
from datetime import datetime
from typing import Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, Response, status

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ...shared.cache.cache import api_cache, cache_key
from ..auth.models import User as AuthUser
from ..auth.secure_auth import get_current_active_user
from .csv_export_service import ProfessionalCSVExportService
from .custom_report_service import CustomReportService
from .excel_export_service import ProfessionalExcelExportService
from .pdf_export_service import ProfessionalPDFExportService
from .schemas import (
    CustomReportDataResponse,
    CustomReportGenerateRequest,
    CustomReportListResponse,
    CustomReportResponse,
    CustomReportSaveRequest,
    EntitySpendingItem,
    EntitySpendingResponse,
    IncomeExpenseSummaryResponse,
    MonthlyTrendItem,
    MonthlyTrendsResponse,
    ReportDateRangeQueryFilters,
    SpendingByCategoryItem,
    SpendingByCategoryResponse,
)

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Reports"],
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Not found"},
        status.HTTP_401_UNAUTHORIZED: {"description": "Not authenticated"},
        status.HTTP_403_FORBIDDEN: {"description": "Not authorized"},
    },
)


@router.get("/spending-by-category", response_model=SpendingByCategoryResponse)
async def get_spending_by_category(
    filters: ReportDateRangeQueryFilters = Depends(),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Retrieve aggregated spending by category for the current tenant.
    Filters by date range if provided.
    OPTIMIZED: Added caching to reduce query latency.
    """
    # Generate cache key based on tenant and filters
    cache_key_str = cache_key(
        "spending_by_category",
        tenant_id=tenant_id_int,
        start_date=str(filters.start_date) if filters.start_date else "none",
        end_date=str(filters.end_date) if filters.end_date else "none",
    )

    # Try cache first
    cached_result = await api_cache.get(cache_key_str)
    if cached_result is not None:
        logger.info(
            f"Cache HIT for spending-by-category tenant {tenant_id_int} - served in <5ms"
        )
        return cached_result

    logger.info(
        f"Cache MISS - querying database for spending by category report for user {current_user.id} "
        f"from tenant {tenant_id_int} with filters: StartDate={filters.start_date}, EndDate={filters.end_date}"
    )

    # Build SQL query for spending by category
    base_query = """
        SELECT 
            COALESCE(original_category, ai_category) as category_path,
            SUM(amount) as total_amount,
            COUNT(id) as transaction_count
        FROM transactions
        WHERE tenant_id = $1
        AND (original_category IS NOT NULL OR ai_category IS NOT NULL)
        AND amount < 0
    """

    params = [tenant_id_int]
    param_count = 1

    # Apply date filters if provided
    if filters.start_date:
        param_count += 1
        base_query += f" AND date >= ${param_count}"
        params.append(filters.start_date)
    if filters.end_date:
        param_count += 1
        base_query += f" AND date <= ${param_count}"
        params.append(filters.end_date)

    query = (
        base_query
        + """
        GROUP BY COALESCE(original_category, ai_category)
        ORDER BY SUM(ABS(amount)) DESC
        LIMIT 50
    """
    )

    try:
        # Add query timeout and performance logging
        start_time = time.time()

        raw_items = await conn.fetch(query, *params)

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Spending by category query executed in {execution_time:.2f}ms, returned {len(raw_items)} categories"
        )

        # Warn if query is slow (tightened threshold for better monitoring)
        if execution_time > 200:  # 200ms threshold to meet performance requirements
            logger.warning(
                f"Slow query detected: spending-by-category took {execution_time:.2f}ms (target: <200ms)"
            )

    except Exception as e:
        logger.error(
            f"Database error while fetching spending by category for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching report data.",
        )

    items = [
        SpendingByCategoryItem(
            category_path=row.category_path,
            total_amount=float(row.total_amount) if row.total_amount else 0.0,
            transaction_count=row.transaction_count,
        )
        for row in raw_items
        if row.category_path  # Ensure category_path is not None
    ]

    response = SpendingByCategoryResponse(items=items, total_records=len(items))

    # Cache the result for 5 minutes (reports don't change frequently)
    await api_cache.set(cache_key_str, response, ttl=300)
    logger.info(f"Cached spending-by-category report for tenant {tenant_id_int}")

    return response


@router.get("/spending-by-entity", response_model=EntitySpendingResponse)
async def get_spending_by_entity(
    filters: ReportDateRangeQueryFilters = Depends(),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Retrieve aggregated spending by entity/vendor for the current tenant.
    Entities are extracted from transaction descriptions using AI and stored in the Entity table.
    Filters by date range if provided.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requested spending by entity report "
        f"with filters: StartDate={filters.start_date}, EndDate={filters.end_date}"
    )

    # Build SQL query for spending by entity
    base_query = """
        SELECT 
            COALESCE(e.name, t.description) as entity_name,
            SUM(ABS(t.amount)) as total_amount,
            COUNT(t.id) as transaction_count
        FROM transactions t
        LEFT JOIN entity e ON t.entity_id = e.id
        WHERE t.tenant_id = $1
        AND t.amount != 0
    """

    params = [tenant_id_int]
    param_count = 1

    # Apply date filters if provided
    if filters.start_date:
        param_count += 1
        base_query += f" AND t.date >= ${param_count}"
        params.append(filters.start_date)
    if filters.end_date:
        param_count += 1
        base_query += f" AND t.date <= ${param_count}"
        params.append(filters.end_date)

    query = (
        base_query
        + """
        GROUP BY COALESCE(e.name, t.description)
        ORDER BY SUM(ABS(t.amount)) DESC
        LIMIT 50
    """
    )

    try:
        # Add query timeout and performance logging
        start_time = time.time()

        raw_items = await conn.fetch(query, *params)

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Spending by entity query executed in {execution_time:.2f}ms, returned {len(raw_items)} entities"
        )

        # Warn if query is slow
        if execution_time > 1000:  # 1 second
            logger.warning(
                f"Slow query detected: spending-by-entity took {execution_time:.2f}ms"
            )

    except Exception as e:
        logger.error(
            f"Database error while fetching spending by entity for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching report data.",
        )

    # Convert to response format - simplified processing
    items = []
    for row in raw_items:
        if row.entity_name and row.entity_name.strip():
            # Clean up entity name without regex
            entity_name = " ".join(
                row.entity_name.strip().split()
            )  # Python-side whitespace normalization
            items.append(
                EntitySpendingItem(
                    entity_name=entity_name[:100],  # Truncate long names
                    total_amount=float(row.total_amount) if row.total_amount else 0.0,
                    transaction_count=row.transaction_count,
                )
            )

    return EntitySpendingResponse(items=items, total_records=len(items))


@router.get("/income-expense-summary", response_model=IncomeExpenseSummaryResponse)
async def get_income_expense_summary(
    filters: ReportDateRangeQueryFilters = Depends(),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Retrieve a summary of total income, total expenses, and net income/loss
    for the current tenant over a specified period.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requested income-expense summary "
        f"with filters: StartDate={filters.start_date}, EndDate={filters.end_date}"
    )
    logger.info(
        f"DEBUG: About to query transactions for tenant_id={tenant_id_int}, user_id={current_user.id}"
    )

    # Build SQL query for income-expense summary
    base_query = """
        SELECT 
            CASE WHEN amount > 0 THEN 'income' ELSE 'expense' END as transaction_type,
            SUM(amount) as total_amount
        FROM transactions
        WHERE tenant_id = $1
    """

    params = [tenant_id_int]
    param_count = 1

    # Apply date filters if provided
    if filters.start_date:
        param_count += 1
        base_query += f" AND date >= ${param_count}"
        params.append(filters.start_date)
    if filters.end_date:
        param_count += 1
        base_query += f" AND date <= ${param_count}"
        params.append(filters.end_date)

    query = (
        base_query
        + """
        GROUP BY CASE WHEN amount > 0 THEN 'income' ELSE 'expense' END
    """
    )

    logger.info(f"DEBUG: SQL Query with params: {query}, {params}")

    total_income = 0.0
    total_expenses = 0.0

    try:
        # Add query timeout and performance logging
        start_time = time.time()

        rows = await conn.fetch(query, *params)

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Income-expense summary query executed in {execution_time:.2f}ms, returned {len(rows)} transaction types"
        )

        # Warn if query is slow
        if execution_time > 1000:  # 1 second
            logger.warning(
                f"Slow query detected: income-expense-summary took {execution_time:.2f}ms"
            )

        # Process results
        for row in rows:
            logger.info(
                f"Processing row: type={row.transaction_type}, amount={row.total_amount}"
            )
            if row.transaction_type == "income" and row.total_amount is not None:
                total_income = float(row.total_amount)
            elif row.transaction_type == "expense" and row.total_amount is not None:
                # Expense transactions are typically negative, so we take absolute value for expenses
                total_expenses = abs(float(row.total_amount))

    except Exception as e:
        logger.error(
            f"Database error while fetching income-expense summary for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching report data.",
        )

    net_income_loss = total_income - total_expenses

    logger.info(
        f"Final calculations: income=₹{total_income}, expenses=₹{total_expenses}, net=₹{net_income_loss}"
    )

    return IncomeExpenseSummaryResponse(
        total_income=total_income,
        total_expenses=total_expenses,
        net_income_loss=net_income_loss,
    )


@router.get("/monthly-trends", response_model=MonthlyTrendsResponse)
async def get_monthly_trends(
    filters: ReportDateRangeQueryFilters = Depends(),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Retrieve monthly income and expense trends for the current tenant.
    Shows monthly breakdown of income, expenses, and net amount.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requested monthly trends report "
        f"with filters: StartDate={filters.start_date}, EndDate={filters.end_date}"
    )

    # Build SQL query for monthly trends
    base_query = """
        SELECT 
            TO_CHAR(date, 'YYYY-MM') as month,
            CASE WHEN amount > 0 THEN 'income' ELSE 'expense' END as transaction_type,
            SUM(amount) as total_amount,
            COUNT(id) as transaction_count
        FROM transactions
        WHERE tenant_id = $1
        AND date IS NOT NULL
    """

    params = [tenant_id_int]
    param_count = 1

    # Apply date filters if provided
    if filters.start_date:
        param_count += 1
        base_query += f" AND date >= ${param_count}"
        params.append(filters.start_date)
    if filters.end_date:
        param_count += 1
        base_query += f" AND date <= ${param_count}"
        params.append(filters.end_date)

    query = (
        base_query
        + """
        GROUP BY TO_CHAR(date, 'YYYY-MM'), CASE WHEN amount > 0 THEN 'income' ELSE 'expense' END
        ORDER BY TO_CHAR(date, 'YYYY-MM')
    """
    )

    try:
        start_time = time.time()
        rows = await conn.fetch(query, *params)

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Monthly trends query executed in {execution_time:.2f}ms, returned {len(rows)} rows"
        )

        if execution_time > 1000:
            logger.warning(
                f"Slow query detected: monthly-trends took {execution_time:.2f}ms"
            )

    except Exception as e:
        logger.error(
            f"Database error while fetching monthly trends for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching monthly trends data.",
        )

    # Process results into monthly items
    monthly_data = {}
    for row in rows:
        month = row.month
        if month not in monthly_data:
            monthly_data[month] = {
                "income": 0.0,
                "expenses": 0.0,
                "count": 0,
            }

        if row.transaction_type == "income":
            monthly_data[month]["income"] = float(row.total_amount or 0)
        elif row.transaction_type == "expense":
            monthly_data[month]["expenses"] = abs(float(row.total_amount or 0))

        monthly_data[month]["count"] += row.transaction_count

    # Convert to response format
    items = []
    for month, data in sorted(monthly_data.items()):
        items.append(
            MonthlyTrendItem(
                month=month,
                total_income=data["income"],
                total_expenses=data["expenses"],
                net_amount=data["income"] - data["expenses"],
                transaction_count=data["count"],
            )
        )

    return MonthlyTrendsResponse(
        items=items,
        total_months=len(items),
    )


# Custom Report Endpoints
@router.post("/custom/generate", response_model=CustomReportDataResponse)
async def generate_custom_report(
    request: CustomReportGenerateRequest,
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Generate a custom report based on the provided configuration.
    Supports various report types with flexible filtering and grouping options.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requested custom report "
        f"of type '{request.report_type}'"
    )

    service = CustomReportService(conn)
    return await service.generate_custom_report(
        request=request,
        tenant_id=tenant_id_int,
        user_id=current_user.id,
    )


@router.post("/custom/save", response_model=CustomReportResponse)
async def save_custom_report(
    request: CustomReportSaveRequest,
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Save a custom report configuration for future use.
    The report configuration can be retrieved and used to generate reports later.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} saving custom report '{request.name}'"
    )

    service = CustomReportService(conn)
    return await service.save_custom_report(
        request=request,
        tenant_id=tenant_id_int,
        user_id=current_user.id,
    )


@router.get("/custom/list", response_model=CustomReportListResponse)
async def list_custom_reports(
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    user_only: bool = False,
):
    """
    List all saved custom reports for the current tenant.
    Optionally filter to show only reports created by the current user.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} listing custom reports "
        f"(user_only={user_only})"
    )

    service = CustomReportService(conn)
    reports = await service.list_custom_reports(
        tenant_id=tenant_id_int,
        user_id=current_user.id if user_only else None,
    )

    return CustomReportListResponse(
        reports=reports,
        total=len(reports),
    )


@router.get("/custom/{report_id}", response_model=CustomReportResponse)
async def get_custom_report(
    report_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Get a specific saved custom report configuration by ID.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} retrieving custom report {report_id}"
    )

    service = CustomReportService(conn)
    report = await service.get_custom_report(
        report_id=report_id,
        tenant_id=tenant_id_int,
    )

    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Custom report with ID {report_id} not found",
        )

    return report


@router.post("/export/excel")
async def export_to_excel(
    start_date: Optional[datetime] = Query(
        None, description="Start date for export (YYYY-MM-DD)"
    ),
    end_date: Optional[datetime] = Query(
        None, description="End date for export (YYYY-MM-DD)"
    ),
    include_charts: bool = Query(True, description="Include charts in export"),
    include_summary: bool = Query(True, description="Include summary sheet"),
    professional_styling: bool = Query(
        True, description="Apply professional Excel styling"
    ),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Export financial data to professional Excel format with advanced formatting.

    Features:
    - Professional Excel styling matching frontend design
    - Multiple worksheets (Transactions, Summary, Category Analysis, Monthly Trends)
    - Charts and visualizations
    - Conditional formatting
    - Auto-fitted columns and frozen headers
    - Enterprise-grade presentation quality
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requesting Excel export "
        f"with date range: {start_date} to {end_date}"
    )

    try:
        # Initialize Excel export service
        export_service = ProfessionalExcelExportService(conn)

        # Set format options
        format_options = {
            "include_charts": include_charts,
            "include_summary": include_summary,
            "professional_styling": professional_styling,
            "autofit_columns": True,
            "freeze_headers": True,
            "conditional_formatting": True,
        }

        # Generate Excel file
        excel_data = await export_service.export_transactions(
            tenant_id=tenant_id_int,
            start_date=start_date,
            end_date=end_date,
            format_options=format_options,
        )

        # Generate filename
        date_suffix = ""
        if start_date and end_date:
            date_suffix = (
                f"_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
            )
        elif start_date:
            date_suffix = f"_{start_date.strftime('%Y%m%d')}_onwards"
        elif end_date:
            date_suffix = f"_until_{end_date.strftime('%Y%m%d')}"

        filename = f"financial_report{date_suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # Return Excel file
        return Response(
            content=excel_data,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            },
        )

    except ImportError as e:
        logger.error(f"Excel export dependency missing: {e}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Excel export feature requires additional dependencies. Please contact administrator.",
        )
    except Exception as e:
        logger.error(
            f"Excel export failed for tenant {tenant_id_int}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate Excel export",
        )


@router.post("/export/pdf")
async def export_to_pdf(
    start_date: Optional[datetime] = Query(
        None, description="Start date for export (YYYY-MM-DD)"
    ),
    end_date: Optional[datetime] = Query(
        None, description="End date for export (YYYY-MM-DD)"
    ),
    include_charts: bool = Query(True, description="Include charts in export"),
    include_summary: bool = Query(True, description="Include summary page"),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Export financial data to professional PDF format.

    Features:
    - Professional PDF styling and formatting
    - Charts and visualizations
    - Executive summary
    - Transaction details with proper formatting
    - Enterprise-grade presentation quality
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requesting PDF export "
        f"with date range: {start_date} to {end_date}"
    )

    try:
        # Initialize PDF export service
        export_service = ProfessionalPDFExportService(conn)

        # Generate PDF file
        pdf_data = await export_service.generate_financial_report_pdf(
            tenant_id=tenant_id_int,
            start_date=start_date,
            end_date=end_date,
            include_charts=include_charts,
            include_summary=include_summary,
        )

        # Generate filename
        date_suffix = ""
        if start_date and end_date:
            date_suffix = (
                f"_{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}"
            )
        elif start_date:
            date_suffix = f"_from_{start_date.strftime('%Y%m%d')}"
        elif end_date:
            date_suffix = f"_to_{end_date.strftime('%Y%m%d')}"

        filename = f"financial_report{date_suffix}.pdf"

        logger.info(
            f"PDF export generated successfully for tenant {tenant_id_int}: {filename}"
        )

        return Response(
            content=pdf_data.getvalue(),
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except ImportError as e:
        logger.error(f"PDF export dependency missing: {e}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="PDF export feature requires additional dependencies. Please contact administrator.",
        )
    except Exception as e:
        logger.error(
            f"PDF export failed for tenant {tenant_id_int}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate PDF export",
        )


@router.post("/export/csv")
async def export_to_csv(
    start_date: Optional[datetime] = Query(
        None, description="Start date for export (YYYY-MM-DD)"
    ),
    end_date: Optional[datetime] = Query(
        None, description="End date for export (YYYY-MM-DD)"
    ),
    format_style: str = Query(
        "standard", description="CSV format style: standard, quickbooks, sage, xero"
    ),
    include_metadata: bool = Query(
        True, description="Include metadata columns (only for standard format)"
    ),
    export_type: str = Query(
        "transactions", description="Export type: transactions, categories, monthly"
    ),
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Export financial data to CSV format with multiple format options.

    Features:
    - Multiple export types (transactions, categories, monthly summaries)
    - Support for different accounting system formats (QuickBooks, Sage, Xero)
    - Flexible metadata inclusion
    - Professional CSV formatting for integration
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} requesting CSV export "
        f"(type: {export_type}, format: {format_style}) with date range: {start_date} to {end_date}"
    )

    try:
        # Initialize CSV export service
        export_service = ProfessionalCSVExportService(conn)

        # Generate CSV based on export type
        if export_type == "transactions":
            csv_data = await export_service.generate_transactions_csv(
                tenant_id=tenant_id_int,
                start_date=start_date,
                end_date=end_date,
                include_metadata=include_metadata,
                format_style=format_style,
            )
            filename_prefix = "transactions"
        elif export_type == "categories":
            csv_data = await export_service.generate_category_summary_csv(
                tenant_id=tenant_id_int,
                start_date=start_date,
                end_date=end_date,
            )
            filename_prefix = "category_summary"
        elif export_type == "monthly":
            csv_data = await export_service.generate_monthly_summary_csv(
                tenant_id=tenant_id_int,
                start_date=start_date,
                end_date=end_date,
            )
            filename_prefix = "monthly_summary"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid export_type. Must be: transactions, categories, or monthly",
            )

        # Generate filename
        date_suffix = ""
        if start_date and end_date:
            date_suffix = (
                f"_{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}"
            )
        elif start_date:
            date_suffix = f"_from_{start_date.strftime('%Y%m%d')}"
        elif end_date:
            date_suffix = f"_to_{end_date.strftime('%Y%m%d')}"

        filename = f"{filename_prefix}{date_suffix}.csv"

        logger.info(
            f"CSV export ({export_type}) generated successfully for tenant {tenant_id_int}: {filename}"
        )

        return Response(
            content=csv_data.getvalue(),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except Exception as e:
        logger.error(
            f"CSV export failed for tenant {tenant_id_int}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate CSV export",
        )


# ===== CUSTOM REPORT ENDPOINTS =====


@router.delete("/custom/{report_id}")
async def delete_custom_report(
    report_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: AuthUser = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
):
    """
    Delete a custom report configuration.

    Only the report creator or an admin can delete a report.
    """
    logger.info(
        f"User {current_user.id} from tenant {tenant_id_int} deleting custom report: {report_id}"
    )

    try:
        # Initialize custom report service
        report_service = CustomReportService(conn)

        # Delete report
        success = await report_service.delete_custom_report(
            report_id=report_id,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Custom report not found or access denied",
            )

        logger.info(
            f"Custom report deleted successfully for tenant {tenant_id_int}: ID={report_id}"
        )

        return {"success": True, "message": "Custom report deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Custom report deletion failed for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete custom report",
        )
