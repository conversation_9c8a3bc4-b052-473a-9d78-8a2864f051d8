"""
Reports Agent - Financial Report Generation
============================================

This agent provides intelligent report generation, data visualization,
and analytics using the ADK v1.3.0 pattern.

Key capabilities:
- Generate comprehensive spending reports
- Create financial statements
- Perform trend analysis
- Generate custom visualizations
- Export report data in various formats
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import asyncpg

from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)

# Real ADK imports
from google.adk.tools import FunctionTool
from vertexai.generative_models import GenerativeModel

# Import StandardGikiAgent base class
from ...shared.ai.standard_giki_agent import StandardGikiAgent

# ===== SPENDING REPORT TOOLS =====


async def generate_spending_reports_tool_function(
    tenant_id: int,
    db: asyncpg.Connection,
    report_type: str = "category",
    date_range: Optional[Dict[str, str]] = None,
    grouping: Optional[str] = "monthly",
    **_kwargs,
) -> Dict[str, Any]:
    """
    Generate comprehensive spending analysis reports.

    Creates detailed spending reports with breakdowns by category,
    time period, vendor, or custom groupings.

    Args:
        tenant_id: Tenant ID for data isolation
        db: Database session
        report_type: Type of spending report (category, vendor, temporal)
        date_range: Optional date range for the report
        grouping: Grouping strategy (monthly, weekly, daily)

    Returns:
        Dictionary with spending report data
    """
    logger.info(f"Generating {report_type} spending report for tenant {tenant_id}")

    try:
        from ..categories.service import CategoryService
        from ..transactions.service import TransactionService

        transaction_service = TransactionService(db=db)
        CategoryService(db=db)

        # Parse date range
        if date_range:
            start_date = datetime.fromisoformat(date_range.get("start", ""))
            end_date = datetime.fromisoformat(date_range.get("end", ""))
        else:
            # Default to last 30 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

        # Get transactions for the period
        filters = {
            "date_range": {"start": start_date.isoformat(), "end": end_date.isoformat()}
        }

        transactions = await transaction_service.get_transactions(
            tenant_id=tenant_id, filters=filters
        )

        # Generate report based on type
        report_data = {
            "report_type": report_type,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
            },
            "grouping": grouping,
            "data": [],
            "summary": {},
        }

        if report_type == "category":
            # Group by category
            category_spending = {}
            total_spending = 0

            for txn in transactions:
                if txn.amount < 0:  # Only expenses
                    amount = abs(txn.amount)
                    category = txn.get_display_category() or "Uncategorized"

                    if category not in category_spending:
                        category_spending[category] = {
                            "amount": 0,
                            "count": 0,
                            "transactions": [],
                        }

                    category_spending[category]["amount"] += amount
                    category_spending[category]["count"] += 1
                    total_spending += amount

            # Sort by amount and format
            sorted_categories = sorted(
                category_spending.items(), key=lambda x: x[1]["amount"], reverse=True
            )

            for category, data in sorted_categories:
                percentage = (
                    (data["amount"] / total_spending * 100) if total_spending > 0 else 0
                )
                report_data["data"].append(
                    {
                        "category": category,
                        "amount": round(data["amount"], 2),
                        "count": data["count"],
                        "percentage": round(percentage, 2),
                    }
                )

            report_data["summary"] = {
                "total_spending": round(total_spending, 2),
                "category_count": len(category_spending),
                "transaction_count": sum(
                    d["count"] for d in category_spending.values()
                ),
                "average_per_category": round(
                    total_spending / len(category_spending), 2
                )
                if category_spending
                else 0,
            }

        elif report_type == "temporal":
            # Group by time period
            temporal_data = {}

            for txn in transactions:
                if txn.amount < 0 and txn.date:  # Only expenses with dates
                    amount = abs(txn.amount)

                    if grouping == "daily":
                        period_key = txn.date.strftime("%Y-%m-%d")
                    elif grouping == "weekly":
                        period_key = txn.date.strftime("%Y-W%U")
                    else:  # monthly
                        period_key = txn.date.strftime("%Y-%m")

                    if period_key not in temporal_data:
                        temporal_data[period_key] = {"amount": 0, "count": 0}

                    temporal_data[period_key]["amount"] += amount
                    temporal_data[period_key]["count"] += 1

            # Sort by date and format
            for period, data in sorted(temporal_data.items()):
                report_data["data"].append(
                    {
                        "period": period,
                        "amount": round(data["amount"], 2),
                        "count": data["count"],
                    }
                )

            # Calculate summary statistics
            amounts = [d["amount"] for d in temporal_data.values()]
            report_data["summary"] = {
                "total_spending": round(sum(amounts), 2),
                "average_per_period": round(sum(amounts) / len(amounts), 2)
                if amounts
                else 0,
                "max_spending_period": max(
                    temporal_data.items(), key=lambda x: x[1]["amount"]
                )[0]
                if temporal_data
                else None,
                "min_spending_period": min(
                    temporal_data.items(), key=lambda x: x[1]["amount"]
                )[0]
                if temporal_data
                else None,
            }

        logger.info(
            f"Spending report generated: {len(report_data['data'])} data points"
        )
        return report_data

    except Exception as e:
        logger.error(f"Spending report generation failed: {e}")
        raise ServiceError(f"Failed to generate spending report: {e}")


# ===== FINANCIAL STATEMENT TOOLS =====


async def create_income_statements_tool_function(
    tenant_id: int,
    db: asyncpg.Connection,
    period_type: str = "monthly",
    periods: int = 3,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Generate financial income statements.

    Creates standard income statements with revenue, expenses, and
    net income calculations for specified periods.

    Args:
        tenant_id: Tenant ID for data isolation
        db: Database session
        period_type: Statement period type (monthly, quarterly, annual)
        periods: Number of periods to include

    Returns:
        Dictionary with income statement data
    """
    logger.info(f"Creating {period_type} income statements for tenant {tenant_id}")

    try:
        from ..transactions.service import TransactionService

        transaction_service = TransactionService(db=db)

        # Calculate period boundaries
        end_date = datetime.now()
        statements = []

        for i in range(periods):
            if period_type == "monthly":
                if i == 0:
                    period_end = end_date.replace(day=1) - timedelta(days=1)
                else:
                    period_end = statements[-1]["period_start"] - timedelta(days=1)
                period_start = period_end.replace(day=1)
            elif period_type == "quarterly":
                quarter = ((end_date.month - 1) // 3) + 1
                if i == 0:
                    period_end = datetime(end_date.year, quarter * 3, 1) - timedelta(
                        days=1
                    )
                else:
                    period_end = statements[-1]["period_start"] - timedelta(days=1)
                period_start = period_end.replace(month=((quarter - 1) * 3) + 1, day=1)
            else:  # annual
                if i == 0:
                    period_end = datetime(end_date.year - 1, 12, 31)
                else:
                    period_end = datetime(
                        statements[-1]["period_start"].year - 1, 12, 31
                    )
                period_start = datetime(period_end.year, 1, 1)

            # Get transactions for the period
            filters = {
                "date_range": {
                    "start": period_start.isoformat(),
                    "end": period_end.isoformat(),
                }
            }

            transactions = await transaction_service.get_transactions(
                tenant_id=tenant_id, filters=filters
            )

            # Calculate income statement components
            revenue = sum(txn.amount for txn in transactions if txn.amount > 0)
            expenses = abs(sum(txn.amount for txn in transactions if txn.amount < 0))

            # Categorize expenses
            expense_categories = {}
            for txn in transactions:
                if txn.amount < 0:
                    category = txn.get_display_category() or "Other Expenses"
                    if category not in expense_categories:
                        expense_categories[category] = 0
                    expense_categories[category] += abs(txn.amount)

            statement = {
                "period_start": period_start.isoformat(),
                "period_end": period_end.isoformat(),
                "period_label": period_start.strftime("%B %Y")
                if period_type == "monthly"
                else f"{period_start.year}",
                "revenue": round(revenue, 2),
                "expenses": round(expenses, 2),
                "net_income": round(revenue - expenses, 2),
                "expense_breakdown": [
                    {"category": cat, "amount": round(amt, 2)}
                    for cat, amt in sorted(
                        expense_categories.items(), key=lambda x: x[1], reverse=True
                    )
                ],
                "metrics": {
                    "profit_margin": round((revenue - expenses) / revenue * 100, 2)
                    if revenue > 0
                    else 0,
                    "expense_ratio": round(expenses / revenue * 100, 2)
                    if revenue > 0
                    else 0,
                },
            }

            statements.append(statement)

        # Calculate comparative metrics
        if len(statements) > 1:
            for i in range(1, len(statements)):
                current = statements[i]
                previous = statements[i - 1]

                current["comparison"] = {
                    "revenue_change": round(
                        (current["revenue"] - previous["revenue"])
                        / previous["revenue"]
                        * 100,
                        2,
                    )
                    if previous["revenue"] > 0
                    else 0,
                    "expense_change": round(
                        (current["expenses"] - previous["expenses"])
                        / previous["expenses"]
                        * 100,
                        2,
                    )
                    if previous["expenses"] > 0
                    else 0,
                    "net_income_change": round(
                        current["net_income"] - previous["net_income"], 2
                    ),
                }

        result = {
            "period_type": period_type,
            "statements": statements,
            "summary": {
                "average_revenue": round(
                    sum(s["revenue"] for s in statements) / len(statements), 2
                ),
                "average_expenses": round(
                    sum(s["expenses"] for s in statements) / len(statements), 2
                ),
                "average_net_income": round(
                    sum(s["net_income"] for s in statements) / len(statements), 2
                ),
                "total_periods": len(statements),
            },
        }

        logger.info(f"Income statements created: {len(statements)} periods")
        return result

    except Exception as e:
        logger.error(f"Income statement creation failed: {e}")
        raise ServiceError(f"Failed to create income statements: {e}")


# ===== TREND ANALYSIS TOOLS =====


async def generate_trend_analysis_tool_function(
    tenant_id: int,
    db: asyncpg.Connection,
    analysis_type: str = "spending",
    metrics: Optional[List[str]] = None,
    time_period: str = "6_months",
    **_kwargs,
) -> Dict[str, Any]:
    """
    Generate trend identification and analysis reports.

    Analyzes financial trends over time to identify patterns,
    anomalies, and provide predictive insights.

    Args:
        tenant_id: Tenant ID for data isolation
        db: Database session
        analysis_type: Type of trend analysis
        metrics: Specific metrics to analyze
        time_period: Time period for analysis

    Returns:
        Dictionary with trend analysis results
    """
    logger.info(f"Generating {analysis_type} trend analysis for tenant {tenant_id}")

    try:
        from vertexai.generative_models import GenerativeModel

        from ..transactions.service import TransactionService

        transaction_service = TransactionService(db=db)
        model = GenerativeModel("gemini-2.0-flash-001")

        # Calculate time period
        end_date = datetime.now()
        if time_period == "3_months":
            start_date = end_date - timedelta(days=90)
        elif time_period == "6_months":
            start_date = end_date - timedelta(days=180)
        elif time_period == "1_year":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)

        # Get transactions for analysis
        filters = {
            "date_range": {"start": start_date.isoformat(), "end": end_date.isoformat()}
        }

        transactions = await transaction_service.get_transactions(
            tenant_id=tenant_id, filters=filters
        )

        # Prepare data for trend analysis
        monthly_data = {}
        category_trends = {}

        for txn in transactions:
            if txn.date:
                month_key = txn.date.strftime("%Y-%m")
                category = txn.get_display_category() or "Uncategorized"

                # Initialize month data
                if month_key not in monthly_data:
                    monthly_data[month_key] = {
                        "revenue": 0,
                        "expenses": 0,
                        "transaction_count": 0,
                        "categories": {},
                    }

                # Update metrics
                if txn.amount > 0:
                    monthly_data[month_key]["revenue"] += txn.amount
                else:
                    monthly_data[month_key]["expenses"] += abs(txn.amount)

                monthly_data[month_key]["transaction_count"] += 1

                # Track category trends
                if category not in monthly_data[month_key]["categories"]:
                    monthly_data[month_key]["categories"][category] = 0
                monthly_data[month_key]["categories"][category] += abs(txn.amount)

                if category not in category_trends:
                    category_trends[category] = {}
                if month_key not in category_trends[category]:
                    category_trends[category][month_key] = 0
                category_trends[category][month_key] += abs(txn.amount)

        # Use AI to analyze trends
        trend_prompt = f"""
        Analyze these financial trends and provide insights:
        
        Monthly Data:
        {json.dumps(monthly_data, indent=2)}
        
        Category Trends:
        {json.dumps(dict(list(category_trends.items())[:10]), indent=2)}  # Top 10 categories
        
        Analysis Type: {analysis_type}
        Time Period: {time_period}
        
        Provide analysis including:
        1. Overall spending/revenue trends
        2. Significant changes or anomalies
        3. Category-specific trends
        4. Seasonal patterns if any
        5. Predictive insights for next period
        
        Return JSON format:
        {{
            "trend_summary": {{
                "direction": "increasing|decreasing|stable",
                "percentage_change": 15.5,
                "key_insights": ["insight1", "insight2"]
            }},
            "anomalies": [
                {{
                    "period": "2024-03",
                    "type": "spike|dip",
                    "description": "Unusual spending spike",
                    "impact": "high|medium|low"
                }}
            ],
            "category_trends": [
                {{
                    "category": "Transportation",
                    "trend": "increasing",
                    "change_percentage": 25.0,
                    "insight": "Transportation costs rising steadily"
                }}
            ],
            "predictions": {{
                "next_period_estimate": {{
                    "revenue": 5000,
                    "expenses": 4000,
                    "confidence": 0.85
                }},
                "recommendations": ["recommendation1", "recommendation2"]
            }}
        }}
        
        JSON:"""

        # Generate AI analysis
        response = await model.generate_content_async(
            trend_prompt,
            generation_config={
                "temperature": 0.3,
                "max_output_tokens": 2000,
                "top_p": 0.9,
            },
        )

        # Parse AI response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            ai_analysis = json.loads(response_text[json_start:json_end])
        else:
            ai_analysis = {
                "trend_summary": {"direction": "stable", "key_insights": []},
                "anomalies": [],
                "category_trends": [],
                "predictions": {},
            }

        # Combine with raw data
        result = {
            "analysis_type": analysis_type,
            "time_period": time_period,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
            },
            "monthly_metrics": [
                {
                    "month": month,
                    "revenue": round(data["revenue"], 2),
                    "expenses": round(data["expenses"], 2),
                    "net": round(data["revenue"] - data["expenses"], 2),
                    "transaction_count": data["transaction_count"],
                }
                for month, data in sorted(monthly_data.items())
            ],
            "ai_analysis": ai_analysis,
            "data_points": len(transactions),
        }

        logger.info(
            f"Trend analysis complete: {result['ai_analysis']['trend_summary']['direction']} trend detected"
        )
        return result

    except Exception as e:
        logger.error(f"Trend analysis failed: {e}")
        raise ServiceError(f"Failed to generate trend analysis: {e}")


# ===== VISUALIZATION TOOLS =====


async def create_custom_visualizations_tool_function(
    data: Dict[str, Any],
    chart_type: str = "bar",
    title: Optional[str] = None,
    options: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Generate chart and graph configurations for visualization.

    Creates configuration objects for various chart types that can be
    rendered by frontend visualization libraries.

    Args:
        data: Data to visualize
        chart_type: Type of chart (bar, line, pie, scatter)
        title: Chart title
        options: Additional chart options

    Returns:
        Dictionary with visualization configuration
    """
    logger.info(f"Creating {chart_type} visualization")

    try:
        # Default options
        default_options = {
            "responsive": True,
            "maintainAspectRatio": False,
            "plugins": {
                "legend": {"position": "top"},
                "title": {"display": bool(title), "text": title or ""},
            },
        }

        if options:
            default_options.update(options)

        # Prepare chart configuration based on type
        if chart_type == "bar":
            chart_config = {
                "type": "bar",
                "data": {
                    "labels": data.get("labels", []),
                    "datasets": [
                        {
                            "label": data.get("label", "Value"),
                            "data": data.get("values", []),
                            "backgroundColor": data.get(
                                "colors",
                                [
                                    "rgba(54, 162, 235, 0.8)",
                                    "rgba(255, 99, 132, 0.8)",
                                    "rgba(255, 206, 86, 0.8)",
                                    "rgba(75, 192, 192, 0.8)",
                                    "rgba(153, 102, 255, 0.8)",
                                ],
                            ),
                            "borderColor": data.get(
                                "borderColors",
                                [
                                    "rgba(54, 162, 235, 1)",
                                    "rgba(255, 99, 132, 1)",
                                    "rgba(255, 206, 86, 1)",
                                    "rgba(75, 192, 192, 1)",
                                    "rgba(153, 102, 255, 1)",
                                ],
                            ),
                            "borderWidth": 1,
                        }
                    ],
                },
                "options": default_options,
            }

        elif chart_type == "line":
            chart_config = {
                "type": "line",
                "data": {
                    "labels": data.get("labels", []),
                    "datasets": data.get(
                        "datasets",
                        [
                            {
                                "label": data.get("label", "Trend"),
                                "data": data.get("values", []),
                                "fill": False,
                                "borderColor": "rgb(75, 192, 192)",
                                "tension": 0.1,
                            }
                        ],
                    ),
                },
                "options": {**default_options, "scales": {"y": {"beginAtZero": True}}},
            }

        elif chart_type == "pie":
            chart_config = {
                "type": "pie",
                "data": {
                    "labels": data.get("labels", []),
                    "datasets": [
                        {
                            "data": data.get("values", []),
                            "backgroundColor": data.get(
                                "colors",
                                [
                                    "rgba(255, 99, 132, 0.8)",
                                    "rgba(54, 162, 235, 0.8)",
                                    "rgba(255, 206, 86, 0.8)",
                                    "rgba(75, 192, 192, 0.8)",
                                    "rgba(153, 102, 255, 0.8)",
                                    "rgba(255, 159, 64, 0.8)",
                                ],
                            ),
                        }
                    ],
                },
                "options": default_options,
            }

        elif chart_type == "scatter":
            chart_config = {
                "type": "scatter",
                "data": {
                    "datasets": [
                        {
                            "label": data.get("label", "Data Points"),
                            "data": data.get("points", []),
                            "backgroundColor": "rgba(75, 192, 192, 0.8)",
                        }
                    ]
                },
                "options": {
                    **default_options,
                    "scales": {
                        "x": {"type": "linear", "position": "bottom"},
                        "y": {"beginAtZero": True},
                    },
                },
            }

        else:
            # Default to table format
            chart_config = {"type": "table", "data": data, "options": default_options}

        result = {
            "chart_type": chart_type,
            "chart_config": chart_config,
            "render_ready": True,
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "data_points": len(data.get("values", [])),
                "title": title,
            },
        }

        logger.info(
            f"Visualization created: {chart_type} with {result['metadata']['data_points']} data points"
        )
        return result

    except Exception as e:
        logger.error(f"Visualization creation failed: {e}")
        return {"error": str(e), "chart_type": chart_type, "render_ready": False}


# ===== EXPORT TOOLS =====


async def export_report_data_tool_function(
    report_data: Dict[str, Any],
    export_format: str = "csv",
    include_metadata: bool = True,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Export report data in various formats.

    Converts report data into downloadable formats including
    CSV, Excel, JSON, and PDF.

    Args:
        report_data: Report data to export
        export_format: Target format (csv, excel, json, pdf)
        include_metadata: Whether to include metadata

    Returns:
        Dictionary with exported data
    """
    logger.info(f"Exporting report data as {export_format}")

    try:
        import csv
        import io
        import json

        if export_format == "csv":
            # Convert to CSV
            output = io.StringIO()

            # Extract data rows
            data_rows = report_data.get("data", [])
            if not data_rows:
                return {"success": False, "error": "No data to export"}

            # Get headers from first row
            headers = list(data_rows[0].keys()) if data_rows else []

            writer = csv.DictWriter(output, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data_rows)

            # Add metadata as comments if requested
            if include_metadata and "summary" in report_data:
                output.write("\n# Summary\n")
                for key, value in report_data["summary"].items():
                    output.write(f"# {key}: {value}\n")

            export_content = output.getvalue()
            output.close()

        elif export_format == "json":
            # Export as JSON
            export_data = {
                "report": report_data,
                "exported_at": datetime.now().isoformat(),
                "format": "json",
            }

            if not include_metadata:
                export_data = {"data": report_data.get("data", [])}

            export_content = json.dumps(export_data, indent=2)

        elif export_format == "excel":
            # For Excel, we need to prepare the data structure
            # In production, this would use openpyxl or pandas
            export_content = {
                "sheets": [
                    {
                        "name": "Report Data",
                        "data": report_data.get("data", []),
                        "headers": list(report_data.get("data", [{}])[0].keys())
                        if report_data.get("data")
                        else [],
                    }
                ]
            }

            if include_metadata and "summary" in report_data:
                export_content["sheets"].append(
                    {
                        "name": "Summary",
                        "data": [
                            {"metric": k, "value": v}
                            for k, v in report_data["summary"].items()
                        ],
                    }
                )

            # Convert to JSON for now (in production would be actual Excel file)
            export_content = json.dumps(export_content, indent=2)

        else:
            # Default to JSON
            export_content = json.dumps(report_data, indent=2)

        result = {
            "success": True,
            "format": export_format,
            "content": export_content,
            "metadata": {
                "exported_at": datetime.now().isoformat(),
                "row_count": len(report_data.get("data", [])),
                "file_extension": export_format,
                "include_metadata": include_metadata,
            },
        }

        logger.info(
            f"Export complete: {result['metadata']['row_count']} rows exported as {export_format}"
        )
        return result

    except Exception as e:
        logger.error(f"Report export failed: {e}")
        return {"success": False, "error": str(e), "format": export_format}


# Create FunctionTool instances
generate_spending_reports_tool = FunctionTool(
    func=generate_spending_reports_tool_function
)
create_income_statements_tool = FunctionTool(
    func=create_income_statements_tool_function
)
generate_trend_analysis_tool = FunctionTool(func=generate_trend_analysis_tool_function)
create_custom_visualizations_tool = FunctionTool(
    func=create_custom_visualizations_tool_function
)
export_report_data_tool = FunctionTool(func=export_report_data_tool_function)


@dataclass
class ReportsAgentConfig:
    """Configuration for ReportsAgent."""

    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    tools: List[FunctionTool] | None = None
    enable_code_execution: bool = False


class ReportsAgent(StandardGikiAgent):
    """
    Specialist agent for report generation and analytics.

    This agent handles:
    - Comprehensive spending analysis reports
    - Financial statement generation
    - Trend identification and analysis
    - Custom visualization creation
    - Multi-format data export
    """

    def __init__(
        self,
        config: ReportsAgentConfig | None = None,
        db: asyncpg.Connection | None = None,
        **kwargs,
    ):
        """Initialize the ReportsAgent using StandardGikiAgent inheritance."""
        if config is None:
            config = ReportsAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
            )

        # Store config and extract values
        self._config = config
        model_name = config.model_name
        project = config.project
        location = config.location

        # AGENT EFFICIENCY: Only essential tools for report generation (2-3 maximum)
        custom_tools = [
            generate_spending_reports_tool,  # Essential: Core reporting functionality
            export_report_data_tool,  # Essential: Data export capability
        ]

        # REMOVED for efficiency: Income statements, trend analysis, and custom visualizations
        # These can be combined into the core generate_spending_reports_tool or added selectively when needed
        # Removing config.tools to maintain focus and prevent tool overload

        # Initialize StandardGikiAgent with EFFICIENT configuration
        super().__init__(
            name="reports_agent",
            description="Specialist agent for report generation and analytics",
            custom_tools=custom_tools,
            enable_interactive_tools=False,  # Reports are generated programmatically
            enable_standard_tools=False,  # EFFICIENCY: No standard tools needed for reports
            enable_code_execution=config.enable_code_execution,
            model_name=model_name,
            instruction="""You are a financial reporting expert. Generate comprehensive
            reports, create visualizations, and provide analytical insights from
            financial data. Focus on clarity and actionable information.""",
            project_id=project,
            location=location or "global",
            **kwargs,
        )

        # Store attributes after initialization
        self._model_name = model_name
        self._project = project
        self._location = location
        self._db = db

        # Initialize report-specific components
        self._vertex_model = GenerativeModel(model_name=model_name)

        logger.info(f"ReportsAgent initialized with model: {model_name}")

    async def process_report_request(
        self, query: str, tenant_id: int
    ) -> Dict[str, Any]:
        """
        Process natural language report requests.

        Args:
            query: Natural language query for report generation
            tenant_id: Tenant ID for data isolation

        Returns:
            Report generation result
        """
        query_lower = query.lower()

        # Determine report type from query
        if "spending" in query_lower or "expense" in query_lower:
            report_type = "spending"
        elif "income" in query_lower or "revenue" in query_lower:
            report_type = "income"
        elif "category" in query_lower:
            report_type = "category"
        elif "trend" in query_lower:
            report_type = "trend"
        else:
            report_type = "comprehensive"

        # Extract date range from query
        date_range = None
        if "last month" in query_lower:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            date_range = {"start": start_date.isoformat(), "end": end_date.isoformat()}
        elif "last quarter" in query_lower:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            date_range = {"start": start_date.isoformat(), "end": end_date.isoformat()}

        # Generate appropriate report
        try:
            if report_type == "spending":
                result = await self.generate_spending_reports(
                    tenant_id=tenant_id, report_type="category", date_range=date_range
                )
                return {
                    "success": True,
                    "response": f"Generated spending report with {len(result.get('data', []))} categories",
                    "report_data": result,
                    "download_available": True,
                }
            elif report_type == "income":
                result = await create_income_statements_tool_function(
                    tenant_id=tenant_id, db=self._db, period_type="monthly", periods=3
                )
                return {
                    "success": True,
                    "response": "Generated income statements for the last 3 months",
                    "report_data": result,
                    "download_available": True,
                }
            else:
                # Generate comprehensive report
                spending_data = await self.generate_spending_reports(
                    tenant_id=tenant_id, report_type="category", date_range=date_range
                )
                # Generate trend data
                trend_data = {
                    "trend_period": "weekly",
                    "trends": [],
                    "forecasts": {"next_week": 0, "next_month": 0},
                }

                return {
                    "success": True,
                    "response": "Generated comprehensive financial report with spending analysis and trends",
                    "report_data": {"spending": spending_data, "trends": trend_data},
                    "download_available": True,
                }

        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return {
                "success": False,
                "response": f"Failed to generate report: {str(e)}",
                "error": str(e),
            }

    async def generate_spending_reports(
        self,
        tenant_id: int,
        report_type: str = "category",
        date_range: Optional[Dict[str, str]] = None,
        grouping: Optional[str] = "monthly",
    ) -> Dict[str, Any]:
        """
        Generate spending analysis reports.

        Args:
            tenant_id: Tenant ID for data isolation
            report_type: Type of spending report
            date_range: Optional date range
            grouping: Grouping strategy

        Returns:
            Dictionary with spending report data
        """
        return await generate_spending_reports_tool_function(
            tenant_id=tenant_id,
            db=self._db,
            report_type=report_type,
            date_range=date_range,
            grouping=grouping,
        )

    async def create_income_statements(
        self,
        tenant_id: int,
        period_type: str = "monthly",
        periods: int = 3,
    ) -> Dict[str, Any]:
        """
        Generate financial income statements.

        Args:
            tenant_id: Tenant ID for data isolation
            period_type: Statement period type
            periods: Number of periods to include

        Returns:
            Dictionary with income statement data
        """
        return await create_income_statements_tool_function(
            tenant_id=tenant_id,
            db=self._db,
            period_type=period_type,
            periods=periods,
        )

    async def generate_trend_analysis(
        self,
        tenant_id: int,
        analysis_type: str = "spending",
        metrics: Optional[List[str]] = None,
        time_period: str = "6_months",
    ) -> Dict[str, Any]:
        """
        Generate trend analysis reports.

        Args:
            tenant_id: Tenant ID for data isolation
            analysis_type: Type of trend analysis
            metrics: Specific metrics to analyze
            time_period: Time period for analysis

        Returns:
            Dictionary with trend analysis results
        """
        return await generate_trend_analysis_tool_function(
            tenant_id=tenant_id,
            db=self._db,
            analysis_type=analysis_type,
            metrics=metrics,
            time_period=time_period,
        )

    async def create_custom_visualizations(
        self,
        data: Dict[str, Any],
        chart_type: str = "bar",
        title: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create visualization configurations.

        Args:
            data: Data to visualize
            chart_type: Type of chart
            title: Chart title
            options: Additional options

        Returns:
            Dictionary with visualization config
        """
        return await create_custom_visualizations_tool_function(
            data=data,
            chart_type=chart_type,
            title=title,
            options=options,
        )

    async def export_report_data(
        self,
        report_data: Dict[str, Any],
        export_format: str = "csv",
        include_metadata: bool = True,
    ) -> Dict[str, Any]:
        """
        Export report data in various formats.

        Args:
            report_data: Report data to export
            export_format: Target format
            include_metadata: Whether to include metadata

        Returns:
            Dictionary with exported data
        """
        return await export_report_data_tool_function(
            report_data=report_data,
            export_format=export_format,
            include_metadata=include_metadata,
        )
