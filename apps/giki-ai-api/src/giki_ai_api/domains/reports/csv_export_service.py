"""
Professional CSV Export Service

Provides high-quality CSV export functionality with flexible formatting options
for financial data integration with external systems.
"""

import csv
import logging
from datetime import datetime
from io import StringIO
from typing import Any, Dict, List, Optional

import asyncpg

from ..transactions.models import Transaction

logger = logging.getLogger(__name__)


class ProfessionalCSVExportService:
    """
    Professional CSV export service for financial data.
    Supports multiple CSV formats and encoding options for integration compatibility.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db

    async def generate_transactions_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_metadata: bool = True,
        format_style: str = "standard",  # standard, quickbooks, sage, xero
    ) -> StringIO:
        """
        Generate CSV export of transaction data with various format options.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for export period
            end_date: End date for export period
            include_metadata: Whether to include metadata columns
            format_style: CSV format style for different accounting systems

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get transactions data
        transactions = await self._get_transactions_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns based on format style
        columns = self._get_columns_for_format(format_style, include_metadata)

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Write transaction data
        for transaction in transactions:
            row_data = self._format_transaction_row(
                transaction, format_style, include_metadata
            )
            writer.writerow(row_data)

        output.seek(0)
        return output

    async def generate_category_summary_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> StringIO:
        """
        Generate CSV export of category summary data.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for summary period
            end_date: End date for summary period

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get category summary data
        category_data = await self._get_category_summary_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns
        columns = [
            "category_path",
            "transaction_count",
            "total_amount",
            "average_amount",
            "min_amount",
            "max_amount",
            "percentage_of_total",
        ]

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Calculate total for percentages
        total_amount = sum(item["total_amount"] for item in category_data)

        # Write category data
        for item in category_data:
            percentage = (
                (item["total_amount"] / total_amount * 100) if total_amount > 0 else 0
            )

            row_data = {
                "category_path": item["category_path"] or "Uncategorized",
                "transaction_count": item["transaction_count"],
                "total_amount": f"{item['total_amount']:.2f}",
                "average_amount": f"{item['average_amount']:.2f}",
                "min_amount": f"{item['min_amount']:.2f}",
                "max_amount": f"{item['max_amount']:.2f}",
                "percentage_of_total": f"{percentage:.2f}",
            }
            writer.writerow(row_data)

        output.seek(0)
        return output

    async def generate_monthly_summary_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> StringIO:
        """
        Generate CSV export of monthly summary data.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for summary period
            end_date: End date for summary period

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get monthly summary data
        monthly_data = await self._get_monthly_summary_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns
        columns = [
            "year_month",
            "transaction_count",
            "total_amount",
            "average_amount",
            "expense_count",
            "expense_amount",
            "income_count",
            "income_amount",
        ]

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Write monthly data
        for item in monthly_data:
            row_data = {
                "year_month": item["year_month"],
                "transaction_count": item["transaction_count"],
                "total_amount": f"{item['total_amount']:.2f}",
                "average_amount": f"{item['average_amount']:.2f}",
                "expense_count": item["expense_count"],
                "expense_amount": f"{item['expense_amount']:.2f}",
                "income_count": item["income_count"],
                "income_amount": f"{item['income_amount']:.2f}",
            }
            writer.writerow(row_data)

        output.seek(0)
        return output

    def _get_columns_for_format(
        self, format_style: str, include_metadata: bool
    ) -> List[str]:
        """Get column names based on export format style."""

        if format_style == "quickbooks":
            columns = [
                "Date",
                "Description",
                "Amount",
                "Account",
                "Memo",
                "Category",
            ]
        elif format_style == "sage":
            columns = [
                "Date",
                "Reference",
                "Description",
                "Net Amount",
                "Tax Code",
                "Department",
            ]
        elif format_style == "xero":
            columns = [
                "Date",
                "Description",
                "Amount",
                "Account Code",
                "Tax Type",
                "Contact",
            ]
        else:  # standard
            columns = [
                "date",
                "description",
                "amount",
                "transaction_type",
                "category_path",
                "category_id",
            ]

        if include_metadata and format_style == "standard":
            columns.extend(
                [
                    "account",
                    "entity_id",
                    "upload_id",
                    "ai_suggested_category_path",
                    "ai_category_confidence",
                    "is_categorized",
                    "is_user_modified",
                    "user_corrected",
                    "created_at",
                    "updated_at",
                ]
            )

        return columns

    def _format_transaction_row(
        self, transaction: Transaction, format_style: str, include_metadata: bool
    ) -> Dict[str, Any]:
        """Format a transaction row based on the export format."""
        if format_style == "quickbooks":
            return {
                "Date": transaction.date.strftime("%m/%d/%Y"),
                "Description": transaction.description,
                "Amount": f"{transaction.amount:.2f}",
                "Account": transaction.account or "",
                "Memo": transaction.description,
                "Category": transaction.category_path or "",
            }
        elif format_style == "sage":
            return {
                "Date": transaction.date.strftime("%d/%m/%Y"),
                "Reference": transaction.id,
                "Description": transaction.description,
                "Net Amount": f"{transaction.amount:.2f}",
                "Tax Code": "T1",  # Default tax code
                "Department": "",
            }
        elif format_style == "xero":
            return {
                "Date": transaction.date.strftime("%d/%m/%Y"),
                "Description": transaction.description,
                "Amount": f"{transaction.amount:.2f}",
                "Account Code": "400",  # Default account code
                "Tax Type": "GST",
                "Contact": "",
            }
        else:  # standard
            row_data = {
                "date": transaction.date.strftime("%Y-%m-%d"),
                "description": transaction.description,
                "amount": f"{transaction.amount:.2f}",
                "transaction_type": transaction.transaction_type,
                "category_path": transaction.category_path or "",
                "category_id": transaction.category_id or "",
            }

            if include_metadata:
                row_data.update(
                    {
                        "account": transaction.account or "",
                        "entity_id": transaction.entity_id or "",
                        "upload_id": transaction.upload_id or "",
                        "ai_suggested_category_path": transaction.ai_suggested_category_path
                        or "",
                        "ai_category_confidence": transaction.ai_category_confidence
                        or "",
                        "is_categorized": transaction.is_categorized,
                        "is_user_modified": transaction.is_user_modified,
                        "user_corrected": transaction.user_corrected,
                        "created_at": transaction.created_at.isoformat()
                        if transaction.created_at
                        else "",
                        "updated_at": transaction.updated_at.isoformat()
                        if transaction.updated_at
                        else "",
                    }
                )

            return row_data

    async def _get_transactions_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Transaction]:
        """Get transactions data for export."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT * FROM transactions 
            WHERE tenant_id = $1{date_filter}
            ORDER BY date DESC
        """

        rows = await self.db.fetch(sql, *params)
        return [Transaction.model_validate(dict(row)) for row in rows]

    async def _get_category_summary_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get category summary data for export."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                category_path,
                COUNT(id) as transaction_count,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount,
                MIN(amount) as min_amount,
                MAX(amount) as max_amount
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY category_path
            ORDER BY SUM(amount) DESC
        """

        rows = await self.db.fetch(sql, *params)

        return [
            {
                "category_path": row["category_path"],
                "transaction_count": row["transaction_count"],
                "total_amount": float(row["total_amount"]),
                "average_amount": float(row["average_amount"]),
                "min_amount": float(row["min_amount"]),
                "max_amount": float(row["max_amount"]),
            }
            for row in rows
        ]

    async def _get_monthly_summary_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get monthly summary data for export."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                TO_CHAR(date, 'YYYY-MM') as year_month,
                COUNT(id) as transaction_count,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount,
                COUNT(CASE WHEN transaction_type = 'expense' THEN 1 END) as expense_count,
                SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as expense_amount,
                COUNT(CASE WHEN transaction_type = 'income' THEN 1 END) as income_count,
                SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as income_amount
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY TO_CHAR(date, 'YYYY-MM')
            ORDER BY TO_CHAR(date, 'YYYY-MM')
        """

        rows = await self.db.fetch(sql, *params)

        return [
            {
                "year_month": row["year_month"],
                "transaction_count": row["transaction_count"],
                "total_amount": float(row["total_amount"] or 0),
                "average_amount": float(row["average_amount"] or 0),
                "expense_count": row["expense_count"] or 0,
                "expense_amount": float(row["expense_amount"] or 0),
                "income_count": row["income_count"] or 0,
                "income_amount": float(row["income_amount"] or 0),
            }
            for row in rows
        ]
