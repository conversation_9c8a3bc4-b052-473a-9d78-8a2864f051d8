"""
Professional Excel Export Service

Provides high-quality Excel export functionality with advanced formatting,
charts, formulas, and professional styling matching frontend Excel-inspired design.
"""

import logging
from datetime import datetime
from io import BytesIO
from typing import Any, Dict, Optional

import asyncpg
import pandas as pd

try:
    from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
    from openpyxl.styles import (
        Alignment,
        Border,
        Font,
        NamedStyle,
        PatternFill,
        Side,
    )
    from openpyxl.workbook import Workbook

    EXCEL_SUPPORT = True
except ImportError:
    EXCEL_SUPPORT = False

logger = logging.getLogger(__name__)


class ProfessionalExcelExportService:
    """
    Professional Excel export service with advanced formatting and charts.
    Matches the Excel-inspired frontend design with enterprise-grade features.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db

        if not EXCEL_SUPPORT:
            logger.warning(
                "Excel export requires openpyxl package. Install with: uv add openpyxl"
            )

    async def export_transactions(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        format_options: Optional[Dict[str, Any]] = None,
    ) -> bytes:
        """
        Export transactions with professional Excel formatting.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date filter (optional)
            end_date: End date filter (optional)
            format_options: Excel formatting options

        Returns:
            Excel file bytes
        """
        if not EXCEL_SUPPORT:
            raise ImportError("Excel export requires openpyxl package")

        # Default format options
        options = {
            "include_charts": True,
            "include_summary": True,
            "professional_styling": True,
            "autofit_columns": True,
            "freeze_headers": True,
            "conditional_formatting": True,
            **(format_options or {}),
        }

        # Create workbook
        wb = Workbook()

        # Set up styles
        self._setup_professional_styles(wb)

        # Export main transaction data
        await self._export_transactions_sheet(
            wb, tenant_id, start_date, end_date, options
        )

        # Export summary sheet
        if options["include_summary"]:
            await self._export_summary_sheet(
                wb, tenant_id, start_date, end_date, options
            )

        # Export category analysis
        await self._export_category_analysis_sheet(
            wb, tenant_id, start_date, end_date, options
        )

        # Export monthly trends
        await self._export_monthly_trends_sheet(
            wb, tenant_id, start_date, end_date, options
        )

        # Remove default sheet
        if "Sheet" in wb.sheetnames:
            wb.remove(wb["Sheet"])

        # Set active sheet to summary
        if "Summary" in wb.sheetnames:
            wb.active = wb["Summary"]

        # Save to bytes
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        return output.getvalue()

    def _setup_professional_styles(self, wb: Workbook):
        """Set up professional Excel styles matching frontend design."""

        # Header style (dark blue background, white text)
        header_style = NamedStyle(name="header")
        header_style.font = Font(name="Segoe UI", size=11, bold=True, color="FFFFFF")
        header_style.fill = PatternFill(
            start_color="1F4E79", end_color="1F4E79", fill_type="solid"
        )
        header_style.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )
        header_style.alignment = Alignment(
            horizontal="center", vertical="center", wrap_text=True
        )
        wb.add_named_style(header_style)

        # Data style (alternating rows)
        data_style = NamedStyle(name="data")
        data_style.font = Font(name="Segoe UI", size=10)
        data_style.border = Border(
            left=Side(style="thin", color="E7E6E6"),
            right=Side(style="thin", color="E7E6E6"),
            top=Side(style="thin", color="E7E6E6"),
            bottom=Side(style="thin", color="E7E6E6"),
        )
        data_style.alignment = Alignment(horizontal="left", vertical="center")
        wb.add_named_style(data_style)

        # Currency style
        currency_style = NamedStyle(name="currency")
        currency_style.font = Font(name="Segoe UI", size=10)
        currency_style.number_format = "$#,##0.00"
        currency_style.border = Border(
            left=Side(style="thin", color="E7E6E6"),
            right=Side(style="thin", color="E7E6E6"),
            top=Side(style="thin", color="E7E6E6"),
            bottom=Side(style="thin", color="E7E6E6"),
        )
        currency_style.alignment = Alignment(horizontal="right", vertical="center")
        wb.add_named_style(currency_style)

        # Summary style (light blue background)
        summary_style = NamedStyle(name="summary")
        summary_style.font = Font(name="Segoe UI", size=11, bold=True)
        summary_style.fill = PatternFill(
            start_color="D9E2F3", end_color="D9E2F3", fill_type="solid"
        )
        summary_style.border = Border(
            left=Side(style="medium"),
            right=Side(style="medium"),
            top=Side(style="medium"),
            bottom=Side(style="medium"),
        )
        summary_style.alignment = Alignment(horizontal="center", vertical="center")
        wb.add_named_style(summary_style)

    async def _export_transactions_sheet(
        self,
        wb: Workbook,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        options: Dict[str, Any],
    ):
        """Export main transactions data sheet."""

        # Query transactions using asyncpg
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT date, description, amount, category_path, transaction_type, account, entity_id
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            ORDER BY date DESC
        """

        rows = await self.db.fetch(sql, *params)
        transactions = rows

        # Create DataFrame
        df = pd.DataFrame(
            [
                {
                    "Date": row["date"],
                    "Description": row["description"],
                    "Amount": row["amount"],
                    "Category": row["category_path"],
                    "Type": row["transaction_type"],
                    "Account": row["account"],
                    "Entity ID": row["entity_id"],
                }
                for row in transactions
            ]
        )

        if df.empty:
            df = pd.DataFrame(
                columns=[
                    "Date",
                    "Description",
                    "Amount",
                    "Category",
                    "Type",
                    "Account",
                    "Entity ID",
                ]
            )

        # Create worksheet
        ws = wb.create_sheet("Transactions")

        # Add title
        ws.merge_cells("A1:G1")
        ws["A1"] = "Transaction Export Report"
        ws["A1"].font = Font(name="Segoe UI", size=16, bold=True, color="1F4E79")
        ws["A1"].alignment = Alignment(horizontal="center")

        # Add metadata
        ws["A2"] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws["A3"] = f"Total Transactions: {len(transactions)}"
        if start_date:
            ws["A4"] = (
                f"Date Range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d') if end_date else 'Present'}"
            )

        # Add headers starting from row 6
        headers = [
            "Date",
            "Description",
            "Amount",
            "Category",
            "Type",
            "Account",
            "Entity ID",
        ]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=6, column=col, value=header)
            cell.style = "header"

        # Add data
        for row_idx, (_, row) in enumerate(df.iterrows(), 7):
            for col_idx, value in enumerate(row, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if col_idx == 3:  # Amount column
                    cell.style = "currency"
                else:
                    cell.style = "data"

                # Conditional formatting for amounts
                if col_idx == 3 and options.get("conditional_formatting", True):
                    if isinstance(value, (int, float)) and value < 0:
                        cell.font = Font(color="C5504B")  # Red for negative
                    elif isinstance(value, (int, float)) and value > 0:
                        cell.font = Font(color="70AD47")  # Green for positive

        # Auto-fit columns
        if options.get("autofit_columns", True):
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except Exception:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

        # Freeze panes
        if options.get("freeze_headers", True):
            ws.freeze_panes = "A7"

    async def _export_summary_sheet(
        self,
        wb: Workbook,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        options: Dict[str, Any],
    ):
        """Export summary analysis sheet."""

        ws = wb.create_sheet("Summary")

        # Title
        ws.merge_cells("A1:E1")
        ws["A1"] = "Financial Summary Report"
        ws["A1"].font = Font(name="Segoe UI", size=16, bold=True, color="1F4E79")
        ws["A1"].alignment = Alignment(horizontal="center")

        # Query summary data using asyncpg
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                transaction_type,
                SUM(amount) as total,
                COUNT(id) as count
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY transaction_type
        """
        summary_data = await self.db.fetch(sql, *params)

        # Calculate totals
        total_income = 0.0
        total_expenses = 0.0
        income_count = 0
        expense_count = 0

        for row in summary_data:
            if row["transaction_type"] == "income":
                total_income = float(row["total"] or 0)
                income_count = row["count"]
            elif row["transaction_type"] == "expense":
                total_expenses = abs(float(row["total"] or 0))
                expense_count = row["count"]

        net_income = total_income - total_expenses

        # Summary table
        ws["A3"] = "Financial Overview"
        ws["A3"].font = Font(name="Segoe UI", size=14, bold=True)

        summary_data_table = [
            ["Metric", "Amount", "Count"],
            ["Total Income", total_income, income_count],
            ["Total Expenses", total_expenses, expense_count],
            ["Net Income", net_income, income_count + expense_count],
        ]

        for row_idx, row_data in enumerate(summary_data_table, 5):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 5:  # Header row
                    cell.style = "header"
                else:
                    if col_idx == 2:  # Amount column
                        cell.style = "currency"
                    else:
                        cell.style = "data"

        # Add chart if requested
        if options.get("include_charts", True):
            self._add_summary_chart(ws, total_income, total_expenses)

        # Auto-fit columns
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except Exception:
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width

    async def _export_category_analysis_sheet(
        self,
        wb: Workbook,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        options: Dict[str, Any],
    ):
        """Export category spending analysis sheet."""

        ws = wb.create_sheet("Category Analysis")

        # Title
        ws.merge_cells("A1:D1")
        ws["A1"] = "Spending by Category Analysis"
        ws["A1"].font = Font(name="Segoe UI", size=16, bold=True, color="1F4E79")
        ws["A1"].alignment = Alignment(horizontal="center")

        # Query category data using asyncpg
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                category_path,
                SUM(ABS(amount)) as total_amount,
                COUNT(id) as transaction_count
            FROM transactions 
            WHERE tenant_id = $1 
              AND category_path IS NOT NULL{date_filter}
            GROUP BY category_path
            ORDER BY SUM(ABS(amount)) DESC
            LIMIT 20
        """
        category_data = await self.db.fetch(sql, *params)

        # Create category table
        headers = ["Category", "Total Amount", "Transaction Count", "Percentage"]

        # Calculate total for percentage
        total_amount = sum(float(row["total_amount"] or 0) for row in category_data)

        # Add headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.style = "header"

        # Add data
        for row_idx, row in enumerate(category_data, 4):
            percentage = (
                (float(row["total_amount"] or 0) / total_amount * 100)
                if total_amount > 0
                else 0
            )

            ws.cell(row=row_idx, column=1, value=row["category_path"]).style = "data"
            ws.cell(
                row=row_idx, column=2, value=float(row["total_amount"] or 0)
            ).style = "currency"
            ws.cell(
                row=row_idx, column=3, value=row["transaction_count"]
            ).style = "data"
            ws.cell(row=row_idx, column=4, value=f"{percentage:.1f}%").style = "data"

        # Auto-fit columns
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except Exception:
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width

    async def _export_monthly_trends_sheet(
        self,
        wb: Workbook,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        options: Dict[str, Any],
    ):
        """Export monthly trends analysis sheet."""

        ws = wb.create_sheet("Monthly Trends")

        # Title
        ws.merge_cells("A1:E1")
        ws["A1"] = "Monthly Trends Analysis"
        ws["A1"].font = Font(name="Segoe UI", size=16, bold=True, color="1F4E79")
        ws["A1"].alignment = Alignment(horizontal="center")

        # Query monthly data using asyncpg
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                TO_CHAR(date, 'YYYY-MM') as month,
                transaction_type,
                SUM(amount) as total_amount
            FROM transactions 
            WHERE tenant_id = $1 
              AND date IS NOT NULL{date_filter}
            GROUP BY TO_CHAR(date, 'YYYY-MM'), transaction_type
            ORDER BY TO_CHAR(date, 'YYYY-MM')
        """

        monthly_data = await self.db.fetch(sql, *params)

        # Process data
        monthly_summary = {}
        for row in monthly_data:
            month = row["month"]
            if month not in monthly_summary:
                monthly_summary[month] = {"income": 0.0, "expenses": 0.0}

            if row["transaction_type"] == "income":
                monthly_summary[month]["income"] = float(row["total_amount"] or 0)
            elif row["transaction_type"] == "expense":
                monthly_summary[month]["expenses"] = abs(
                    float(row["total_amount"] or 0)
                )

        # Add headers
        headers = ["Month", "Income", "Expenses", "Net Income", "Savings Rate"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.style = "header"

        # Add data
        for row_idx, (month, data) in enumerate(sorted(monthly_summary.items()), 4):
            income = data["income"]
            expenses = data["expenses"]
            net_income = income - expenses
            savings_rate = (net_income / income * 100) if income > 0 else 0

            ws.cell(row=row_idx, column=1, value=month).style = "data"
            ws.cell(row=row_idx, column=2, value=income).style = "currency"
            ws.cell(row=row_idx, column=3, value=expenses).style = "currency"
            ws.cell(row=row_idx, column=4, value=net_income).style = "currency"
            ws.cell(row=row_idx, column=5, value=f"{savings_rate:.1f}%").style = "data"

        # Auto-fit columns
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except Exception:
                    pass
            adjusted_width = min(max_length + 2, 25)
            ws.column_dimensions[column_letter].width = adjusted_width

    def _add_summary_chart(self, ws, total_income: float, total_expenses: float):
        """Add a pie chart for income vs expenses."""
        try:
            # Create pie chart
            chart = PieChart()
            chart.title = "Income vs Expenses"
            chart.style = 2

            # Add data (simplified approach)
            ws["F5"] = "Income"
            ws["G5"] = total_income
            ws["F6"] = "Expenses"
            ws["G6"] = total_expenses

            # Chart data reference
            data = Reference(ws, min_col=7, min_row=5, max_row=6)
            labels = Reference(ws, min_col=6, min_row=5, max_row=6)

            chart.add_data(data)
            chart.set_categories(labels)

            # Add chart to worksheet
            ws.add_chart(chart, "F8")

        except Exception as e:
            logger.warning(f"Failed to add chart: {e}")


class ExcelExportError(Exception):
    """Custom exception for Excel export errors."""

    pass
