"""
Report Generator Service
========================

Service for generating customizable financial reports.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from asyncpg import Connection

logger = logging.getLogger(__name__)


class ReportGenerator:
    """Service for generating customizable financial reports."""

    def __init__(self, conn: Connection):
        """Initialize the report generator."""
        self.conn = conn
        logger.info("ReportGenerator initialized")

    async def generate_report(
        self,
        tenant_id: int,
        report_type: str,
        date_range: Dict[str, Any],
        filters: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Generate a customizable financial report.

        Args:
            tenant_id: Tenant identifier
            report_type: Type of report to generate
            date_range: Date range for the report
            filters: Optional filters to apply

        Returns:
            Generated report data
        """
        logger.info(f"Generating {report_type} report for tenant {tenant_id}")

        try:
            # Build SQL query for transactions
            query_parts = ["SELECT * FROM transactions WHERE tenant_id = $1"]
            params = [tenant_id]
            param_count = 1

            # Apply date range filters
            if date_range.get("start_date"):
                param_count += 1
                query_parts.append(f"AND date >= ${param_count}")
                params.append(date_range["start_date"])
            if date_range.get("end_date"):
                param_count += 1
                query_parts.append(f"AND date <= ${param_count}")
                params.append(date_range["end_date"])

            # Apply additional filters
            if filters:
                if filters.get("category_ids"):
                    param_count += 1
                    placeholders = ",".join(
                        [
                            f"${i}"
                            for i in range(
                                param_count + 1,
                                param_count + 1 + len(filters["category_ids"]),
                            )
                        ]
                    )
                    query_parts.append(f"AND category_id IN ({placeholders})")
                    params.extend(filters["category_ids"])
                    param_count += len(filters["category_ids"])
                if filters.get("min_amount"):
                    param_count += 1
                    query_parts.append(f"AND amount >= ${param_count}")
                    params.append(filters["min_amount"])
                if filters.get("max_amount"):
                    param_count += 1
                    query_parts.append(f"AND amount <= ${param_count}")
                    params.append(filters["max_amount"])

            # Execute query
            query = " ".join(query_parts)
            rows = await self.conn.fetch(query, *params)

            # Convert rows to Transaction-like objects
            from ..transactions.models import Transaction

            transactions = [Transaction(**dict(row)) for row in rows]

            if not transactions:
                from ...shared.exceptions import ServiceError

                raise ServiceError(
                    "No transaction data found for report generation",
                    error_code="NO_DATA_FOUND",
                    tenant_id=tenant_id,
                    severity="medium",
                )

            # Generate report based on type
            if report_type == "transaction_summary":
                report_data = await self._generate_transaction_summary(transactions)
            elif report_type == "category_breakdown":
                report_data = await self._generate_category_breakdown(transactions)
            elif report_type == "monthly_trends":
                report_data = await self._generate_monthly_trends(transactions)
            elif report_type == "expense_analysis":
                report_data = await self._generate_expense_analysis(transactions)
            elif report_type == "income_analysis":
                report_data = await self._generate_income_analysis(transactions)
            else:
                from ...shared.exceptions import ServiceError

                raise ServiceError(
                    f"Unsupported report type: {report_type}",
                    error_code="INVALID_REPORT_TYPE",
                    tenant_id=tenant_id,
                    severity="medium",
                )

            return {
                "report_type": report_type,
                "tenant_id": tenant_id,
                "date_range": date_range,
                "filters": filters or {},
                "generated_at": datetime.now().isoformat(),
                "data": report_data,
                "summary": {
                    "total_transactions": len(transactions),
                    "total_amount": sum(t.amount for t in transactions if t.amount),
                    "categories": list(
                        set(t.category_id for t in transactions if t.category_id)
                    ),
                },
            }

        except Exception as e:
            logger.error(f"Report generation failed for {report_type}: {e}")
            from ...exceptions import ServiceError

            raise ServiceError(
                f"Failed to generate {report_type} report: {e}",
                error_code="REPORT_GENERATION_FAILED",
                tenant_id=tenant_id,
                original_error=e,
                severity="high",
            )

    async def get_available_report_types(self) -> List[str]:
        """Get list of available report types."""
        return [
            "transaction_summary",
            "category_breakdown",
            "monthly_trends",
            "expense_analysis",
            "income_analysis",
        ]

    async def _generate_transaction_summary(self, transactions) -> List[Dict[str, Any]]:
        """Generate transaction summary report data."""
        return [
            {
                "transaction_id": tx.id,
                "date": tx.date.isoformat() if tx.date else None,
                "description": tx.description,
                "amount": float(tx.amount) if tx.amount else 0.0,
                "category_id": tx.category_id,
            }
            for tx in transactions
        ]

    async def _generate_category_breakdown(self, transactions) -> List[Dict[str, Any]]:
        """Generate category breakdown report data."""
        category_totals = {}
        for tx in transactions:
            category_id = tx.category_id or "uncategorized"
            if category_id not in category_totals:
                category_totals[category_id] = {"count": 0, "total_amount": 0.0}
            category_totals[category_id]["count"] += 1
            category_totals[category_id]["total_amount"] += (
                float(tx.amount) if tx.amount else 0.0
            )

        return [
            {
                "category_id": cat_id,
                "transaction_count": data["count"],
                "total_amount": data["total_amount"],
            }
            for cat_id, data in category_totals.items()
        ]

    async def _generate_monthly_trends(self, transactions) -> List[Dict[str, Any]]:
        """Generate monthly trends report data."""
        monthly_data = {}
        for tx in transactions:
            if tx.date:
                month_key = tx.date.strftime("%Y-%m")
                if month_key not in monthly_data:
                    monthly_data[month_key] = {"count": 0, "total_amount": 0.0}
                monthly_data[month_key]["count"] += 1
                monthly_data[month_key]["total_amount"] += (
                    float(tx.amount) if tx.amount else 0.0
                )

        return [
            {
                "month": month,
                "transaction_count": data["count"],
                "total_amount": data["total_amount"],
            }
            for month, data in sorted(monthly_data.items())
        ]

    async def _generate_expense_analysis(self, transactions) -> List[Dict[str, Any]]:
        """Generate expense analysis report data."""
        expenses = [tx for tx in transactions if tx.amount and tx.amount < 0]
        return await self._generate_transaction_summary(expenses)

    async def _generate_income_analysis(self, transactions) -> List[Dict[str, Any]]:
        """Generate income analysis report data."""
        income = [tx for tx in transactions if tx.amount and tx.amount > 0]
        return await self._generate_transaction_summary(income)
