"""
Professional PDF Export Service

Provides high-quality PDF export functionality with professional formatting,
charts, and enterprise-grade styling for financial reports.
"""

import logging
from datetime import datetime
from io import BytesIO
from typing import Any, Dict, List, Optional

import asyncpg

try:
    from reportlab.graphics.charts.piecharts import Pie
    from reportlab.graphics.shapes import Drawing
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter
    from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
    from reportlab.lib.units import inch
    from reportlab.platypus import (
        Paragraph,
        SimpleDocTemplate,
        Spacer,
        Table,
        TableStyle,
    )

    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

from ..transactions.models import Transaction

logger = logging.getLogger(__name__)


class ProfessionalPDFExportService:
    """
    Professional PDF export service with enterprise-grade formatting and charts.
    Creates publication-ready financial reports with charts and professional styling.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db

        if not PDF_SUPPORT:
            logger.warning(
                "PDF export requires reportlab package. Install with: uv add reportlab"
            )

    async def generate_financial_report_pdf(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_charts: bool = True,
        include_summary: bool = True,
    ) -> BytesIO:
        """
        Generate a comprehensive financial report in PDF format.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for report period
            end_date: End date for report period
            include_charts: Whether to include charts and visualizations
            include_summary: Whether to include executive summary

        Returns:
            BytesIO: PDF file content as bytes
        """
        if not PDF_SUPPORT:
            raise ImportError("PDF export requires reportlab package")

        # Create PDF buffer
        buffer = BytesIO()

        # Create document with professional styling
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18,
        )

        # Get transactions data
        transactions_data = await self._get_transactions_data(
            tenant_id, start_date, end_date
        )

        # Get summary statistics
        summary_stats = await self._get_summary_statistics(
            tenant_id, start_date, end_date
        )

        # Build story (content)
        story = []
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            "CustomTitle",
            parent=styles["Heading1"],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor("#1f2937"),
            alignment=1,  # Center
        )

        header_style = ParagraphStyle(
            "CustomHeader",
            parent=styles["Heading2"],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor("#374151"),
        )

        # Add title
        story.append(Paragraph("Financial Transaction Report", title_style))
        story.append(Spacer(1, 12))

        # Add report period
        period_text = f"Report Period: {start_date.strftime('%Y-%m-%d') if start_date else 'All'} to {end_date.strftime('%Y-%m-%d') if end_date else 'All'}"
        story.append(Paragraph(period_text, styles["Normal"]))
        story.append(Spacer(1, 20))

        if include_summary:
            # Add executive summary
            story.append(Paragraph("Executive Summary", header_style))
            summary_table_data = [
                ["Metric", "Value"],
                ["Total Transactions", f"{summary_stats['total_transactions']:,}"],
                ["Total Amount", f"${summary_stats['total_amount']:,.2f}"],
                ["Average Transaction", f"${summary_stats['avg_transaction']:,.2f}"],
                ["Date Range", f"{summary_stats['date_range']}"],
            ]

            summary_table = Table(summary_table_data, colWidths=[2 * inch, 2 * inch])
            summary_table.setStyle(
                TableStyle(
                    [
                        ("BACKGROUND", (0, 0), (-1, 0), colors.HexColor("#f3f4f6")),
                        ("TEXTCOLOR", (0, 0), (-1, 0), colors.HexColor("#374151")),
                        ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, 0), 12),
                        ("BOTTOMPADDING", (0, 0), (-1, 0), 12),
                        ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                        ("GRID", (0, 0), (-1, -1), 1, colors.HexColor("#e5e7eb")),
                    ]
                )
            )

            story.append(summary_table)
            story.append(Spacer(1, 30))

        # Add transactions table
        story.append(Paragraph("Transaction Details", header_style))

        # Prepare transaction table data
        table_data = [["Date", "Description", "Amount", "Category"]]
        for transaction in transactions_data[:50]:  # Limit to first 50 for PDF
            table_data.append(
                [
                    transaction.date.strftime("%Y-%m-%d"),
                    transaction.description[:40] + "..."
                    if len(transaction.description) > 40
                    else transaction.description,
                    f"${transaction.amount:.2f}",
                    transaction.category_path or "Uncategorized",
                ]
            )

        transactions_table = Table(
            table_data, colWidths=[1 * inch, 3 * inch, 1 * inch, 2 * inch]
        )
        transactions_table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (-1, 0), colors.HexColor("#f3f4f6")),
                    ("TEXTCOLOR", (0, 0), (-1, 0), colors.HexColor("#374151")),
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                    ("FONTSIZE", (0, 0), (-1, 0), 10),
                    ("FONTSIZE", (0, 1), (-1, -1), 9),
                    ("BOTTOMPADDING", (0, 0), (-1, 0), 12),
                    ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                    ("GRID", (0, 0), (-1, -1), 1, colors.HexColor("#e5e7eb")),
                ]
            )
        )

        story.append(transactions_table)

        if include_charts:
            story.append(Spacer(1, 30))
            story.append(Paragraph("Category Breakdown", header_style))

            # Add category pie chart (simplified for this implementation)
            chart_drawing = Drawing(400, 200)
            pie = Pie()
            pie.x = 50
            pie.y = 50
            pie.width = 100
            pie.height = 100

            # Get category data for chart
            category_data = await self._get_category_breakdown(
                tenant_id, start_date, end_date
            )
            pie.data = [item["amount"] for item in category_data]
            pie.labels = [item["category"] for item in category_data]

            chart_drawing.add(pie)
            story.append(chart_drawing)

        # Build PDF
        doc.build(story)
        buffer.seek(0)

        return buffer

    async def _get_transactions_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Transaction]:
        """Get transactions data for the report."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT * FROM transactions 
            WHERE tenant_id = $1{date_filter}
            ORDER BY date DESC
        """
        rows = await self.db.fetch(sql, *params)
        return [Transaction.model_validate(dict(row)) for row in rows]

    async def _get_summary_statistics(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> Dict[str, Any]:
        """Get summary statistics for the report."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                COUNT(id) as total_transactions,
                SUM(amount) as total_amount,
                AVG(amount) as avg_transaction,
                MIN(date) as min_date,
                MAX(date) as max_date
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
        """
        row = await self.db.fetchrow(sql, *params)

        return {
            "total_transactions": row["total_transactions"] or 0,
            "total_amount": float(row["total_amount"] or 0),
            "avg_transaction": float(row["avg_transaction"] or 0),
            "date_range": f"{row['min_date']} to {row['max_date']}"
            if row["min_date"] and row["max_date"]
            else "No data",
        }

    async def _get_category_breakdown(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get category breakdown for charts."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                category_path,
                SUM(amount) as amount,
                COUNT(id) as count
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY category_path
            ORDER BY SUM(amount) DESC
        """
        rows = await self.db.fetch(sql, *params)

        return [
            {
                "category": row["category_path"] or "Uncategorized",
                "amount": float(row["amount"]),
                "count": row["count"],
            }
            for row in rows[:10]  # Top 10 categories
        ]
