import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react'; // Added RenderResult here
// You can add any global providers here if needed, e.g., ThemeProvider, Router, Redux Provider
// import { ThemeProvider } from './components/ThemeProvider'; // Example

const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // return <ThemeProvider>{children}</ThemeProvider>; // Example
  return <>{children}</>; // Default: no providers
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
): RenderResult => render(ui, { wrapper: AllTheProviders, ...options }); // Added RenderResult return type

// re-export everything
export * from '@testing-library/react';

// override render method
export { customRender as render };
