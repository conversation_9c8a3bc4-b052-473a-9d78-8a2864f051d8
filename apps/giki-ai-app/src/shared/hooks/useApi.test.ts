/**
 * useApi Hook Tests
 * Critical API interaction hook tests for production reliability
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import {
  useApiGet,
  useApiPost,
  useApiPut,
  useApiPatch,
  useApiDelete,
  useApiPostFormUrlEncoded,
  useApi,
} from './useApi';
import { apiClient } from '@/shared/services/api/apiClient';
import { ErrorType } from '@/shared/types/errors';
import { createApiError } from '@/shared/utils/errorHandling';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    postFormUrlEncoded: vi.fn(),
  },
}));

// Mock error handling
vi.mock('@/shared/utils/errorHandling', async () => {
  const actual = await vi.importActual('@/shared/utils/errorHandling');
  return {
    ...actual,
    createApiError: vi.fn(),
  };
});

describe('useApi Hooks - Production API Management', () => {
  const mockApiClient = vi.mocked(apiClient);
  const mockCreateApiError = vi.mocked(createApiError);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('useApiGet Hook', () => {
    it('initializes with default state', () => {
      const { result } = renderHook(() => useApiGet('/api/users'));

      expect(result.current[0]).toEqual({
        data: null,
        isLoading: false,
        error: null,
      });
      expect(result.current[1]).toHaveProperty('execute');
      expect(result.current[1]).toHaveProperty('reset');
    });

    it('handles successful GET request', async () => {
      const mockData = { users: [{ id: 1, name: 'John' }] };
      const mockResponse = { data: mockData, status: 200 };
      mockApiClient?.get?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiGet('/api/users'));

      await act(async () => {
        const response = await result.current[1].execute();
        expect(response).toEqual(mockResponse);
      });

      expect(result.current[0]).toEqual({
        data: mockData,
        isLoading: false,
        error: null,
      });
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/users', {
        params: {},
      });
    });

    it('handles GET request with parameters', async () => {
      const mockData = { users: [] };
      const mockResponse = { data: mockData };
      mockApiClient?.get?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() =>
        useApiGet('/api/users', { params: { page: 1 } }),
      );

      await act(async () => {
        await result.current[1].execute({ limit: 10 });
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/users', {
        params: { page: 1, limit: 10 },
      });
    });

    it('sets loading state during request', async () => {
      let resolvePromise: (value: unknown) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockApiClient?.get?.mockReturnValueOnce(promise);

      const { result } = renderHook(() => useApiGet('/api/users'));

      act(() => {
        void result.current[1].execute();
      });

      expect(result.current[0].isLoading).toBe(true);

      await act(async () => {
        resolvePromise({ data: { users: [] } });
        await promise;
      });

      expect(result.current[0].isLoading).toBe(false);
    });

    it('handles API errors correctly', async () => {
      const mockError = new Error('Network error');
      const mockApiError = {
        type: ErrorType.NETWORK,
        message: 'Network error',
        originalError: mockError,
      };
      mockApiClient?.get?.mockRejectedValueOnce(mockError);
      mockCreateApiError.mockReturnValueOnce(mockApiError);

      const { result } = renderHook(() => useApiGet('/api/users'));

      await act(async () => {
        try {
          await result.current[1].execute();
        } catch (error) {
          expect(error).toEqual(mockApiError);
        }
      });

      expect(result.current[0]).toEqual({
        data: null,
        isLoading: false,
        error: mockApiError,
      });
      expect(mockCreateApiError).toHaveBeenCalledWith({
        type: ErrorType.UNKNOWN,
        message: 'Network error',
        originalError: mockError,
      });
    });

    it('resets state correctly', () => {
      const { result } = renderHook(() => useApiGet('/api/users'));

      // Set some state first
      act(() => {
        result.current[0] = {
          data: { users: [] },
          isLoading: true,
          error: { type: ErrorType.NETWORK, message: 'Error' },
        } as unknown;
      });

      act(() => {
        result.current[1].reset();
      });

      expect(result.current[0]).toEqual({
        data: null,
        isLoading: false,
        error: null,
      });
    });
  });

  describe('useApiPost Hook', () => {
    it('handles successful POST request', async () => {
      const postData = { name: 'New User', email: '<EMAIL>' };
      const responseData = { id: 1, ...postData };
      const mockResponse = { data: responseData, status: 201 };
      mockApiClient?.post?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiPost('/api/users'));

      await act(async () => {
        const response = await result.current[1].execute(postData);
        expect(response).toEqual(mockResponse);
      });

      expect(result.current[0]).toEqual({
        data: responseData,
        isLoading: false,
        error: null,
      });
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/users',
        postData,
        {},
      );
    });

    it('handles POST without data', async () => {
      const mockResponse = { data: { success: true }, status: 200 };
      mockApiClient?.post?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiPost('/api/action'));

      await act(async () => {
        await result.current[1].execute();
      });

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/action',
        undefined,
        {},
      );
    });
  });

  describe('useApiPut Hook', () => {
    it('handles successful PUT request', async () => {
      const updateData = { name: 'Updated User' };
      const responseData = { id: 1, ...updateData };
      const mockResponse = { data: responseData };
      mockApiClient?.put?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiPut('/api/users/1'));

      await act(async () => {
        await result.current[1].execute(updateData);
      });

      expect(result.current[0].data).toEqual(responseData);
      expect(mockApiClient.put).toHaveBeenCalledWith(
        '/api/users/1',
        updateData,
        {},
      );
    });
  });

  describe('useApiPatch Hook', () => {
    it('handles successful PATCH request', async () => {
      const patchData = { status: 'active' };
      const responseData = { id: 1, name: 'User', status: 'active' };
      const mockResponse = { data: responseData };
      mockApiClient?.patch?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiPatch('/api/users/1'));

      await act(async () => {
        await result.current[1].execute(patchData);
      });

      expect(result.current[0].data).toEqual(responseData);
      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/api/users/1',
        patchData,
        {},
      );
    });
  });

  describe('useApiDelete Hook', () => {
    it('handles successful DELETE request', async () => {
      const mockResponse = { data: { success: true }, status: 204 };
      mockApiClient?.delete?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiDelete('/api/users/1'));

      await act(async () => {
        const response = await result.current[1].execute();
        expect(response).toEqual(mockResponse);
      });

      expect(result.current[0].data).toEqual({ success: true });
      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/users/1', {});
    });

    it('does not accept parameters in execute method', async () => {
      const mockResponse = { data: { success: true } };
      mockApiClient?.delete?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApiDelete('/api/users/1'));

      await act(async () => {
        // TypeScript should prevent this, but test runtime behavior
        await result.current[1].execute();
      });

      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/users/1', {});
    });
  });

  describe('useApiPostFormUrlEncoded Hook', () => {
    it('handles form URL encoded POST request', async () => {
      const formData = {
        username: '<EMAIL>',
        password: 'password123',
      };
      const responseData = { token: 'abc123', user: { id: 1 } };
      const mockResponse = { data: responseData };
      mockApiClient?.postFormUrlEncoded?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() =>
        useApiPostFormUrlEncoded('/api/auth/login'),
      );

      await act(async () => {
        await result.current[1].execute(formData);
      });

      expect(result.current[0].data).toEqual(responseData);
      expect(mockApiClient.postFormUrlEncoded).toHaveBeenCalledWith(
        '/api/auth/login',
        formData,
        {},
      );
    });

    it('handles form data type conversion', async () => {
      const formData = { key: 'value', number: '123' };
      const mockResponse = { data: { success: true } };
      mockApiClient?.postFormUrlEncoded?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() =>
        useApiPostFormUrlEncoded('/api/form-submit'),
      );

      await act(async () => {
        await result.current[1].execute(formData as unknown);
      });

      expect(mockApiClient.postFormUrlEncoded).toHaveBeenCalledWith(
        '/api/form-submit',
        formData,
        {},
      );
    });
  });

  describe('useApi Generic Hook', () => {
    it('delegates to useApiGet for GET method', async () => {
      const mockResponse = { data: { users: [] } };
      mockApiClient?.get?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApi('GET', '/api/users'));

      await act(async () => {
        await result.current[1].execute();
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/users', {
        params: {},
      });
    });

    it('delegates to useApiPost for POST method', async () => {
      const postData = { name: 'Test' };
      const mockResponse = { data: { id: 1, ...postData } };
      mockApiClient?.post?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApi('POST', '/api/users'));

      await act(async () => {
        await result.current[1].execute(postData);
      });

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/users',
        postData,
        {},
      );
    });

    it('delegates to useApiPut for PUT method', async () => {
      const putData = { name: 'Updated' };
      const mockResponse = { data: { id: 1, ...putData } };
      mockApiClient?.put?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApi('PUT', '/api/users/1'));

      await act(async () => {
        await result.current[1].execute(putData);
      });

      expect(mockApiClient.put).toHaveBeenCalledWith(
        '/api/users/1',
        putData,
        {},
      );
    });

    it('delegates to useApiPatch for PATCH method', async () => {
      const patchData = { status: 'active' };
      const mockResponse = { data: { id: 1, status: 'active' } };
      mockApiClient?.patch?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApi('PATCH', '/api/users/1'));

      await act(async () => {
        await result.current[1].execute(patchData);
      });

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/api/users/1',
        patchData,
        {},
      );
    });

    it('delegates to useApiDelete for DELETE method', async () => {
      const mockResponse = { data: { success: true } };
      mockApiClient?.delete?.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useApi('DELETE', '/api/users/1'));

      await act(async () => {
        await result.current[1].execute();
      });

      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/users/1', {});
    });

    it('throws error for unsupported method', () => {
      expect(() => {
        renderHook(() => useApi('INVALID' as unknown, '/api/test'));
      }).toThrow('Unsupported method: INVALID');
    });
  });

  describe('Error Handling Across All Hooks', () => {
    const testErrorHandling = (
      hookName: string,
      useHook: () => any,
      clientMethod: keyof typeof mockApiClient,
    ) => {
      it(`${hookName} handles API errors consistently`, async () => {
        const mockError = {
          response: {
            status: 422,
            data: {
              detail: [
                {
                  loc: ['body', 'email'],
                  msg: 'Invalid email',
                  type: 'value_error',
                },
              ],
            },
          },
        };
        const mockApiError = {
          type: ErrorType.VALIDATION,
          message: 'Validation failed',
          details: { email: ['Invalid email'] },
          statusCode: 422,
        };

        (mockApiClient[clientMethod] as unknown).mockRejectedValueOnce(
          mockError,
        );
        mockCreateApiError.mockImplementation((errorInput) => {
          if (typeof errorInput === 'object' && errorInput?.response) {
            return mockApiError;
          }
          return {
            type: ErrorType.UNKNOWN,
            message: 'Test error',
            originalError: errorInput,
          };
        });

        const { result } = renderHook(useHook);

        await act(async () => {
          try {
            await result.current[1].execute();
          } catch (error) {
            expect(error).toEqual(mockApiError);
          }
        });

        expect(result.current[0].error).toEqual(mockApiError);
        expect(result.current[0].isLoading).toBe(false);
      });
    };

    testErrorHandling('useApiGet', () => useApiGet('/test'), 'get');
    testErrorHandling('useApiPost', () => useApiPost('/test'), 'post');
    testErrorHandling('useApiPut', () => useApiPut('/test'), 'put');
    testErrorHandling('useApiPatch', () => useApiPatch('/test'), 'patch');
    testErrorHandling('useApiDelete', () => useApiDelete('/test'), 'delete');
  });

  describe('Hook Dependencies and Re-execution', () => {
    it('updates when endpoint changes', () => {
      let endpoint = '/api/users';
      const { result, rerender } = renderHook(() => useApiGet(endpoint));

      const firstExecute = result.current[1].execute;

      endpoint = '/api/posts';
      rerender();

      const secondExecute = result.current[1].execute;

      // Functions should be different due to dependency change
      expect(firstExecute).not.toBe(secondExecute);
    });

    it('updates when options change', () => {
      let options = { params: { page: 1 } };
      const { result, rerender } = renderHook(() =>
        useApiGet('/api/users', options),
      );

      const firstExecute = result.current[1].execute;

      options = { params: { page: 2 } };
      rerender();

      const secondExecute = result.current[1].execute;

      expect(firstExecute).not.toBe(secondExecute);
    });

    it('maintains stable reset function', () => {
      const { result, rerender } = renderHook(() => useApiGet('/api/users'));

      const firstReset = result.current[1].reset;
      rerender();
      const secondReset = result.current[1].reset;

      expect(firstReset).toBe(secondReset);
    });
  });

  describe('Concurrent Request Handling', () => {
    it('handles multiple concurrent requests correctly', async () => {
      const { result } = renderHook(() => useApiGet('/api/users'));

      // Start first request
      const promise1 = act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 100));
        return result.current[1].execute({ page: 1 });
      });

      // Start second request immediately
      const promise2 = act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 50));
        return result.current[1].execute({ page: 2 });
      });

      mockApiClient.get
        .mockResolvedValueOnce({ data: { users: [], page: 1 } })
        .mockResolvedValueOnce({ data: { users: [], page: 2 } });

      await Promise.all([promise1, promise2]);

      // Should reflect the last completed request
      expect(result.current[0].isLoading).toBe(false);
    });
  });

  describe('Memory Leak Prevention', () => {
    it('cleans up properly on unmount', () => {
      const { result, unmount } = renderHook(() => useApiGet('/api/users'));

      // Trigger a request
      act(() => {
        void result.current[1].execute();
      });

      // Unmount should not cause any issues
      expect(() => unmount()).not.toThrow();
    });
  });
});
