/**
 * React hook for real-time progress tracking via Server-Sent Events (SSE).
 *
 * This hook connects to the backend SSE endpoint to receive real-time
 * progress updates for long-running tasks like file processing.
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';

export interface ProgressUpdate {
  type: 'status' | 'progress' | 'final' | 'error';
  task_id: string;
  stage?: string;
  message?: string;
  progress?: number;
  timestamp?: number;
  metadata?: Record<string, unknown>;
  error?: string;
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  total_stages?: number;
  current_stage?: number;
  result?: Record<string, unknown>;
}

export interface UseProgressTrackingOptions {
  onUpdate?: (update: ProgressUpdate) => void;
  onComplete?: (result: unknown) => void;
  onError?: (error: string) => void;
  autoConnect?: boolean;
}

export interface ProgressTrackingState {
  isConnected: boolean;
  isLoading: boolean;
  status: 'idle' | 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentStage: string;
  message: string;
  error: string | null;
  updates: ProgressUpdate[];
  result: unknown;
}

export function useProgressTracking(
  taskId: string | null,
  options: UseProgressTrackingOptions = {},
) {
  const { token } = useAuth();
  const [state, setState] = useState<ProgressTrackingState>({
    isConnected: false,
    isLoading: false,
    status: 'idle',
    progress: 0,
    currentStage: '',
    message: '',
    error: null,
    updates: [],
    result: null,
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { onUpdate, onComplete, onError, autoConnect = true } = options;

  const handleUpdate = useCallback(
    (update: ProgressUpdate) => {
      setState((prev) => {
        const newState = { ...prev };

        // Add update to history
        newState.updates = [...prev.updates, update];

        // Update state based on update type
        switch (update.type) {
          case 'status':
            newState.status = update.status || 'pending';
            newState.progress = update.progress || 0;
            if (update.total_stages) {
              newState.currentStage = `Stage ${update.current_stage || 0}/${update.total_stages}`;
            }
            break;

          case 'progress':
            newState.status = 'processing';
            newState.progress = update.progress || prev.progress;
            newState.currentStage = update.stage || prev.currentStage;
            newState.message = update.message || prev.message;
            break;

          case 'final':
            newState.status = update.status || 'completed';
            newState.progress = 1;
            newState.result = update.result;
            if (update.error) {
              newState.status = 'failed';
              newState.error = update.error;
            }
            break;

          case 'error':
            newState.status = 'failed';
            newState.error = update.error || 'Unknown error';
            break;
        }

        return newState;
      });

      // Call user callbacks
      onUpdate?.(update);

      if (update.type === 'final') {
        if (update.error) {
          onError?.(update.error);
        } else {
          onComplete?.(update.result);
        }
      } else if (update.type === 'error') {
        onError?.(update.error || 'Unknown error');
      }
    },
    [onUpdate, onComplete, onError],
  );

  const connect = useCallback(() => {
    if (!taskId || !token || eventSourceRef.current) {
      return;
    }

    setState((prev) => ({ ...prev, isLoading: true }));

    const baseUrl =
      import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';
    const url = `${baseUrl}/progress/${taskId}`;

    // Create EventSource with auth header via URL parameter
    // Note: EventSource doesn't support custom headers, so we'd need to
    // pass the token as a query parameter or use a polyfill
    const eventSource = new EventSource(url, {
      withCredentials: true,
    });

    eventSource.onopen = () => {
      console.log(`Connected to progress stream for task ${taskId}`);
      setState((prev) => ({
        ...prev,
        isConnected: true,
        isLoading: false,
      }));
    };

    eventSource.onmessage = (event) => {
      try {
        const update: ProgressUpdate = JSON.parse(event.data);
        handleUpdate(update);
      } catch (error) {
        console.error('Failed to parse progress update:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('EventSource error:', error);
      setState((prev) => ({
        ...prev,
        isConnected: false,
        isLoading: false,
      }));

      // Close the connection
      eventSource.close();
      eventSourceRef.current = null;

      // Try to reconnect after a delay if not completed
      if (state.status !== 'completed' && state.status !== 'failed') {
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 3000);
      }
    };

    eventSourceRef.current = eventSource;
  }, [taskId, token, handleUpdate, state.status]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef?.current?.close();
      eventSourceRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setState((prev) => ({
      ...prev,
      isConnected: false,
      isLoading: false,
    }));
  }, []);

  const reset = useCallback(() => {
    disconnect();
    setState({
      isConnected: false,
      isLoading: false,
      status: 'idle',
      progress: 0,
      currentStage: '',
      message: '',
      error: null,
      updates: [],
      result: null,
    });
  }, [disconnect]);

  // Auto-connect when taskId changes
  useEffect(() => {
    if (autoConnect && taskId) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [taskId, autoConnect, connect, disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    reset,
  };
}

// Example usage:
/*
function FileUploadComponent() {
  const [taskId, setTaskId] = useState<string | null>(null);
  
  const {
    isConnected,
    status,
    progress,
    currentStage,
    message,
    error,
    result,
  } = useProgressTracking(taskId, {
    onUpdate: (update) => {
      console.log('Progress update:', update);
    },
    onComplete: (result) => {
      console.log('Task completed:', result);
      toast.success('File processed successfully!');
    },
    onError: (error) => {
      console.error('Task failed:', error);
      toast.error(`Processing failed: ${error}`);
    },
  });
  
  const handleFileUpload = async (file: File) => {
    // Upload file and get task ID
    const response = await uploadFile(file);
    setTaskId(response.task_id);
  };
  
  return (
    <div>
      {status === 'processing' && (
        <div>
          <h3>{currentStage}</h3>
          <p>{message}</p>
          <ProgressBar value={progress * 100} />
        </div>
      )}
    </div>
  );
}
*/
