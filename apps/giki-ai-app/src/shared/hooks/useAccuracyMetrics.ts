import { useState, useCallback } from 'react';
import { useToast } from '@/shared/components/ui/use-toast';
import { apiClient } from '@/shared/services/api/apiClient';

export interface MonthlyAccuracyMetrics {
  month: number;
  year: number;
  accuracy: number;
  total_transactions: number;
  correctly_categorized: number;
  incorrectly_categorized: number;
}

export interface AccuracyMetricsState {
  monthlyAccuracyData: unknown[];
  isLoadingAccuracy: boolean;
  accuracyError: string | null;

  // Month details state
  isLoadingMonthDetails: boolean;
  selectedMonth: number | null;
  monthDetailsData: unknown;
  isExportingExcel: boolean;
}

// Helper functions
const formatAccuracy = (accuracy: number): string => {
  return `${accuracy.toFixed(1)}%`;
};

const getAccuracyStatusColor = (accuracy: number): string => {
  if (accuracy >= 85) return 'text-green-600';
  if (accuracy >= 70) return 'text-yellow-600';
  return 'text-red-600';
};

// API functions
const getMonthlyAccuracyMetrics = async (): Promise<
  MonthlyAccuracyMetrics[]
> => {
  const response = await apiClient.get<{ metrics: MonthlyAccuracyMetrics[] }>(
    '/intelligence/accuracy-metrics',
  );
  return response?.data?.metrics || [];
};

const getMonthlyAccuracyDetails = async (month: number): Promise<unknown> => {
  const response = await apiClient.get(
    `/intelligence/accuracy-metrics/${month}/details`,
  );
  return response.data;
};

const exportMonthlyAccuracyExcel = async (month: number): Promise<void> => {
  const response = await apiClient.get(
    `/intelligence/accuracy-metrics/${month}/export`,
    {
      responseType: 'blob',
    },
  );

  // Create download link
  const url = window?.URL?.createObjectURL(response.data as Blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', `accuracy-metrics-month-${month}.xlsx`);
  document?.body?.appendChild(link);
  link.click();
  link.remove();
  window?.URL?.revokeObjectURL(url);
};

export const useAccuracyMetrics = () => {
  const { toast } = useToast();

  // Core accuracy state
  const [monthlyAccuracyData, setMonthlyAccuracyData] = useState<unknown[]>([]);
  const [isLoadingAccuracy, setIsLoadingAccuracy] = useState<boolean>(false);
  const [accuracyError, setAccuracyError] = useState<string | null>(null);

  // Month details state
  const [isLoadingMonthDetails, setIsLoadingMonthDetails] =
    useState<boolean>(false);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [monthDetailsData, setMonthDetailsData] = useState<unknown>(null);
  const [isExportingExcel, setIsExportingExcel] = useState<boolean>(false);

  const loadMonthlyAccuracyData = useCallback(async () => {
    setIsLoadingAccuracy(true);
    setAccuracyError(null);

    try {
      const data = await getMonthlyAccuracyMetrics();
      setMonthlyAccuracyData(data);
    } catch (error) {
      console.error('Error loading monthly accuracy data:', error);
      setAccuracyError('Failed to load accuracy metrics');
      toast({
        title: 'Error',
        description: 'Failed to load monthly accuracy metrics',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingAccuracy(false);
    }
  }, [toast]);

  const loadMonthDetails = useCallback(
    async (month: number) => {
      setIsLoadingMonthDetails(true);
      setSelectedMonth(month);

      try {
        const data = await getMonthlyAccuracyDetails(month);
        setMonthDetailsData(data);
      } catch (error) {
        console.error('Error loading month details:', error);
        toast({
          title: 'Error',
          description: `Failed to load details for month ${month}`,
          variant: 'destructive',
        });
        setSelectedMonth(null);
      } finally {
        setIsLoadingMonthDetails(false);
      }
    },
    [toast],
  );

  const handleExportExcel = useCallback(
    async (month: number) => {
      setIsExportingExcel(true);

      try {
        await exportMonthlyAccuracyExcel(month);
        toast({
          title: 'Export Started',
          description: `Excel export for month ${month} has been initiated`,
        });
      } catch (error) {
        console.error('Error exporting Excel:', error);
        toast({
          title: 'Export Error',
          description: `Failed to export Excel for month ${month}`,
          variant: 'destructive',
        });
      } finally {
        setIsExportingExcel(false);
      }
    },
    [toast],
  );

  const closeMonthDetails = useCallback(() => {
    setSelectedMonth(null);
    setMonthDetailsData(null);
  }, []);

  const getAccuracyColor = useCallback((accuracy: number): string => {
    return getAccuracyStatusColor(accuracy);
  }, []);

  const formatAccuracyValue = useCallback((accuracy: number): string => {
    return formatAccuracy(accuracy);
  }, []);

  // Calculate summary statistics
  const accuracySummary = useCallback(() => {
    if (monthlyAccuracyData.length === 0) {
      return {
        averageAccuracy: 0,
        totalTransactions: 0,
        bestMonth: null,
        worstMonth: null,
        trend: 'stable' as 'improving' | 'declining' | 'stable',
      };
    }

    const accuracies = monthlyAccuracyData.map((item) => item.accuracy || 0);
    const transactions = monthlyAccuracyData.map(
      (item) => item.total_transactions || 0,
    );

    const averageAccuracy =
      accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const totalTransactions = transactions.reduce(
      (sum, count) => sum + count,
      0,
    );

    const bestMonth = monthlyAccuracyData.reduce((best, current) =>
      (current.accuracy || 0) > (best?.accuracy || 0) ? current : best,
    );

    const worstMonth = monthlyAccuracyData.reduce((worst, current) =>
      (current.accuracy || 100) < (worst?.accuracy || 100) ? current : worst,
    );

    // Calculate trend (comparing first half to second half)
    const midpoint = Math.floor(accuracies.length / 2);
    const firstHalf = accuracies.slice(0, midpoint);
    const secondHalf = accuracies.slice(midpoint);

    const firstHalfAvg =
      firstHalf.reduce((sum, acc) => sum + acc, 0) / firstHalf.length;
    const secondHalfAvg =
      secondHalf.reduce((sum, acc) => sum + acc, 0) / secondHalf.length;

    let trend: 'improving' | 'declining' | 'stable' = 'stable';
    const difference = secondHalfAvg - firstHalfAvg;

    if (difference > 2) trend = 'improving';
    else if (difference < -2) trend = 'declining';

    return {
      averageAccuracy,
      totalTransactions,
      bestMonth,
      worstMonth,
      trend,
    };
  }, [monthlyAccuracyData]);

  return {
    // State
    monthlyAccuracyData,
    isLoadingAccuracy,
    accuracyError,
    isLoadingMonthDetails,
    selectedMonth,
    monthDetailsData,
    isExportingExcel,

    // Actions
    loadMonthlyAccuracyData,
    loadMonthDetails,
    handleExportExcel,
    closeMonthDetails,
    setSelectedMonth,

    // Utilities
    getAccuracyColor,
    formatAccuracyValue,
    accuracySummary,
  };
};
