/**
 * Error Handling Utilities Tests
 * Critical error handling and logging service tests for production reliability
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  Logger,
  LogLevel,
  logger,
  ErrorType,
  createApiError,
  handleApiError,
  isApiError,
  extractErrorMessage,
} from './errorHandling';

// Mock the toast function
const mockToast = vi.fn();
vi.mock('@/shared/components/ui/use-toast', () => ({
  toast: mockToast,
}));

describe('Error Handling Utilities - Production Error Management', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockToast.mockClear();
    logger.clearLogs();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Logger Service', () => {
    it('creates singleton instance', () => {
      const logger1 = Logger.getInstance();
      const logger2 = Logger.getInstance();

      expect(logger1).toBe(logger2);
      expect(logger1).toBe(logger);
    });

    it('logs messages with different levels', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const consoleWarnSpy = vi
        .spyOn(console, 'warn')
        .mockImplementation(() => {});
      const consoleInfoSpy = vi
        .spyOn(console, 'info')
        .mockImplementation(() => {});

      logger.debug('Debug message', 'TEST_CONTEXT', { test: 'data' });
      logger.info('Info message', 'TEST_CONTEXT');
      logger.warn('Warning message', 'TEST_CONTEXT');
      logger.error('Error message', 'TEST_CONTEXT', new Error('Test error'));
      logger.fatal('Fatal message', 'TEST_CONTEXT', new Error('Fatal error'));

      const logs = logger.getLogs();
      expect(logs).toHaveLength(5);
      expect(logs[0].level).toBe(LogLevel.DEBUG);
      expect(logs[1].level).toBe(LogLevel.INFO);
      expect(logs[2].level).toBe(LogLevel.WARN);
      expect(logs[3].level).toBe(LogLevel.ERROR);
      expect(logs[4].level).toBe(LogLevel.FATAL);

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
      consoleWarnSpy.mockRestore();
      consoleInfoSpy.mockRestore();
    });

    it('filters logs by level', () => {
      logger.debug('Debug message');
      logger.info('Info message');
      logger.error('Error message');

      const errorLogs = logger.getLogs(LogLevel.ERROR);
      expect(errorLogs).toHaveLength(1);
      expect(errorLogs[0].level).toBe(LogLevel.ERROR);
      expect(errorLogs[0].message).toBe('Error message');
    });

    it('maintains log buffer limit', () => {
      // Test with a smaller limit for performance
      const logger = Logger.getInstance();

      // Add logs beyond the limit
      for (let i = 0; i < 1005; i++) {
        logger.info(`Message ${i}`);
      }

      const logs = logger.getLogs();
      expect(logs.length).toBeLessThanOrEqual(1000);

      // Should have the most recent logs
      const lastLog = logs[logs.length - 1];
      expect(lastLog.message).toBe('Message 1004');
    });

    it('clears logs when requested', () => {
      logger.info('Test message');
      expect(logger.getLogs()).toHaveLength(1);

      logger.clearLogs();
      expect(logger.getLogs()).toHaveLength(0);
    });

    it('includes timestamp and context in log entries', () => {
      const beforeTime = new Date().toISOString();
      logger.info('Test message', 'TEST_CONTEXT', { key: 'value' });
      const afterTime = new Date().toISOString();

      const logs = logger.getLogs();
      const log = logs[0];

      expect(log.timestamp).toBeDefined();
      expect(log.timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(log.timestamp).toBeLessThanOrEqual(afterTime);
      expect(log.context).toBe('TEST_CONTEXT');
      expect(log.data).toEqual({ key: 'value' });
    });
  });

  describe('createApiError', () => {
    it('returns existing ApiError unchanged', () => {
      const existingError = {
        type: ErrorType.VALIDATION,
        message: 'Validation failed',
        statusCode: 400,
      };

      const result = createApiError(existingError);
      expect(result).toBe(existingError);
    });

    it('handles JavaScript Error objects', () => {
      const error = new Error('Something went wrong');
      const result = createApiError(error);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Something went wrong');
      expect(result.originalError).toBe(error);
    });

    it('handles Axios-like error objects', () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            message: 'Invalid email format',
            errors: { email: ['Must be a valid email'] },
          },
        },
        config: {},
      };

      const result = createApiError(axiosError);

      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.message).toBe('Invalid email format');
      expect(result.statusCode).toBe(400);
      expect(result.details).toEqual({ email: ['Must be a valid email'] });
    });

    it('handles FastAPI validation error format', () => {
      const fastApiError = {
        response: {
          status: 422,
          data: {
            detail: [
              {
                loc: ['body', 'email'],
                msg: 'Invalid email format',
                type: 'value_error',
              },
              {
                loc: ['body', 'password'],
                msg: 'Password too short',
                type: 'value_error',
              },
            ],
          },
        },
        config: {},
      };

      const result = createApiError(fastApiError);

      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.message).toBe(
        'Validation failed. Please check the details.',
      );
      expect(result.details).toEqual({
        email: ['Invalid email format'],
        password: ['Password too short'],
      });
    });

    it('maps HTTP status codes correctly', () => {
      const testCases = [
        { status: 401, expectedType: ErrorType.AUTHENTICATION },
        { status: 403, expectedType: ErrorType.AUTHORIZATION },
        { status: 404, expectedType: ErrorType.NOT_FOUND },
        { status: 422, expectedType: ErrorType.VALIDATION },
        { status: 500, expectedType: ErrorType.SERVER },
        { status: 502, expectedType: ErrorType.SERVER },
      ];

      testCases.forEach(({ status, expectedType }) => {
        const error = {
          response: { status, data: {} },
          config: {},
        };

        const result = createApiError(error);
        expect(result.type).toBe(expectedType);
        expect(result.statusCode).toBe(status);
      });
    });

    it('handles fetch Response objects', () => {
      const response = {
        status: 404,
        json: vi.fn(),
      };

      const result = createApiError(response);

      expect(result.type).toBe(ErrorType.NOT_FOUND);
      expect(result.message).toBe('The requested resource was not found');
      expect(result.statusCode).toBe(404);
    });

    it('handles string errors', () => {
      const result = createApiError('Network connection failed');

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Network connection failed');
    });

    it('handles unknown error types with default message', () => {
      const result = createApiError(null, 'Custom default');

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Custom default');
      expect(result.originalError).toBe(null);
    });

    it('preserves server error messages when meaningful', () => {
      const serverError = {
        response: {
          status: 500,
          data: {
            message: 'Database connection timeout',
          },
        },
        config: {},
      };

      const result = createApiError(serverError);

      expect(result.type).toBe(ErrorType.SERVER);
      expect(result.message).toBe('Database connection timeout');
    });
  });

  describe('handleApiError', () => {
    it('creates API error and logs it', () => {
      const error = new Error('Test error');
      const result = handleApiError(error, {
        context: 'USER_LOGIN',
        showToast: false,
      });

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Test error');

      const logs = logger.getLogs(LogLevel.ERROR);
      expect(logs).toHaveLength(1);
      expect(logs[0].context).toBe('USER_LOGIN');
      expect(logs[0].message).toContain('API Error: Test error');
    });

    it('shows toast notification by default', () => {
      const error = new Error('Connection failed');
      handleApiError(error);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Connection failed',
        variant: 'destructive',
      });
    });

    it('skips toast when disabled', () => {
      const error = new Error('Silent error');
      handleApiError(error, { showToast: false });

      expect(mockToast).not.toHaveBeenCalled();
    });

    it('calls custom error handler', () => {
      const onError = vi.fn();
      const error = new Error('Custom handling');

      const result = handleApiError(error, { onError });

      expect(onError).toHaveBeenCalledWith(result);
    });

    it('uses custom default message', () => {
      const result = handleApiError(null, {
        defaultMessage: 'Operation failed',
        showToast: false,
      });

      expect(result.message).toBe('Operation failed');
    });
  });

  describe('isApiError', () => {
    it('correctly identifies ApiError objects', () => {
      const apiError = {
        type: ErrorType.VALIDATION,
        message: 'Invalid data',
      };

      expect(isApiError(apiError)).toBe(true);
    });

    it('rejects non-ApiError objects', () => {
      expect(isApiError(new Error('Regular error'))).toBe(false);
      expect(isApiError('String error')).toBe(false);
      expect(isApiError(null)).toBe(false);
      expect(isApiError({})).toBe(false);
      expect(isApiError({ message: 'Missing type' })).toBe(false);
      expect(
        isApiError({ type: 'invalid_type', message: 'Invalid type' }),
      ).toBe(false);
    });
  });

  describe('extractErrorMessage', () => {
    it('extracts message from ApiError', () => {
      const apiError = {
        type: ErrorType.VALIDATION,
        message: 'Validation failed',
      };

      const result = extractErrorMessage(apiError);
      expect(result).toBe('Validation failed');
    });

    it('extracts message from Error object', () => {
      const error = new Error('JavaScript error');
      const result = extractErrorMessage(error);
      expect(result).toBe('JavaScript error');
    });

    it('returns string errors directly', () => {
      const result = extractErrorMessage('String error message');
      expect(result).toBe('String error message');
    });

    it('extracts message property from objects', () => {
      const errorObj = { message: 'Object error' };
      const result = extractErrorMessage(errorObj);
      expect(result).toBe('Object error');
    });

    it('extracts detail property from objects', () => {
      const errorObj = { detail: 'Detail error' };
      const result = extractErrorMessage(errorObj);
      expect(result).toBe('Detail error');
    });

    it('returns default message for unknown types', () => {
      const result = extractErrorMessage(null, 'Default error');
      expect(result).toBe('Default error');
    });

    it('prioritizes message over detail', () => {
      const errorObj = {
        message: 'Primary message',
        detail: 'Secondary detail',
      };
      const result = extractErrorMessage(errorObj);
      expect(result).toBe('Primary message');
    });
  });

  describe('Error Type Mapping', () => {
    it('contains all expected error types', () => {
      const expectedTypes = [
        'network',
        'authentication',
        'authorization',
        'validation',
        'not_found',
        'server',
        'unknown',
      ];

      const actualTypes = Object.values(ErrorType);
      expect(actualTypes).toEqual(expect.arrayContaining(expectedTypes));
      expect(actualTypes).toHaveLength(expectedTypes.length);
    });
  });

  describe('Production Error Scenarios', () => {
    it('handles network timeout errors', () => {
      const networkError = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded',
        isAxiosError: true,
      };

      const result = createApiError(networkError);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('timeout of 5000ms exceeded');
    });

    it('handles CORS errors', () => {
      const corsError = new TypeError('Failed to fetch');
      const result = createApiError(corsError);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Failed to fetch');
    });

    it('handles authentication token expiry', () => {
      const tokenError = {
        response: {
          status: 401,
          data: {
            detail: 'Token has expired',
          },
        },
        config: {},
      };

      const result = createApiError(tokenError);

      expect(result.type).toBe(ErrorType.AUTHENTICATION);
      expect(result.message).toBe('Token has expired');
    });

    it('handles malformed JSON responses', () => {
      const jsonError = new SyntaxError('Unexpected token < in JSON');
      const result = createApiError(jsonError);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Unexpected token < in JSON');
    });
  });

  describe('Error Logging in Different Environments', () => {
    it('logs to console in development', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      // Mock development environment
      vi.stubGlobal('import', { meta: { env: { DEV: true, PROD: false } } });

      logger.error('Development error', 'DEV_TEST');

      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});
