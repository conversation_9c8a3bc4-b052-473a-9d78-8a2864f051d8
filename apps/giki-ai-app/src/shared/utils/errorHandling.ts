import { toast } from '@/shared/components/ui/use-toast';

/**
 * Log levels for structured logging
 */
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

/**
 * Structured log entry
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: string;
  timestamp: string;
  data?: any;
  error?: Error;
}

/**
 * Logger service for structured logging
 */
export class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private log(
    level: LogLevel,
    message: string,
    context?: string,
    data?: any,
    error?: Error,
  ): void {
    const entry: LogEntry = {
      level,
      message,
      context,
      timestamp: new Date().toISOString(),
      data,
      error,
    };

    // Add to internal log buffer
    this?.logs?.push(entry);
    if (this?.logs?.length > this.maxLogs) {
      this?.logs?.shift();
    }

    // In development, output to console
    if (import.meta.env.DEV) {
      const consoleMethod =
        level === LogLevel.ERROR || level === LogLevel.FATAL
          ? 'error'
          : level === LogLevel.WARN
            ? 'warn'
            : level === LogLevel.INFO
              ? 'info'
              : 'log';

      console[consoleMethod](
        `[${entry.timestamp}] [${level.toUpperCase()}]${context ? ` [${context}]` : ''} ${message}`,
        data || error || '',
      );
    }

    // In production, you would send to a logging service
    if (
      import.meta.env.PROD &&
      (level === LogLevel.ERROR || level === LogLevel.FATAL)
    ) {
      // TODO: Send to error tracking service (e.g., Sentry, LogRocket)
      // this.sendToErrorTracking(entry);
    }
  }

  debug(message: string, context?: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, context, data);
  }

  info(message: string, context?: string, data?: any): void {
    this.log(LogLevel.INFO, message, context, data);
  }

  warn(message: string, context?: string, data?: any): void {
    this.log(LogLevel.WARN, message, context, data);
  }

  error(message: string, context?: string, error?: Error, data?: any): void {
    this.log(LogLevel.ERROR, message, context, data, error);
  }

  fatal(message: string, context?: string, error?: Error, data?: any): void {
    this.log(LogLevel.FATAL, message, context, data, error);
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level) {
      return this?.logs?.filter((log) => log.level === level);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

/**
 * Standard error types that can be handled consistently
 */
export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  UNKNOWN = 'unknown',
}

/**
 * Structured API error response
 */
export interface ApiError {
  type: ErrorType;
  message: string;
  details?: Record<string, string[]>; // Field-specific validation errors
  statusCode?: number;
  originalError?: unknown;
}

/**
 * Creates a standardized API error object
 */
export function createApiError(
  error: unknown,
  defaultMessage = 'An unexpected error occurred',
): ApiError {
  // If it's already an ApiError, return it
  if (typeof error === 'object' && error !== null && 'type' in error) {
    return error as ApiError;
  }

  // Handle Error objects
  if (error instanceof Error) {
    return {
      type: ErrorType.UNKNOWN,
      message: error.message || defaultMessage,
      originalError: error,
    };
  }

  // Handle AxiosError-like objects
  if (
    typeof error === 'object' &&
    error !== null &&
    ((error as unknown).isAxiosError === true ||
      ('response' in error && (error as unknown).config)) && // Check for AxiosError signature
    (error as unknown).response &&
    typeof (error as unknown).response.status === 'number'
  ) {
    const axiosError = error as unknown; // More specific type would be AxiosError, but any for simplicity here
    const response = axiosError.response;
    const statusCode = response.status;
    const responseData = response.data;

    let type = ErrorType.UNKNOWN;
    // Try to get a more specific message from responseData first
    let message = defaultMessage;
    if (responseData && typeof responseData === 'object') {
      if (responseData.message && typeof responseData.message === 'string') {
        message = responseData.message;
      } else if (
        responseData.detail &&
        typeof responseData.detail === 'string'
      ) {
        message = responseData.detail;
      } else if (responseData.error && typeof responseData.error === 'string') {
        message = responseData.error;
      }
    }

    let details: Record<string, string[]> | undefined = undefined;
    if (
      responseData &&
      typeof responseData === 'object' &&
      responseData.errors &&
      typeof responseData.errors === 'object'
    ) {
      details = responseData.errors;
    } else if (
      responseData &&
      typeof responseData === 'object' &&
      responseData.detail &&
      Array.isArray(responseData.detail) &&
      responseData?.detail?.every(
        (item: unknown) => item.loc && item.msg && item.type,
      )
    ) {
      // FastAPI validation error format
      details = responseData?.detail?.reduce(
        (acc: Record<string, string[]>, current: unknown) => {
          const field =
            current.loc && current?.loc?.length > 1
              ? current.loc[1]
              : 'general';
          if (!acc[field]) {
            acc[field] = [];
          }
          acc[field].push(current.msg);
          return acc;
        },
        {},
      );
      if (
        (message === defaultMessage ||
          message === 'Server error, please try again later') &&
        details &&
        Object.keys(details).length > 0
      ) {
        message = 'Validation failed. Please check the details.';
      }
    }

    switch (statusCode) {
      case 400:
        type = ErrorType.VALIDATION;
        message =
          message !== defaultMessage &&
          message !== 'Server error, please try again later' &&
          !/Internal server error/i.test(message)
            ? message
            : 'Invalid request data';
        break;
      case 401:
        type = ErrorType.AUTHENTICATION;
        message = 'Authentication required'; // Keep specific message for 401
        break;
      case 403:
        type = ErrorType.AUTHORIZATION;
        message = 'You do not have permission to perform this action'; // Keep specific
        break;
      case 404:
        type = ErrorType.NOT_FOUND;
        message =
          message !== defaultMessage &&
          message !== 'Server error, please try again later' &&
          !/Internal server error/i.test(message)
            ? message
            : 'The requested resource was not found';
        break;
      case 422: // Unprocessable Entity
        type = ErrorType.VALIDATION;
        message =
          message !== defaultMessage &&
          message !== 'Server error, please try again later' &&
          !/Internal server error/i.test(message)
            ? message
            : 'Validation failed';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        type = ErrorType.SERVER;
        // Only use generic server error message if a more specific one isn't available from the response
        message =
          message !== defaultMessage &&
          !/^(Invalid|Authentication|Authorization|Validation|The requested)/i.test(
            message,
          )
            ? message
            : 'Server error, please try again later';
        break;
      default:
        if (statusCode >= 400 && statusCode < 500) {
          type = ErrorType.VALIDATION;
          message =
            message !== defaultMessage &&
            message !== 'Server error, please try again later' &&
            !/Internal server error/i.test(message)
              ? message
              : 'Request error';
        } else if (statusCode >= 500) {
          type = ErrorType.SERVER;
          message =
            message !== defaultMessage &&
            message !== 'Server error, please try again later' &&
            !/Internal server error/i.test(message)
              ? message
              : 'Server error';
        }
    }

    return {
      type,
      message,
      details,
      statusCode,
      originalError: error,
    };
  }

  // Handle generic Response objects (e.g., from fetch)
  // This block should come AFTER AxiosError handling
  if (
    typeof error === 'object' &&
    error !== null &&
    'status' in error && // This implies it's a Response-like object
    typeof (error as unknown).status === 'number' &&
    typeof (error as unknown).json === 'function' // Differentiator for fetch Response
  ) {
    const response = error as Response;
    const statusCode = response.status;

    // Map HTTP status codes to error types
    let type = ErrorType.UNKNOWN;
    let message = defaultMessage;

    switch (statusCode) {
      case 400:
        type = ErrorType.VALIDATION;
        message = 'Invalid request data';
        break;
      case 401:
        type = ErrorType.AUTHENTICATION;
        message = 'Authentication required';
        break;
      case 403:
        type = ErrorType.AUTHORIZATION;
        message = 'You do not have permission to perform this action';
        break;
      case 404:
        type = ErrorType.NOT_FOUND;
        message = 'The requested resource was not found';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        type = ErrorType.SERVER;
        message = 'Server error, please try again later';
        break;
      default:
        if (statusCode >= 400 && statusCode < 500) {
          type = ErrorType.VALIDATION;
          message = 'Request error';
        } else if (statusCode >= 500) {
          type = ErrorType.SERVER;
          message = 'Server error';
        }
    }

    return {
      type,
      message,
      statusCode,
      originalError: error,
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      type: ErrorType.UNKNOWN,
      message: error || defaultMessage,
    };
  }

  // Default case
  return {
    type: ErrorType.UNKNOWN,
    message: defaultMessage,
    originalError: error,
  };
}

/**
 * Handles API errors consistently across the application
 */
export function handleApiError(
  error: unknown,
  options?: {
    showToast?: boolean;
    defaultMessage?: string;
    context?: string;
    onError?: (apiError: ApiError) => void;
  },
): ApiError {
  const {
    showToast = true,
    defaultMessage = 'An unexpected error occurred',
    context,
    onError,
  } = options || {};

  // Create standardized error
  const apiError = createApiError(error, defaultMessage);

  // Log error with context using structured logger
  logger.error(`API Error: ${apiError.message}`, context, apiError);

  // Show toast notification if requested
  if (showToast) {
    toast({
      title: 'Error',
      description: apiError.message,
      variant: 'destructive',
    });
  }

  // Call custom error handler if provided
  if (onError) {
    onError(apiError);
  }

  return apiError;
}

/**
 * Type guard to check if an object is an ApiError
 */
export function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'type' in error &&
    'message' in error &&
    Object.values(ErrorType).includes((error as ApiError).type)
  );
}

/**
 * Extracts a user-friendly error message from various error types
 */
export function extractErrorMessage(
  error: unknown,
  defaultMessage = 'An unexpected error occurred',
): string {
  if (isApiError(error)) {
    return error.message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  if (typeof error === 'object' && error !== null) {
    if ('message' in error && typeof (error as unknown).message === 'string') {
      return (error as unknown).message;
    }
    if ('detail' in error && typeof (error as unknown).detail === 'string') {
      return (error as unknown).detail;
    }
  }

  return defaultMessage;
}
