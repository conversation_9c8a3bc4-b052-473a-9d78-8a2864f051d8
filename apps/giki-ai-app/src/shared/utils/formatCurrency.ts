/**
 * Financial number formatting utilities for giki.ai
 * Follows enterprise financial software standards
 */

export interface CurrencyFormatOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  showCurrency?: boolean;
  showSign?: boolean;
}

/**
 * Format a number as currency following financial standards
 * - Right-aligned for tables (handled via CSS classes)
 * - Consistent decimal places
 * - Proper thousands separators
 * - Negative numbers handling
 */
export const formatCurrency = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  const {
    currency = 'USD',
    locale = 'en-US',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showCurrency = true,
    showSign = false,
  } = options;

  // Handle null/undefined values
  if (amount === null || amount === undefined) {
    return showCurrency ? '$0.00' : '0.00';
  }

  // Convert to number
  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers
  if (isNaN(numericAmount)) {
    return showCurrency ? '$0.00' : '0.00';
  }

  // Format using Intl.NumberFormat for proper localization
  const formatter = new Intl.NumberFormat(locale, {
    style: showCurrency ? 'currency' : 'decimal',
    currency: currency,
    minimumFractionDigits,
    maximumFractionDigits,
  });

  let formatted = formatter.format(Math.abs(numericAmount));

  // Handle sign display for financial data
  if (numericAmount < 0) {
    // Negative amounts (expenses)
    if (showCurrency) {
      formatted = `-${formatted}`;
    } else {
      formatted = `-${formatted}`;
    }
  } else if (showSign && numericAmount > 0) {
    // Positive amounts with explicit sign (income)
    formatted = `+${formatted}`;
  }

  return formatted;
};

/**
 * Format currency for data tables (right-aligned, consistent width)
 */
export const formatCurrencyForTable = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  return formatCurrency(amount, { ...options, showCurrency: true });
};

/**
 * Format percentage for financial displays
 */
export const formatPercentage = (
  value: number | string | null | undefined,
  decimals: number = 1,
): string => {
  if (value === null || value === undefined) return '0.0%';

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numericValue)) return '0.0%';

  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(numericValue / 100);
};

/**
 * Determine color class for financial amounts
 * Green for positive/income, red for negative/expenses
 */
export const getAmountColorClass = (
  amount: number | string | null | undefined,
  type?: 'income' | 'expense' | 'auto',
): string => {
  if (amount === null || amount === undefined) return 'text-muted-foreground';

  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numericAmount)) return 'text-muted-foreground';

  if (type === 'income') {
    return 'text-green-600 dark:text-green-400';
  } else if (type === 'expense') {
    return 'text-red-600 dark:text-red-400';
  } else {
    // Auto-detect based on sign
    return numericAmount >= 0
      ? 'text-green-600 dark:text-green-400'
      : 'text-red-600 dark:text-red-400';
  }
};

/**
 * CSS classes for financial number alignment in tables
 */
export const FINANCIAL_TABLE_CLASSES = {
  amount: 'text-right tabular-nums font-mono',
  percentage: 'text-right tabular-nums',
  currency: 'text-right tabular-nums font-mono',
} as const;
