/**
 * Custom error types for consistent error management across the application
 */

// Base error type
export enum ErrorType {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NETWORK = 'network',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  UNKNOWN = 'unknown',
}

// Base error interface
export interface AppError {
  type: ErrorType;
  message: string;
  statusCode?: number;
  field?: string; // Kept for ValidationError and other specific AppError subtypes
  details?: Record<string, string[]>; // Aligned with ApiError from lib/utils/errorHandling.ts
  originalError?: unknown; // Added from ApiError
}

// Validation error
export interface ValidationError extends AppError {
  type: ErrorType.VALIDATION;
  field: string;
}

// Authentication error
export interface AuthenticationError extends AppError {
  type: ErrorType.AUTHENTICATION;
}

// Password validation rules
export const PASSWORD_RULES = {
  MIN_LENGTH: 8,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBER: true,
  REQUIRE_SPECIAL_CHAR: true,
};

// Password validation error messages
export const PASSWORD_ERROR_MESSAGES = {
  MIN_LENGTH: `Password must be at least ${PASSWORD_RULES.MIN_LENGTH} characters long`,
  REQUIRE_UPPERCASE: 'Password must contain at least one uppercase letter',
  REQUIRE_LOWERCASE: 'Password must contain at least one lowercase letter',
  REQUIRE_NUMBER: 'Password must contain at least one number',
  REQUIRE_SPECIAL_CHAR: 'Password must contain at least one special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
};

// Helper function to validate password
export function validatePassword(password: string): ValidationError | null {
  if (!password || password.length < PASSWORD_RULES.MIN_LENGTH) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.MIN_LENGTH,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_UPPERCASE,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_LOWERCASE,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_NUMBER && !/\d/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_NUMBER,
      field: 'password',
    };
  }

  if (
    PASSWORD_RULES.REQUIRE_SPECIAL_CHAR &&
    !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)
  ) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_SPECIAL_CHAR,
      field: 'password',
    };
  }

  return null;
}

// Helper function to create API errors
export function createApiError(error: Partial<AppError>): AppError {
  return {
    type: error.type || ErrorType.UNKNOWN,
    message:
      error.message || 'An error occurred while communicating with the server',
    statusCode: error.statusCode,
    details: error.details,
    originalError: error.originalError,
    field: error.field,
  };
}

// Helper function to create validation errors
export function createValidationError(
  field: string,
  message: string,
): ValidationError {
  return {
    type: ErrorType.VALIDATION,
    field,
    message,
  };
}

// Helper function to create authentication errors
export function createAuthenticationError(
  message: string,
  statusCode?: number,
): AuthenticationError {
  return {
    type: ErrorType.AUTHENTICATION,
    message,
    statusCode,
  };
}
// Type guard for AppError
export function isAppError(error: unknown): error is AppError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'type' in error &&
    'message' in error &&
    Object.values(ErrorType).includes((error as AppError).type)
  );
}
