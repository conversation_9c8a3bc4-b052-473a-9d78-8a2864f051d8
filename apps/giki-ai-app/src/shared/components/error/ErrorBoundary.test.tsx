/**
 * ErrorBoundary Component Tests
 * Critical error handling component tests for production reliability
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import ErrorBoundary from './ErrorBoundary';

// Mock logger
vi.mock('@/shared/utils/errorHandling', () => ({
  logger: {
    error: vi.fn(),
  },
}));

import { logger } from '@/shared/utils/errorHandling';

// Test component that throws an error when shouldError is true
const ProblematicComponent = ({ shouldError }: { shouldError: boolean }) => {
  if (shouldError) {
    throw new Error('Test error message');
  }
  return <div data-testid="working-component">Component is working</div>;
};

// Component that throws specific error types
const ErrorThrowingComponent = ({ errorType }: { errorType: string }) => {
  switch (errorType) {
    case 'network':
      throw new Error('Network connection failed');
    case 'api':
      throw new Error('API server error');
    case 'validation':
      throw new Error('Validation failed');
    case 'permission':
      throw new Error('Access denied');
    default:
      throw new Error('Generic error');
  }
};

describe('ErrorBoundary - Production Error Handling Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock window methods
    Object.defineProperty(window, 'location', {
      value: { href: '', reload: vi.fn() },
      writable: true,
    });
  });

  describe('Normal Operation', () => {
    it('renders children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={false} />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
      expect(screen.getByText('Component is working')).toBeInTheDocument();
    });

    it('renders multiple children correctly', () => {
      render(
        <ErrorBoundary>
          <div data-testid="child-1">First child</div>
          <div data-testid="child-2">Second child</div>
          <ProblematicComponent shouldError={false} />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });

    it('passes through all props to children', () => {
      const TestChild = ({ testProp }: { testProp: string }) => (
        <div data-testid="test-child">{testProp}</div>
      );

      render(
        <ErrorBoundary>
          <TestChild testProp="test value" />
        </ErrorBoundary>,
      );

      expect(screen.getByText('test value')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('catches and displays error with default UI', () => {
      // Suppress console.error for this test
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
      expect(screen.getByText('Test error message')).toBeInTheDocument();
      expect(screen.getByText('Runtime Error')).toBeInTheDocument();
      expect(
        screen.getByText(/Something unexpected happened/),
      ).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('logs error with structured logging', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(logger.error).toHaveBeenCalledWith(
        'Error caught by ErrorBoundary',
        'ErrorBoundary',
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
          errorBoundary: true,
        }),
      );

      consoleSpy.mockRestore();
    });

    it('displays custom fallback UI when provided', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const customFallback = (
        <div data-testid="custom-fallback">Custom error fallback</div>
      );

      render(
        <ErrorBoundary fallback={customFallback}>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom error fallback')).toBeInTheDocument();
      expect(screen.queryByText('Application Error')).not.toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles different error types appropriately', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const errorTypes = ['network', 'api', 'validation', 'permission'];

      errorTypes.forEach((errorType) => {
        const { unmount } = render(
          <ErrorBoundary>
            <ErrorThrowingComponent errorType={errorType} />
          </ErrorBoundary>,
        );

        expect(screen.getByText('Application Error')).toBeInTheDocument();

        unmount();
      });

      consoleSpy.mockRestore();
    });

    it('generates unique error IDs', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const { unmount } = render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      const errorId1 = screen.getByText(/Error ID:/).textContent;
      unmount();

      // Small delay to ensure different timestamp
      vi.advanceTimersByTime(1);

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      const errorId2 = screen.getByText(/Error ID:/).textContent;
      expect(errorId1).not.toBe(errorId2);

      consoleSpy.mockRestore();
    });
  });

  describe('Error Recovery', () => {
    it('allows retry through Try Again button', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      const TestComponent = ({ shouldError }: { shouldError: boolean }) => {
        if (shouldError) {
          throw new Error('Test error');
        }
        return <div data-testid="recovered">Component recovered</div>;
      };

      const { rerender } = render(
        <ErrorBoundary>
          <TestComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();

      const tryAgainButton = screen.getByRole('button', { name: /try again/i });
      await user.click(tryAgainButton);

      // Rerender with no error
      rerender(
        <ErrorBoundary>
          <TestComponent shouldError={false} />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId('recovered')).toBeInTheDocument();
      expect(screen.queryByText('Application Error')).not.toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('provides home navigation', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      const homeButton = screen.getByRole('button', { name: /go home/i });
      await user.click(homeButton);

      expect(window?.location?.href).toBe('/');

      consoleSpy.mockRestore();
    });

    it('provides page reload functionality', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      const reloadButton = screen.getByRole('button', { name: /reload page/i });
      await user.click(reloadButton);

      expect(window?.location?.reload).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Error Information Display', () => {
    it('shows expandable stack trace', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      const stackTraceToggle = screen.getByText('View Stack Trace');
      expect(stackTraceToggle).toBeInTheDocument();

      await user.click(stackTraceToggle);

      // Stack trace should be visible after clicking
      expect(screen.getByText(/ProblematicComponent/)).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('displays helpful recovery suggestions', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('What can you do?')).toBeInTheDocument();
      expect(screen.getByText(/Try refreshing the page/)).toBeInTheDocument();
      expect(
        screen.getByText(/Check your internet connection/),
      ).toBeInTheDocument();
      expect(screen.getByText(/Contact support/)).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('shows error details section', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Error Details')).toBeInTheDocument();
      expect(screen.getByText('Technical Information')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility and Usability', () => {
    it('uses proper semantic HTML structure', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      // Check for proper button roles
      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /reload page/i }),
      ).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('provides keyboard navigation support', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByRole('button', { name: /go home/i })).toHaveFocus();

      await user.tab();
      expect(
        screen.getByRole('button', { name: /reload page/i }),
      ).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /try again/i })).toHaveFocus();

      consoleSpy.mockRestore();
    });

    it('maintains focus management', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      // Error boundary should not interfere with focus
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).not.toHaveAttribute('tabindex', '-1');
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('handles errors in error boundary itself gracefully', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      // Mock a scenario where the error boundary itself might fail
      const _originalGetDerivedStateFromError =
        ErrorBoundary.getDerivedStateFromError;

      render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles multiple rapid errors', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const MultiErrorComponent = ({ errorCount }: { errorCount: number }) => {
        for (let i = 0; i < errorCount; i++) {
          if (i === 0) throw new Error(`Error ${i + 1}`);
        }
        return <div>No errors</div>;
      };

      render(
        <ErrorBoundary>
          <MultiErrorComponent errorCount={3} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
      expect(logger.error).toHaveBeenCalledTimes(1); // Should only log once

      consoleSpy.mockRestore();
    });

    it('handles errors with no message', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const EmptyErrorComponent = () => {
        throw new Error('');
      };

      render(
        <ErrorBoundary>
          <EmptyErrorComponent />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
      expect(screen.getByText('An unknown error occurred')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles null/undefined errors', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const NullErrorComponent = () => {
        throw null;
      };

      render(
        <ErrorBoundary>
          <NullErrorComponent />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Memory', () => {
    it('does not cause memory leaks during error recovery', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const user = userEvent.setup();

      const { rerender } = render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      // Cycle through error and recovery multiple times
      for (let i = 0; i < 5; i++) {
        const tryAgainButton = screen.getByRole('button', {
          name: /try again/i,
        });
        await user.click(tryAgainButton);

        rerender(
          <ErrorBoundary>
            <ProblematicComponent shouldError={false} />
          </ErrorBoundary>,
        );

        rerender(
          <ErrorBoundary>
            <ProblematicComponent shouldError={true} />
          </ErrorBoundary>,
        );
      }

      expect(screen.getByText('Application Error')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles error boundary cleanup on unmount', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const { unmount } = render(
        <ErrorBoundary>
          <ProblematicComponent shouldError={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();

      unmount(); // Should not throw or cause issues

      consoleSpy.mockRestore();
    });

    it('renders efficiently even with large component trees', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const LargeComponentTree = () => (
        <div>
          {Array.from({ length: 100 }, (_, i) => (
            <div key={i}>Component {i}</div>
          ))}
          <ProblematicComponent shouldError={true} />
        </div>
      );

      const startTime = performance.now();
      render(
        <ErrorBoundary>
          <LargeComponentTree />
        </ErrorBoundary>,
      );
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should render quickly
      expect(screen.getByText('Application Error')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });
});
