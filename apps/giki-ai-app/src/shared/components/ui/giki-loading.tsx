/**
 * Giki Loading Components
 *
 * Professional loading indicators with brand styling
 */
import React from 'react';
import { cn } from '@/shared/utils/utils';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'branded' | 'dots' | 'pulse';
  className?: string;
}

function LoadingSpinner({
  size = 'md',
  variant = 'default',
  className,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  if (variant === 'dots') {
    const dotSizes = {
      xs: 'w-1 h-1',
      sm: 'w-1.5 h-1.5',
      md: 'w-2 h-2',
      lg: 'w-2.5 h-2.5',
      xl: 'w-3 h-3',
    };

    return (
      <div className={cn('flex space-x-1', className)}>
        <div
          className={cn(
            dotSizes[size],
            'bg-primary rounded-full animate-bounce',
          )}
          style={{ animationDelay: '0ms' }}
        />
        <div
          className={cn(
            dotSizes[size],
            'bg-primary rounded-full animate-bounce',
          )}
          style={{ animationDelay: '150ms' }}
        />
        <div
          className={cn(
            dotSizes[size],
            'bg-primary rounded-full animate-bounce',
          )}
          style={{ animationDelay: '300ms' }}
        />
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div
        className={cn(
          sizeClasses[size],
          'bg-primary rounded-full animate-pulse',
          className,
        )}
      />
    );
  }

  if (variant === 'branded') {
    return (
      <div className={cn(sizeClasses[size], 'relative', className)}>
        <div className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-[hsl(var(--giki-brand-green-dark))] via-[hsl(var(--giki-brand-blue-dark))] to-[hsl(var(--giki-brand-purple-dark))] animate-spin" />
        <div className="absolute inset-1 rounded-full bg-background" />
      </div>
    );
  }

  // Default spinner
  return (
    <div
      className={cn(
        sizeClasses[size],
        'border-2 border-muted border-t-primary rounded-full animate-spin',
        className,
      )}
    />
  );
}

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'card' | 'avatar' | 'button';
  rows?: number;
}

function LoadingSkeleton({
  className,
  variant = 'text',
  rows = 1,
}: LoadingSkeletonProps) {
  const baseClasses =
    'animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted';

  if (variant === 'text') {
    if (rows === 1) {
      return <div className={cn(baseClasses, 'h-4 rounded', className)} />;
    }

    return (
      <div className={cn('space-y-2', className)}>
        {Array.from({ length: rows }).map((_, i) => (
          <div
            key={i}
            className={cn(
              baseClasses,
              'h-4 rounded',
              i === rows - 1 ? 'w-3/4' : 'w-full',
            )}
          />
        ))}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className={cn('space-y-3 p-4', className)}>
        <div className={cn(baseClasses, 'h-6 w-3/4 rounded')} />
        <div className="space-y-2">
          <div className={cn(baseClasses, 'h-4 rounded')} />
          <div className={cn(baseClasses, 'h-4 w-5/6 rounded')} />
        </div>
      </div>
    );
  }

  if (variant === 'avatar') {
    return (
      <div className={cn(baseClasses, 'w-10 h-10 rounded-full', className)} />
    );
  }

  if (variant === 'button') {
    return (
      <div className={cn(baseClasses, 'h-10 w-24 rounded-lg', className)} />
    );
  }

  return <div className={cn(baseClasses, 'h-4 rounded', className)} />;
}

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  variant?: 'default' | 'branded' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

function ProgressBar({
  value,
  max = 100,
  className,
  variant = 'default',
  showLabel = false,
  size = 'md',
}: ProgressBarProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const sizeClasses = {
    sm: 'h-1.5',
    md: 'h-2.5',
    lg: 'h-4',
  };

  const variantClasses = {
    default: 'bg-primary',
    branded:
      'bg-gradient-to-r from-[hsl(var(--giki-brand-green-dark))] via-[hsl(var(--giki-brand-blue-dark))] to-[hsl(var(--giki-brand-purple-dark))]',
    success: 'bg-success',
    warning: 'bg-warning',
    error: 'bg-destructive',
  };

  return (
    <div className={cn('space-y-1', className)}>
      {showLabel && (
        <div className="flex flex-wrap justify-between truncate text-caption text-muted-foreground">
          <span>Progress</span>
          <span>{percentage.toFixed(0)}%</span>
        </div>
      )}
      <div
        className={cn(
          'w-full bg-muted rounded-full overflow-hidden',
          sizeClasses[size],
        )}
      >
        <div
          className={cn(
            'h-full transition-all duration-300 ease-out rounded-full',
            variantClasses[variant],
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  message?: string;
  className?: string;
}

function LoadingOverlay({
  isLoading,
  children,
  message = 'Loading...',
  className,
}: LoadingOverlayProps) {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/70 backdrop-blur-sm flex flex-wrap items-center justify-center z-modal rounded-lg">
          <div className="flex flex-wrap flex-col items-center space-y-3">
            <LoadingSpinner variant="branded" size="lg" />
            <p className="truncatelabel text-foreground">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Loading page component
interface LoadingPageProps {
  message?: string;
  description?: string;
}

function LoadingPage({
  message = 'Loading',
  description = 'Please wait while we prepare your data...',
}: LoadingPageProps) {
  return (
    <div className="flex flex-wrap flex-col items-center justify-center min-h-[400px] overflow-y-auto space-y-6">
      <div className="relative">
        <LoadingSpinner variant="branded" size="xl" />
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 blur-xl opacity-50" />
      </div>
      <div className="text-center space-y-2">
        <h3 className="truncate text-heading-4 text-foreground">{message}</h3>
        <p className="truncatebody-small max-w-sm">{description}</p>
      </div>
    </div>
  );
}

export {
  LoadingSpinner,
  LoadingSkeleton,
  ProgressBar,
  LoadingOverlay,
  LoadingPage,
  type LoadingSpinnerProps,
  type LoadingSkeletonProps,
  type ProgressBarProps,
  type LoadingOverlayProps,
  type LoadingPageProps,
};
