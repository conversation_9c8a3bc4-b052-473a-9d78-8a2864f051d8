import React from 'react';
import { Check, Circle } from 'lucide-react';
import { cn } from '@/shared/utils/utils';

export interface WorkflowStep {
  id: string;
  label: string;
  description?: string;
  status: 'completed' | 'active' | 'pending' | 'error';
  timestamp?: Date;
}

interface WorkflowProgressProps {
  steps: WorkflowStep[];
  variant?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  showTimestamps?: boolean;
  className?: string;
}

export const WorkflowProgress: React.FC<WorkflowProgressProps> = ({
  steps,
  variant = 'horizontal',
  size = 'md',
  showTimestamps = false,
  className,
}) => {
  const isHorizontal = variant === 'horizontal';

  const sizeClasses = {
    sm: {
      icon: 'h-6 w-6',
      text: 'text-xs',
      spacing: isHorizontal ? 'gap-2' : 'gap-3',
      lineHeight: isHorizontal ? 'h-0.5' : 'w-0.5',
    },
    md: {
      icon: 'h-8 w-8',
      text: 'text-sm',
      spacing: isHorizontal ? 'gap-3' : 'gap-4',
      lineHeight: isHorizontal ? 'h-0.5' : 'w-0.5',
    },
    lg: {
      icon: 'h-10 w-10',
      text: 'text-base',
      spacing: isHorizontal ? 'gap-4' : 'gap-6',
      lineHeight: isHorizontal ? 'h-1' : 'w-1',
    },
  };

  const sizes = sizeClasses[size];

  const getStepIcon = (step: WorkflowStep) => {
    if (step.status === 'completed') {
      return (
        <div className="rounded-full bg-green-600 p-1 truncatewhite">
          <Check className={cn(sizes.icon, 'scale-75')} />
        </div>
      );
    }

    if (step.status === 'active') {
      return (
        <div className="rounded-full bg-blue-600 p-1">
          <div className={cn(sizes.icon, 'rounded-full bg-white scale-50')} />
        </div>
      );
    }

    if (step.status === 'error') {
      return (
        <div className="rounded-full bg-red-600 p-1 truncatewhite">
          <span className={cn(sizes.text, 'font-bold')}>!</span>
        </div>
      );
    }

    return (
      <div className="rounded-full border-2 border-border bg-white p-1">
        <Circle className={cn(sizes.icon, 'scale-75 text-gray-300')} />
      </div>
    );
  };

  const getStepColor = (step: WorkflowStep) => {
    switch (step.status) {
      case 'completed':
        return 'text-green-600';
      case 'active':
        return 'text-blue-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-400';
    }
  };

  const getLineColor = (currentStep: WorkflowStep, nextStep?: WorkflowStep) => {
    if (currentStep.status === 'completed' && nextStep?.status !== 'pending') {
      return 'bg-green-600';
    }
    return 'bg-gray-300';
  };

  return (
    <div
      className={cn(
        'flex',
        isHorizontal ? 'items-center' : 'flex-col',
        sizes.spacing,
        className,
      )}
    >
      {steps.map((step, index) => (
        <div
          key={step.id}
          className={cn(
            'flex items-center',
            isHorizontal ? 'flex-row' : 'flex-col',
            sizes.spacing,
          )}
        >
          {/* Step */}
          <div
            className={cn(
              'flex',
              isHorizontal ? 'flex-col items-center' : 'flex-row items-start',
              sizes.spacing,
            )}
          >
            {/* Icon */}
            <div className="relative z-10">{getStepIcon(step)}</div>

            {/* Label and description */}
            <div
              className={cn(
                'flex flex-col',
                isHorizontal ? 'items-center text-center' : 'items-start',
              )}
            >
              <span
                className={cn(sizes.text, 'font-medium', getStepColor(step))}
              >
                {step.label}
              </span>

              {step.description && (
                <span
                  className={cn(sizes.text, 'text-muted-foreground mt-0.5')}
                >
                  {step.description}
                </span>
              )}

              {showTimestamps && step.timestamp && (
                <span className={cn('text-xs text-gray-400 mt-1')}>
                  {step.timestamp.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>

          {/* Connector line */}
          {index < steps.length - 1 && (
            <div
              className={cn(
                'flex-1',
                sizes.lineHeight,
                getLineColor(step, steps[index + 1]),
                isHorizontal ? 'min-w-[2rem]' : 'min-h-[2rem]',
              )}
            />
          )}
        </div>
      ))}
    </div>
  );
};

// Example usage component
export const UploadWorkflowProgress: React.FC<{
  currentStep: 'upload' | 'processing' | 'validation' | 'review' | 'complete';
}> = ({ currentStep }) => {
  const stepOrder = [
    'upload',
    'processing',
    'validation',
    'review',
    'complete',
  ];
  const currentIndex = stepOrder.indexOf(currentStep);

  const steps: WorkflowStep[] = [
    {
      id: 'upload',
      label: 'Upload',
      description: 'Select files',
      status: currentIndex >= 0 ? 'completed' : 'pending',
    },
    {
      id: 'processing',
      label: 'Processing',
      description: 'Extract data',
      status:
        currentIndex >= 1
          ? 'completed'
          : currentIndex === 0
            ? 'active'
            : 'pending',
    },
    {
      id: 'validation',
      label: 'Validation',
      description: 'Verify accuracy',
      status:
        currentIndex >= 2
          ? 'completed'
          : currentIndex === 1
            ? 'active'
            : 'pending',
    },
    {
      id: 'review',
      label: 'Review',
      description: 'Check results',
      status:
        currentIndex >= 3
          ? 'completed'
          : currentIndex === 2
            ? 'active'
            : 'pending',
    },
    {
      id: 'complete',
      label: 'Complete',
      description: 'Ready to use',
      status:
        currentIndex >= 4
          ? 'completed'
          : currentIndex === 3
            ? 'active'
            : 'pending',
    },
  ];

  return <WorkflowProgress steps={steps} variant="horizontal" size="md" />;
};

export default WorkflowProgress;
