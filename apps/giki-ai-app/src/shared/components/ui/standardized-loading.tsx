/**
 * Standardized Loading Components
 *
 * Provides consistent loading states across the application.
 * Replaces inconsistent custom spinners and basic text patterns.
 */
import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({
  size = 'md',
  className,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <Loader2 className={cn('animate-spin', sizeClasses[size], className)} />
  );
}

interface LoadingStateProps {
  title?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingState({
  title = 'Loading...',
  description,
  size = 'md',
  className,
}: LoadingStateProps) {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center space-y-3 py-8',
        className,
      )}
    >
      <LoadingSpinner size={size} />
      <div className="text-center">
        <h3 className="truncatelabel">{title}</h3>
        {description && (
          <p className="truncate text-caption text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </div>
    </div>
  );
}

interface InlineLoadingProps {
  text?: string;
  className?: string;
}

export function InlineLoading({
  text = 'Loading...',
  className,
}: InlineLoadingProps) {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      <LoadingSpinner size="sm" />
      <span className="truncatebody-small">{text}</span>
    </div>
  );
}

interface ButtonLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export function ButtonLoading({
  isLoading,
  children,
  loadingText = 'Loading...',
}: ButtonLoadingProps) {
  if (isLoading) {
    return (
      <>
        <LoadingSpinner size="sm" className="mr-2" />
        {loadingText}
      </>
    );
  }
  return <>{children}</>;
}

// Standardized loading states for common scenarios
export const StandardLoadingStates = {
  Dashboard: () => (
    <LoadingState
      title="Loading Dashboard"
      description="Gathering your financial overview..."
    />
  ),
  Transactions: () => (
    <LoadingState
      title="Loading Transactions"
      description="Fetching your transaction data..."
    />
  ),
  Upload: () => (
    <LoadingState
      title="Processing Upload"
      description="Analyzing your financial data..."
    />
  ),
  Reports: () => (
    <LoadingState
      title="Generating Report"
      description="Creating your financial insights..."
    />
  ),
  AI: () => <InlineLoading text="AI is processing..." />,
};

export default LoadingState;
