import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/shared/utils/utils';

const alertVariants = cva(
  'relative w-full rounded-xl border px-6 py-5 text-sm shadow-lg backdrop-blur-sm transition-all duration-200 [&>svg~*]:pl-8 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-6 [&>svg]:top-5 [&>svg]:text-foreground [&>svg]:w-5 [&>svg]:h-5',
  {
    variants: {
      variant: {
        default: 'bg-background/95 text-foreground border-border/50 shadow-md',
        destructive:
          'border-destructive/30 text-destructive bg-destructive/8 [&>svg]:text-destructive shadow-destructive/10 shadow-lg',
        warning:
          'border-warning/30 text-warning bg-warning/8 [&>svg]:text-warning shadow-warning/10 shadow-lg',
        success:
          'border-success/30 text-success bg-success/8 [&>svg]:text-success shadow-success/10 shadow-lg',
        info: 'border-primary/30 text-primary bg-primary/8 [&>svg]:text-primary shadow-primary/10 shadow-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
));
Alert.displayName = 'Alert';

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn('mb-2 font-semibold leading-none tracking-tight', className)}
    {...props}
  />
));
AlertTitle.displayName = 'AlertTitle';

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('text-sm leading-relaxed [&_p]:leading-relaxed', className)}
    {...props}
  />
));
AlertDescription.displayName = 'AlertDescription';

export { Alert, AlertTitle, AlertDescription };
