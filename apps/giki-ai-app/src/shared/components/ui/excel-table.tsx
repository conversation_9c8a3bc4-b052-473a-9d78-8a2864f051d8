/**
 * Excel-Inspired Table Component
 *
 * Professional spreadsheet-style table with Excel-like features and appearance.
 * Designed for financial data visualization with enterprise-grade styling.
 */
import React, { useState, useMemo } from 'react';
import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import { ArrowUpDown, Download, Eye, Calculator } from 'lucide-react';

interface ExcelTableColumn {
  key: string;
  label: string;
  type?: 'text' | 'number' | 'currency' | 'date' | 'status' | 'percentage';
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  formatValue?: (value: unknown) => string | React.ReactNode;
  aggregatable?: boolean;
}

interface ExcelTableProps {
  data: unknown[];
  columns: ExcelTableColumn[];
  title?: string;
  showAggregations?: boolean;
  enableSelection?: boolean;
  enableFiltering?: boolean;
  enableExport?: boolean;
  className?: string;
}

const ExcelTable: React.FC<ExcelTableProps> = ({
  data,
  columns,
  title,
  showAggregations = false,
  enableSelection = false,
  enableFiltering = true,
  enableExport = true,
  className,
}) => {
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());

  // Format cell values based on column type
  const formatCellValue = (value: unknown, column: ExcelTableColumn) => {
    if (column.formatValue) {
      return column.formatValue(value);
    }

    switch (column.type) {
      case 'currency':
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(Number(value) || 0);

      case 'number':
        return new Intl.NumberFormat('en-IN', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(Number(value) || 0);

      case 'percentage':
        return `${((Number(value) || 0) * 100).toFixed(1)}%`;

      case 'date':
        return value &&
          (typeof value === 'string' ||
            typeof value === 'number' ||
            value instanceof Date)
          ? new Date(value).toLocaleDateString()
          : '';

      case 'status':
        return (
          <Badge
            variant={value === 'completed' ? 'default' : 'secondary'}
            className="truncate text-caption max-w-[150px] truncate"
          >
            {typeof value === 'string' ? value : ''}
          </Badge>
        );

      default: {
        // Type guard to ensure value is stringifiable
        if (
          value === null ||
          value === undefined ||
          typeof value === 'object' ||
          typeof value === 'symbol' ||
          typeof value === 'function'
        ) {
          return '';
        }
        // At this point, value is string | number | boolean | bigint
        return String(value as string | number | boolean | bigint);
      }
    }
  };

  // Calculate aggregations for numeric columns
  const getColumnAggregation = (column: ExcelTableColumn) => {
    if (
      !column.aggregatable ||
      !['number', 'currency'].includes(column.type || '')
    ) {
      return null;
    }

    const values = filteredData
      .map((row) => row[column.key] as number)
      .filter((v): v is number => v != null && !isNaN(v));
    if (values.length === 0) return null;

    const sum = values.reduce((acc, val) => acc + val, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { sum, avg, min, max, count: values.length };
  };

  // Apply filters and sorting
  const filteredData = useMemo(() => {
    const filtered = data.filter((row) => {
      return Object.entries(filters).every(([key, filterValue]) => {
        if (!filterValue) return true;
        const rowValue = (row as Record<string, unknown>)[key];
        let cellValue = '';
        // Type guard to ensure rowValue is stringifiable
        if (
          rowValue !== null &&
          rowValue !== undefined &&
          typeof rowValue !== 'object' &&
          typeof rowValue !== 'symbol' &&
          typeof rowValue !== 'function'
        ) {
          // At this point, rowValue is string | number | boolean | bigint
          cellValue = String(
            rowValue as string | number | boolean | bigint,
          ).toLowerCase();
        }
        return cellValue.includes(filterValue.toLowerCase());
      });
    });

    if (sortColumn) {
      filtered.sort((a, b) => {
        const aVal = (a as Record<string, unknown>)[sortColumn];
        const bVal = (b as Record<string, unknown>)[sortColumn];

        if (aVal === bVal) return 0;

        const comparison = aVal < bVal ? -1 : 1;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }, [data, filters, sortColumn, sortDirection]);

  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  const handleSelectAll = () => {
    if (selectedRows.size === filteredData.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(filteredData.map((_, index) => index)));
    }
  };

  const _toggleColumnVisibility = (columnKey: string) => {
    const newHidden = new Set(hiddenColumns);
    if (newHidden.has(columnKey)) {
      newHidden.delete(columnKey);
    } else {
      newHidden.add(columnKey);
    }
    setHiddenColumns(newHidden);
  };

  const visibleColumns = columns.filter((col) => !hiddenColumns.has(col.key));

  return (
    <div className={cn('excel-table-container', className)}>
      {/* Table Header */}
      <div className="excel-table-header bg-[hsl(var(--giki-bg-muted))] border border-[hsl(var(--giki-border-primary))] p-3 rounded-t-[var(--giki-radius-lg)]">
        <div className="flex flex-wrap items-center justify-between">
          <div className="flex flex-wrap items-center gap-3">
            {title && (
              <h3 className="truncatestatus-badge text-[hsl(var(--giki-text-primary))] m-0">
                {title}
              </h3>
            )}
            <Badge
              variant="outline"
              className="truncate text-caption max-w-[150px] truncate"
            >
              {filteredData.length} rows
            </Badge>
          </div>

          <div className="flex flex-wrap items-center gap-2">
            {/* Column Visibility Toggle */}
            <div className="relative">
              <Button
                className="max-w-full h-7 px-2 truncate text-caption"
                variant="ghost"
                size="sm"
              >
                <Eye className="h-3 w-3 mr-1" />
                Columns
              </Button>
            </div>

            {enableExport && (
              <Button
                className="max-w-full h-7 px-2 truncate text-caption"
                variant="ghost"
                size="sm"
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
            )}
          </div>
        </div>

        {/* Filters Row */}
        {enableFiltering && (
          <div className="flex flex-wrap gap-2 mt-3">
            {visibleColumns
              .filter((col) => col.filterable !== false)
              .map((column) => (
                <div key={column.key} className="flex flex-wrap-1 min-w-0">
                  <Input
                    placeholder={`Filter ${column.label}...`}
                    value={filters[column.key] || ''}
                    onChange={(e) =>
                      setFilters((prev) => ({
                        ...prev,
                        [column.key]: e?.target?.value,
                      }))
                    }
                    className="h-7 truncate text-caption"
                  />
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Table Body */}
      <div className="excel-table-body border border-t-0 border-[hsl(var(--giki-border-primary))] bg-[hsl(var(--giki-card-bg))] overflow-auto max-h-96">
        <div className="overflow-x-auto">
          <table className="w-full truncate text-caption">
            {/* Headers */}
            <thead className="bg-[hsl(var(--giki-bg-muted))] sticky top-0 z-10">
              <tr>
                {enableSelection && (
                  <th className="excel-cell excel-header-cell w-10">
                    <input
                      type="checkbox"
                      checked={
                        selectedRows.size === filteredData.length &&
                        filteredData.length > 0
                      }
                      onChange={handleSelectAll}
                      className="rounded border-[#ced4da]"
                    />
                  </th>
                )}
                {visibleColumns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      'excel-cell excel-header-cell',
                      column.sortable !== false &&
                        'cursor-pointer hover:bg-[hsl(var(--giki-bg-secondary))]',
                    )}
                    style={{ width: column.width }}
                    onClick={() =>
                      column.sortable !== false && handleSort(column.key)
                    }
                  >
                    <div className="flex flex-wrap items-center justify-between">
                      <span className="font-medium truncate[hsl(var(--giki-text-primary))] text-data-table">
                        {column.label}
                      </span>
                      {column.sortable !== false && (
                        <ArrowUpDown className="h-3 w-3 truncate text-muted" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            {/* Data Rows */}
            <tbody>
              {filteredData.map((row, index) => (
                <tr
                  key={index}
                  className={cn(
                    'hover:bg-[hsl(var(--giki-bg-muted))] transition-colors',
                    selectedRows.has(index) &&
                      'bg-[hsl(var(--giki-bg-accent))]',
                    index % 2 === 0
                      ? 'bg-[hsl(var(--giki-card-bg))]'
                      : 'bg-[hsl(var(--giki-bg-muted))]/30',
                  )}
                >
                  {enableSelection && (
                    <td className="excel-cell max-w-0 overflow-hidden">
                      <div className="truncate">
                        <input
                          type="checkbox"
                          checked={selectedRows.has(index)}
                          onChange={(e) => {
                            const newSelected = new Set(selectedRows);
                            if (e?.target?.checked) {
                              newSelected.add(index);
                            } else {
                              newSelected.delete(index);
                            }
                            setSelectedRows(newSelected);
                          }}
                          className="rounded border-[#ced4da]"
                        />
                      </div>
                    </td>
                  )}
                  {visibleColumns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        'excel-cell',
                        ['number', 'currency', 'percentage'].includes(
                          column.type || '',
                        ) && 'text-right text-financial',
                        column.type === 'currency' && 'text-financial',
                      )}
                    >
                      {formatCellValue(
                        (row as Record<string, unknown>)[column.key],
                        column,
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>

            {/* Aggregation Row */}
            {showAggregations && (
              <tfoot className="bg-[hsl(var(--giki-bg-muted))] border-t-2 border-[hsl(var(--giki-border-primary))]">
                <tr>
                  {enableSelection && (
                    <td className="excel-cell max-w-0 overflow-hidden">
                      <div className="truncate"></div>
                    </td>
                  )}
                  {visibleColumns.map((column) => {
                    const agg = getColumnAggregation(column);
                    return (
                      <td
                        key={column.key}
                        className="excel-cell font-semibold truncate[hsl(var(--giki-text-primary))] text-financial max-w-0 overflow-hidden"
                      >
                        <div className="truncate">
                          {agg && (
                            <div className="text-right">
                              {column.type === 'currency'
                                ? new Intl.NumberFormat('en-IN', {
                                    style: 'currency',
                                    currency: 'INR',
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                  }).format(agg.sum)
                                : new Intl.NumberFormat('en-IN').format(
                                    agg.sum,
                                  )}
                            </div>
                          )}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              </tfoot>
            )}
          </table>
        </div>
      </div>

      {/* Table Footer */}
      <div className="excel-table-footer bg-[hsl(var(--giki-bg-muted))] border border-t-0 border-[hsl(var(--giki-border-primary))] p-2 rounded-b-[var(--giki-radius-lg)]">
        <div className="flex flex-wrap items-center justify-between truncate text-caption text-[hsl(var(--giki-text-muted))] text-data-table">
          <div className="flex flex-wrap items-center gap-4">
            <span>
              Showing {filteredData.length} of {data.length} records
            </span>
            {selectedRows.size > 0 && (
              <span className="font-medium">{selectedRows.size} selected</span>
            )}
          </div>

          {showAggregations && (
            <div className="flex flex-wrap items-center gap-4">
              {visibleColumns
                .filter((col) => col.aggregatable)
                .map((column) => {
                  const agg = getColumnAggregation(column);
                  if (!agg) return null;

                  return (
                    <div
                      key={column.key}
                      className="flex flex-wrap items-center gap-1"
                    >
                      <Calculator className="h-3 w-3" />
                      <span>
                        {column.label}:{' '}
                        {column.type === 'currency'
                          ? new Intl.NumberFormat('en-IN', {
                              style: 'currency',
                              currency: 'INR',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            }).format(agg.sum)
                          : new Intl.NumberFormat('en-IN').format(agg.sum)}
                      </span>
                    </div>
                  );
                })}
            </div>
          )}
        </div>
      </div>

      <style>{`
        .excel-cell {
          padding: var(--giki-space-2) var(--giki-space-3);
          border-right: 1px solid hsl(var(--giki-border-primary));
          border-bottom: 1px solid hsl(var(--giki-border-primary));
          vertical-align: middle;
          line-height: 1.2;
          font-family: var(--giki-font-family-sans);
        }
        
        .excel-header-cell {
          background-color: hsl(var(--giki-bg-muted));
          font-weight: var(--giki-font-weight-semibold);
          color: hsl(var(--giki-text-primary));
          border-bottom: 2px solid hsl(var(--giki-border-primary));
        }
        
        .excel-table-container {
          font-family: var(--giki-font-family-sans);
          border-radius: var(--giki-radius-lg);
          overflow: hidden;
          box-shadow: var(--giki-card-shadow);
        }
        
        .excel-cell[class*="text-financial"] {
          font-family: var(--giki-font-family-mono);
          font-feature-settings: 'tnum' 1;
          font-variant-numeric: tabular-nums;
        }
      `}</style>
    </div>
  );
};

export default ExcelTable;
