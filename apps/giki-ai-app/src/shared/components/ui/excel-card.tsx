/**
 * Excel-Inspired Card Component
 *
 * Professional card component mimicking Excel's worksheet appearance with
 * grid-like structure and financial data presentation patterns.
 */
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { LucideIcon } from 'lucide-react';

interface ExcelCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  icon?: LucideIcon;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

const ExcelCard: React.FC<ExcelCardProps> = ({
  title,
  value,
  subtitle,
  trend,
  icon: Icon,
  variant = 'default',
  size = 'md',
  className,
  children,
}) => {
  const formatValue = (val: string | number) => {
    // Return the value as-is since formatting is now handled by parent components
    // This allows for better control over currency and number formatting
    return val;
  };

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-error';
      default:
        return 'text-muted';
    }
  };

  const getVariantStyles = (variant: string) => {
    switch (variant) {
      case 'success':
        return 'border-l-4 border-l-[hsl(var(--giki-success))] bg-[hsl(var(--giki-success))]/5';
      case 'warning':
        return 'border-l-4 border-l-[hsl(var(--giki-warning))] bg-[hsl(var(--giki-warning))]/5';
      case 'error':
        return 'border-l-4 border-l-[hsl(var(--giki-destructive))] bg-[hsl(var(--giki-destructive))]/5';
      case 'info':
        return 'border-l-4 border-l-[hsl(var(--giki-info))] bg-[hsl(var(--giki-info))]/5';
      default:
        return 'border-l-4 border-l-[hsl(var(--giki-border-primary))]';
    }
  };

  const getSizeStyles = (size: string) => {
    switch (size) {
      case 'sm':
        return 'p-[var(--giki-space-3)]';
      case 'lg':
        return 'p-[var(--giki-space-6)]';
      default:
        return 'p-[var(--giki-space-4)]';
    }
  };

  return (
    <div
      className={cn(
        // Excel-like base styling with design tokens
        'excel-card bg-[hsl(var(--giki-card-bg))] border border-[hsl(var(--giki-card-border))] rounded-[var(--giki-radius-lg)]',
        'shadow-[var(--giki-card-shadow)] transition-[var(--giki-transition-normal)]',
        'hover:shadow-[var(--giki-card-shadow-elevated)] hover:border-[hsl(var(--giki-border-focus))]',
        // Variant and size styling
        getVariantStyles(variant),
        getSizeStyles(size),
        className,
      )}
    >
      {/* Header Section */}
      <div className="excel-card-header flex flex-wrap items-center justify-between mb-3">
        <div className="flex flex-wrap items-center gap-2">
          {Icon && (
            <div className="excel-icon-container p-[var(--giki-space-2)] bg-[hsl(var(--giki-bg-muted))] rounded-[var(--giki-radius-md)]">
              <Icon className="h-4 w-4 truncate[hsl(var(--giki-text-secondary))]" />
            </div>
          )}
          <h3 className="excel-card-title truncatestatus-badge text-[hsl(var(--giki-text-secondary))] uppercase tracking-wide">
            {title}
          </h3>
        </div>
      </div>

      {/* Main Value Section */}
      <div className="excel-card-body">
        <div className="excel-value text-2xl font-bold text-[hsl(var(--giki-text-primary))] mb-1 text-financial">
          {formatValue(value)}
        </div>

        {subtitle && (
          <div className="excel-subtitle text-sm text-[hsl(var(--giki-text-muted))] mb-2">
            {subtitle}
          </div>
        )}

        {/* Trend Indicator */}
        {trend && (
          <div
            className={cn(
              'excel-trend flex items-center gap-1 text-xs font-medium',
              getTrendColor(trend.direction),
            )}
          >
            <span className="excel-trend-arrow">
              {trend.direction === 'up'
                ? '↗'
                : trend.direction === 'down'
                  ? '↘'
                  : '→'}
            </span>
            <span>
              {trend.direction === 'up' ? '+' : ''}
              {trend.value}% {trend.label}
            </span>
          </div>
        )}

        {/* Additional Content */}
        {children && (
          <div className="excel-card-content mt-3 pt-3 border-t border-[hsl(var(--giki-border-primary))]">
            {children}
          </div>
        )}
      </div>

      <style>{`
        .excel-card {
          font-family: var(--giki-font-family-sans);
          position: relative;
        }
        
        .excel-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg, transparent 0%, hsl(var(--giki-border-primary)) 20%, hsl(var(--giki-border-primary)) 80%, transparent 100%);
        }
        
        .excel-card-title {
          letter-spacing: 0.5px;
        }
        
        .excel-value {
          line-height: 1.1;
          font-family: var(--giki-font-family-mono);
          font-feature-settings: 'tnum' 1;
          font-variant-numeric: tabular-nums;
        }
        
        .excel-icon-container {
          background: linear-gradient(135deg, hsl(var(--giki-bg-muted)) 0%, hsl(var(--giki-border-primary)) 100%);
        }
        
        .excel-trend-arrow {
          font-size: 14px;
          font-weight: bold;
        }
        
        /* Hover effect for interactive cards */
        .excel-card:hover {
          improve: translateY(-1px);
        }
        
        /* Excel-like grid lines effect */
        .excel-card-content {
          background-image: 
            linear-gradient(hsla(var(--giki-border-primary), 0.3) 1px, transparent 1px),
            linear-gradient(90deg, hsla(var(--giki-border-primary), 0.3) 1px, transparent 1px);
          background-size: var(--giki-space-5) var(--giki-space-5);
        }
      `}</style>
    </div>
  );
};

export default ExcelCard;
