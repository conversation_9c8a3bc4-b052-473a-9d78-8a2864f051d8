/**
 * GikiCard Component
 *
 * Professional card component with brand gradients and glass morphism effects
 * Designed to replace generic cards throughout the application
 */
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { LucideIcon } from 'lucide-react';

interface GikiCardProps {
  children?: React.ReactNode;
  className?: string;

  // Card variants
  variant?:
    | 'default'
    | 'branded'
    | 'glass'
    | 'elevated'
    | 'success'
    | 'warning'
    | 'error';

  // Header props
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  iconColor?: string;
  action?: React.ReactNode;

  // Visual props
  gradient?: 'brand' | 'green' | 'blue' | 'purple' | 'pink' | 'none';
  hover?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';

  // Interactive props
  onClick?: () => void;
  interactive?: boolean;
}

function GikiCard({
  children,
  className,
  variant = 'default',
  title,
  subtitle,
  icon: Icon,
  iconColor,
  action,
  gradient = 'none',
  hover = true,
  padding = 'md',
  onClick,
  interactive = false,
  ...props
}: GikiCardProps) {
  // Base styles
  const baseStyles = cn(
    'relative overflow-hidden rounded-xl transition-all duration-300',
    interactive && 'cursor-pointer',
    hover && 'hover:shadow-xl hover:-translate-y-0.5',
  );

  // Variant styles
  const variantStyles = {
    default: 'bg-card border border-border shadow-md',
    branded: 'bg-card border border-transparent shadow-lg',
    glass: 'bg-card/70 backdrop-blur-md border border-border/50 shadow-lg',
    elevated: 'bg-card border border-border shadow-xl',
    success: 'bg-success/5 border border-success/20 shadow-md',
    warning: 'bg-warning/5 border border-warning/20 shadow-md',
    error: 'bg-destructive/5 border border-destructive/20 shadow-md',
  };

  // Padding styles
  const paddingStyles = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  // Gradient overlay styles
  const gradientStyles = {
    none: '',
    brand:
      'before:absolute before:inset-0 before:bg-gradient-to-br before:from-primary/5 before:via-transparent before:to-accent/5 before:pointer-events-none',
    green:
      'before:absolute before:inset-0 before:bg-gradient-to-br before:from-[hsl(var(--giki-brand-green-dark))]/5 before:to-transparent before:pointer-events-none',
    blue: 'before:absolute before:inset-0 before:bg-gradient-to-br before:from-[hsl(var(--giki-brand-blue-dark))]/5 before:to-transparent before:pointer-events-none',
    purple:
      'before:absolute before:inset-0 before:bg-gradient-to-br before:from-[hsl(var(--giki-brand-purple-dark))]/5 before:to-transparent before:pointer-events-none',
    pink: 'before:absolute before:inset-0 before:bg-gradient-to-br before:from-[hsl(var(--giki-brand-pink-dark))]/5 before:to-transparent before:pointer-events-none',
  };

  // Gradient border for branded variant
  const hasBrandedBorder = variant === 'branded';

  return (
    <div
      className={cn(
        baseStyles,
        variantStyles[variant],
        paddingStyles[padding],
        gradientStyles[gradient],
        className,
      )}
      onClick={onClick}
      {...props}
    >
      {/* Branded gradient border */}
      {hasBrandedBorder && (
        <div
          className="absolute inset-0 rounded-xl p-[1px] pointer-events-none"
          style={{
            background:
              'linear-gradient(135deg, hsl(var(--giki-brand-green-dark)) 0%, hsl(var(--giki-brand-blue-dark)) 33%, hsl(var(--giki-brand-purple-dark)) 66%, hsl(var(--giki-brand-pink-dark)) 100%)',
            maskImage:
              'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
            maskComposite: 'exclude',
            WebkitMaskImage:
              'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
            WebkitMaskComposite: 'xor',
          }}
        />
      )}

      {/* Content wrapper */}
      <div className="relative z-10">
        {/* Header section */}
        {(title || subtitle || Icon || action) && (
          <div className="flex flex-wrap items-start justify-between gap-4 mb-4">
            <div className="flex flex-wrap items-start gap-3 flex-1">
              {Icon && (
                <div
                  className={cn(
                    'p-2.5 rounded-lg bg-gradient-to-br shadow-sm',
                    iconColor || 'from-primary/20 to-primary/10 text-primary',
                  )}
                >
                  <Icon className="w-5 h-5" strokeWidth={2} />
                </div>
              )}

              <div className="flex flex-wrap-1 min-w-0">
                {title && (
                  <h3 className="text-card-foreground-title leading-tight">
                    {title}
                  </h3>
                )}
                {subtitle && (
                  <p className="text-card-foreground-subtitle mt-1">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>

            {action && <div className="flex flex-wrap-shrink-0">{action}</div>}
          </div>
        )}

        {/* Main content */}
        {children}
      </div>

      {/* Hover effect overlay */}
      {hover && interactive && (
        <div className="absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      )}
    </div>
  );
}

// Specialized card components
function GikiCardHeader({
  className,
  children,
  ...props
}: React.ComponentProps<'div'>) {
  return (
    <div
      className={cn('pb-4 mb-4 border-b border-border/50', className)}
      {...props}
    >
      {children}
    </div>
  );
}

function GikiCardContent({
  className,
  children,
  ...props
}: React.ComponentProps<'div'>) {
  return (
    <div className={cn('space-y-4', className)} {...props}>
      {children}
    </div>
  );
}

function GikiCardFooter({
  className,
  children,
  ...props
}: React.ComponentProps<'div'>) {
  return (
    <div
      className={cn(
        'pt-4 mt-4 border-t border-border/50 flex items-center justify-between',
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Metric card variant
interface GikiMetricCardProps extends Omit<GikiCardProps, 'children'> {
  value: string | number;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  chart?: React.ReactNode;
}

function GikiMetricCard({
  title: _title,
  value,
  trend,
  chart,
  ...props
}: GikiMetricCardProps) {
  return (
    <GikiCard {...props}>
      <div className="space-y-3">
        <div className="flex flex-wrap items-baseline justify-between">
          <div className="text-card-foreground-metric">{value}</div>
          {trend && (
            <div
              className={cn(
                'text-sm font-medium flex items-center gap-1',
                trend.isPositive ? 'text-success' : 'text-destructive',
              )}
            >
              <span>{trend.isPositive ? '↑' : '↓'}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
        {chart && <div className="h-16">{chart}</div>}
      </div>
    </GikiCard>
  );
}

export {
  GikiCard,
  GikiCardHeader,
  GikiCardContent,
  GikiCardFooter,
  GikiMetricCard,
  type GikiCardProps,
  type GikiMetricCardProps,
};
