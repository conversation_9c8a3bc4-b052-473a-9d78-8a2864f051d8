/**
 * GikiTable Component
 *
 * Professional table component with Excel-inspired styling for financial data
 * Features sorting, filtering, selection, and branded design elements
 */
import React, { useState, useMemo } from 'react';
import { cn } from '@/shared/utils/utils';
import { ChevronUp, ChevronDown, Filter, Download, Eye } from 'lucide-react';
import { Button } from './button';
import { Input } from './input';
import { GikiCard } from './giki-card';

interface GikiTableColumn<T = unknown> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  type?: 'text' | 'number' | 'currency' | 'date' | 'status';
  hidden?: boolean;
}

interface GikiTableProps<T = unknown> {
  data: T[];
  columns: GikiTableColumn<T>[];
  className?: string;
  variant?: 'default' | 'bordered' | 'striped' | 'financial';
  size?: 'compact' | 'default' | 'comfortable';
  loading?: boolean;
  selectable?: boolean;
  onSelectionChange?: (selectedRows: T[], selectedRowKeys: string[]) => void;
  rowKey?: string | ((record: T) => string);
  onRowClick?: (record: T, index: number) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  exportable?: boolean;
  onExport?: () => void;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

type SortOrder = 'asc' | 'desc' | null;

interface SortState {
  column: string | null;
  order: SortOrder;
}

function GikiTable<T extends Record<string, unknown>>({
  data,
  columns,
  className,
  variant = 'default',
  size = 'default',
  loading = false,
  selectable = false,
  onSelectionChange,
  rowKey = 'id',
  onRowClick,
  pagination,
  exportable = false,
  onExport,
  title,
  subtitle,
  actions,
}: GikiTableProps<T>) {
  const [sortState, setSortState] = useState<SortState>({
    column: null,
    order: null,
  });
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());

  // Get row key function
  const getRowKey =
    typeof rowKey === 'function'
      ? rowKey
      : (record: T) => record[rowKey as keyof T] as string | number;

  // Filtered and sorted data
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply filters
    Object.entries(filters).forEach(([columnKey, filterValue]) => {
      if (filterValue) {
        const column = columns.find((col) => col.key === columnKey);
        if (column?.dataIndex) {
          result = result.filter((item) => {
            const value = item[column.dataIndex];
            let stringValue = '';
            // Type guard to ensure value is stringifiable
            if (
              value !== null &&
              value !== undefined &&
              typeof value !== 'object' &&
              typeof value !== 'symbol' &&
              typeof value !== 'function'
            ) {
              // At this point, value is string | number | boolean | bigint
              stringValue = String(value as string | number | boolean | bigint);
            }
            return stringValue
              .toLowerCase()
              .includes(filterValue.toLowerCase());
          });
        }
      }
    });

    // Apply sorting
    if (sortState.column && sortState.order) {
      const column = columns.find((col) => col.key === sortState.column);
      if (column?.dataIndex) {
        result.sort((a, b) => {
          const aVal = a[column.dataIndex];
          const bVal = b[column.dataIndex];

          let comparison = 0;
          if (column.type === 'number' || column.type === 'currency') {
            comparison = Number(aVal) - Number(bVal);
          } else if (column.type === 'date') {
            comparison = new Date(aVal).getTime() - new Date(bVal).getTime();
          } else {
            let aStr = '';
            let bStr = '';
            // Type guards to ensure values are stringifiable
            if (
              aVal !== null &&
              aVal !== undefined &&
              typeof aVal !== 'object' &&
              typeof aVal !== 'symbol' &&
              typeof aVal !== 'function'
            ) {
              aStr = String(aVal as string | number | boolean | bigint);
            }
            if (
              bVal !== null &&
              bVal !== undefined &&
              typeof bVal !== 'object' &&
              typeof bVal !== 'symbol' &&
              typeof bVal !== 'function'
            ) {
              bStr = String(bVal as string | number | boolean | bigint);
            }
            comparison = aStr.localeCompare(bStr);
          }

          return sortState.order === 'asc' ? comparison : -comparison;
        });
      }
    }

    return result;
  }, [data, filters, sortState, columns]);

  // Visible columns
  const visibleColumns = useMemo(
    () => columns.filter((col) => !hiddenColumns.has(col.key)),
    [columns, hiddenColumns],
  );

  // Handle sorting
  const handleSort = (columnKey: string) => {
    const column = columns.find((col) => col.key === columnKey);
    if (!column?.sortable) return;

    setSortState((prev) => {
      if (prev.column === columnKey) {
        if (prev.order === 'asc') return { column: columnKey, order: 'desc' };
        if (prev.order === 'desc') return { column: null, order: null };
      }
      return { column: columnKey, order: 'asc' };
    });
  };

  // Handle filter change
  const handleFilterChange = (columnKey: string, value: string) => {
    setFilters((prev) => ({ ...prev, [columnKey]: value }));
  };

  // Handle selection
  const handleSelectRow = (record: T, checked: boolean) => {
    const key = getRowKey(record);
    const newSelectedKeys = checked
      ? [...selectedRowKeys, key]
      : selectedRowKeys.filter((k) => k !== key);

    setSelectedRowKeys(newSelectedKeys);

    if (onSelectionChange) {
      const selectedRows = processedData.filter((item) =>
        newSelectedKeys.includes(getRowKey(item)),
      );
      onSelectionChange(selectedRows, newSelectedKeys);
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    const newSelectedKeys = checked ? processedData.map(getRowKey) : [];
    setSelectedRowKeys(newSelectedKeys);

    if (onSelectionChange) {
      const selectedRows = checked ? processedData : [];
      onSelectionChange(selectedRows, newSelectedKeys);
    }
  };

  // Toggle column visibility
  const toggleColumnVisibility = (columnKey: string) => {
    setHiddenColumns((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(columnKey)) {
        newSet.delete(columnKey);
      } else {
        newSet.add(columnKey);
      }
      return newSet;
    });
  };

  // Style variants
  const tableVariants = {
    default: 'border border-border',
    bordered: 'border-2 border-border',
    striped: 'border border-border',
    financial:
      'border-2 border-primary/20 bg-gradient-to-br from-primary/2 to-accent/2',
  };

  const sizeVariants = {
    compact: 'text-xs',
    default: 'text-sm',
    comfortable: 'text-base',
  };

  const cellPadding = {
    compact: 'px-2 py-1.5',
    default: 'px-4 py-3',
    comfortable: 'px-6 py-4',
  };

  // Format cell value based on column type
  const formatCellValue = (value: unknown, column: GikiTableColumn<T>) => {
    if (column.type === 'currency' && typeof value === 'number') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(value);
    }
    if (column.type === 'number' && typeof value === 'number') {
      return new Intl.NumberFormat('en-US').format(value);
    }
    if (column.type === 'date' && value) {
      return new Date(value).toLocaleDateString();
    }
    return value;
  };

  const renderCellContent = (
    column: GikiTableColumn<T>,
    record: T,
    index: number,
  ) => {
    if (column.render) {
      return column.render(record[column.dataIndex], record, index);
    }

    const value = column.dataIndex ? record[column.dataIndex] : null;
    return formatCellValue(value, column);
  };

  return (
    <GikiCard
      variant={variant === 'financial' ? 'branded' : 'default'}
      padding="none"
      className={cn('overflow-hidden', className)}
    >
      {/* Table Header */}
      {(title || subtitle || actions || exportable) && (
        <div className="p-6 pb-4 border-b border-border/50">
          <div className="flex flex-wrap items-start justify-between gap-4">
            <div>
              {title && (
                <h3 className="text-card-foreground-title text-foreground mb-1">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p id="table-description" className="truncatebody-small">
                  {subtitle}
                </p>
              )}
            </div>

            <div className="flex flex-wrap items-center gap-2">
              {/* Column visibility toggle */}
              <div className="relative group">
                <Button className="max-w-full" variant="ghost" size="sm">
                  <Eye className="w-4 h-4" />
                </Button>
                <div className="absolute right-0 top-full mt-1 bg-popover border border-border rounded-lg shadow-lg p-2 min-w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-modal">
                  <div className="space-y-1">
                    {columns.map((column) => (
                      <label
                        key={column.key}
                        className="flex flex-wrap items-center gap-2 truncate text-caption hover:bg-accent rounded px-2 py-1 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={!hiddenColumns.has(column.key)}
                          onChange={() => toggleColumnVisibility(column.key)}
                          className="rounded border-border"
                        />
                        <span>{column.title}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {exportable && (
                <Button
                  className="max-w-full gap-1"
                  variant="outline"
                  size="sm"
                  onClick={onExport}
                >
                  <Download className="w-4 h-4" />
                  Export
                </Button>
              )}

              {actions}
            </div>
          </div>

          {/* Selection summary */}
          {selectable && selectedRowKeys.length > 0 && (
            <div className="mt-4 p-3 bg-primary/5 border border-primary/20 rounded-lg">
              <p className="truncatelabel text-primary">
                {selectedRowKeys.length} row
                {selectedRowKeys.length !== 1 ? 's' : ''} selected
              </p>
            </div>
          )}
        </div>
      )}

      {/* Table Container - single scroll wrapper */}
      <div className="overflow-x-auto">
        <table
          className={cn(
            'w-full border-collapse',
            tableVariants[variant],
            sizeVariants[size],
          )}
          role="table"
          aria-label={title || 'Data table'}
          aria-describedby={subtitle ? 'table-description' : undefined}
        >
          {/* Table Header */}
          <thead className="bg-muted/30 sticky top-0 z-10">
            <tr>
              {selectable && (
                <th
                  className={cn(
                    'border-b border-border text-left font-semibold text-muted-foreground',
                    cellPadding[size],
                  )}
                >
                  <input
                    type="checkbox"
                    checked={
                      selectedRowKeys.length === processedData.length &&
                      processedData.length > 0
                    }
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-border"
                  />
                </th>
              )}

              {visibleColumns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    'border-b border-border text-left font-semibold text-muted-foreground hover:bg-muted/50 transition-colors',
                    cellPadding[size],
                    column.sortable && 'cursor-pointer select-none',
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right',
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex flex-wrap items-center gap-2">
                    <span>{column.title}</span>

                    {column.sortable && (
                      <div className="flex flex-wrap flex-col">
                        <ChevronUp
                          className={cn(
                            'w-3 h-3',
                            sortState.column === column.key &&
                              sortState.order === 'asc'
                              ? 'text-primary'
                              : 'text-muted-foreground/50',
                          )}
                        />
                        <ChevronDown
                          className={cn(
                            'w-3 h-3 -mt-1',
                            sortState.column === column.key &&
                              sortState.order === 'desc'
                              ? 'text-primary'
                              : 'text-muted-foreground/50',
                          )}
                        />
                      </div>
                    )}

                    {column.filterable && (
                      <Filter className="w-3 h-3 text-muted-foreground/50" />
                    )}
                  </div>

                  {/* Column filter */}
                  {column.filterable && (
                    <div className="mt-2">
                      <Input
                        size="sm"
                        placeholder={`Filter ${column.title.toLowerCase()}...`}
                        value={filters[column.key] || ''}
                        onChange={(e) =>
                          handleFilterChange(column.key, e.target.value)
                        }
                        className="h-7 truncate text-caption"
                      />
                    </div>
                  )}
                </th>
              ))}
            </tr>
          </thead>

          {/* Table Body */}
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={(selectable ? 1 : 0) + visibleColumns.length}
                  className={cn('text-center py-12', cellPadding[size])}
                >
                  <div className="flex flex-wrap items-center justify-center gap-3">
                    <div className="w-4 h-4 border-2 border-primary/30 border-t-primary rounded-full animate-spin" />
                    <span className="text-muted-foreground">
                      Loading data...
                    </span>
                  </div>
                </td>
              </tr>
            ) : processedData.length === 0 ? (
              <tr>
                <td
                  colSpan={(selectable ? 1 : 0) + visibleColumns.length}
                  className={cn(
                    'text-center py-12 text-muted-foreground',
                    cellPadding[size],
                  )}
                >
                  No data available
                </td>
              </tr>
            ) : (
              processedData.map((record, index) => {
                const rowKey = getRowKey(record);
                const isSelected = selectedRowKeys.includes(rowKey);

                return (
                  <tr
                    key={rowKey}
                    className={cn(
                      'border-b border-border/50 hover:bg-muted/30 transition-colors',
                      variant === 'striped' && index % 2 === 1 && 'bg-muted/20',
                      isSelected && 'bg-primary/5 border-primary/20',
                      onRowClick && 'cursor-pointer',
                      'group',
                    )}
                    onClick={() => onRowClick?.(record, index)}
                  >
                    {selectable && (
                      <td
                        className={cn(
                          'border-b border-border/50',
                          cellPadding[size],
                        )}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) =>
                            handleSelectRow(record, e.target.checked)
                          }
                          onClick={(e) => e.stopPropagation()}
                          className="rounded border-border"
                        />
                      </td>
                    )}

                    {visibleColumns.map((column) => (
                      <td
                        key={column.key}
                        className={cn(
                          'border-b border-border/50 transition-colors',
                          cellPadding[size],
                          column.align === 'center' && 'text-center',
                          column.align === 'right' && 'text-right',
                          column.type === 'currency' &&
                            'font-mono tabular-nums',
                          column.type === 'number' && 'font-mono tabular-nums',
                        )}
                      >
                        {renderCellContent(column, record, index)}
                      </td>
                    ))}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="p-4 border-t border-border/50 flex flex-wrap items-center justify-between">
          <div className="truncatebody-small">
            Showing {(pagination.current - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(
              pagination.current * pagination.pageSize,
              pagination.total,
            )}{' '}
            of {pagination.total} entries
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              disabled={pagination.current <= 1}
              onClick={() =>
                pagination.onChange(pagination.current - 1, pagination.pageSize)
              }
            >
              Previous
            </Button>

            <span className="truncatelabel px-3 py-1 bg-muted rounded">
              {pagination.current}
            </span>

            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              disabled={
                pagination.current >=
                Math.ceil(pagination.total / pagination.pageSize)
              }
              onClick={() =>
                pagination.onChange(pagination.current + 1, pagination.pageSize)
              }
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </GikiCard>
  );
}

export { GikiTable, type GikiTableProps, type GikiTableColumn };
