/**
 * ErrorDisplay Component Tests
 * Critical error display component tests for production reliability
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import ErrorDisplay from './error-display';

// Mock the error handling utility
vi.mock('@/shared/utils/errorHandling', () => ({
  extractErrorMessage: vi.fn((error, defaultMessage) => {
    if (typeof error === 'string') return error;
    if (error instanceof Error) return error.message;
    return defaultMessage;
  }),
}));

describe('ErrorDisplay - Error Information Display Component', () => {
  const mockOnRetry = vi.fn();
  const mockOnGoHome = vi.fn();
  const mockOnGoBack = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Error Display', () => {
    it('renders with default props', () => {
      render(<ErrorDisplay error="Test error message" />);

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('Test error message')).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toBeInTheDocument();
    });

    it('displays custom title', () => {
      render(<ErrorDisplay error="Test error" title="Custom Error Title" />);

      expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
      expect(
        screen.queryByText('Something went wrong'),
      ).not.toBeInTheDocument();
    });

    it('handles Error object input', () => {
      const testError = new Error('Error object message');

      render(<ErrorDisplay error={testError} />);

      expect(screen.getByText('Error object message')).toBeInTheDocument();
    });

    it('handles string error input', () => {
      render(<ErrorDisplay error="String error message" />);

      expect(screen.getByText('String error message')).toBeInTheDocument();
    });

    it('handles unknown error types', () => {
      const unknownError = { customProp: 'custom value' };

      render(<ErrorDisplay error={unknownError} />);

      expect(
        screen.getByText('An unexpected error occurred'),
      ).toBeInTheDocument();
    });
  });

  describe('Error Type Detection', () => {
    it('detects network errors', () => {
      render(<ErrorDisplay error="Network connection failed" />);

      expect(screen.getByText('Network connection failed')).toBeInTheDocument();
      expect(
        screen.getByText('Check your internet connection'),
      ).toBeInTheDocument();
      expect(screen.getByText('Try refreshing the page')).toBeInTheDocument();
    });

    it('detects server errors', () => {
      render(<ErrorDisplay error="Server error 500" />);

      expect(screen.getByText('Server error 500')).toBeInTheDocument();
      expect(
        screen.getByText('Our servers are experiencing issues'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Please try again in a few minutes'),
      ).toBeInTheDocument();
    });

    it('detects authentication errors', () => {
      render(<ErrorDisplay error="Unauthorized access 401" />);

      expect(screen.getByText('Unauthorized access 401')).toBeInTheDocument();
      expect(screen.getByText('Please log in again')).toBeInTheDocument();
      expect(
        screen.getByText('Your session may have expired'),
      ).toBeInTheDocument();
    });

    it('detects timeout errors', () => {
      render(<ErrorDisplay error="Request timeout" />);

      expect(screen.getByText('Request timeout')).toBeInTheDocument();
      expect(
        screen.getByText('The request took too long to complete'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Try again with a smaller file or dataset'),
      ).toBeInTheDocument();
    });

    it('handles generic errors', () => {
      render(<ErrorDisplay error="Generic error message" />);

      expect(screen.getByText('Generic error message')).toBeInTheDocument();
      expect(screen.getByText('Try refreshing the page')).toBeInTheDocument();
      expect(screen.getByText('Clear your browser cache')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('shows retry button by default', () => {
      render(<ErrorDisplay error="Test error" onRetry={mockOnRetry} />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();
    });

    it('hides retry button when showRetry is false', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showRetry={false}
          onRetry={mockOnRetry}
        />,
      );

      expect(
        screen.queryByRole('button', { name: /try again/i }),
      ).not.toBeInTheDocument();
    });

    it('calls onRetry when retry button is clicked', async () => {
      const user = userEvent.setup();

      render(<ErrorDisplay error="Test error" onRetry={mockOnRetry} />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('shows go home button when enabled', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showGoHome={true}
          onGoHome={mockOnGoHome}
        />,
      );

      const homeButton = screen.getByRole('button', {
        name: /go to dashboard/i,
      });
      expect(homeButton).toBeInTheDocument();
    });

    it('calls onGoHome when home button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <ErrorDisplay
          error="Test error"
          showGoHome={true}
          onGoHome={mockOnGoHome}
        />,
      );

      const homeButton = screen.getByRole('button', {
        name: /go to dashboard/i,
      });
      await user.click(homeButton);

      expect(mockOnGoHome).toHaveBeenCalledTimes(1);
    });

    it('shows go back button when enabled', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showGoBack={true}
          onGoBack={mockOnGoBack}
        />,
      );

      const backButton = screen.getByRole('button', { name: /go back/i });
      expect(backButton).toBeInTheDocument();
    });

    it('calls onGoBack when back button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <ErrorDisplay
          error="Test error"
          showGoBack={true}
          onGoBack={mockOnGoBack}
        />,
      );

      const backButton = screen.getByRole('button', { name: /go back/i });
      await user.click(backButton);

      expect(mockOnGoBack).toHaveBeenCalledTimes(1);
    });

    it('shows multiple buttons when enabled', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showRetry={true}
          showGoHome={true}
          showGoBack={true}
          onRetry={mockOnRetry}
          onGoHome={mockOnGoHome}
          onGoBack={mockOnGoBack}
        />,
      );

      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go to dashboard/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });
  });

  describe('Error-Specific Suggestions', () => {
    it('provides network-specific suggestions', () => {
      render(<ErrorDisplay error="Network connection failed" />);

      expect(screen.getByText('What you can try:')).toBeInTheDocument();
      expect(
        screen.getByText('Check your internet connection'),
      ).toBeInTheDocument();
      expect(screen.getByText('Try refreshing the page')).toBeInTheDocument();
      expect(
        screen.getByText('Contact support if the issue persists'),
      ).toBeInTheDocument();
    });

    it('provides server-specific suggestions', () => {
      render(<ErrorDisplay error="Internal server error" />);

      expect(
        screen.getByText('Our servers are experiencing issues'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Please try again in a few minutes'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Contact support if the problem continues'),
      ).toBeInTheDocument();
    });

    it('provides auth-specific suggestions', () => {
      render(<ErrorDisplay error="User unauthorized" />);

      expect(screen.getByText('Please log in again')).toBeInTheDocument();
      expect(
        screen.getByText('Your session may have expired'),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          'Contact support if you continue having access issues',
        ),
      ).toBeInTheDocument();
    });

    it('provides timeout-specific suggestions', () => {
      render(<ErrorDisplay error="Operation timeout" />);

      expect(
        screen.getByText('The request took too long to complete'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Try again with a smaller file or dataset'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Check your internet connection'),
      ).toBeInTheDocument();
    });

    it('provides generic suggestions for unknown errors', () => {
      render(<ErrorDisplay error="Unknown error occurred" />);

      expect(screen.getByText('Try refreshing the page')).toBeInTheDocument();
      expect(screen.getByText('Clear your browser cache')).toBeInTheDocument();
      expect(
        screen.getByText('Contact support if the issue persists'),
      ).toBeInTheDocument();
    });
  });

  describe('Visual Styling and Icons', () => {
    it('applies correct styling for network errors', () => {
      render(<ErrorDisplay error="Network connection failed" />);

      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('bg-warning/10', 'border-warning/20');
    });

    it('applies correct styling for server errors', () => {
      render(<ErrorDisplay error="Server error occurred" />);

      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('bg-destructive/10', 'border-destructive/20');
    });

    it('applies custom className', () => {
      render(
        <ErrorDisplay error="Test error" className="custom-error-class" />,
      );

      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('custom-error-class');
    });

    it('includes slide-in animation', () => {
      render(<ErrorDisplay error="Test error" />);

      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('animate-slide-in');
    });
  });

  describe('Accessibility', () => {
    it('uses proper ARIA roles', () => {
      render(<ErrorDisplay error="Test error" onRetry={mockOnRetry} />);

      expect(screen.getByRole('alert')).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toBeInTheDocument();
    });

    it('provides accessible button labels', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showRetry={true}
          showGoHome={true}
          showGoBack={true}
          onRetry={mockOnRetry}
          onGoHome={mockOnGoHome}
          onGoBack={mockOnGoBack}
        />,
      );

      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toHaveAccessibleName();
      expect(
        screen.getByRole('button', { name: /go to dashboard/i }),
      ).toHaveAccessibleName();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toHaveAccessibleName();
    });

    it('maintains proper heading hierarchy', () => {
      render(<ErrorDisplay error="Test error" title="Error Occurred" />);

      const title = screen.getByText('Error Occurred');
      expect(title).toHaveClass('text-xl', 'font-semibold');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <ErrorDisplay
          error="Test error"
          showRetry={true}
          showGoBack={true}
          onRetry={mockOnRetry}
          onGoBack={mockOnGoBack}
        />,
      );

      await user.tab();
      expect(screen.getByRole('button', { name: /try again/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /go back/i })).toHaveFocus();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles null error input', () => {
      render(<ErrorDisplay error={null} />);

      expect(
        screen.getByText('An unexpected error occurred'),
      ).toBeInTheDocument();
    });

    it('handles undefined error input', () => {
      render(<ErrorDisplay error={undefined} />);

      expect(
        screen.getByText('An unexpected error occurred'),
      ).toBeInTheDocument();
    });

    it('handles empty string error', () => {
      render(<ErrorDisplay error="" />);

      expect(
        screen.getByText('An unexpected error occurred'),
      ).toBeInTheDocument();
    });

    it('handles very long error messages', () => {
      const longError =
        'This is a very long error message that should be displayed properly without breaking the layout or causing overflow issues in the user interface component when shown to users in production environments.';

      render(<ErrorDisplay error={longError} />);

      expect(screen.getByText(longError)).toBeInTheDocument();
    });

    it('handles error messages with special characters', () => {
      const specialCharError = 'Error with special chars: <>&"\'';

      render(<ErrorDisplay error={specialCharError} />);

      expect(screen.getByText(specialCharError)).toBeInTheDocument();
    });

    it('handles rapid button clicks', async () => {
      const user = userEvent.setup();

      render(<ErrorDisplay error="Test error" onRetry={mockOnRetry} />);

      const retryButton = screen.getByRole('button', { name: /try again/i });

      // Rapid clicks
      await user.click(retryButton);
      await user.click(retryButton);
      await user.click(retryButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(3);
    });

    it('handles missing callback functions gracefully', () => {
      render(
        <ErrorDisplay
          error="Test error"
          showRetry={true}
          showGoHome={true}
          showGoBack={true}
          // No callback functions provided
        />,
      );

      // Buttons should not be shown if no callbacks provided
      expect(
        screen.queryByRole('button', { name: /try again/i }),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /go to dashboard/i }),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /go back/i }),
      ).not.toBeInTheDocument();
    });
  });

  describe('Performance and Memory', () => {
    it('renders efficiently with complex error objects', () => {
      const complexError = {
        message: 'Complex error',
        stack: 'Very long stack trace...',
        code: 'ERR_COMPLEX',
        details: {
          nested: {
            deeply: {
              nested: 'value',
            },
          },
        },
      };

      const startTime = performance.now();
      render(<ErrorDisplay error={complexError} />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // Should render quickly
      expect(
        screen.getByText('An unexpected error occurred'),
      ).toBeInTheDocument();
    });

    it('handles frequent prop changes efficiently', () => {
      const { rerender } = render(<ErrorDisplay error="Initial error" />);

      // Multiple rapid rerenders
      for (let i = 0; i < 10; i++) {
        rerender(<ErrorDisplay error={`Error ${i}`} />);
      }

      expect(screen.getByText('Error 9')).toBeInTheDocument();
    });

    it('cleans up properly on unmount', () => {
      const { unmount } = render(
        <ErrorDisplay error="Test error" onRetry={mockOnRetry} />,
      );

      expect(screen.getByText('Test error')).toBeInTheDocument();

      // Should unmount without issues
      expect(() => unmount()).not.toThrow();
    });
  });
});
