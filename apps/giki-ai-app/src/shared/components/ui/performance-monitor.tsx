/**
 * Performance Monitor Component
 *
 * Real-time performance monitoring and user feedback system for API operations.
 * Provides transparency during slow operations and performance insights.
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import {
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wifi,
  WifiOff,
  RefreshCw,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface PerformanceMetric {
  id: string;
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'pending' | 'completed' | 'failed' | 'timeout';
  target: number; // Target response time in ms
  endpoint?: string;
}

interface PerformanceStats {
  avgResponseTime: number;
  successRate: number;
  slowOperations: number;
  totalOperations: number;
  currentLoad: number;
}

interface PerformanceMonitorProps {
  className?: string;
  showDetailedStats?: boolean;
  autoHide?: boolean;
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left';
}

interface PerformanceToastProps {
  metric: PerformanceMetric;
  onDismiss: () => void;
}

const PerformanceToast: React.FC<PerformanceToastProps> = ({
  metric,
  onDismiss,
}) => {
  const getStatusColor = (status: string, duration: number, target: number) => {
    if (status === 'failed') return 'text-red-600 bg-red-50 border-red-200';
    if (status === 'timeout')
      return 'text-orange-600 bg-orange-50 border-orange-200';
    if (duration > target * 2) return 'text-red-600 bg-red-50 border-red-200';
    if (duration > target)
      return 'text-orange-600 bg-orange-50 border-orange-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  const getStatusIcon = (status: string, duration: number, target: number) => {
    if (status === 'failed') return <XCircle className="h-4 w-4" />;
    if (status === 'timeout') return <AlertTriangle className="h-4 w-4" />;
    if (duration > target) return <AlertTriangle className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div
      className={cn(
        'performance-toast p-3 rounded-lg border text-sm',
        getStatusColor(metric.status, metric.duration || 0, metric.target),
      )}
    >
      <div className="flex flex-wrap items-center justify-between">
        <div className="flex flex-wrap items-center gap-2">
          {getStatusIcon(metric.status, metric.duration || 0, metric.target)}
          <span className="font-medium">{metric.operation}</span>
        </div>
        <Button
          className="max-w-full h-6 w-6 p-0"
          variant="ghost"
          size="sm"
          onClick={() => void onDismiss()}
        >
          ×
        </Button>
      </div>

      <div className="mt-1 truncate text-caption opacity-80">
        {metric.status === 'pending' ? (
          <div className="flex flex-wrap items-center gap-1">
            <RefreshCw className="h-3 w-3 animate-spin" />
            Processing...
          </div>
        ) : (
          <div className="flex flex-wrap items-center justify-between">
            <span>Duration: {formatDuration(metric.duration || 0)}</span>
            <span>Target: {formatDuration(metric.target)}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className,
  showDetailedStats = false,
  autoHide = true,
  position = 'bottom-right',
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [stats, setStats] = useState<PerformanceStats>({
    avgResponseTime: 0,
    successRate: 100,
    slowOperations: 0,
    totalOperations: 0,
    currentLoad: 0,
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const [toasts, setToasts] = useState<PerformanceMetric[]>([]);

  // Simulate performance monitoring (in real app, this would connect to actual metrics)
  useEffect(() => {
    const interval = setInterval(() => {
      // Update current load simulation
      setStats((prev) => ({
        ...prev,
        currentLoad: Math.max(0, prev.currentLoad + (Math.random() - 0.5) * 20),
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Performance tracking functions
  const _trackOperation = useCallback(
    (operation: string, target: number = 200, endpoint?: string) => {
      const metric: PerformanceMetric = {
        id: Date.now().toString(),
        operation,
        startTime: Date.now(),
        status: 'pending',
        target,
        endpoint,
      };

      setMetrics((prev) => [...prev.slice(-19), metric]); // Keep last 20 metrics
      return metric.id;
    },
    [],
  );

  const _completeOperation = useCallback(
    (id: string, success: boolean = true) => {
      setMetrics((prev) =>
        prev.map((metric) => {
          if (metric.id === id) {
            const endTime = Date.now();
            const duration = endTime - metric.startTime;
            const updated = {
              ...metric,
              endTime,
              duration,
              status: success ? 'completed' : 'failed',
            } as PerformanceMetric;

            // Add to toasts if slow or failed
            if (!success || duration > metric.target) {
              setToasts((prev) => [...prev.slice(-4), updated]); // Keep last 5 toasts
            }

            return updated;
          }
          return metric;
        }),
      );

      // Update stats
      setStats((prev) => {
        const completedMetrics = metrics.filter(
          (m) => m.status === 'completed',
        );
        const avgResponseTime =
          completedMetrics.length > 0
            ? completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) /
              completedMetrics.length
            : 0;

        const successfulOps = completedMetrics.filter(
          (m) => m.duration && m.duration <= m.target * 1.5,
        ).length;
        const successRate =
          completedMetrics.length > 0
            ? (successfulOps / completedMetrics.length) * 100
            : 100;

        const slowOps = completedMetrics.filter(
          (m) => m.duration && m.duration > m.target,
        ).length;

        return {
          avgResponseTime,
          successRate,
          slowOperations: slowOps,
          totalOperations: completedMetrics.length,
          currentLoad: prev.currentLoad,
        };
      });
    },
    [metrics],
  );

  const dismissToast = (id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  };

  const getPositionClasses = (pos: string) => {
    switch (pos) {
      case 'top-right':
        return 'fixed top-4 right-4 z-modal';
      case 'bottom-left':
        return 'fixed bottom-4 left-4 z-modal';
      case 'top-left':
        return 'fixed top-4 left-4 z-modal';
      default:
        return 'fixed bottom-4 right-4 z-modal';
    }
  };

  const getCurrentStatus = () => {
    if (stats.avgResponseTime > 2000)
      return { status: 'critical', color: 'text-red-600', icon: WifiOff };
    if (stats.avgResponseTime > 1000)
      return {
        status: 'warning',
        color: 'text-orange-600',
        icon: AlertTriangle,
      };
    if (stats.avgResponseTime > 500)
      return { status: 'slow', color: 'text-yellow-600', icon: Clock };
    return { status: 'good', color: 'text-green-600', icon: Wifi };
  };

  const currentStatus = getCurrentStatus();
  const StatusIcon = currentStatus.icon;

  // Auto-hide when performance is good and no recent issues
  if (
    autoHide &&
    currentStatus.status === 'good' &&
    toasts.length === 0 &&
    !isExpanded
  ) {
    return null;
  }

  return (
    <>
      {/* Performance Monitor Widget */}
      <div className={cn(getPositionClasses(position), className)}>
        <Card className="w-80 shadow-lg">
          <CardHeader className="pb-2">
            <CardTitle className="flex flex-wrap items-center justify-between text-sm">
              <div className="flex flex-wrap items-center gap-2">
                <Activity className="h-4 w-4" />
                Performance Monitor
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <StatusIcon className={cn('h-4 w-4', currentStatus.color)} />
                <Button
                  className="max-w-full h-6 w-6 p-0"
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? '−' : '+'}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-3 overflow-hidden">
            {/* Current Status */}
            <div className="flex flex-wrap items-center justify-between">
              <span className="truncate text-caption text-muted-foreground">
                Avg Response
              </span>
              <div className="flex flex-wrap items-center gap-2">
                <Badge
                  variant={
                    currentStatus.status === 'good' ? 'default' : 'destructive'
                  }
                  className="max-w-[150px] truncate"
                >
                  {stats.avgResponseTime > 0
                    ? `${Math.round(stats.avgResponseTime)}ms`
                    : '—'}
                </Badge>
                {stats.avgResponseTime > 200 && (
                  <span className="truncate text-caption text-muted-foreground">
                    ({Math.round(stats.avgResponseTime / 200)}x target)
                  </span>
                )}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-2 truncate text-caption">
              <div className="flex flex-wrap justify-between">
                <span>Success Rate:</span>
                <span
                  className={
                    stats.successRate < 90 ? 'text-destructive' : 'text-success'
                  }
                >
                  {stats?.successRate?.toFixed(0)}%
                </span>
              </div>
              <div className="flex flex-wrap justify-between">
                <span>Slow Ops:</span>
                <span
                  className={
                    stats.slowOperations > 5
                      ? 'text-destructive'
                      : 'text-muted-foreground'
                  }
                >
                  {stats.slowOperations}
                </span>
              </div>
            </div>

            {/* Current Load */}
            <div className="space-y-1">
              <div className="flex flex-wrap justify-between truncate text-caption">
                <span>Current Load</span>
                <span>{Math.round(stats.currentLoad)}%</span>
              </div>
              <Progress value={stats.currentLoad} className="h-1" />
            </div>

            {/* Expanded Details */}
            {isExpanded && showDetailedStats && (
              <div className="pt-2 border-t space-y-2">
                <div className="truncate text-caption font-medium">
                  Recent Operations
                </div>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {metrics
                    .slice(-5)
                    .reverse()
                    .map((metric) => (
                      <div
                        key={metric.id}
                        className="flex flex-wrap items-center justify-between truncate text-caption"
                      >
                        <span className="truncate">{metric.operation}</span>
                        <div className="flex flex-wrap items-center gap-1">
                          {metric.status === 'pending' ? (
                            <RefreshCw className="h-3 w-3 animate-spin" />
                          ) : (
                            <>
                              <span
                                className={cn(
                                  metric.duration &&
                                    metric.duration > metric.target
                                    ? 'text-destructive'
                                    : 'text-success',
                                )}
                              >
                                {metric.duration ? `${metric.duration}ms` : '—'}
                              </span>
                              {metric.status === 'completed' &&
                                metric.duration &&
                                metric.duration <= metric.target && (
                                  <CheckCircle className="h-3 w-3 text-success" />
                                )}
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Toasts */}
      <div className={cn('fixed bottom-20 right-4 z-overlay space-y-2')}>
        {toasts.map((toast) => (
          <PerformanceToast
            key={toast.id}
            metric={toast}
            onDismiss={() => dismissToast(toast.id)}
          />
        ))}
      </div>
    </>
  );
};

// Hook for tracking performance in components
export const usePerformanceTracking = () => {
  const [currentMetrics, setCurrentMetrics] = useState<Map<string, string>>(
    new Map(),
  );

  const trackOperation = useCallback(
    (operation: string, target: number = 200) => {
      const id =
        Date.now().toString() + Math.random().toString(36).substr(2, 9);
      setCurrentMetrics((prev) => new Map(prev.set(operation, id)));

      // In a real implementation, this would integrate with the global performance monitor
      console.log(
        `[Performance] Started tracking: ${operation} (target: ${target}ms)`,
      );

      return id;
    },
    [],
  );

  const completeOperation = useCallback(
    (operation: string, success: boolean = true) => {
      const id = currentMetrics.get(operation);
      if (id) {
        setCurrentMetrics((prev) => {
          const next = new Map(prev);
          next.delete(operation);
          return next;
        });

        // In a real implementation, this would integrate with the global performance monitor
        console.log(
          `[Performance] Completed: ${operation} (success: ${success})`,
        );
      }
    },
    [currentMetrics],
  );

  return { trackOperation, completeOperation };
};

export default PerformanceMonitor;
