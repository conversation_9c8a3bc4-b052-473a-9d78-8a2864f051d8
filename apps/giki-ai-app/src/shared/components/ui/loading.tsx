import { Loader2 } from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullPage?: boolean;
  className?: string;
}

export function Loading({
  size = 'md',
  text = 'Loading...',
  fullPage = false,
  className,
}: LoadingProps) {
  const sizeMap = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  const content = (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullPage ? 'min-h-[50vh]' : 'py-8',
        className,
      )}
    >
      <Loader2 className={cn('animate-spin text-primary', sizeMap[size])} />
      {text && (
        <p className="mt-4 text-sm text-foreground font-medium">{text}</p>
      )}
    </div>
  );

  if (fullPage) {
    return (
      <div className="flex flex-wrap h-full w-full items-center justify-center">
        {content}
      </div>
    );
  }

  return content;
}
