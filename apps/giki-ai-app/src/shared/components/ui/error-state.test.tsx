/**
 * ErrorState Component Tests
 * Critical error state display component tests for production reliability
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import {
  ErrorState,
  APIErrorState,
  NotFoundErrorState,
  PermissionErrorState,
  LoadingErrorState,
} from './error-state';

describe('ErrorState - Error State Display Components', () => {
  const mockOnAction = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock window methods
    Object.defineProperty(window, 'location', {
      value: { href: '' },
      writable: true,
    });

    Object.defineProperty(window, 'history', {
      value: { back: vi.fn() },
      writable: true,
    });
  });

  describe('Basic ErrorState Component', () => {
    it('renders with default props', () => {
      render(<ErrorState />);

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(
        screen.getByText(/We encountered an unexpected error/),
      ).toBeInTheDocument();
      expect(screen.getByText('Error Details')).toBeInTheDocument();
    });

    it('displays custom title and description', () => {
      render(
        <ErrorState
          title="Custom Error Title"
          description="Custom error description"
        />,
      );

      expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
      expect(screen.getByText('Custom error description')).toBeInTheDocument();
    });

    it('shows action button when onAction is provided', () => {
      render(
        <ErrorState onAction={mockOnAction} actionLabel="Custom Action" />,
      );

      const actionButton = screen.getByRole('button', {
        name: /custom action/i,
      });
      expect(actionButton).toBeInTheDocument();
    });

    it('calls onAction when action button is clicked', async () => {
      const user = userEvent.setup();

      render(<ErrorState onAction={mockOnAction} actionLabel="Test Action" />);

      const actionButton = screen.getByRole('button', { name: /test action/i });
      await user.click(actionButton);

      expect(mockOnAction).toHaveBeenCalledTimes(1);
    });

    it('hides action button when onAction is not provided', () => {
      render(<ErrorState actionLabel="Hidden Action" />);

      expect(
        screen.queryByRole('button', { name: /hidden action/i }),
      ).not.toBeInTheDocument();
    });
  });

  describe('Navigation Buttons', () => {
    it('shows home button when enabled', () => {
      render(<ErrorState showHomeButton={true} />);

      const homeButton = screen.getByRole('button', { name: /go home/i });
      expect(homeButton).toBeInTheDocument();
    });

    it('shows back button when enabled', () => {
      render(<ErrorState showBackButton={true} />);

      const backButton = screen.getByRole('button', { name: /go back/i });
      expect(backButton).toBeInTheDocument();
    });

    it('navigates to home when home button is clicked', async () => {
      const user = userEvent.setup();

      render(<ErrorState showHomeButton={true} />);

      const homeButton = screen.getByRole('button', { name: /go home/i });
      await user.click(homeButton);

      expect(window?.location?.href).toBe('/');
    });

    it('calls history.back when back button is clicked', async () => {
      const user = userEvent.setup();

      render(<ErrorState showBackButton={true} />);

      const backButton = screen.getByRole('button', { name: /go back/i });
      await user.click(backButton);

      expect(window?.history?.back).toHaveBeenCalled();
    });

    it('shows all buttons when enabled', () => {
      render(
        <ErrorState
          onAction={mockOnAction}
          showHomeButton={true}
          showBackButton={true}
          actionLabel="Test Action"
        />,
      );

      expect(
        screen.getByRole('button', { name: /test action/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });
  });

  describe('Error Variants', () => {
    it('applies error variant styling', () => {
      render(<ErrorState variant="error" />);

      const alertIcon = screen.getByRole('img', { hidden: true });
      expect(alertIcon).toBeInTheDocument();
    });

    it('applies warning variant styling', () => {
      render(<ErrorState variant="warning" />);

      const alert = screen.getByText('Error Details').closest('[role="alert"]');
      expect(alert).toBeInTheDocument();
    });

    it('applies info variant styling', () => {
      render(<ErrorState variant="info" />);

      const alert = screen.getByText('Error Details').closest('[role="alert"]');
      expect(alert).toBeInTheDocument();
    });

    it('defaults to error variant', () => {
      render(<ErrorState />);

      // Should render error styling by default
      const errorIcon = screen.getAllByRole('img', { hidden: true });
      expect(errorIcon.length).toBeGreaterThan(0);
    });
  });

  describe('Visual Styling and Layout', () => {
    it('applies custom className', () => {
      render(<ErrorState className="custom-error-class" />);

      const container = screen
        .getByText('Something went wrong')
        .closest('div.custom-error-class');
      expect(container).toBeInTheDocument();
    });

    it('maintains proper spacing and layout', () => {
      render(<ErrorState />);

      const title = screen.getByText('Something went wrong');
      expect(title).toHaveClass('text-2xl', 'font-bold');
    });

    it('displays error icon with proper styling', () => {
      render(<ErrorState />);

      const iconContainer = screen
        .getByText('Something went wrong')
        .closest('div')
        ?.querySelector('.bg-destructive\\/10');
      expect(iconContainer).toBeInTheDocument();
    });

    it('centers content properly', () => {
      render(<ErrorState />);

      const container = screen.getByText('Something went wrong').closest('div');
      expect(container).toHaveClass(
        'flex',
        'flex-col',
        'items-center',
        'justify-center',
      );
    });
  });

  describe('Accessibility', () => {
    it('uses proper semantic HTML structure', () => {
      render(
        <ErrorState
          onAction={mockOnAction}
          showHomeButton={true}
          showBackButton={true}
        />,
      );

      expect(
        screen.getByRole('button', { name: /try again/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });

    it('provides accessible button labels', () => {
      render(
        <ErrorState
          onAction={mockOnAction}
          actionLabel="Retry Operation"
          showHomeButton={true}
          showBackButton={true}
        />,
      );

      expect(
        screen.getByRole('button', { name: /retry operation/i }),
      ).toHaveAccessibleName();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toHaveAccessibleName();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toHaveAccessibleName();
    });

    it('maintains proper heading hierarchy', () => {
      render(<ErrorState title="Error Title" />);

      const title = screen.getByText('Error Title');
      expect(title.tagName).toBe('H2');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <ErrorState
          onAction={mockOnAction}
          showBackButton={true}
          showHomeButton={true}
        />,
      );

      await user.tab();
      expect(screen.getByRole('button', { name: /try again/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /go back/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /go home/i })).toHaveFocus();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty title gracefully', () => {
      render(<ErrorState title="" />);

      // Should still render the container
      expect(
        screen.getByText(/We encountered an unexpected error/),
      ).toBeInTheDocument();
    });

    it('handles empty description gracefully', () => {
      render(<ErrorState description="" />);

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('handles long titles properly', () => {
      const longTitle =
        'This is a very long error title that should wrap properly and not break the layout';

      render(<ErrorState title={longTitle} />);

      expect(screen.getByText(longTitle)).toBeInTheDocument();
    });

    it('handles long descriptions properly', () => {
      const longDescription =
        'This is a very long error description that should wrap properly and maintain readability without breaking the component layout or causing overflow issues in the user interface.';

      render(<ErrorState description={longDescription} />);

      expect(screen.getByText(longDescription)).toBeInTheDocument();
    });

    it('handles rapid button clicks', async () => {
      const user = userEvent.setup();

      render(<ErrorState onAction={mockOnAction} />);

      const actionButton = screen.getByRole('button', { name: /try again/i });

      // Rapid clicks
      await user.click(actionButton);
      await user.click(actionButton);
      await user.click(actionButton);

      expect(mockOnAction).toHaveBeenCalledTimes(3);
    });
  });

  describe('Performance', () => {
    it('renders efficiently', () => {
      const startTime = performance.now();
      render(<ErrorState />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50);
    });

    it('handles frequent prop changes efficiently', () => {
      const { rerender } = render(<ErrorState title="Initial" />);

      for (let i = 0; i < 10; i++) {
        rerender(<ErrorState title={`Title ${i}`} />);
      }

      expect(screen.getByText('Title 9')).toBeInTheDocument();
    });

    it('cleans up properly on unmount', () => {
      const { unmount } = render(<ErrorState onAction={mockOnAction} />);

      expect(() => unmount()).not.toThrow();
    });
  });
});

describe('Specialized Error Components', () => {
  const mockOnRetry = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    Object.defineProperty(window, 'location', {
      value: { href: '' },
      writable: true,
    });
  });

  describe('APIErrorState', () => {
    it('renders with API-specific content', () => {
      render(<APIErrorState onRetry={mockOnRetry} />);

      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(
        screen.getByText(/Unable to connect to our servers/),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /retry connection/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });

    it('calls onRetry when retry button is clicked', async () => {
      const user = userEvent.setup();

      render(<APIErrorState onRetry={mockOnRetry} />);

      const retryButton = screen.getByRole('button', {
        name: /retry connection/i,
      });
      await user.click(retryButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('works without onRetry prop', () => {
      render(<APIErrorState />);

      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /retry connection/i }),
      ).not.toBeInTheDocument();
    });
  });

  describe('NotFoundErrorState', () => {
    it('renders with 404-specific content', () => {
      render(<NotFoundErrorState />);

      expect(screen.getByText('Page Not Found')).toBeInTheDocument();
      expect(
        screen.getByText(/The page you're looking for doesn't exist/),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go to dashboard/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toBeInTheDocument();
    });

    it('navigates to dashboard when button is clicked', async () => {
      const user = userEvent.setup();

      render(<NotFoundErrorState />);

      const dashboardButton = screen.getByRole('button', {
        name: /go to dashboard/i,
      });
      await user.click(dashboardButton);

      expect(window?.location?.href).toBe('/');
    });
  });

  describe('PermissionErrorState', () => {
    it('renders with permission-specific content', () => {
      render(<PermissionErrorState />);

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(
        screen.getByText(/You don't have permission to access/),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go home/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });

    it('shows warning variant styling', () => {
      render(<PermissionErrorState />);

      // Should use warning variant
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('LoadingErrorState', () => {
    it('renders with loading error content', () => {
      render(<LoadingErrorState onRetry={mockOnRetry} />);

      expect(screen.getByText('Failed to Load Data')).toBeInTheDocument();
      expect(
        screen.getByText(/We couldn't load the requested data/),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /reload data/i }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /go back/i }),
      ).toBeInTheDocument();
    });

    it('calls onRetry when reload button is clicked', async () => {
      const user = userEvent.setup();

      render(<LoadingErrorState onRetry={mockOnRetry} />);

      const reloadButton = screen.getByRole('button', { name: /reload data/i });
      await user.click(reloadButton);

      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('works without onRetry prop', () => {
      render(<LoadingErrorState />);

      expect(screen.getByText('Failed to Load Data')).toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /reload data/i }),
      ).not.toBeInTheDocument();
    });
  });

  describe('Specialized Components Integration', () => {
    it('all specialized components render without errors', () => {
      const components = [
        <APIErrorState key="api" onRetry={mockOnRetry} />,
        <NotFoundErrorState key="404" />,
        <PermissionErrorState key="permission" />,
        <LoadingErrorState key="loading" onRetry={mockOnRetry} />,
      ];

      components.forEach((component, _index) => {
        const { unmount } = render(component);
        expect(() => unmount()).not.toThrow();
      });
    });

    it('specialized components maintain accessibility', () => {
      render(<APIErrorState onRetry={mockOnRetry} />);

      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toHaveAccessibleName();
      });
    });

    it('specialized components handle user interactions', async () => {
      const user = userEvent.setup();

      render(<LoadingErrorState onRetry={mockOnRetry} />);

      // Should handle keyboard navigation
      await user.tab();
      expect(
        screen.getByRole('button', { name: /reload data/i }),
      ).toHaveFocus();
    });
  });
});
