import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/shared/utils/utils'; // Utility for conditional classes
import Logo from '@/shared/components/ui/logo';
import useAuthStore from '@/shared/services/auth/authStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';

import { LogOut, Home, CreditCard, PieChart } from 'lucide-react';

// Clean, minimal navigation structure
const navItems = [
  {
    href: '/dashboard',
    label: 'Dashboard',
    icon: <Home size={20} />,
  },
  {
    href: '/transactions',
    label: 'Transactions',
    icon: <CreditCard size={20} />,
  },
  {
    href: '/reports',
    label: 'Reports',
    icon: <PieChart size={20} />,
  },
];

interface LeftNavProps {
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const LeftNav: React.FC<LeftNavProps> = ({
  isCollapsed = true, // Always collapsed by default
  onToggleCollapse,
}) => {
  const location = useLocation();
  const { logout, userId } = useAuthStore();

  return (
    <TooltipProvider>
      <div className="flex flex-col h-full bg-card border-r border-border">
        {/* Simple header */}
        <div className="flex items-center justify-center h-16 border-b border-border">
          <Link to="/" className="flex items-center">
            <Logo size="sm" showText={false} isDarkBackground={false} />
          </Link>
        </div>

        {/* Simple navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const isActive =
                location?.pathname?.startsWith(item.href) && item.href !== '/';
              return (
                <li key={item.href}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to={item.href}
                        className={cn(
                          'flex items-center justify-center w-full h-12 rounded-lg transition-all',
                          isActive
                            ? 'bg-primary text-primary-foreground'
                            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                        )}
                        aria-current={isActive ? 'page' : undefined}
                      >
                        {React.cloneElement(item.icon, {
                          className: 'w-5 h-5',
                          strokeWidth: 2,
                        })}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{item.label}</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Simple user section */}
        <div className="p-4 border-t border-border">
          <UserMenuDropdown isCollapsed={true} onLogout={logout} />
        </div>
      </div>
    </TooltipProvider>
  );
};

// User Menu Dropdown Component for E2E test compatibility
interface UserMenuDropdownProps {
  isCollapsed: boolean;
  onLogout: () => void;
}

const UserMenuDropdown: React.FC<UserMenuDropdownProps> = ({
  isCollapsed,
  onLogout,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          data-testid="user-menu"
          onClick={onLogout}
          className="w-full h-12 flex items-center justify-center rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-all"
          aria-label="Logout"
        >
          <LogOut className="w-5 h-5" />
        </button>
      </TooltipTrigger>
      <TooltipContent side="right">
        <p>Logout</p>
      </TooltipContent>
    </Tooltip>
  );
};

export default LeftNav;
