import React from 'react';
import Logo from '@/shared/components/ui/logo';
import ThemeToggle from '@/shared/components/ui/ThemeToggle';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
  showLogo?: boolean;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showLogo = true,
}) => {
  return (
    <div className="min-h-screen bg-background text-foreground relative overflow-hidden">
      {/* Theme Toggle - consistent with main app */}
      <div className="absolute top-4 right-4 z-modal">
        <ThemeToggle />
      </div>

      {/* Background Pattern - consistent with MainContent */}
      <div className="absolute inset-0 bg-grid-pattern opacity-3 pointer-events-none" />
      <div className="absolute inset-0 bg-gradient-to-br from-primary/2 via-transparent to-accent/2 pointer-events-none" />

      {/* Main Content Container */}
      <div className="relative z-10 flex flex-wrap items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          {/* Auth Card - Excel-inspired design consistent with app theme */}
          <div className="bg-card text-card-foreground-foreground rounded-lg shadow-xl border border-border/50 overflow-hidden">
            {/* Header Section */}
            <div className="px-8 pt-8 pb-6 border-b border-border/50 bg-excel-card">
              {showLogo && (
                <div className="flex flex-wrap justify-center items-center mb-6">
                  <Logo size="lg" showText={true} />
                </div>
              )}
              <div className="text-center">
                <h1 className="truncate text-heading-3 text-gradient mb-2">
                  {title}
                </h1>
                <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                  {subtitle}
                </p>
              </div>
            </div>

            {/* Form Content */}
            <div className="px-8 py-6">{children}</div>
          </div>

          {/* Footer - Professional branding */}
          <div className="mt-6 text-center">
            <p className="truncate text-caption text-[hsl(var(--giki-text-muted))]">
              © 2024 Giki AI. Professional financial data management platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
