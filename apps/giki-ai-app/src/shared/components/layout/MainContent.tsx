import React from 'react';
import { ScrollContainer } from '../ui/ScrollContainer';

interface MainContentProps {
  children: React.ReactNode;
}

export const MainContent: React.FC<MainContentProps> = ({ children }) => {
  return (
    <main className="flex-1 flex flex-col bg-background h-full">
      <ScrollContainer
        className="flex-1 h-full"
        preserveScrollPosition={true}
        showScrollToTop={true}
        scrollToTopThreshold={200}
        maxHeight="100%"
      >
        <div className="w-full p-6">{children}</div>
      </ScrollContainer>
    </main>
  );
};

export default MainContent;
