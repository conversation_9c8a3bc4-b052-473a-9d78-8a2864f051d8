import React, { useCallback, useRef } from 'react';
import { LeftNav } from './LeftNav'; // Updated import
import { MainContent } from './MainContent';
import { usePanelState } from '@/core/providers/PanelStateContext';
import ThemeToggle from '@/shared/components/ui/ThemeToggle';

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  // Use global panel state context
  const {
    isAgentPanelCollapsed,
    isLeftNavCollapsed,
    agentPanelWidth,
    toggleAgentPanel,
    toggleLeftNav,
    setAgentPanelWidth,
  } = usePanelState();

  const isResizing = useRef(false);
  const startX = useRef(0);
  const startWidth = useRef(0);

  // Resizing handlers - now using global state
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing.current) return;

      const deltaX = startX.current - e.clientX; // Negative delta = expand right
      const newWidth = Math.max(
        320,
        Math.min(600, startWidth.current + deltaX),
      );
      setAgentPanelWidth(newWidth);
    },
    [setAgentPanelWidth],
  );

  const handleMouseUp = useCallback(() => {
    isResizing.current = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      isResizing.current = true;
      startX.current = e.clientX;
      startWidth.current = agentPanelWidth;
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      e.preventDefault();
    },
    [agentPanelWidth, handleMouseMove, handleMouseUp],
  );

  return (
    <div className="h-screen bg-background text-foreground flex">
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>

      {/* Simplified Left Navigation - Always collapsed by default */}
      <div
        className={`fixed top-0 left-0 h-full bg-card border-r border-border transition-all duration-200 z-40 ${
          isLeftNavCollapsed ? 'w-16' : 'w-64'
        }`}
      >
        <LeftNav
          isCollapsed={true} // Always start collapsed
          onToggleCollapse={toggleLeftNav}
        />
      </div>

      {/* Maximized Main Content Area - No bottom padding, no complex margins */}
      <main
        id="main-content"
        className="flex-1 h-full ml-16"
        tabIndex={-1}
        role="main"
        aria-label="Main content"
      >
        <MainContent>{children}</MainContent>
      </main>

      {/* Optional minimal chat button - bottom right */}
      <div className="fixed bottom-6 right-6 z-30">
        <button
          onClick={() => void toggleAgentPanel()}
          className="w-12 h-12 bg-primary text-primary-foreground rounded-full shadow-md hover:shadow-lg transition-shadow flex items-center justify-center"
          aria-label="AI Help"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default AppLayout;
