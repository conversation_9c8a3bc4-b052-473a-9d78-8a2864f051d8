import React, { useState, useEffect, useRef, useCallback } from 'react';
import { sendMessageToAgent } from '@/features/intelligence/services/agentService';
import type { AgentResponse, ChatMessage } from '@/shared/types/api';
import { Input } from '@/shared/components/ui/input';
import { Button } from '@/shared/components/ui/button';
import { useRealtimeEmit } from '@/shared/services/realtime/realtimeSync';
import { TooltipProvider } from '@/shared/components/ui/tooltip';
import { AudioInput } from './AudioInput';
// import { PerformanceMonitor } from './PerformanceMonitor'; // Temporarily disabled
import { RefreshCw, X, Network } from 'lucide-react';
import '@/styles/agent-interface.css';

// Enhanced conversation and performance tracking interfaces
interface ConversationHistory {
  id: string;
  title: string;
  timestamp: string;
  messageCount: number;
  lastActivity: string;
}

interface PerformanceMetrics {
  responseTime: number;
  accuracy: number;
  tokensUsed: number;
  audioProcessingTime?: number;
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [conversationId, setConversationId] = useState<string | undefined>(
    undefined,
  );
  const [agentActivity, setAgentActivity] = useState<string[]>([]);
  const [processingState, setProcessingState] = useState<
    'idle' | 'thinking' | 'processing' | 'responding'
  >('idle');

  // SINGLE AGENT EXPERIENCE (Rule #12): Customers only see "Giki"
  // Backend multi-agent architecture is hidden from users
  const [gikiStatus, setGikiStatus] = useState<
    'healthy' | 'degraded' | 'offline'
  >('healthy');
  const [gikiCapabilities, _setGikiCapabilities] = useState<string[]>([
    'Transaction Analysis & Categorization',
    'Financial Report Generation',
    'File Upload & Processing',
    'Spending Pattern Analysis',
    'Data Export & Download',
    'Real-time Chart Visualization',
    'Custom Query Handling',
  ]);
  const [_lastInteraction, setLastInteraction] = useState<Date | null>(null);
  // Removed: availableAgents, currentAgent, showAgentSelector - violates Rule #12

  // Real-time sync
  const { emit: _emit } = useRealtimeEmit();

  // Enhanced state for conversation management
  const [_conversationHistory, setConversationHistory] = useState<
    ConversationHistory[]
  >([]);
  const [_showHistory, setShowHistory] = useState<boolean>(false);
  const [currentConversationTitle, setCurrentConversationTitle] =
    useState<string>('New Conversation');

  // Performance tracking
  const [_performanceMetrics, setPerformanceMetrics] =
    useState<PerformanceMetrics>({
      responseTime: 0,
      accuracy: 0,
      tokensUsed: 0,
    });
  const [processingProgress, setProcessingProgress] = useState<number>(0);
  // const [showPerformanceMonitor, setShowPerformanceMonitor] =
  //   useState<boolean>(false);

  const messagesEndRef = useRef<null | HTMLDivElement>(null);
  const startTimeRef = useRef<number>(0);

  const scrollToBottom = () => {
    if (
      messagesEndRef.current &&
      typeof messagesEndRef?.current?.scrollIntoView === 'function'
    ) {
      messagesEndRef?.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages.length]);

  // SINGLE AGENT EXPERIENCE: Initialize "Giki" agent with personality
  useEffect(() => {
    const initializeGiki = () => {
      try {
        // Health check to determine Giki's capabilities
        setGikiStatus('healthy');
        setLastInteraction(new Date());

        // Add a welcome message for first-time users
        if (messages.length === 0) {
          const welcomeMessage: ChatMessage = {
            id: `giki-welcome-${Date.now()}`,
            text: "Hi! I'm Giki, your AI financial assistant. I can help you with:\n• Categorizing transactions and assigning GL codes\n• Generating comprehensive financial reports\n• Uploading and processing bank data files\n• Analyzing spending patterns and trends\n• Exporting data in various formats\n• Creating real-time visualizations\n• Answering any financial questions\n\nYou can accomplish everything through our conversation - just ask me what you need!",
            sender: 'agent',
            timestamp: new Date().toISOString(),
          };
          setMessages([welcomeMessage]);
        }

        // Preload common queries for better performance
        // await preloadCommonQueries();
      } catch (error) {
        console.error('Failed to initialize Giki:', error);
        setGikiStatus('offline');
      }
    };

    void initializeGiki();
  }, [messages.length]);

  // Enhanced conversation management functions
  const saveCurrentConversation = useCallback(() => {
    if (messages.length > 0 && conversationId) {
      const newConversation: ConversationHistory = {
        id: conversationId,
        title: currentConversationTitle,
        timestamp: new Date().toISOString(),
        messageCount: messages.length,
        lastActivity: new Date().toLocaleString(),
      };

      setConversationHistory((prev) => {
        const existing = prev.find((conv) => conv.id === conversationId);
        if (existing) {
          return prev.map((conv) =>
            conv.id === conversationId ? newConversation : conv,
          );
        }
        return [newConversation, ...prev].slice(0, 10); // Keep last 10 conversations
      });
    }
  }, [messages, conversationId, currentConversationTitle]);

  const startNewConversation = useCallback(() => {
    saveCurrentConversation();
    setMessages([]);
    setConversationId(undefined);
    setCurrentConversationTitle('New Conversation');
    setAgentActivity([]);
    setError(null);
    setProcessingProgress(0);
  }, [saveCurrentConversation]);

  const _loadConversation = useCallback((conversation: ConversationHistory) => {
    // In a real implementation, you'd load messages from storage
    // For now, we'll just switch to that conversation context
    setConversationId(conversation.id);
    setCurrentConversationTitle(conversation.title);
    setShowHistory(false);
  }, []);

  const _clearConversationHistory = useCallback(() => {
    setConversationHistory([]);
    setShowHistory(false);
  }, []);

  // UNIFIED GIKI PROCESSING (Rule #12): Single agent experience with personality
  // Backend routing is invisible to users - they only interact with "Giki"
  const processRequestThroughGiki = useCallback(
    async (userQuery: string) => {
      try {
        setLastInteraction(new Date());
        setAgentActivity((prev) => [
          ...prev,
          'Giki is analyzing your request...',
        ]);
        setProcessingState('thinking');

        // Determine the type of request for better user feedback
        const queryType = userQuery.toLowerCase();
        let processingMessage = 'Processing your request...';

        if (
          queryType.includes('categorize') ||
          queryType.includes('category')
        ) {
          processingMessage = 'Analyzing transactions for categorization...';
        } else if (
          queryType.includes('report') ||
          queryType.includes('generate')
        ) {
          processingMessage = 'Generating your financial report...';
        } else if (queryType.includes('upload') || queryType.includes('file')) {
          processingMessage = 'Preparing to process your financial data...';
        } else if (
          queryType.includes('insight') ||
          queryType.includes('analysis')
        ) {
          processingMessage = 'Analyzing patterns in your financial data...';
        }

        setAgentActivity((prev) => [...prev, processingMessage]);

        // Backend will handle agent coordination invisibly
        // CustomerAgent will route to appropriate backend agents as needed
        const response = await sendMessageToAgent(userQuery, conversationId);

        // Add personality to responses
        if (response.message.text) {
          // Enhance response with Giki's personality if it's too technical
          const enhancedText = response.message.text.startsWith('Error:')
            ? `I apologize, but I encountered an issue: ${response.message.text.replace('Error:', '')}`
            : response.message.text;

          response.message.text = enhancedText;
        }

        setAgentActivity((prev) => [
          ...prev,
          'Giki has completed your request',
        ]);
        setProcessingState('idle');

        return response;
      } catch (error) {
        console.error('Failed to process request through Giki:', error);
        const friendlyError =
          error instanceof Error
            ? `I'm having trouble processing that request. ${error.message}`
            : 'I encountered an unexpected issue. Please try again or rephrase your request.';
        setError(friendlyError);
        setProcessingState('idle');
        throw error;
      }
    },
    [conversationId],
  );

  // Auto-save conversation periodically
  useEffect(() => {
    const interval = setInterval(saveCurrentConversation, 30000); // Save every 30 seconds
    return () => clearInterval(interval);
  }, [saveCurrentConversation]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Auto-generate conversation title from first message
    if (messages.length === 0) {
      const title =
        inputValue.length > 30
          ? `${inputValue.substring(0, 30)}...`
          : inputValue;
      setCurrentConversationTitle(title);
    }

    const userMessage: ChatMessage = {
      id: `user-msg-${Date.now()}`,
      text: inputValue,
      sender: 'user',
      timestamp: new Date().toISOString(),
    };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setError(null);
    setProcessingProgress(0);

    // Start performance tracking
    startTimeRef.current = Date.now();

    // Agent activity tracking with progress
    setProcessingState('thinking');
    setAgentActivity(['Analyzing query']);
    setProcessingProgress(10);

    // Real-time progress tracking based on actual API call
    const updateProgress = (
      progress: number,
      activity: string,
      status?: 'idle' | 'thinking' | 'processing' | 'responding',
    ) => {
      setProcessingProgress(progress);
      if (status) setProcessingState(status);
      setAgentActivity((prev) => [...prev, activity]);
    };

    // Update progress before API call
    updateProgress(25, 'Connecting to AI agent', 'processing');

    try {
      // Update progress during API call
      updateProgress(50, 'Processing with AI agent');

      // SINGLE AGENT PROCESSING: Route through unified Giki interface (Rule #12)
      const response: AgentResponse = await processRequestThroughGiki(
        userMessage.text,
      );

      // Update progress after receiving response
      updateProgress(75, 'Processing response');

      // Calculate performance metrics
      const responseTime = Date.now() - startTimeRef.current;
      setPerformanceMetrics((prev) => ({
        ...prev,
        responseTime,
        tokensUsed: prev.tokensUsed + (response.metadata?.tokens_used || 0),
        accuracy: response.metadata?.confidence || prev.accuracy,
      }));

      setMessages((prevMessages) => [...prevMessages, response.message]);
      if (response.conversation_id) {
        setConversationId(response.conversation_id);
      }

      // Final progress update with real metrics
      updateProgress(
        100,
        `Response complete - ${responseTime}ms (${response.metadata?.tokens_used || 0} tokens)`,
        'idle',
      );
    } catch (err) {
      const errorMessageText =
        err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessageText);
      console.error(err);
      const errorMessage: ChatMessage = {
        id: `err-msg-${Date.now()}`,
        text: `Error: ${errorMessageText}`,
        sender: 'system',
        timestamp: new Date().toISOString(),
      };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
      setAgentActivity((prev) => [...prev, 'ERROR: Processing failed']);
      setProcessingState('idle');
      setProcessingProgress(0);
    } finally {
      setIsLoading(false);
      // Clear progress after a delay
      setTimeout(() => setProcessingProgress(0), 2000);
    }
  };

  // Enhanced audio processing with performance tracking
  const handleAudioProcessed = async (audioResult: {
    success?: boolean;
    result?: {
      audio_result?: {
        transcription?: string;
        intent?: string;
        parameters?: unknown;
        confidence?: number;
        language_detected?: string;
      };
    };
  }) => {
    if (!audioResult.success || !audioResult.result?.audio_result) {
      setError('Failed to process audio input');
      return;
    }

    const audioStartTime = Date.now();
    const { transcription, intent, parameters } =
      audioResult.result.audio_result;

    // Auto-generate conversation title from first audio message
    if (messages.length === 0 && transcription) {
      const title =
        transcription.length > 30
          ? `Audio: ${transcription.substring(0, 30)}...`
          : `Audio: ${transcription}`;
      setCurrentConversationTitle(title);
    }

    // Add transcription as user message
    const audioMessage: ChatMessage = {
      id: `audio-msg-${Date.now()}`,
      text: `[Audio] ${transcription || 'No transcription available'}`,
      sender: 'user',
      timestamp: new Date().toISOString(),
    };
    setMessages((prevMessages) => [...prevMessages, audioMessage]);

    // Process the transcribed text as a regular message
    setIsLoading(true);
    setError(null);
    setProcessingProgress(0);
    setProcessingState('processing');
    setAgentActivity(['Processing audio transcription']);
    setProcessingProgress(20);

    // Audio-specific progress steps
    setTimeout(() => {
      setProcessingProgress(40);
      setAgentActivity((prev) => [...prev, 'Analyzing audio intent']);
    }, 200);

    setTimeout(() => {
      setProcessingProgress(60);
      setAgentActivity((prev) => [...prev, 'Processing financial query']);
    }, 400);

    try {
      setProcessingProgress(80);
      setAgentActivity((prev) => [...prev, 'Generating response']);

      // SINGLE AGENT PROCESSING: Route audio through unified Giki interface (Rule #12)
      const response: AgentResponse = await processRequestThroughGiki(
        transcription || 'No transcription available',
      );

      // Calculate audio processing performance
      const audioProcessingTime = Date.now() - audioStartTime;
      setPerformanceMetrics((prev) => ({
        ...prev,
        audioProcessingTime,
        tokensUsed: prev.tokensUsed + (response.metadata?.tokens_used || 0),
        accuracy:
          audioResult?.result?.audio_result?.confidence || prev.accuracy,
      }));

      // Add agent response with audio metadata
      const agentMessage: ChatMessage = {
        ...response.message,
        contentType: 'audio_response',
        contentData: {
          original_audio: {
            transcription,
            intent,
            parameters,
            confidence: audioResult?.result?.audio_result?.confidence,
            language: audioResult?.result?.audio_result?.language_detected,
            processing_time: audioProcessingTime,
          },
        },
      };

      setMessages((prevMessages) => [...prevMessages, agentMessage]);
      if (response.conversation_id) {
        setConversationId(response.conversation_id);
      }

      setProcessingProgress(100);
      setAgentActivity((prev) => [
        ...prev,
        `Audio processing complete - ${audioProcessingTime}ms`,
      ]);
      setProcessingState('idle');
    } catch (err) {
      const errorMessageText =
        err instanceof Error
          ? err.message
          : 'Failed to process audio transcription';
      setError(errorMessageText);
      console.error(err);
      setAgentActivity((prev) => [...prev, 'ERROR: Audio processing failed']);
      setProcessingState('idle');
      setProcessingProgress(0);
    } finally {
      setIsLoading(false);
      // Clear progress after a delay
      setTimeout(() => setProcessingProgress(0), 2000);
    }
  };

  // Professional message bubble styling with glass morphism
  const getMessageBubbleClasses = (sender: string) => {
    const baseClasses =
      'max-w-md px-4 py-3 rounded-xl transition-all duration-200 relative overflow-hidden';
    if (sender === 'user') {
      return `${baseClasses} bg-gradient-to-br from-primary to-primary-hover text-primary-foreground ml-auto shadow-lg border border-primary/20`;
    } else if (sender === 'agent') {
      return `${baseClasses} bg-card/70 backdrop-blur-sm border border-border shadow-md hover:shadow-lg`;
    } else {
      return `${baseClasses} bg-destructive/10 text-destructive border border-destructive/20 backdrop-blur-sm`;
    }
  };

  return (
    <TooltipProvider>
      {/* Professional AI Assistant Interface with Glass Morphism */}
      <div className="h-full flex flex-wrap flex-col bg-gradient-to-b from-background/95 to-muted/10 backdrop-blur-sm relative overflow-hidden">
        {/* Subtle Brand Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/2 pointer-events-none" />
        {/* SINGLE AGENT INTERFACE: Professional Giki Header with Glass Effect */}
        <div className="relative z-10 px-4 py-3 border-b border-border/50 flex flex-wrap items-center justify-between backdrop-blur-md bg-gradient-to-r from-primary/10 via-primary/5 to-accent/10 shadow-lg">
          <div className="flex flex-wrap items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-primary to-primary-hover border border-primary/20 shadow-md">
              <Network className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <div className="flex flex-wrap items-center gap-2">
                <h3 className="text-lg font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  Giki
                </h3>
                <span className="truncate text-caption px-3 py-1 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 text-foreground font-medium border border-primary/30">
                  AI Financial Assistant
                </span>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    gikiStatus === 'healthy'
                      ? 'bg-success shadow-success/50 shadow-sm'
                      : gikiStatus === 'degraded'
                        ? 'bg-warning shadow-warning/50 shadow-sm'
                        : 'bg-destructive shadow-destructive/50 shadow-sm'
                  }`}
                />
                <p className="truncate text-caption text-muted-foreground font-medium">
                  {gikiStatus === 'healthy'
                    ? 'Ready to assist'
                    : gikiStatus === 'degraded'
                      ? 'Limited functionality'
                      : 'Offline'}
                </p>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => void startNewConversation()}
              className="max-w-full h-8 w-8 p-0 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-all duration-200"
              title="Start new conversation"
            >
              <RefreshCw className="w-3.5 h-3.5" />
            </Button>
          </div>
        </div>

        {/* SINGLE AGENT STATUS: Professional Status Strip with Glass Effect */}
        <div className="relative z-10 px-4 py-2.5 bg-muted/30 backdrop-blur-sm border-b border-border/50 flex flex-wrap items-center gap-3">
          <div className="flex flex-wrap items-center gap-2">
            <div
              className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                processingState === 'idle'
                  ? 'bg-success shadow-success/50 shadow-sm'
                  : processingState === 'thinking'
                    ? 'bg-info animate-pulse shadow-info/50 shadow-sm'
                    : processingState === 'processing'
                      ? 'bg-warning animate-pulse shadow-warning/50 shadow-sm'
                      : 'bg-success animate-pulse shadow-success/50 shadow-sm'
              }`}
            />
            <span className="truncatelabel text-foreground/80">
              {processingState === 'idle' ? '🟢 Ready' : '🔄 Processing...'}
            </span>
          </div>
          <div className="flex flex-wrap items-center gap-3 ml-auto">
            {processingState !== 'idle' && (
              <div className="w-3 h-3 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
            )}
          </div>
        </div>

        {/* Messages Area with Enhanced Styling */}
        <div className="relative z-10 flex-1 px-4 py-4 space-y-4 overflow-y-auto min-h-0 scroll-smooth scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
          {messages.length === 0 ? (
            <div className="flex flex-wrap flex-col items-center justify-center h-full text-center py-12">
              <div className="w-16 sm:w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/20 to-accent/20 flex flex-wrap items-center justify-center mb-6 shadow-lg border border-primary/30">
                <Network className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-2">
                Welcome to Giki AI
              </h3>
              <p className="truncatebody-small mb-6 max-w-sm">
                {gikiStatus === 'healthy'
                  ? `Ready to help with ${gikiCapabilities?.length || 0} financial capabilities`
                  : gikiStatus === 'degraded'
                    ? 'Limited functionality available'
                    : 'Temporarily offline - please try again'}
              </p>
              <div className="grid grid-cols-1 gap-2 w-full max-w-sm">
                <button
                  className="text-sm px-4 py-2.5 bg-card/50 backdrop-blur-sm rounded-lg text-center border border-border hover:bg-primary/10 hover:text-primary hover:border-primary/50 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md font-medium quick-action-pill"
                  onClick={() => setInputValue('Categorize my transactions')}
                >
                  &quot;Categorize my transactions&quot;
                </button>
                <button
                  className="truncate text-caption text-muted-foreground p-2 bg-muted/50 rounded text-center border border-border hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200 cursor-pointer"
                  onClick={() => setInputValue('Generate my financial report')}
                >
                  &quot;Generate my financial report&quot;
                </button>
                <button
                  className="truncate text-caption text-muted-foreground p-2 bg-muted/50 rounded text-center border border-border hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200 cursor-pointer"
                  onClick={() => setInputValue('Upload my bank statements')}
                >
                  &quot;Upload bank statements&quot;
                </button>
                <button
                  className="truncate text-caption text-muted-foreground p-2 bg-muted/50 rounded text-center border border-border hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200 cursor-pointer"
                  onClick={() => setInputValue('Show my spending trends')}
                >
                  &quot;View spending trends&quot;
                </button>
                <button
                  className="truncate text-caption text-muted-foreground p-2 bg-muted/50 rounded text-center border border-border hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200 cursor-pointer"
                  onClick={() => setInputValue('Export my data to Excel')}
                >
                  &quot;Export to Excel&quot;
                </button>
              </div>
            </div>
          ) : (
            messages.map((msg) => (
              <div
                key={msg.id}
                className={`flex ${
                  msg.sender === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`${getMessageBubbleClasses(msg.sender)} message-bubble ${msg.sender === 'agent' ? 'message-bubble-agent' : msg.sender === 'user' ? 'message-bubble-user' : ''}`}
                >
                  {msg.sender === 'agent' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 pointer-events-none" />
                  )}
                  <p className="text-sm relative z-10 leading-relaxed">
                    {msg.text}
                  </p>
                  {msg.sender === 'agent' &&
                    msg.contentType &&
                    msg.contentType !== 'text' && (
                      <div className="mt-4 p-4 border-t border-border/50 bg-muted/20 rounded-lg backdrop-blur-sm">
                        <p className="truncate text-caption font-semibold text-foreground/80 mb-3 uppercase tracking-wide">
                          {msg.contentType.replace(/_/g, ' ')}
                        </p>
                        <pre className="truncate text-caption bg-background/50 text-muted-foreground p-3 rounded-lg overflow-x-auto border border-border/50 text-financial">
                          {JSON.stringify(msg.contentData, null, 2)}
                        </pre>
                      </div>
                    )}
                  <p className="truncate text-caption mt-2 text-muted-foreground/70 font-medium">
                    {new Date(msg.timestamp).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
              </div>
            ))
          )}
          {isLoading && (
            <div className="flex flex-wrap justify-start">
              <div className="px-5 py-4 rounded-xl bg-card/70 backdrop-blur-sm border border-border shadow-lg">
                <div className="flex flex-wrap items-center gap-3">
                  <div className="flex flex-wrap space-x-1">
                    <div
                      className="w-2 h-2 bg-primary rounded-full animate-bounce"
                      style={{ animationDelay: '0ms' }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-primary rounded-full animate-bounce"
                      style={{ animationDelay: '150ms' }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-primary rounded-full animate-bounce"
                      style={{ animationDelay: '300ms' }}
                    ></div>
                  </div>
                  <p className="text-sm text-foreground font-medium">
                    Giki is thinking...
                  </p>
                </div>
                {processingProgress > 0 && (
                  <div className="mt-3">
                    <div className="w-full bg-muted/50 rounded-full h-1.5 overflow-hidden">
                      <div
                        className="h-full rounded-full transition-all duration-300 bg-gradient-to-r from-primary via-accent to-primary"
                        style={{
                          width: `${processingProgress}%`,
                          backgroundSize: '200% 100%',
                          animation: 'gradient-shift 2s ease-in-out infinite',
                        }}
                      ></div>
                    </div>
                    {agentActivity.length > 0 && (
                      <p className="truncate text-caption text-muted-foreground mt-2 font-medium">
                        {agentActivity[agentActivity.length - 1]}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
          {error && (
            <div className="flex flex-wrap justify-start">
              <div className="bg-destructive/5 backdrop-blur-sm border border-destructive/20 text-destructive p-5 rounded-xl max-w-[85%] shadow-lg">
                <div className="flex flex-wrap items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-destructive/20 to-destructive/10 flex flex-wrap items-center justify-center border border-destructive/30">
                    <span className="text-destructive text-lg font-bold">
                      ⚠️
                    </span>
                  </div>
                  <p className="truncatestatus-badge">Error Occurred</p>
                </div>
                <p className="text-sm ml-[52px] text-destructive/90">{error}</p>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Professional Input Section with Glass Effect */}
        <div className="relative z-10 p-4 border-t border-border/50 bg-card/30 backdrop-blur-md space-y-3">
          {/* SINGLE AGENT QUICK ACTIONS: Unified Giki capabilities (Rule #12) */}
          <div className="flex flex-wrap gap-1.5 flex-wrap">
            <Button
              variant="outline"
              onClick={() => setInputValue('Categorize my transactions')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Categorize
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputValue('Generate my financial report')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Reports
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputValue('Upload my bank statements')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Upload
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputValue('Show my spending patterns')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Insights
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputValue('Export my data to Excel')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Export
            </Button>
            <Button
              variant="outline"
              onClick={() => setInputValue('Take me to the transactions page')}
              className="max-w-full truncate text-caption px-3 py-1.5 h-8 rounded-full bg-card/50 backdrop-blur-sm border border-border text-muted-foreground font-medium hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm hover:shadow-md quick-action-pill"
            >
              Navigate
            </Button>
          </div>

          {/* Professional Audio Input with Glass Effect */}
          <div className="bg-card/50 backdrop-blur-sm rounded-lg border border-border/50 p-2 shadow-sm">
            <AudioInput
              onAudioProcessed={(audioResult) =>
                void handleAudioProcessed(audioResult)
              }
              disabled={isLoading}
            />
          </div>

          {/* Modern Text Input */}
          <div className="flex flex-wrap gap-2 items-end">
            <div className="relative flex flex-wrap-1">
              <Input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e?.target?.value)}
                onKeyDown={(e) =>
                  e.key === 'Enter' && !isLoading && void handleSendMessage()
                }
                placeholder="Ask me anything about your finances..."
                className="h-11 pr-10 text-sm bg-background/50 backdrop-blur-sm border border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 rounded-lg transition-all duration-200 font-medium shadow-sm giki-chat-input"
                disabled={isLoading}
              />
              {inputValue && (
                <Button
                  variant="ghost"
                  onClick={() => setInputValue('')}
                  className="max-w-full absolute right-1.5 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted/60 rounded-md transition-colors text-muted-foreground hover:text-foreground"
                  title="Clear input"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            <Button
              onClick={() => void handleSendMessage()}
              disabled={isLoading || !inputValue.trim()}
              size="sm"
              className="max-w-full h-11 px-6 bg-gradient-to-r from-primary to-primary-hover hover:from-primary-hover hover:to-primary text-primary-foreground font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 border border-primary/20 send-button-gradient"
            >
              {isLoading ? (
                <div className="flex flex-wrap items-center gap-2">
                  <div className="w-3 h-3 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin"></div>
                  <span className="truncatelabel">Processing</span>
                </div>
              ) : (
                <span className="truncatelabel">Send</span>
              )}
            </Button>
          </div>
          {/* Performance Monitor temporarily disabled
          {showPerformanceMonitor && (
            <div className="mt-4 p-4 bg-muted rounded-lg border">
              <PerformanceMonitor
                responseTime={performanceMetrics.responseTime}
                accuracy={performanceMetrics.accuracy}
                tokensUsed={performanceMetrics.tokensUsed}
                audioProcessingTime={performanceMetrics.audioProcessingTime}
              />
            </div>
          )}
          */}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default ChatInterface;
