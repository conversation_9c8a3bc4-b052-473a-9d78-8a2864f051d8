/* Accessibility improvements for giki.ai
   Ensures WCAG 2.1 AA compliance */

/* Improved color contrast for navigation */
.left-nav-text-secondary {
  color: hsl(var(--giki-nav-text-secondary));
  /* Increase opacity for better contrast on dark backgrounds */
  opacity: 0.85;
}

.left-nav-text-secondary:hover {
  opacity: 1;
}

/* Trust indicators - Fix critical contrast issue */
.trust-indicators {
  color: hsl(210 10% 25%); /* Dark gray for proper contrast on white */
  font-weight: 500;
  font-size: 0.875rem;
}

.trust-indicators svg {
  color: hsl(210 10% 40%);
}

/* Enhanced focus indicators */
:focus-visible {
  outline: 2px solid hsl(var(--giki-primary));
  outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  background-color: hsl(var(--giki-primary));
  color: hsl(var(--giki-primary-foreground));
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  font-weight: 600;
  z-index: 999999;
  border-radius: 0.375rem;
}

.skip-link:focus {
  position: fixed;
  top: 0.5rem;
  left: 0.5rem;
  width: auto;
  height: auto;
  overflow: visible;
}

/* Improved button touch targets */
button,
a[role="button"],
.clickable {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Small buttons need padding adjustment */
.btn-sm {
  min-height: 36px;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

/* Icon-only buttons */
.btn-icon-only {
  padding: 0.625rem;
}

/* Progress step indicators */
.progress-step {
  position: relative;
  min-height: 44px;
  min-width: 44px;
}

.progress-step[aria-current="step"] {
  font-weight: 600;
  background-color: hsl(var(--giki-primary) / 0.1);
  border-color: hsl(var(--giki-primary));
  border-width: 2px;
}

.progress-step[aria-disabled="true"] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* File upload drop zone */
.dropzone[aria-dropeffect="move"] {
  border-color: hsl(var(--giki-primary));
  background-color: hsl(var(--giki-primary) / 0.05);
  border-width: 3px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .trust-indicators {
    color: #000;
    font-weight: 600;
  }
  
  .left-nav-text-secondary {
    opacity: 1;
  }
  
  :focus-visible {
    outline-width: 3px;
  }
  
  button,
  a[role="button"] {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-bounce,
  .animate-pulse,
  .animate-spin,
  .animate-ripple {
    animation: none;
  }
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Improved form field focus */
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid hsl(var(--giki-primary));
  outline-offset: 2px;
  border-color: hsl(var(--giki-primary));
}

/* Error states with icons */
.field-error {
  color: hsl(var(--destructive));
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Loading states */
[aria-busy="true"] {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

[aria-busy="true"]::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: hsl(var(--background) / 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Monospace for financial data */
.financial-amount {
  font-family: 'IBM Plex Mono', 'Consolas', 'Monaco', monospace;
  font-variant-numeric: tabular-nums;
  text-align: right;
}

/* Negative values */
.amount-negative {
  color: hsl(var(--destructive));
}

.amount-negative::before {
  content: '(';
}

.amount-negative::after {
  content: ')';
}

/* Data table improvements */
.data-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  background-color: hsl(var(--muted));
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tbody tr:hover {
  background-color: hsl(var(--accent));
}

.data-table tbody tr[aria-selected="true"] {
  background-color: hsl(var(--primary) / 0.1);
  border-left: 3px solid hsl(var(--primary));
}

/* Improved chat UI */
.chat-message[aria-live] {
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.chat-message-user {
  background-color: hsl(var(--primary) / 0.1);
  margin-left: 2rem;
}

.chat-message-ai {
  background-color: hsl(var(--muted));
  margin-right: 2rem;
}

.chat-typing-indicator {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem;
}

/* Keyboard navigation indicators */
.keyboard-navigable:focus {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
}

/* Improved contrast for muted text */
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
  /* Ensure minimum contrast ratio */
  opacity: 0.8;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
}

.status-success {
  color: hsl(var(--success));
}

.status-warning {
  color: hsl(var(--warning));
}

.status-error {
  color: hsl(var(--destructive));
}

/* Improved link styling */
a:not(.btn):not([role="button"]) {
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

a:not(.btn):not([role="button"]):hover {
  text-decoration-thickness: 2px;
}

a:not(.btn):not([role="button"]):focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  text-decoration: none;
}