/**
 * Auth Store Tests
 * Critical authentication state management tests for production security
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import useAuthStore from './authStore';
import {
  setAccessToken,
  setRefreshToken,
  clearTokens,
  getAccessToken,
  getRefreshToken,
} from '@/features/auth/services/auth';
import {
  refreshToken,
  logout as authServiceLogout,
} from '@/features/auth/services/authService';
import { ErrorType, createAuthenticationError } from '@/shared/types/errors';

// Mock auth services
vi.mock('@/features/auth/services/auth', () => ({
  setAccessToken: vi.fn(),
  setRefreshToken: vi.fn(),
  clearTokens: vi.fn(),
  getAccessToken: vi.fn(),
  getRefreshToken: vi.fn(),
}));

vi.mock('@/features/auth/services/authService', () => ({
  refreshToken: vi.fn(),
  logout: vi.fn(),
}));

vi.mock('@/shared/types/errors', async () => {
  const actual = await vi.importActual('@/shared/types/errors');
  return {
    ...actual,
    createAuthenticationError: vi.fn(),
  };
});

describe('Auth Store - Production Authentication State', () => {
  const mockSetAccessToken = vi.mocked(setAccessToken);
  const mockSetRefreshToken = vi.mocked(setRefreshToken);
  const mockClearTokens = vi.mocked(clearTokens);
  const mockGetAccessToken = vi.mocked(getAccessToken);
  const mockGetRefreshToken = vi.mocked(getRefreshToken);
  const mockRefreshToken = vi.mocked(refreshToken);
  const mockAuthServiceLogout = vi.mocked(authServiceLogout);
  const mockCreateAuthenticationError = vi.mocked(createAuthenticationError);

  // Valid JWT token for testing (encoded payload: {"sub":"user123","tenant_id":1,"exp":9999999999})
  const validToken =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9?.eyJzdWIiOiJ1c2VyMTIzIiwidGVuYW50X2lkIjoxLCJleHAiOjk5OTk5OTk5OTl9?.invalidSignature';
  const validRefreshToken = 'refresh_token_123';

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store state
    useAuthStore.setState({
      token: null,
      refreshToken: null,
      userId: null,
      tenantId: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      errorDetails: null,
    });

    mockCreateAuthenticationError.mockImplementation((message) => ({
      type: ErrorType.AUTHENTICATION,
      message,
      statusCode: 401,
    }));
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('initializes with tokens from localStorage', () => {
      mockGetAccessToken.mockReturnValueOnce(validToken);
      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);

      // Create new store instance to test initialization
      const { getState: _getState } = useAuthStore;

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(mockGetRefreshToken).toHaveBeenCalled();
    });

    it('initializes as unauthenticated when no tokens exist', () => {
      mockGetAccessToken.mockReturnValueOnce(null);
      mockGetRefreshToken.mockReturnValueOnce(null);

      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.userId).toBeNull();
      expect(state.tenantId).toBeNull();
    });
  });

  describe('Login Method', () => {
    it('successfully processes valid tokens', async () => {
      const tokens = {
        accessToken: validToken,
        refreshToken: validRefreshToken,
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(mockSetAccessToken).toHaveBeenCalledWith(validToken);
      expect(mockSetRefreshToken).toHaveBeenCalledWith(validRefreshToken);
      expect(state.isAuthenticated).toBe(true);
      expect(state.token).toBe(validToken);
      expect(state.refreshToken).toBe(validRefreshToken);
      expect(state.userId).toBe('user123');
      expect(state.tenantId).toBe('1');
      expect(state.error).toBeNull();
    });

    it('handles login without refresh token', async () => {
      const tokens = {
        accessToken: validToken,
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(mockSetAccessToken).toHaveBeenCalledWith(validToken);
      expect(mockSetRefreshToken).not.toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(true);
      expect(state.refreshToken).toBeNull();
    });

    it('throws error when access token is missing', async () => {
      const tokens = {
        accessToken: '',
      };

      const mockError = {
        type: ErrorType.AUTHENTICATION,
        message: 'Access token is missing.',
        statusCode: 401,
      };
      mockCreateAuthenticationError.mockReturnValueOnce(mockError);

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Failed to process login tokens.');
      expect(state.errorDetails).toEqual(mockError);
    });

    it('handles invalid JWT token format', async () => {
      const tokens = {
        accessToken: 'invalid?.jwt?.token',
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Failed to process login tokens.');
    });

    it('validates essential token fields', async () => {
      // Token without required fields (no sub or exp)
      const invalidToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9?.eyJ0ZW5hbnRfaWQiOjF9?.invalidSignature';
      const tokens = {
        accessToken: invalidToken,
      };

      const mockError = {
        type: ErrorType.AUTHENTICATION,
        message: 'Invalid token data received.',
        statusCode: 401,
      };
      mockCreateAuthenticationError.mockReturnValueOnce(mockError);

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(false);
      expect(state.errorDetails).toEqual(mockError);
    });
  });

  describe('Logout Method', () => {
    it('clears all auth data and calls auth service', () => {
      // Set initial authenticated state
      useAuthStore.setState({
        token: validToken,
        refreshToken: validRefreshToken,
        userId: 'user123',
        tenantId: '1',
        isAuthenticated: true,
      });

      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);

      const { logout } = useAuthStore.getState();
      logout();

      const state = useAuthStore.getState();

      expect(mockAuthServiceLogout).toHaveBeenCalledWith(validRefreshToken);
      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.userId).toBeNull();
      expect(state.tenantId).toBeNull();
      expect(state.error).toBeNull();
    });

    it('handles logout without refresh token', () => {
      useAuthStore.setState({
        token: validToken,
        isAuthenticated: true,
      });

      mockGetRefreshToken.mockReturnValueOnce(null);

      const { logout } = useAuthStore.getState();
      logout();

      expect(mockAuthServiceLogout).not.toHaveBeenCalled();
      expect(mockClearTokens).toHaveBeenCalled();
    });
  });

  describe('Check Auth Method', () => {
    it('validates and sets auth state for valid token', () => {
      mockGetAccessToken.mockReturnValueOnce(validToken);
      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);

      const { checkAuth } = useAuthStore.getState();
      checkAuth();

      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(true);
      expect(state.userId).toBe('user123');
      expect(state.tenantId).toBe('1');
      expect(state.error).toBeNull();
    });

    it('handles expired token by attempting refresh', () => {
      // Expired token (exp: 1000000000 is in the past)
      const expiredToken =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9?.eyJzdWIiOiJ1c2VyMTIzIiwidGVuYW50X2lkIjoxLCJleHAiOjEwMDAwMDAwMDB9?.invalidSignature';

      mockGetAccessToken.mockReturnValueOnce(expiredToken);
      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);

      const { checkAuth } = useAuthStore.getState();
      checkAuth();

      // Since refreshTokens is async and we can't easily await it in checkAuth,
      // we verify that the refresh attempt was initiated
      expect(mockGetRefreshToken).toHaveBeenCalled();
    });

    it('clears auth state for invalid token', () => {
      const invalidToken = 'invalid.token';
      mockGetAccessToken.mockReturnValueOnce(invalidToken);

      const { checkAuth } = useAuthStore.getState();
      checkAuth();

      const state = useAuthStore.getState();

      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
    });

    it('handles missing token gracefully', () => {
      mockGetAccessToken.mockReturnValueOnce(null);
      mockGetRefreshToken.mockReturnValueOnce(null);

      const { checkAuth } = useAuthStore.getState();
      checkAuth();

      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
    });

    it('handles token with missing essential fields', () => {
      const tokenWithoutSub =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9?.eyJ0ZW5hbnRfaWQiOjEsImV4cCI6OTk5OTk5OTk5OX0?.invalidSignature';

      mockGetAccessToken.mockReturnValueOnce(tokenWithoutSub);
      mockCreateAuthenticationError.mockReturnValueOnce({
        type: ErrorType.AUTHENTICATION,
        message: 'Invalid session token data.',
        statusCode: 401,
      });

      const { checkAuth } = useAuthStore.getState();
      checkAuth();

      const state = useAuthStore.getState();

      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Invalid session token.');
    });
  });

  describe('Refresh Tokens Method', () => {
    it('successfully refreshes tokens', async () => {
      const newTokens = {
        accessToken: validToken,
        refreshToken: 'new_refresh_token',
      };

      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);
      mockRefreshToken.mockResolvedValueOnce(newTokens);

      const { refreshTokens } = useAuthStore.getState();
      const result = await refreshTokens();

      const state = useAuthStore.getState();

      expect(mockRefreshToken).toHaveBeenCalledWith(validRefreshToken);
      expect(mockSetAccessToken).toHaveBeenCalledWith(newTokens.accessToken);
      expect(mockSetRefreshToken).toHaveBeenCalledWith(newTokens.refreshToken);
      expect(result).toBe(true);
      expect(state.isAuthenticated).toBe(true);
      expect(state.token).toBe(newTokens.accessToken);
      expect(state.refreshToken).toBe(newTokens.refreshToken);
    });

    it('handles refresh without new refresh token', async () => {
      const newTokens = {
        accessToken: validToken,
      };

      mockGetRefreshToken
        .mockReturnValueOnce(validRefreshToken) // For initial check
        .mockReturnValueOnce(validRefreshToken); // For fallback in set
      mockRefreshToken.mockResolvedValueOnce(newTokens);

      const { refreshTokens } = useAuthStore.getState();
      await refreshTokens();

      const state = useAuthStore.getState();

      expect(mockSetAccessToken).toHaveBeenCalledWith(newTokens.accessToken);
      expect(state.refreshToken).toBe(validRefreshToken); // Keeps existing refresh token
    });

    it('throws error when no refresh token available', async () => {
      mockGetRefreshToken.mockReturnValueOnce(null);
      const mockError = {
        type: ErrorType.AUTHENTICATION,
        message: 'No refresh token available',
        statusCode: 401,
      };
      mockCreateAuthenticationError.mockReturnValueOnce(mockError);

      const { refreshTokens } = useAuthStore.getState();

      await expect(refreshTokens()).rejects.toEqual(mockError);

      expect(mockCreateAuthenticationError).toHaveBeenCalledWith(
        'No refresh token available',
      );
    });

    it('handles refresh service failure', async () => {
      const serviceError = new Error('Refresh failed');

      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);
      mockRefreshToken.mockRejectedValueOnce(serviceError);
      mockCreateAuthenticationError.mockReturnValueOnce({
        type: ErrorType.AUTHENTICATION,
        message: 'Refresh failed',
        statusCode: 401,
      });

      const { refreshTokens } = useAuthStore.getState();

      await expect(refreshTokens()).rejects.toMatchObject({
        type: ErrorType.AUTHENTICATION,
        message: 'Refresh failed',
      });

      const state = useAuthStore.getState();

      expect(mockClearTokens).toHaveBeenCalled();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Session expired. Please log in again.');
    });

    it('handles AppError from refresh service', async () => {
      const appError = {
        type: ErrorType.AUTHENTICATION,
        message: 'Token expired',
        statusCode: 401,
      };

      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);
      mockRefreshToken.mockRejectedValueOnce(appError);

      const { refreshTokens } = useAuthStore.getState();

      await expect(refreshTokens()).rejects.toEqual(appError);

      const state = useAuthStore.getState();

      expect(state.errorDetails).toEqual(appError);
      expect(state.error).toBe('Session expired. Please log in again.');
    });
  });

  describe('Register Method', () => {
    it('clears loading and error state', async () => {
      useAuthStore.setState({
        isLoading: true,
        error: 'Previous error',
        errorDetails: { type: ErrorType.VALIDATION, message: 'Previous error' },
      });

      const { register } = useAuthStore.getState();
      await register();

      const state = useAuthStore.getState();

      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state.errorDetails).toBeNull();
    });
  });

  describe('JWT Token Parsing', () => {
    it('correctly parses valid JWT tokens', async () => {
      const tokenWithTid =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9?.eyJzdWIiOiJ1c2VyNDU2IiwidGlkIjoidGVuYW50XzEyMyIsImV4cCI6OTk5OTk5OTk5OX0?.invalidSignature';

      const tokens = {
        accessToken: tokenWithTid,
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(state.userId).toBe('user456');
      expect(state.tenantId).toBe('tenant_123'); // Uses tid field
    });

    it('handles malformed JWT tokens gracefully', async () => {
      const malformedToken = 'not?.a?.jwt';

      const tokens = {
        accessToken: malformedToken,
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      const state = useAuthStore.getState();

      expect(state.isAuthenticated).toBe(false);
      expect(mockClearTokens).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('creates consistent error objects', async () => {
      const mockError = {
        type: ErrorType.AUTHENTICATION,
        message: 'Test error',
        statusCode: 401,
      };
      mockCreateAuthenticationError.mockReturnValueOnce(mockError);

      const tokens = {
        accessToken: '',
      };

      const { login } = useAuthStore.getState();
      await login(tokens);

      expect(mockCreateAuthenticationError).toHaveBeenCalledWith(
        'Access token is missing.',
      );
    });

    it('handles unknown error types in refresh', async () => {
      const unknownError = 'String error';

      mockGetRefreshToken.mockReturnValueOnce(validRefreshToken);
      mockRefreshToken.mockRejectedValueOnce(unknownError);
      mockCreateAuthenticationError.mockReturnValueOnce({
        type: ErrorType.AUTHENTICATION,
        message: 'Session expired. Please log in again.',
        statusCode: 401,
      });

      const { refreshTokens } = useAuthStore.getState();

      await expect(refreshTokens()).rejects.toMatchObject({
        type: ErrorType.AUTHENTICATION,
      });

      expect(mockCreateAuthenticationError).toHaveBeenCalledWith(
        'Session expired. Please log in again.',
      );
    });
  });

  describe('State Consistency', () => {
    it('maintains consistent state during login process', async () => {
      const tokens = {
        accessToken: validToken,
        refreshToken: validRefreshToken,
      };

      const { login } = useAuthStore.getState();

      // Check loading state is set during login
      const loginPromise = login(tokens);

      // Login process should complete successfully
      await loginPromise;

      const finalState = useAuthStore.getState();
      expect(finalState.isLoading).toBe(false);
      expect(finalState.isAuthenticated).toBe(true);
    });

    it('ensures logout completely clears sensitive data', () => {
      // Set a fully authenticated state with potential sensitive data
      useAuthStore.setState({
        token: validToken,
        refreshToken: validRefreshToken,
        userId: 'user123',
        tenantId: '1',
        isAuthenticated: true,
        error: 'Previous error',
        errorDetails: { type: ErrorType.VALIDATION, message: 'Previous error' },
      });

      const { logout } = useAuthStore.getState();
      logout();

      const state = useAuthStore.getState();

      // Verify all sensitive data is cleared
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.userId).toBeNull();
      expect(state.tenantId).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
      // Note: errorDetails might be preserved for debugging, but sensitive auth data is cleared
    });
  });
});
