/**
 * API Client Tests
 * Core API communication service tests
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import ApiClient, { apiClient } from './apiClient';

// Mock the auth module
vi.mock('@/features/auth/services/auth', () => ({
  getAccessToken: vi.fn(),
  getRefreshToken: vi.fn(),
  setAccessToken: vi.fn(),
  setRefreshToken: vi.fn(),
  clearTokens: vi.fn(),
}));

// Mock the auth service
vi.mock('@/features/auth/services/authService', () => ({
  refreshToken: vi.fn(),
}));

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('ApiClient', () => {
  let client: ApiClient;

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
    client = new ApiClient('http://test-api.com');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic HTTP Methods', () => {
    it('makes GET requests correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ data: 'test' }),
      });

      const result = await client.get('/test', { requiresAuth: false });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test',
        expect.objectContaining({
          method: 'GET',
          credentials: 'include',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Accept: 'application/json',
          }),
        }),
      );
      expect(result.data).toEqual({ data: 'test' });
      expect(result.status).toBe(200);
    });

    it('makes POST requests with body', async () => {
      const testData = { name: 'test', value: 123 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ id: 1, ...testData }),
      });

      const result = await client.post('/test', testData, {
        requiresAuth: false,
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData),
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        }),
      );
      expect(result.data).toEqual({ id: 1, ...testData });
    });

    it('makes PUT requests', async () => {
      const updateData = { name: 'updated' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => updateData,
      });

      const result = await client.put('/test/1', updateData, {
        requiresAuth: false,
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData),
        }),
      );
      expect(result.data).toEqual(updateData);
    });

    it('makes DELETE requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 204,
        headers: new Headers(),
      });

      const result = await client.delete('/test/1', { requiresAuth: false });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test/1',
        expect.objectContaining({
          method: 'DELETE',
        }),
      );
      expect(result.status).toBe(204);
    });
  });

  describe('Query Parameters', () => {
    it('builds URLs with query parameters correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ data: [] }),
      });

      await client.get('/test', {
        requiresAuth: false,
        params: {
          page: 1,
          limit: 10,
          active: true,
          filter: 'test value',
        },
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test?page=1&limit=10&active=true&filter=test%20value',
        expect.any(Object),
      );
    });

    it('ignores null and undefined parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ data: [] }),
      });

      await client.get('/test', {
        requiresAuth: false,
        params: {
          page: 1,
          limit: undefined,
          active: null,
          filter: 'test',
        },
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/test?page=1&filter=test',
        expect.any(Object),
      );
    });
  });

  describe('Error Handling', () => {
    it('handles 400 validation errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({
          detail: 'Validation failed',
          errors: { field: ['Required'] },
        }),
      });

      await expect(
        client.get('/test', { requiresAuth: false }),
      ).rejects.toThrow('Validation failed');
    });

    it('handles 401 authentication errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ detail: 'Unauthorized' }),
      });

      await expect(
        client.get('/test', { requiresAuth: false }),
      ).rejects.toThrow('Authentication failed');
    });

    it('handles 404 not found errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ detail: 'Not found' }),
      });

      await expect(
        client.get('/test', { requiresAuth: false }),
      ).rejects.toThrow('The requested resource was not found');
    });

    it('handles 500 server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ detail: 'Server error' }),
      });

      await expect(
        client.get('/test', { requiresAuth: false }),
      ).rejects.toThrow('A server error occurred');
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Failed to fetch'));

      await expect(
        client.get('/test', { requiresAuth: false }),
      ).rejects.toThrow('Network error');
    });
  });

  describe('Special Request Types', () => {
    it('handles form data uploads', async () => {
      const formData = new FormData();
      formData.append('file', new Blob(['test']), 'test.txt');

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ uploaded: true }),
      });

      const result = await client.postFormData('/upload', formData, {
        requiresAuth: false,
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/upload',
        expect.objectContaining({
          method: 'POST',
          body: formData,
        }),
      );
      expect(result.data).toEqual({ uploaded: true });
    });

    it('handles form URL encoded requests', async () => {
      const data = { username: 'test', password: 'pass' };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ token: 'abc123' }),
      });

      const result = await client.postFormUrlEncoded('/auth/token', data, {
        requiresAuth: false,
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://test-api.com/auth/token',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded',
          }),
        }),
      );
      expect(result.data).toEqual({ token: 'abc123' });
    });
  });

  describe('Singleton Instance', () => {
    it('exports a singleton apiClient instance', () => {
      expect(apiClient).toBeDefined();
      expect(apiClient).toBeInstanceOf(ApiClient);
    });

    it('uses environment variable for base URL', () => {
      // The singleton uses the environment variable
      expect(apiClient.getBaseUrl()).toBe('http://localhost:8000');
    });
  });
});
