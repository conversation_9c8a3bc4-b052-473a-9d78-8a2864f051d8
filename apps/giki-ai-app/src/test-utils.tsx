/**
 * Test utilities for React components
 * Provides testing wrappers and utilities for financial UI components
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Mock the auth context for testing
const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="mock-auth-provider">{children}</div>;
};

// Create a wrapper component for testing
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <MockAuthProvider>{children}</MockAuthProvider>
    </BrowserRouter>
  );
};

// Custom render function with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
): RenderResult => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Mock transaction data for testing
export const mockTransaction = {
  id: '1',
  date: '2024-01-15',
  description: 'STARBUCKS COFFEE #12345',
  amount: -4.95,
  account: 'Credit Card',
  category_id: 1,
  category_path: ['Dining', 'Coffee'],
  ai_suggested_category_id: 1,
  ai_suggested_category_path: ['Dining', 'Coffee'],
  ai_category_confidence: 0.89,
  is_categorized: true,
  is_user_modified: false,
  user_corrected: false,
  entity_id: 'starbucks',
  upload_id: 'upload-123',
  status: 'processed',
};

// Mock file for testing file uploads
export const mockFile = new File(['test content'], 'test.csv', {
  type: 'text/csv',
});

// Mock Excel file
export const mockExcelFile = new File(['test excel'], 'test.xlsx', {
  type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
});

// Mock category data
export const mockCategory = {
  id: 1,
  name: 'Dining',
  parent_id: null,
  level: 1,
  tenant_id: 1,
  gl_code: '6100',
  gl_account_name: 'Meals & Entertainment',
  gl_account_type: 'Expense',
  color: '#FF6B6B',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Mock user data
export const mockUser = {
  id: 1,
  email: '<EMAIL>',
  full_name: 'Test User',
  is_active: true,
  is_admin: false,
  tenant_id: 1,
};

// Mock column mapping data
export const mockColumnMapping = {
  date_column: 'Date',
  description_column: 'Description',
  amount_column: 'Amount',
  category_column: 'Category',
  vendor_column: null,
};

// Financial test utilities
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Test assertions helpers
export const expectToBeFinanciallyValid = (amount: string | number) => {
  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;
  expect(numericAmount).toBeTypeOf('number');
  expect(isNaN(numericAmount)).toBeFalsy();
  expect(isFinite(numericAmount)).toBeTruthy();
};
