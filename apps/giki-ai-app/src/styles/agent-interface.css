/* Agent Interface Professional Styling */

/* Glass morphism effects for chat interface */
.agent-glass-panel {
  background: hsla(var(--giki-card-bg), 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid hsla(var(--giki-border-primary), 0.2);
}

/* Message animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-bubble {
  animation: messageSlideIn 0.3s ease-out;
}

/* Gradient shift animation */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Professional loading dots */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Smooth scrollbar styling */
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: hsla(var(--giki-bg-muted), 0.3);
  border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: hsla(var(--giki-primary), 0.3);
  border-radius: 3px;
  transition: background 0.2s;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsla(var(--giki-primary), 0.5);
}

/* Message bubble hover effects */
.message-bubble-agent:hover {
  transform: translateX(2px);
  box-shadow: 0 4px 20px hsla(var(--giki-primary), 0.1);
}

.message-bubble-user:hover {
  transform: translateX(-2px);
  box-shadow: 0 4px 20px hsla(var(--giki-primary), 0.15);
}

/* Input field focus glow */
.giki-chat-input:focus {
  box-shadow: 
    0 0 0 3px hsla(var(--giki-primary), 0.1),
    0 2px 8px hsla(var(--giki-primary), 0.2);
}

/* Quick action button hover */
.quick-action-pill {
  position: relative;
  overflow: hidden;
}

.quick-action-pill::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    hsla(var(--giki-primary), 0.2),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.quick-action-pill:hover::before {
  transform: translateX(100%);
}

/* Status indicator pulse */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-indicator {
  animation: statusPulse 2s ease-in-out infinite;
}

/* Professional send button */
.send-button-gradient {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.send-button-gradient::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(
    45deg,
    hsl(var(--giki-brand-green-dark)),
    hsl(var(--giki-brand-blue-dark)),
    hsl(var(--giki-brand-purple-dark)),
    hsl(var(--giki-brand-pink-dark))
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.send-button-gradient:hover::before {
  opacity: 1;
}

.send-button-gradient > * {
  position: relative;
  z-index: 1;
}