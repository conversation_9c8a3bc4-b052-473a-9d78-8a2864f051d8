/* GIKI.AI DESIGN TOKENS - Single Source of Truth
 * Version: 2.1
 * Last Updated: 2025-01-27
 * Purpose: Consolidated design system tokens for consistent UI/UX
 *
 * === SYSTEMATIC COLOR MAPPING PHILOSOPHY ===
 *
 * Our design system is built on a systematic mapping between the light colors
 * in our logo and their corresponding dark variants used throughout the application.
 *
 * MAPPING PRINCIPLE:
 * The relationship between logo light green (140 69% 83%) and brand dark green
 * (140 60% 25%) establishes the foundation for all color mappings in our system.
 *
 * TRANSFORMATION FORMULA:
 * - Hue: Preserved exactly (maintains color identity)
 * - Saturation: Reduced from ~69% to ~60-70% (professional appearance)
 * - Lightness: Dramatically reduced from ~83-85% to ~25% (dark variants)
 *
 * COMPLETE MAPPING SYSTEM:
 * Logo Light → App Dark
 * • Green:  140 69% 83% (#B4F0C5) → 140 60% 25% (#1A5F2F)
 * • Pink:   0 69% 85% (#FFB6B6)  → 0 60% 25% (#5F1A1A)
 * • Blue:   210 100% 85% (#B6E0FF) → 210 70% 25% (#1A3F5F)
 * • Purple: 270 69% 85% (#E0B6FF) → 270 60% 25% (#3F1A5F)
 *
 * GRADIENT IMPLEMENTATION:
 * All gradients use equal contributions (0%, 33%, 66%, 100%) from each mapped
 * color, creating a unified design language that flows from logo to UI elements.
 *
 * This systematic approach ensures:
 * 1. Brand consistency between logo and application
 * 2. Professional dark variants suitable for UI elements
 * 3. Cohesive gradient systems across all components
 * 4. Scalable color system for future brand extensions
 *
 * For implementation guidelines, see: ./component-style-guide.md
 */

:root {
  /* === BRAND COLORS (Giki.ai Inspired Excel-like Palette) === */
  /* Primary Brand Color: Giki.ai Dark Green - Professional & Trustworthy */
  --giki-primary: 158 25% 25%; /* #295343 - Giki.ai brand dark green */
  --giki-primary-hover: 158 25% 22%; /* #22463A - Darker hover */
  --giki-primary-active: 158 25% 28%; /* #2F5D4B - Lighter active */
  --giki-primary-foreground: 0 0% 100%; /* #FFFFFF */

  /* Logo-inspired Accent Colors - From Giki.ai SVG Logo */
  --giki-brand-green: 140 69% 83%; /* #B4F0C5 - Logo light green */
  --giki-brand-green-hover: 140 69% 78%; /* #9FE8B7 - Darker green */
  --giki-brand-green-active: 140 69% 73%; /* #8AE0A9 - Active green */
  --giki-brand-green-foreground: 158 25% 15%; /* #1A3328 - Dark green text */
  --giki-brand-green-dark: 140 60% 25%; /* #1A5F2F - Proper dark green for gradients */

  --giki-brand-pink: 0 69% 85%; /* #FFB6B6 - Logo coral/pink */
  --giki-brand-pink-hover: 0 69% 80%; /* #FF9F9F - Darker pink */
  --giki-brand-pink-active: 0 69% 75%; /* #FF8888 - Active pink */
  --giki-brand-pink-foreground: 0 50% 20%; /* #803333 - Dark text on pink */
  --giki-brand-pink-dark: 0 60% 25%; /* #5F1A1A - Proper dark red/pink for gradients */

  --giki-brand-blue: 210 100% 85%; /* #B6E0FF - Logo light blue */
  --giki-brand-blue-hover: 210 100% 80%; /* #9FD4FF - Darker blue */
  --giki-brand-blue-active: 210 100% 75%; /* #88C8FF - Active blue */
  --giki-brand-blue-foreground: 210 50% 20%; /* #336699 - Dark blue text */
  --giki-brand-blue-dark: 210 70% 25%; /* #1A3F5F - Proper dark blue for gradients */

  --giki-brand-purple: 270 69% 85%; /* #E0B6FF - Logo light purple */
  --giki-brand-purple-hover: 270 69% 80%; /* #D59FFF - Darker purple */
  --giki-brand-purple-active: 270 69% 75%; /* #CA88FF - Active purple */
  --giki-brand-purple-foreground: 270 50% 20%; /* #663399 - Dark purple text */
  --giki-brand-purple-dark: 270 60% 25%; /* #3F1A5F - Proper dark purple for gradients */

  /* Excel-inspired Professional Colors */
  --giki-excel-green: 158 30% 35%; /* #3F6B57 - Excel-like green */
  --giki-excel-green-hover: 158 30% 32%; /* #396152 - Hover */
  --giki-excel-green-active: 158 30% 38%; /* #45754C - Active */
  --giki-excel-green-foreground: 0 0% 100%; /* #FFFFFF */

  /* === SEMANTIC COLORS (Functional) === */
  /* Success States - Using Excel-inspired green */
  --giki-success: 158 40% 40%; /* #4A7A66 - Excel-like success green */
  --giki-success-hover: 158 40% 35%; /* #3F6B57 - Darker hover */
  --giki-success-active: 158 40% 45%; /* #558975 - Lighter active */
  --giki-success-foreground: 0 0% 100%; /* #FFFFFF */

  /* Error States */
  --giki-destructive: 0 75% 50%; /* #DC2626 Professional Error Red */
  --giki-destructive-hover: 0 75% 45%; /* #C41E1E */
  --giki-destructive-active: 0 75% 40%; /* #AC1616 */
  --giki-destructive-foreground: 0 0% 100%; /* #FFFFFF */

  /* Warning States */
  --giki-warning: 35 85% 50%; /* #E67E22 Professional Warning Orange */
  --giki-warning-hover: 35 85% 45%; /* #D4711F */
  --giki-warning-active: 35 85% 40%; /* #C2641C */
  --giki-warning-foreground: 0 0% 100%; /* #FFFFFF */

  /* Info States */
  --giki-info: 207 100% 82%; /* #A9D0F5 - Using brand blue for info */
  --giki-info-hover: 207 100% 77%; /* #94C5F0 */
  --giki-info-active: 207 100% 72%; /* #7FB9EB */
  --giki-info-foreground: 210 10% 15%; /* #1E293B */

  /* === NEUTRAL PALETTE (Professional Grays) === */
  --giki-neutral-white: 0 0% 100%; /* #FFFFFF */
  --giki-neutral-50: 210 40% 98%; /* #f8fafc Tailwind slate-50 */
  --giki-neutral-100: 210 40% 96%; /* #f1f5f9 Tailwind slate-100 */
  --giki-neutral-200: 210 40% 94%; /* #e2e8f0 Tailwind slate-200 */
  --giki-neutral-300: 215 25% 87%; /* #cbd5e1 Tailwind slate-300 */
  --giki-neutral-400: 215 20% 65%; /* #94a3b8 Tailwind slate-400 */
  --giki-neutral-500: 215 16% 47%; /* #64748b Tailwind slate-500 */
  --giki-neutral-600: 215 19% 35%; /* #475569 Tailwind slate-600 */
  --giki-neutral-700: 215 25% 27%; /* #334155 Tailwind slate-700 */
  --giki-neutral-800: 215 28% 17%; /* #1e293b Tailwind slate-800 */
  --giki-neutral-900: 215 39% 11%; /* #0f172a Tailwind slate-900 */
  --giki-neutral-950: 215 39% 7%; /* #020617 Tailwind slate-950 */

  /* === TEXT COLORS (WCAG AA Compliant & Standardized) === */
  --giki-text-primary: var(--giki-neutral-900); /* Maximum contrast - main content */
  --giki-text-secondary: var(--giki-neutral-700); /* High contrast - secondary content */
  --giki-text-muted: var(--giki-neutral-600); /* Improved contrast - captions, labels */
  --giki-text-disabled: var(--giki-neutral-500); /* Better contrast - disabled states */
  --giki-text-on-dark: var(--giki-neutral-white); /* White on dark backgrounds */
  --giki-text-on-primary: var(--giki-primary-foreground); /* White on primary */
  --giki-text-success: var(--giki-success); /* Success messages */
  --giki-text-error: var(--giki-destructive); /* Error messages */
  --giki-text-warning: var(--giki-warning); /* Warning messages */
  --giki-text-link: var(--giki-primary); /* Links and interactive text */
  --giki-text-link-hover: var(--giki-primary-hover); /* Link hover state */

  /* === BACKGROUND COLORS === */
  --giki-bg-primary: var(--giki-neutral-white); /* Main background */
  --giki-bg-secondary: var(--giki-neutral-50); /* Secondary background */
  --giki-bg-tertiary: var(--giki-neutral-100); /* Tertiary background */
  --giki-bg-muted: var(--giki-neutral-200); /* Muted background */
  --giki-bg-accent: var(--giki-neutral-100); /* Accent background */

  /* === BORDER COLORS === */
  --giki-border-primary: var(--giki-neutral-200); /* Primary borders */
  --giki-border-secondary: var(--giki-neutral-300); /* Secondary borders */
  --giki-border-muted: var(--giki-neutral-100); /* Muted borders */
  --giki-border-focus: var(--giki-primary); /* Focus ring color */
  
  /* === COMPONENT-SPECIFIC TOKENS === */
  /* Cards */
  --giki-card-bg: var(--giki-bg-primary);
  --giki-card-border: var(--giki-border-primary);
  --giki-card-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 1px 3px 0 rgba(0, 0, 0, 0.04);
  --giki-card-shadow-premium: 0 4px 20px 0 rgba(0, 0, 0, 0.12), 0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 1px 3px 0 rgba(0, 0, 0, 0.04);
  --giki-card-shadow-elevated: 0 8px 32px 0 rgba(0, 0, 0, 0.16), 0 4px 16px 0 rgba(0, 0, 0, 0.12), 0 2px 8px 0 rgba(0, 0, 0, 0.08);

  /* Inputs */
  --giki-input-bg: var(--giki-bg-primary);
  --giki-input-border: var(--giki-border-secondary);
  --giki-input-border-focus: var(--giki-border-focus);
  --giki-input-text: var(--giki-text-primary);
  --giki-input-placeholder: var(--giki-neutral-600); /* Better contrast for placeholders */

  /* Navigation */
  --giki-nav-bg: 158 25% 20%; /* #1F3D30 - Dark green panel background */
  --giki-nav-border: 158 25% 15%; /* #193328 - Darker border */
  --giki-nav-text: 0 0% 98%; /* #FAFAFA - Very light text on dark */
  --giki-nav-text-active: 0 0% 100%; /* #FFFFFF - White for active */
  --giki-nav-text-secondary: 0 0% 90%; /* #E6E6E6 - Better contrast secondary text */
  --giki-nav-icon-inactive: 0 0% 85%; /* #D9D9D9 - Better contrast for inactive icons */
  --giki-nav-icon-hover: 0 0% 100%; /* #FFFFFF - Pure white on hover */
  --giki-nav-bg-hover: 158 25% 25%; /* #295343 - Hover background */
  --giki-nav-bg-active: var(--giki-primary);
  --giki-nav-item-active: var(--giki-primary);
  --giki-nav-item-hover: var(--giki-nav-bg-hover);

  /* Agent Panel */
  --giki-agent-bg: var(--giki-bg-primary);
  --giki-agent-border: var(--giki-border-primary);
  --giki-agent-accent: var(--giki-brand-purple);
  --giki-agent-accent-foreground: var(--giki-brand-purple-foreground);

  /* === SPACING SCALE === */
  --giki-space-0: 0;
  --giki-space-1: 0.25rem; /* 4px */
  --giki-space-2: 0.5rem; /* 8px */
  --giki-space-3: 0.75rem; /* 12px */
  --giki-space-4: 1rem; /* 16px */
  --giki-space-5: 1.25rem; /* 20px */
  --giki-space-6: 1.5rem; /* 24px */
  --giki-space-8: 2rem; /* 32px */
  --giki-space-10: 2.5rem; /* 40px */
  --giki-space-12: 3rem; /* 48px */
  --giki-space-16: 4rem; /* 64px */
  --giki-space-20: 5rem; /* 80px */
  --giki-space-24: 6rem; /* 96px */

  /* === BORDER RADIUS SCALE === */
  --giki-radius-none: 0;
  --giki-radius-sm: 0.25rem; /* 4px */
  --giki-radius-md: 0.375rem; /* 6px */
  --giki-radius-lg: 0.5rem; /* 8px */
  --giki-radius-xl: 0.75rem; /* 12px */
  --giki-radius-2xl: 1rem; /* 16px */
  --giki-radius-full: 9999px;

  /* === SHADOW SCALE === */
  --giki-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --giki-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --giki-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --giki-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --giki-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* === TYPOGRAPHY SYSTEM === */
  --giki-font-sans: system-ui, -apple-system, sans-serif;
  --giki-font-mono: ui-monospace, 'SF Mono', 'Consolas', monospace;
  
  /* Font family tokens for CSS usage */
  --giki-font-family-sans: var(--giki-font-sans);
  --giki-font-family-display: var(--giki-font-sans);
  --giki-font-family-mono: var(--giki-font-mono);
  
  /* Font Sizes - Comprehensive Scale */
  --giki-text-xs: 0.75rem;    /* 12px */
  --giki-text-sm: 0.875rem;   /* 14px */
  --giki-text-base: 1rem;     /* 16px */
  --giki-text-lg: 1.125rem;   /* 18px */
  --giki-text-xl: 1.25rem;    /* 20px */
  --giki-text-2xl: 1.5rem;    /* 24px */
  --giki-text-3xl: 1.875rem;  /* 30px */
  --giki-text-4xl: 2.25rem;   /* 36px */
  --giki-text-5xl: 3rem;      /* 48px */
  
  /* Font Weights */
  --giki-font-light: 300;
  --giki-font-normal: 400;
  --giki-font-medium: 500;
  --giki-font-semibold: 600;
  --giki-font-bold: 700;
  --giki-font-extrabold: 800;
  
  /* Font weight aliases for CSS usage */
  --giki-font-weight-light: var(--giki-font-light);
  --giki-font-weight-normal: var(--giki-font-normal);
  --giki-font-weight-medium: var(--giki-font-medium);
  --giki-font-weight-semibold: var(--giki-font-semibold);
  --giki-font-weight-bold: var(--giki-font-bold);
  --giki-font-weight-extrabold: var(--giki-font-extrabold);
  
  /* Line Heights */
  --giki-leading-tight: 1.25;
  --giki-leading-normal: 1.5;
  --giki-leading-relaxed: 1.75;

  /* === LAYOUT CONSTANTS === */
  --giki-layout-nav-width-collapsed: 4rem; /* 64px */
  --giki-layout-nav-width-expanded: 16rem; /* 256px */
  --giki-layout-agent-width-min: 20rem; /* 320px */
  --giki-layout-agent-width-max: 30rem; /* 480px */
  --giki-layout-header-height: 4rem; /* 64px */
  --giki-layout-content-padding: var(--giki-space-6); /* 24px */

  /* === Z-INDEX SCALE === */
  --giki-z-base: 0;
  --giki-z-dropdown: 10;
  --giki-z-sticky: 20;
  --giki-z-fixed: 30;
  --giki-z-modal-backdrop: 40;
  --giki-z-modal: 50;
  --giki-z-popover: 60;
  --giki-z-tooltip: 70;
  --giki-z-toast: 80;
  --giki-z-max: 9999;

  /* === ANIMATION TOKENS === */
  --giki-transition-fast: 150ms ease-in-out;
  --giki-transition-normal: 200ms ease-in-out;
  --giki-transition-slow: 300ms ease-in-out;

  /* Button ripple animation */
  --giki-ripple-duration: 600ms;

  /* === LEGACY COMPATIBILITY (Temporary) === */
  /* These map to the new tokens for backward compatibility */
  --color-primary: var(--giki-primary);
  --color-primary-foreground: var(--giki-primary-foreground);
  --color-primary-hover: var(--giki-primary-hover);
  --color-primary-active: var(--giki-primary-active);
  
  --color-background: var(--giki-bg-secondary);
  --color-foreground: var(--giki-text-primary);
  --color-card: var(--giki-card-bg);
  --color-card-foreground: var(--giki-text-primary);
  --color-border: var(--giki-border-primary);
  --color-input: var(--giki-input-border);
  --color-ring: var(--giki-border-focus);
  
  --color-muted: var(--giki-bg-muted);
  --color-muted-foreground: var(--giki-text-muted);
  --color-accent: var(--giki-brand-purple);
  --color-accent-foreground: var(--giki-brand-purple-foreground);
  
  --color-destructive: var(--giki-destructive);
  --color-destructive-foreground: var(--giki-destructive-foreground);
  --color-success: var(--giki-success);
  --color-success-foreground: var(--giki-success-foreground);
  --color-warning: var(--giki-warning);
  --color-warning-foreground: var(--giki-warning-foreground);
  
  --radius: var(--giki-radius-md);
  --shadow-excel-card: var(--giki-card-shadow);
}

/* === COMPONENT ANIMATIONS === */
@keyframes ripple {
  from {
    transform: scale(0);
    opacity: 0.6;
  }
  to {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ripple {
  animation: ripple var(--giki-ripple-duration) ease-out;
}
