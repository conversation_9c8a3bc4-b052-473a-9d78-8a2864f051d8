# Component Library Documentation

## Overview

This document provides comprehensive documentation for the Giki AI component library, focusing on the professional financial UI components and their usage patterns.

## Core Design Principles

1. **Financial-First Design**: All components are optimized for financial data display
2. **Excel-Inspired Patterns**: Familiar patterns for finance professionals
3. **Design Token System**: Consistent theming through CSS variables
4. **Accessibility**: WCAG 2.1 AA compliant components

## Component Categories

### 1. Excel-Inspired Components

#### ExcelCard
Professional card component with Excel-inspired styling.

```tsx
import { ExcelCard } from '@/shared/components/ui/excel-card';

// Basic usage
<ExcelCard
  title="Total Expenses"
  value={formatCurrency(12500)}
  icon={<TrendingUp />}
  trend="up"
  variant="default"
/>

// With footer actions
<ExcelCard
  title="Uncategorized"
  value="45 transactions"
  variant="warning"
  footer={
    <Button size="sm" variant="ghost">
      Review Now
    </Button>
  }
/>
```

**Props:**
- `title`: Card title
- `value`: Primary display value
- `icon`: Optional icon component
- `trend`: "up" | "down" | "neutral"
- `variant`: "default" | "success" | "warning" | "error"
- `footer`: Optional footer content

#### ExcelTable
Financial data table with Excel-like features.

```tsx
import { ExcelTable } from '@/shared/components/ui/excel-table';

<ExcelTable
  data={transactions}
  columns={[
    {
      header: 'Date',
      cell: (row) => formatDate(row.date),
      width: '120px'
    },
    {
      header: 'Amount',
      cell: (row) => formatCurrency(row.amount),
      className: 'text-financial text-right',
      width: '150px'
    }
  ]}
  onRowClick={(row) => handleRowClick(row)}
  striped
  hoverable
/>
```

**Features:**
- Sortable columns
- Row selection
- Financial number formatting
- Sticky headers
- Export functionality

### 2. Chart Components

#### SpendingTrendChart
Area/Line chart for financial trends with proper formatting.

```tsx
import { SpendingTrendChart } from '@/features/dashboard/components/SpendingTrendChart';

<SpendingTrendChart
  data={monthlyData}
  variant="area" // or "line"
  onViewDetails={() => navigateToDetails()}
/>
```

**Best Practices:**
- Always use `text-financial` class for currency displays
- Use semantic color tokens (success/destructive)
- Include tooltips with formatted values

### 3. Financial Display Components

#### Currency Display
Consistent currency formatting across the application.

```tsx
// Always use text-financial class
<span className="text-financial text-success">
  {formatCurrency(amount)}
</span>

// For tables with aligned numbers
<td className="text-financial tabular-nums text-right">
  {formatCurrency(row.amount)}
</td>
```

#### Progress Indicators
For categorization and processing status.

```tsx
<div className="w-full bg-muted rounded-full h-2">
  <div
    className="bg-primary h-2 rounded-full transition-all duration-300"
    style={{ width: `${percentage}%` }}
  />
</div>
```

### 4. Form Components

#### Financial Input Fields
Input fields optimized for financial data entry.

```tsx
<Input
  type="number"
  step="0.01"
  placeholder="0.00"
  className="text-financial text-right"
  onChange={(e) => handleAmountChange(e.target.value)}
/>
```

## Design Tokens

### Color Tokens
All colors must use CSS variables:

```css
/* Brand Colors */
--giki-brand-green: 142 71% 45%
--giki-brand-purple: 271 91% 65%
--giki-brand-blue: 217 91% 60%

/* Semantic Colors */
--giki-success: var(--giki-brand-green)
--giki-destructive: 0 84% 60%
--giki-warning: 25 95% 53%
--giki-info: var(--giki-brand-blue)
```

### Typography Classes

```css
/* Financial Text */
.text-financial {
  font-variant-numeric: tabular-nums;
  font-feature-settings: 'tnum' on;
}

/* Data Tables */
.text-data-table {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-variant-numeric: tabular-nums;
}
```

## Accessibility Guidelines

1. **Color Contrast**: Maintain WCAG AA contrast ratios
2. **Keyboard Navigation**: All interactive elements keyboard accessible
3. **Screen Readers**: Proper ARIA labels for financial data
4. **Focus Indicators**: Clear focus states for all inputs

## Migration Guide

### From Hardcoded Colors
```tsx
// Before
<span className="text-emerald-600">

// After
<span className="text-success">
```

### From Regular Tables to ExcelTable
```tsx
// Before
<table className="w-full">
  <thead>...</thead>
  <tbody>...</tbody>
</table>

// After
<ExcelTable
  data={data}
  columns={columns}
  className="w-full"
/>
```

### Adding Financial Formatting
```tsx
// Before
<span>{amount}</span>

// After
<span className="text-financial">{formatCurrency(amount)}</span>
```

## Component Checklist

Before creating or modifying a component:

- [ ] Uses design tokens for all colors
- [ ] Includes `text-financial` for currency displays
- [ ] Uses semantic color names (success/destructive)
- [ ] Follows Excel-inspired patterns where appropriate
- [ ] Includes proper TypeScript types
- [ ] Has keyboard navigation support
- [ ] Maintains proper contrast ratios
- [ ] Uses consistent spacing tokens

## Examples

### Complete Dashboard Card
```tsx
<ExcelCard
  title="Monthly Revenue"
  value={
    <span className="text-financial text-2xl font-bold">
      {formatCurrency(revenue)}
    </span>
  }
  icon={<TrendingUp className="w-5 h-5" />}
  trend={revenue > lastMonthRevenue ? 'up' : 'down'}
  variant={revenue > target ? 'success' : 'warning'}
  footer={
    <div className="flex items-center justify-between">
      <span className="text-sm text-muted-foreground">
        vs. {formatCurrency(lastMonthRevenue)} last month
      </span>
      <Button size="sm" variant="ghost">
        View Details
      </Button>
    </div>
  }
/>
```

### Financial Data Table
```tsx
<ExcelTable
  data={transactions}
  columns={[
    {
      header: 'Date',
      cell: (row) => formatDate(row.date),
      sortable: true
    },
    {
      header: 'Description',
      cell: (row) => row.description,
      className: 'max-w-[300px] truncate'
    },
    {
      header: 'Category',
      cell: (row) => (
        <Badge variant={row.category ? 'default' : 'secondary'}>
          {row.category || 'Uncategorized'}
        </Badge>
      )
    },
    {
      header: 'Amount',
      cell: (row) => (
        <span className={cn(
          'text-financial font-semibold',
          row.type === 'income' ? 'text-success' : 'text-destructive'
        )}>
          {row.type === 'income' ? '+' : '-'}
          {formatCurrency(Math.abs(row.amount))}
        </span>
      ),
      className: 'text-right',
      sortable: true
    }
  ]}
  onRowClick={(row) => handleTransactionClick(row)}
  striped
  hoverable
  className="border-border"
/>
```

## Version History

- v1.0.0 - Initial component library with Excel-inspired design
- v1.1.0 - Added financial formatting utilities
- v1.2.0 - Implemented complete design token system
- v1.3.0 - Added accessibility improvements