import React, { useState, useCallback, useEffect, Suspense } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';

// Shared Components and Providers
import {
  AppLayout,
  ErrorBoundary,
  Loading,
  ThemeProvider,
  ToastProvider,
  ToastViewport,
} from '@/shared/components';
import { PanelStateProvider } from '@/core/providers/PanelStateContext';
import { skipToMain } from '@/shared/utils/accessibility';
import '@/shared/styles/accessibility.css';

// Feature Imports using new structure
import { LoginPage, RegisterPage, useAuth } from '@/features/auth';

import { TransactionReviewPage } from '@/features/transactions';

import { WorkPage } from '@/features/files';

import { ReportsPage } from '@/features/reports';

import { DashboardPage } from '@/features/dashboard';

// Import real implementations
import { ColumnMappingModal } from '../../features/files';
import { SettingsPage } from '@/features/admin';
import { CategoryManagementPage } from '@/features/categories';
import { OnboardingPage } from '@/features/files';

// Import accuracy testing pages
import { AccuracyTestsPage } from '@/features/accuracy/pages/AccuracyTestsPage';
import { CreateAccuracyTestPage } from '@/features/accuracy/pages/CreateAccuracyTestPage';
import { TestResultsPage } from '@/features/accuracy/pages/TestResultsPage';

// Protected Route component
interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, checkAuth } = useAuth();

  React.useEffect(() => {
    checkAuth(); // Check auth status on component mount
  }, [checkAuth]);

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// Main App component with new domain-driven structure
function App() {
  const navigate = useNavigate();
  const { checkAuth } = useAuth();

  // Check authentication status on app load
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Add keyboard shortcut for skip to main
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt + M to skip to main content
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        skipToMain();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // State for Column Mapping Modal
  const [isMappingModalOpen, setIsMappingModalOpen] = useState(false);
  const [currentUploadId, setCurrentUploadId] = useState<string | null>(null);

  // Callback to update file processing results in upload component
  const [onFileProcessingComplete, setOnFileProcessingComplete] = useState<
    | ((
        uploadId: string,
        reportId: string | null,
        processingStats?: {
          totalRows?: number;
          successfulRows?: number;
          failedRows?: number;
          dataQualityScore?: number;
        },
      ) => void)
    | null
  >(null);

  // Handler for successful file upload with optional callback for processing completion
  const handleUploadSuccessInApp = (
    uploadId: string,
    processingCompleteCallback?: (
      uploadId: string,
      reportId: string | null,
      processingStats?: {
        totalRows?: number;
        successfulRows?: number;
        failedRows?: number;
        dataQualityScore?: number;
      },
    ) => void,
  ) => {
    console.log('🚀 handleUploadSuccess called with uploadId:', uploadId);
    setCurrentUploadId(uploadId);
    setIsMappingModalOpen(true);

    // Store the callback to use after processing is complete
    if (processingCompleteCallback) {
      setOnFileProcessingComplete(() => processingCompleteCallback);
    }
  };

  // Handler for successful mapping confirmation
  const handleMappingConfirm = useCallback(
    async (uploadId: string, mapping: Record<string, string | null>) => {
      if (!uploadId) {
        setIsMappingModalOpen(false);
        return;
      }

      try {
        // Import submitColumnMapping function
        const { submitColumnMapping } = await import(
          '@/features/files/services/fileService'
        );

        // Submit column mapping and process transactions
        const processingResult = await submitColumnMapping(uploadId, mapping);

        if ('type' in processingResult) {
          // Handle API error
          throw new Error(processingResult.message);
        }

        // Close modal first
        setIsMappingModalOpen(false);

        // Update file processing results if callback is available
        if (onFileProcessingComplete && processingResult.report_id) {
          // Calculate processing stats from response if available
          const processingStats = {
            totalRows: processingResult.records_processed || 0,
            successfulRows: processingResult.records_processed || 0,
            failedRows: 0, // Would need to calculate from errors if available
            dataQualityScore: processingResult.errors?.length === 0 ? 100 : 85, // Simple calculation
          };

          onFileProcessingComplete(
            uploadId,
            processingResult.report_id,
            processingStats,
          );
        }

        setCurrentUploadId(null);

        // Navigate to the transactions view
        navigate('/transactions');
      } catch (err) {
        console.error('Error processing column mapping:', err);
        setIsMappingModalOpen(false);
      }
    },
    [navigate, onFileProcessingComplete],
  );

  return (
    <ThemeProvider defaultTheme="light">
      <ToastProvider>
        <PanelStateProvider>
          <div className="min-h-screen bg-background text-foreground">
            {/* Skip to main content link for keyboard navigation */}
            <a
              href="#main-content"
              className="skip-link"
              onClick={(e) => {
                e.preventDefault();
                skipToMain();
              }}
            >
              Skip to main content
            </a>
            <ErrorBoundary>
              <Routes>
                {/* Authentication Routes */}
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />

                {/* Dashboard - Main Entry Point */}
                <Route
                  path="/"
                  element={<Navigate to="/dashboard" replace />}
                />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading dashboard..." />
                            }
                          >
                            <DashboardPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/onboarding"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading setup..." />
                            }
                          >
                            <OnboardingPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />

                {/* Transactions - Upload and Review */}
                <Route
                  path="/transactions"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading
                                fullPage
                                text="Loading transactions..."
                              />
                            }
                          >
                            <TransactionReviewPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/transactions/upload"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading upload..." />
                            }
                          >
                            <WorkPage
                              onUploadSuccess={handleUploadSuccessInApp}
                            />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />

                {/* Reports and Settings */}
                <Route
                  path="/reports"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading reports..." />
                            }
                          >
                            <ReportsPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading settings..." />
                            }
                          >
                            <SettingsPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />

                {/* Legacy Route Redirects - Business-Friendly */}
                <Route
                  path="/work"
                  element={<Navigate to="/transactions/upload" replace />}
                />
                <Route
                  path="/upload"
                  element={<Navigate to="/transactions/upload" replace />}
                />
                <Route
                  path="/transaction-analysis"
                  element={<Navigate to="/transactions" replace />}
                />
                <Route
                  path="/categories"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading fullPage text="Loading categories..." />
                            }
                          >
                            <CategoryManagementPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />

                {/* Accuracy Testing Routes */}
                <Route
                  path="/accuracy"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading
                                fullPage
                                text="Loading accuracy tests..."
                              />
                            }
                          >
                            <AccuracyTestsPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/accuracy/create"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading
                                fullPage
                                text="Loading test creation..."
                              />
                            }
                          >
                            <CreateAccuracyTestPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/accuracy/tests/:testId/results"
                  element={
                    <ProtectedRoute>
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense
                            fallback={
                              <Loading
                                fullPage
                                text="Loading test results..."
                              />
                            }
                          >
                            <TestResultsPage />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/reports-index"
                  element={<Navigate to="/reports" replace />}
                />
                <Route
                  path="/reports/processing"
                  element={<Navigate to="/reports" replace />}
                />
                <Route
                  path="/reports/processing/:reportId"
                  element={<Navigate to="/reports" replace />}
                />
                <Route
                  path="/knowledge-hub"
                  element={<Navigate to="/admin" replace />}
                />
                <Route
                  path="/review"
                  element={<Navigate to="/transactions" replace />}
                />
                <Route
                  path="/settings"
                  element={<Navigate to="/admin" replace />}
                />
                <Route
                  path="/temporal-validation"
                  element={<Navigate to="/admin" replace />}
                />
                <Route
                  path="/rag-corpus-management"
                  element={<Navigate to="/admin" replace />}
                />

                {/* Redirect legacy routes */}

                {/* 404 route */}
                <Route
                  path="*"
                  element={
                    <div className="min-h-screen flex flex-wrap items-center justify-center">
                      <div className="text-center">
                        <h1 className="text-4xl font-bold text-foreground mb-4">
                          404
                        </h1>
                        <p className="text-muted-foreground mb-6">
                          Page not found
                        </p>
                        <a
                          href="/"
                          className="text-primary hover:text-primary/80"
                        >
                          Go home
                        </a>
                      </div>
                    </div>
                  }
                />
              </Routes>

              {/* Column Mapping Modal */}
              {isMappingModalOpen && (
                <ColumnMappingModal
                  isOpen={isMappingModalOpen}
                  onClose={() => {
                    setIsMappingModalOpen(false);
                    setCurrentUploadId(null);
                  }}
                  onConfirm={(uploadId, mapping) =>
                    void handleMappingConfirm(uploadId, mapping)
                  }
                  uploadId={currentUploadId}
                />
              )}
            </ErrorBoundary>
          </div>
        </PanelStateProvider>
        <ToastViewport />
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
