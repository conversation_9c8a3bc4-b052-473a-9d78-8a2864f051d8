import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';

interface PanelState {
  isAgentPanelCollapsed: boolean;
  isLeftNavCollapsed: boolean;
  agentPanelWidth: number;
  userPreferences: {
    rememberPanelStates: boolean;
    defaultAgentPanelCollapsed: boolean;
    defaultLeftNavCollapsed: boolean;
    defaultAgentPanelWidth: number;
  };
}

interface PanelStateContextType extends PanelState {
  toggleAgentPanel: () => void;
  toggleLeftNav: () => void;
  setAgentPanelWidth: (width: number) => void;
  resetToDefaults: () => void;
  updateUserPreferences: (
    preferences: Partial<PanelState['userPreferences']>,
  ) => void;
}

const defaultPanelState: PanelState = {
  isAgentPanelCollapsed: false, // DRD-PANEL-04: Agent panel must be visible on ALL pages
  isLeftNavCollapsed: false,
  agentPanelWidth: 400,
  userPreferences: {
    rememberPanelStates: true,
    defaultAgentPanelCollapsed: false, // DRD-PANEL-04: Default to visible
    defaultLeftNavCollapsed: false,
    defaultAgentPanelWidth: 320,
  },
};

const STORAGE_KEY = 'giki-ai-panel-state';

const PanelStateContext = createContext<PanelStateContextType | undefined>(
  undefined,
);

interface PanelStateProviderProps {
  children: ReactNode;
}

export const PanelStateProvider: React.FC<PanelStateProviderProps> = ({
  children,
}) => {
  const [panelState, setPanelState] = useState<PanelState>(defaultPanelState);

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState) as PanelState;
        // Validate and merge with defaults to handle version changes
        setPanelState({
          ...defaultPanelState,
          ...parsedState,
          userPreferences: {
            ...defaultPanelState.userPreferences,
            ...parsedState.userPreferences,
          },
        });
      }
    } catch (error) {
      console.warn('Failed to load panel state from localStorage:', error);
      // Reset to defaults if corrupted
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (panelState?.userPreferences?.rememberPanelStates) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(panelState));
      } catch (error) {
        console.warn('Failed to save panel state to localStorage:', error);
      }
    }
  }, [panelState]);

  // Handle window resize for smart panel management
  useEffect(() => {
    const handleResize = () => {
      const currentWidth = window.innerWidth;

      setPanelState((prev) => {
        // If both panels are expanded and screen becomes narrow, collapse left nav
        if (
          !prev.isAgentPanelCollapsed &&
          !prev.isLeftNavCollapsed &&
          currentWidth < 1400
        ) {
          return {
            ...prev,
            isLeftNavCollapsed: true,
          };
        }

        // If screen becomes wide and agent panel is collapsed, restore left nav based on user preference
        if (
          prev.isAgentPanelCollapsed &&
          prev.isLeftNavCollapsed &&
          currentWidth >= 1400 &&
          !prev?.userPreferences?.defaultLeftNavCollapsed
        ) {
          return {
            ...prev,
            isLeftNavCollapsed: false,
          };
        }

        return prev;
      });
    };

    window.addEventListener('resize', handleResize);

    // Initial check on mount
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleAgentPanel = () => {
    setPanelState((prev) => {
      const newCollapsed = !prev.isAgentPanelCollapsed;

      // USER REQUEST: Always collapse left nav when agent panel expands
      let shouldCollapseLeftNav = prev.isLeftNavCollapsed;

      if (!newCollapsed) {
        // When agent panel expands, ALWAYS collapse left nav for focused AI experience
        shouldCollapseLeftNav = true;
      } else {
        // When agent panel collapses, restore left nav if user preference allows
        shouldCollapseLeftNav =
          prev?.userPreferences?.defaultLeftNavCollapsed || false;
      }

      return {
        ...prev,
        isAgentPanelCollapsed: newCollapsed,
        isLeftNavCollapsed: shouldCollapseLeftNav,
      };
    });
  };

  const toggleLeftNav = () => {
    setPanelState((prev) => ({
      ...prev,
      isLeftNavCollapsed: !prev.isLeftNavCollapsed,
    }));
  };

  const setAgentPanelWidth = (width: number) => {
    // Constrain width between 320px and 600px
    const constrainedWidth = Math.max(320, Math.min(600, width));
    setPanelState((prev) => ({
      ...prev,
      agentPanelWidth: constrainedWidth,
    }));
  };

  const resetToDefaults = () => {
    setPanelState((prev) => ({
      ...prev,
      isAgentPanelCollapsed: prev?.userPreferences?.defaultAgentPanelCollapsed,
      isLeftNavCollapsed: prev?.userPreferences?.defaultLeftNavCollapsed,
      agentPanelWidth: prev?.userPreferences?.defaultAgentPanelWidth,
    }));
  };

  const updateUserPreferences = (
    preferences: Partial<PanelState['userPreferences']>,
  ) => {
    setPanelState((prev) => ({
      ...prev,
      userPreferences: {
        ...prev.userPreferences,
        ...preferences,
      },
    }));
  };

  const contextValue: PanelStateContextType = {
    ...panelState,
    toggleAgentPanel,
    toggleLeftNav,
    setAgentPanelWidth,
    resetToDefaults,
    updateUserPreferences,
  };

  return (
    <PanelStateContext.Provider value={contextValue}>
      {children}
    </PanelStateContext.Provider>
  );
};

export const usePanelState = (): PanelStateContextType => {
  const context = useContext(PanelStateContext);
  if (context === undefined) {
    throw new Error('usePanelState must be used within a PanelStateProvider');
  }
  return context;
};

export default PanelStateProvider;
