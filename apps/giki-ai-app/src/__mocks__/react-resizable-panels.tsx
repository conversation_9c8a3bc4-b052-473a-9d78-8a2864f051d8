import React from 'react';

// Simple mock implementation for react-resizable-panels
const PanelGroup = function (
  props: React.PropsWithChildren<unknown>,
): React.ReactNode {
  return props.children;
};

const Panel = function (
  props: React.PropsWithChildren<unknown>,
): React.ReactNode {
  return props.children;
};

const PanelResizeHandle = function (
  props: React.PropsWithChildren<unknown>,
): React.ReactNode {
  return props.children || null;
};

// Named exports
export { PanelGroup, Panel, PanelResizeHandle };

// Default export
const defaultExportObject = { PanelGroup, Panel, PanelResizeHandle };
export default defaultExportObject;

// CommonJS compatibility
module.exports = {
  PanelGroup,
  Panel,
  PanelResizeHandle,
  default: defaultExportObject,
};
