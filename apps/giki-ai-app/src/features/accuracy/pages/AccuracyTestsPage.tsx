/**
 * Accuracy Tests Dashboard Page
 *
 * Main page for managing accuracy tests across all three scenarios.
 * Provides overview dashboard, test creation, and results management.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../shared/components/ui/tabs';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  Plus,
  BarChart3,
  TrendingUp,
  FileCheck,
  Filter,
  RefreshCw,
  Download,
} from 'lucide-react';

import AccuracyTestCard from '../components/AccuracyTestCard';
import { accuracyService } from '../services/accuracyService';
import type {
  AccuracyTestSummary,
  AccuracyDashboardData,
  AccuracyTestScenario,
  AccuracyTestFilter,
} from '../types/accuracy';

export const AccuracyTestsPage: React.FC = () => {
  const navigate = useNavigate();
  const [tests, setTests] = useState<AccuracyTestSummary[]>([]);
  const [dashboardData, setDashboardData] =
    useState<AccuracyDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<number | null>(null);
  const [filter] = useState<AccuracyTestFilter>({});
  const [activeTab, setActiveTab] = useState<'all' | AccuracyTestScenario>(
    'all',
  );

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const [testsResponse, dashboard] = await Promise.all([
        accuracyService.listTests(filter),
        accuracyService.getDashboardData(),
      ]);

      setTests(testsResponse.tests);
      setDashboardData(dashboard);
    } catch (error) {
      console.error('Failed to load accuracy data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load accuracy tests. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    void loadData();
  }, [loadData]);

  const handleCreateTest = () => {
    navigate('/accuracy/create');
  };

  const handleViewResults = (testId: number) => {
    navigate(`/accuracy/tests/${testId}/results`);
  };

  const handleDeleteTest = async (testId: number) => {
    if (
      !confirm(
        'Are you sure you want to delete this test? This action cannot be undone.',
      )
    ) {
      return;
    }

    try {
      setActionLoading(testId);
      await accuracyService.deleteTest(testId);

      toast({
        title: 'Success',
        description: 'Test deleted successfully.',
      });

      await loadData();
    } catch (error) {
      console.error('Failed to delete test:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete test. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleRestartTest = async (testId: number) => {
    try {
      setActionLoading(testId);
      await accuracyService.runTest(testId);

      toast({
        title: 'Success',
        description: 'Test restarted successfully.',
      });

      await loadData();
    } catch (error) {
      console.error('Failed to restart test:', error);
      toast({
        title: 'Error',
        description: 'Failed to restart test. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const getFilteredTests = () => {
    if (activeTab === 'all') return tests;
    return tests.filter((test) => test.scenario === activeTab);
  };

  const getScenarioColor = (scenario: AccuracyTestScenario): string => {
    switch (scenario) {
      case 'historical_data':
        return 'bg-blue-100 text-blue-800';
      case 'schema_only':
        return 'bg-green-100 text-green-800';
      case 'zero_onboarding':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Accuracy Testing</h1>
          <p className="text-gray-600 mt-1">
            Measure categorization quality across historical data, schema-only,
            and zero-onboarding scenarios
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => void loadData()}
            disabled={loading}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>

          <Button onClick={handleCreateTest}>
            <Plus className="w-4 h-4 mr-2" />
            New Test
          </Button>
        </div>
      </div>

      {/* Dashboard Overview */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <FileCheck className="w-4 h-4 mr-2" />
                Total Tests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {dashboardData.total_tests}
              </div>
              <p className="text-xs text-gray-600">Across all scenarios</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                Average Accuracy
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {dashboardData.average_accuracy.toFixed(1)}%
              </div>
              <Progress
                value={dashboardData.average_accuracy}
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Quality Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {(
                  (dashboardData.quality_metrics.non_generic_rate +
                    dashboardData.quality_metrics.hierarchical_rate +
                    dashboardData.quality_metrics.gl_code_rate) /
                  3
                ).toFixed(1)}
                %
              </div>
              <p className="text-xs text-gray-600">
                Non-generic + Hierarchical + GL codes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Tests by Scenario
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(dashboardData.tests_by_scenario).map(
                  ([scenario, count]) => (
                    <div
                      key={scenario}
                      className="flex items-center justify-between"
                    >
                      <Badge
                        variant="outline"
                        className={`text-xs ${getScenarioColor(scenario as AccuracyTestScenario)}`}
                      >
                        {scenario.replace('_', ' ')}
                      </Badge>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tests List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Accuracy Tests</CardTitle>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>

              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={(value) =>
              setActiveTab(value as 'all' | AccuracyTestScenario)
            }
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Tests</TabsTrigger>
              <TabsTrigger value="historical_data">Historical Data</TabsTrigger>
              <TabsTrigger value="schema_only">Schema Only</TabsTrigger>
              <TabsTrigger value="zero_onboarding">Zero Onboarding</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {getFilteredTests().length === 0 ? (
                <div className="text-center py-12">
                  <FileCheck className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No tests found
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {activeTab === 'all'
                      ? 'Create your first accuracy test to get started.'
                      : `No tests found for ${activeTab.replace('_', ' ')} scenario.`}
                  </p>
                  <Button onClick={handleCreateTest}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Test
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {getFilteredTests().map((test) => (
                    <AccuracyTestCard
                      key={test.test_id}
                      test={test}
                      onViewResults={handleViewResults}
                      onDeleteTest={(id) => void handleDeleteTest(id)}
                      onRestartTest={(id) => void handleRestartTest(id)}
                      isLoading={actionLoading === test.test_id}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccuracyTestsPage;
