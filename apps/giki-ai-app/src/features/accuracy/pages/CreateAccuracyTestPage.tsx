/**
 * Create Accuracy Test Page
 *
 * Form for creating new accuracy tests with scenario selection,
 * dataset upload, and configuration options.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../shared/components/ui/form';
import { Input } from '../../../shared/components/ui/input';
import { Textarea } from '../../../shared/components/ui/textarea';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../shared/components/ui/radio-group';
import { Label } from '../../../shared/components/ui/label';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  ArrowLeft,
  Upload,
  FileSpreadsheet,
  Info,
  CheckCircle,
  Database,
  Brain,
  Zap,
} from 'lucide-react';

import { accuracyService } from '../services/accuracyService';
import type { AccuracyTestScenario } from '../types/accuracy';

const formSchema = z.object({
  test_name: z
    .string()
    .min(3, 'Test name must be at least 3 characters')
    .max(100, 'Test name must not exceed 100 characters'),

  description: z
    .string()
    .max(500, 'Description must not exceed 500 characters')
    .optional(),

  scenario: z.enum([
    'historical_data',
    'schema_only',
    'zero_onboarding',
  ] as const),

  dataset_file: z.any().optional(),

  category_schema_file: z.any().optional(),

  max_transactions: z
    .number()
    .min(10, 'Minimum 10 transactions required')
    .max(1000, 'Maximum 1000 transactions allowed')
    .default(100),

  confidence_threshold: z
    .number()
    .min(0.1, 'Minimum confidence threshold is 0.1')
    .max(1.0, 'Maximum confidence threshold is 1.0')
    .default(0.7),
});

type FormData = z.infer<typeof formSchema>;

const scenarioDescriptions = {
  historical_data: {
    title: 'Historical Data',
    description:
      'RAG-based categorization using onboarding corpus and transaction history',
    icon: Database,
    features: [
      'Uses historical transaction data',
      'RAG-enabled categorization',
      'Highest accuracy expected',
    ],
    requirements: [
      'Transaction dataset with existing categories',
      'Optional: category schema for validation',
    ],
  },
  schema_only: {
    title: 'Schema Only',
    description:
      'Category schema-based categorization without transaction history',
    icon: Brain,
    features: [
      'Uses predefined category schema',
      'No historical data required',
      'Good for structured categorization',
    ],
    requirements: [
      'Category schema file (JSON/Excel)',
      'Transaction dataset for testing',
    ],
  },
  zero_onboarding: {
    title: 'Zero Onboarding',
    description: 'Pure AI-driven categorization without prior data or schema',
    icon: Zap,
    features: [
      'No prior data required',
      'Pure AI intelligence',
      'Tests AI capabilities in isolation',
    ],
    requirements: [
      'Transaction dataset only',
      'AI creates categories dynamically',
    ],
  },
};

export const CreateAccuracyTestPage: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      test_name: '',
      description: '',
      scenario: 'zero_onboarding',
      max_transactions: 100,
      confidence_threshold: 0.7,
    },
  });

  const selectedScenario = form.watch('scenario');

  const handleFileUpload = (
    file: File,
    fieldName: 'dataset_file' | 'category_schema_file',
  ) => {
    // Basic file validation
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/json',
    ];

    if (file.size > maxSize) {
      toast({
        title: 'File too large',
        description: 'File size must be less than 10MB.',
        variant: 'destructive',
      });
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload Excel (.xlsx), CSV, or JSON files only.',
        variant: 'destructive',
      });
      return;
    }

    form.setValue(fieldName, file);

    // Simulate upload progress
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 100);
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('test_name', data.test_name);
      formData.append('scenario', data.scenario);
      formData.append('max_transactions', data.max_transactions.toString());
      formData.append(
        'confidence_threshold',
        data.confidence_threshold.toString(),
      );

      if (data.description) {
        formData.append('description', data.description);
      }

      if (data.dataset_file instanceof File) {
        formData.append('dataset_file', data.dataset_file);
      }

      if (data.category_schema_file instanceof File) {
        formData.append('category_schema_file', data.category_schema_file);
      }

      const result = await accuracyService.createTest(formData);

      toast({
        title: 'Success',
        description:
          'Accuracy test created successfully. Starting execution...',
      });

      // Navigate to test results page
      navigate(`/accuracy/tests/${result.test_id}/results`);
    } catch (error) {
      console.error('Failed to create test:', error);
      toast({
        title: 'Error',
        description: 'Failed to create accuracy test. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" onClick={() => navigate('/accuracy')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>

        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Create Accuracy Test
          </h1>
          <p className="text-gray-600 mt-1">
            Set up a new categorization accuracy test to measure AI performance
          </p>
        </div>
      </div>

      <Form {...form}>
        <form
          onSubmit={(e) => void form.handleSubmit(onSubmit)(e)}
          className="space-y-8"
        >
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Test Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="test_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Q4 2024 Nuvie Data Accuracy Test"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this accuracy test
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this test..."
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description for additional context
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Scenario Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Test Scenario</CardTitle>
              <p className="text-sm text-gray-600">
                Choose the categorization scenario to test
              </p>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="scenario"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="space-y-4"
                      >
                        {(
                          Object.entries(scenarioDescriptions) as [
                            AccuracyTestScenario,
                            typeof scenarioDescriptions.historical_data,
                          ][]
                        ).map(([key, scenario]) => {
                          const Icon = scenario.icon;
                          return (
                            <div key={key} className="relative">
                              <Label
                                htmlFor={key}
                                className={`block p-4 border rounded-lg cursor-pointer transition-colors ${
                                  field.value === key
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                              >
                                <div className="flex items-start gap-4">
                                  <RadioGroupItem
                                    value={key}
                                    id={key}
                                    className="mt-1"
                                  />
                                  <Icon className="w-6 h-6 text-blue-600 mt-0.5" />
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <h3 className="font-semibold text-gray-900">
                                        {scenario.title}
                                      </h3>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {key.replace('_', ' ')}
                                      </Badge>
                                    </div>
                                    <p className="text-sm text-gray-600 mb-3">
                                      {scenario.description}
                                    </p>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                                      <div>
                                        <h4 className="font-medium text-gray-700 mb-1">
                                          Features
                                        </h4>
                                        <ul className="space-y-1">
                                          {scenario.features.map(
                                            (feature, index) => (
                                              <li
                                                key={index}
                                                className="flex items-center gap-1 text-gray-600"
                                              >
                                                <CheckCircle className="w-3 h-3 text-green-500" />
                                                {feature}
                                              </li>
                                            ),
                                          )}
                                        </ul>
                                      </div>

                                      <div>
                                        <h4 className="font-medium text-gray-700 mb-1">
                                          Requirements
                                        </h4>
                                        <ul className="space-y-1">
                                          {scenario.requirements.map(
                                            (requirement, index) => (
                                              <li
                                                key={index}
                                                className="flex items-center gap-1 text-gray-600"
                                              >
                                                <Info className="w-3 h-3 text-blue-500" />
                                                {requirement}
                                              </li>
                                            ),
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* File Uploads */}
          <Card>
            <CardHeader>
              <CardTitle>Dataset & Schema Files</CardTitle>
              <p className="text-sm text-gray-600">
                Upload the required files for your selected scenario
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Transaction Dataset */}
              <div>
                <Label className="text-sm font-medium flex items-center gap-2 mb-3">
                  <FileSpreadsheet className="w-4 h-4" />
                  Transaction Dataset
                  <Badge variant="destructive" className="text-xs">
                    Required
                  </Badge>
                </Label>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Drop your transaction file here or click to browse
                  </p>
                  <p className="text-xs text-gray-500 mb-4">
                    Supports Excel (.xlsx), CSV, and JSON formats. Max 10MB.
                  </p>

                  <Input
                    type="file"
                    accept=".xlsx,.xls,.csv,.json"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload(file, 'dataset_file');
                    }}
                    className="hidden"
                    id="dataset-upload"
                  />

                  <Label htmlFor="dataset-upload">
                    <Button
                      type="button"
                      variant="outline"
                      className="cursor-pointer"
                    >
                      Choose File
                    </Button>
                  </Label>

                  {form.getValues('dataset_file') && (
                    <div className="mt-4 p-3 bg-green-50 rounded border text-left">
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="font-medium text-green-800">
                          {(form.getValues('dataset_file') as File)?.name}
                        </span>
                      </div>
                      {uploadProgress < 100 && (
                        <Progress value={uploadProgress} className="mt-2 h-1" />
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Category Schema (for schema_only scenario) */}
              {selectedScenario === 'schema_only' && (
                <div>
                  <Label className="text-sm font-medium flex items-center gap-2 mb-3">
                    <Database className="w-4 h-4" />
                    Category Schema
                    <Badge variant="destructive" className="text-xs">
                      Required for Schema Only
                    </Badge>
                  </Label>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600 mb-2">
                      Upload category schema file
                    </p>
                    <p className="text-xs text-gray-500 mb-4">
                      JSON file with hierarchical category structure
                    </p>

                    <Input
                      type="file"
                      accept=".json,.xlsx,.csv"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file)
                          handleFileUpload(file, 'category_schema_file');
                      }}
                      className="hidden"
                      id="schema-upload"
                    />

                    <Label htmlFor="schema-upload">
                      <Button
                        type="button"
                        variant="outline"
                        className="cursor-pointer"
                      >
                        Choose Schema File
                      </Button>
                    </Label>

                    {form.getValues('category_schema_file') && (
                      <div className="mt-4 p-3 bg-green-50 rounded border text-left">
                        <div className="flex items-center gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="font-medium text-green-800">
                            {
                              (form.getValues('category_schema_file') as File)
                                ?.name
                            }
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Test Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Test Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="max_transactions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Transactions</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={10}
                          max={1000}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Number of transactions to test (10-1000)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confidence_threshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confidence Threshold</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step={0.1}
                          min={0.1}
                          max={1.0}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Minimum confidence for valid categorization (0.1-1.0)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex items-center justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/accuracy')}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                'Create Test'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateAccuracyTestPage;
