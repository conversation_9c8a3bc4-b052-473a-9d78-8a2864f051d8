/**
 * Accuracy Test Card Component
 *
 * Displays accuracy test summary information in a card format
 * with actions for viewing results, rerunning tests, and deletion.
 */

import React from 'react';
import { Badge } from '../../../shared/components/ui/badge';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import { Progress } from '../../../shared/components/ui/progress';
import {
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  Eye,
  Play,
  Trash2,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { formatDistanceToNow, parseISO } from 'date-fns';
import type {
  AccuracyTestSummary,
  AccuracyTestScenario,
  AccuracyTestStatus,
} from '../types/accuracy';

interface AccuracyTestCardProps {
  test: AccuracyTestSummary;
  onViewResults: (testId: number) => void;
  onDeleteTest: (testId: number) => void;
  onRestartTest: (testId: number) => void;
  isLoading?: boolean;
}

const scenarioLabels: Record<AccuracyTestScenario, string> = {
  historical_data: 'Historical Data',
  schema_only: 'Schema Only',
  zero_onboarding: 'Zero Onboarding',
};

const scenarioDescriptions: Record<AccuracyTestScenario, string> = {
  historical_data: 'RAG-based categorization with onboarding corpus',
  schema_only: 'Category schema without transaction history',
  zero_onboarding: 'No schema, no historical data (pure AI-driven)',
};

const statusIcons: Record<
  AccuracyTestStatus,
  React.ComponentType<{ className?: string }>
> = {
  pending: Clock,
  running: Play,
  completed: CheckCircle,
  failed: XCircle,
  cancelled: AlertCircle,
};

const statusColors: Record<AccuracyTestStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  running: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800',
};

export const AccuracyTestCard: React.FC<AccuracyTestCardProps> = ({
  test,
  onViewResults,
  onDeleteTest,
  onRestartTest,
  isLoading = false,
}) => {
  const StatusIcon = statusIcons[test.status];

  const formatAccuracy = (value?: number): string => {
    if (value === undefined || value === null) return 'N/A';
    return `${value.toFixed(1)}%`;
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return 'N/A';

    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  const getAccuracyColor = (accuracy?: number): string => {
    if (!accuracy) return 'text-gray-500';
    if (accuracy >= 90) return 'text-green-600';
    if (accuracy >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const canViewResults = test.status === 'completed';
  const canRestart = test.status === 'failed' || test.status === 'completed';
  const canDelete = test.status !== 'running';

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 truncate">
              {test.test_name}
            </CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                {scenarioLabels[test.scenario]}
              </Badge>
              <Badge className={`text-xs ${statusColors[test.status]}`}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {scenarioDescriptions[test.scenario]}
            </p>
          </div>

          <div className="flex items-center gap-1 ml-4">
            {canViewResults && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewResults(test.test_id)}
                disabled={isLoading}
                className="h-8 w-8 p-0"
              >
                <Eye className="w-4 h-4" />
              </Button>
            )}

            {canRestart && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRestartTest(test.test_id)}
                disabled={isLoading}
                className="h-8 w-8 p-0"
              >
                <Play className="w-4 h-4" />
              </Button>
            )}

            {canDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteTest(test.test_id)}
                disabled={isLoading}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Test Results Summary */}
        {test.status === 'completed' && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Overall Accuracy
                </span>
                <span
                  className={`text-sm font-semibold ${getAccuracyColor(test.overall_accuracy)}`}
                >
                  {formatAccuracy(test.overall_accuracy)}
                </span>
              </div>

              {test.overall_accuracy !== undefined && (
                <Progress value={test.overall_accuracy} className="h-2" />
              )}
            </div>

            <div className="space-y-2">
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-medium text-gray-700">Precision</div>
                  <div className="text-gray-600">
                    {formatAccuracy(test.precision && test.precision * 100)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-gray-700">Recall</div>
                  <div className="text-gray-600">
                    {formatAccuracy(test.recall && test.recall * 100)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-gray-700">F1</div>
                  <div className="text-gray-600">
                    {formatAccuracy(test.f1_score && test.f1_score * 100)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Transaction Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {test.total_transactions.toLocaleString()}
            </div>
            <div className="text-xs text-gray-600">Total Transactions</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">
              {test.successful_categorizations.toLocaleString()}
            </div>
            <div className="text-xs text-gray-600">Categorized</div>
          </div>

          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">
              {formatAccuracy(test.success_rate)}
            </div>
            <div className="text-xs text-gray-600">Success Rate</div>
          </div>
        </div>

        {/* AI Judge Summary */}
        {test.status === 'completed' &&
          test.ai_judge_accuracy !== undefined && (
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  AI Judge Evaluation
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Accuracy: </span>
                  <span className="font-medium">
                    {formatAccuracy(test.ai_judge_accuracy)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Avg Confidence: </span>
                  <span className="font-medium">
                    {test.ai_judge_confidence_avg
                      ? (test.ai_judge_confidence_avg * 100).toFixed(1) + '%'
                      : 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          )}

        {/* Quality Indicators */}
        {test.status === 'completed' && (
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="text-sm font-medium text-blue-700">
                {Math.round(
                  (test.non_generic_categories / test.total_transactions) * 100,
                )}
                %
              </div>
              <div className="text-xs text-blue-600">Non-Generic</div>
            </div>

            <div className="text-center p-2 bg-purple-50 rounded">
              <div className="text-sm font-medium text-purple-700">
                {Math.round(
                  (test.hierarchical_categories / test.total_transactions) *
                    100,
                )}
                %
              </div>
              <div className="text-xs text-purple-600">Hierarchical</div>
            </div>

            <div className="text-center p-2 bg-green-50 rounded">
              <div className="text-sm font-medium text-green-700">
                {Math.round(
                  (test.categories_with_gl_codes / test.total_transactions) *
                    100,
                )}
                %
              </div>
              <div className="text-xs text-green-600">With GL Codes</div>
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {test.completed_at
              ? `Completed ${formatDistanceToNow(parseISO(test.completed_at))} ago`
              : `Created ${formatDistanceToNow(parseISO(test.completed_at || new Date().toISOString()))} ago`}
          </div>

          {test.execution_duration_seconds && (
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {formatDuration(test.execution_duration_seconds)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AccuracyTestCard;
