/**
 * Accuracy Testing Service
 *
 * Handles all API interactions for accuracy testing functionality,
 * including test creation, execution monitoring, and results retrieval.
 */

import { apiClient } from '../../../shared/services/api/apiClient';
import type {
  AccuracyTestSummary,
  AccuracyTestDetails,
  AccuracyTestFilter,
  AccuracyDashboardData,
  TransactionResultsFilter,
  TransactionResultsResponse,
  AccuracyAnalytics,
  CreateTestResponse,
} from '../types/accuracy';

class AccuracyService {
  private readonly baseUrl = '/api/v1/accuracy';

  /**
   * Get dashboard data with summary statistics
   */
  async getDashboardData(): Promise<AccuracyDashboardData> {
    const response = await apiClient.get<AccuracyDashboardData>(
      `${this.baseUrl}/dashboard`,
    );
    return response.data;
  }

  /**
   * List accuracy tests with optional filtering
   */
  async listTests(
    filter?: AccuracyTestFilter,
  ): Promise<{ tests: AccuracyTestSummary[] }> {
    const params = new URLSearchParams();

    if (filter?.scenario) params.append('scenario', filter.scenario);
    if (filter?.status) params.append('status', filter.status);
    if (filter?.created_after)
      params.append('created_after', filter.created_after);
    if (filter?.created_before)
      params.append('created_before', filter.created_before);

    const response = await apiClient.get<{ tests: AccuracyTestSummary[] }>(
      `${this.baseUrl}/tests?${params.toString()}`,
    );
    return response.data;
  }

  /**
   * Get detailed information for a specific test
   */
  async getTestDetails(testId: number): Promise<AccuracyTestDetails> {
    const response = await apiClient.get<AccuracyTestDetails>(
      `${this.baseUrl}/tests/${testId}`,
    );
    return response.data;
  }

  /**
   * Create a new accuracy test
   */
  async createTest(formData: FormData): Promise<CreateTestResponse> {
    const response = await apiClient.post<CreateTestResponse>(
      `${this.baseUrl}/tests`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response.data;
  }

  /**
   * Run/restart an existing test
   */
  async runTest(testId: number): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      `${this.baseUrl}/tests/${testId}/run`,
    );
    return response.data;
  }

  /**
   * Cancel a running test
   */
  async cancelTest(testId: number): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      `${this.baseUrl}/tests/${testId}/cancel`,
    );
    return response.data;
  }

  /**
   * Delete a test
   */
  async deleteTest(testId: number): Promise<{ message: string }> {
    const response = await apiClient.delete<{ message: string }>(
      `${this.baseUrl}/tests/${testId}`,
    );
    return response.data;
  }

  /**
   * Get transaction results for a test with filtering and pagination
   */
  async getTransactionResults(
    testId: number,
    filter?: TransactionResultsFilter,
  ): Promise<TransactionResultsResponse> {
    const params = new URLSearchParams();

    if (filter?.page) params.append('page', filter.page.toString());
    if (filter?.page_size)
      params.append('page_size', filter.page_size.toString());
    if (filter?.search) params.append('search', filter.search);
    if (filter?.correctness) params.append('correctness', filter.correctness);
    if (filter?.confidence_range)
      params.append('confidence_range', filter.confidence_range);
    if (filter?.quality_filter)
      params.append('quality_filter', filter.quality_filter);
    if (filter?.sort_by) params.append('sort_by', filter.sort_by);
    if (filter?.sort_order) params.append('sort_order', filter.sort_order);

    const response = await apiClient.get<TransactionResultsResponse>(
      `${this.baseUrl}/tests/${testId}/results/transactions?${params.toString()}`,
    );
    return response.data;
  }

  /**
   * Get analytics and detailed metrics for a test
   */
  async getTestAnalytics(testId: number): Promise<AccuracyAnalytics> {
    const response = await apiClient.get<AccuracyAnalytics>(
      `${this.baseUrl}/tests/${testId}/analytics`,
    );
    return response.data;
  }

  /**
   * Export test results as Excel file
   */
  async exportTestResults(testId: number): Promise<Blob> {
    const response = await apiClient.get<Blob>(
      `${this.baseUrl}/tests/${testId}/export`,
      {
        responseType: 'blob',
      },
    );
    return response.data;
  }

  /**
   * Get test status for polling
   */
  async getTestStatus(testId: number): Promise<{
    status: string;
    progress?: number;
    message?: string;
  }> {
    const response = await apiClient.get<{
      status: string;
      progress?: number;
      message?: string;
    }>(`${this.baseUrl}/tests/${testId}/status`);
    return response.data;
  }

  /**
   * Upload category schema file
   */
  async uploadCategorySchema(file: File): Promise<{
    schema_id: string;
    categories_count: number;
  }> {
    const formData = new FormData();
    formData.append('schema_file', file);

    const response = await apiClient.post<{
      schema_id: string;
      categories_count: number;
    }>(`${this.baseUrl}/schemas`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Validate dataset file before test creation
   */
  async validateDataset(file: File): Promise<{
    is_valid: boolean;
    transaction_count: number;
    has_categories: boolean;
    sample_transactions: Array<{
      description: string;
      amount: number;
      category?: string;
    }>;
    validation_errors?: string[];
  }> {
    const formData = new FormData();
    formData.append('dataset_file', file);

    const response = await apiClient.post<{
      is_valid: boolean;
      transaction_count: number;
      has_categories: boolean;
      sample_transactions: Array<{
        description: string;
        amount: number;
        category?: string;
      }>;
      validation_errors?: string[];
    }>(`${this.baseUrl}/validate-dataset`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Get comparative analysis between multiple tests
   */
  async compareTests(testIds: number[]): Promise<{
    tests: AccuracyTestSummary[];
    comparison_metrics: {
      scenario: string;
      tests: Array<{
        test_id: number;
        test_name: string;
        overall_accuracy: number;
        success_rate: number;
        quality_score: number;
      }>;
    }[];
    recommendations: string[];
  }> {
    const params = new URLSearchParams();
    testIds.forEach((id) => params.append('test_ids', id.toString()));

    const response = await apiClient.get<{
      tests: AccuracyTestSummary[];
      comparison_metrics: {
        scenario: string;
        tests: Array<{
          test_id: number;
          test_name: string;
          overall_accuracy: number;
          success_rate: number;
          quality_score: number;
        }>;
      }[];
      recommendations: string[];
    }>(`${this.baseUrl}/compare?${params.toString()}`);
    return response.data;
  }

  /**
   * Get historical accuracy trends
   */
  async getAccuracyTrends(options?: {
    days?: number;
    scenario?: string;
  }): Promise<{
    trend_data: Array<{
      date: string;
      accuracy: number;
      test_count: number;
      scenario: string;
    }>;
    summary: {
      avg_accuracy: number;
      improvement_rate: number;
      total_tests: number;
    };
  }> {
    const params = new URLSearchParams();
    if (options?.days) params.append('days', options.days.toString());
    if (options?.scenario) params.append('scenario', options.scenario);

    const response = await apiClient.get<{
      trend_data: Array<{
        date: string;
        accuracy: number;
        test_count: number;
        scenario: string;
      }>;
      summary: {
        avg_accuracy: number;
        improvement_rate: number;
        total_tests: number;
      };
    }>(`${this.baseUrl}/trends?${params.toString()}`);
    return response.data;
  }
}

export const accuracyService = new AccuracyService();
