import { realtimeSync } from '@/shared/services/realtime/realtimeSync';

export interface ReportRequest {
  type:
    | 'spending_by_category'
    | 'income_vs_expense'
    | 'monthly_trends'
    | 'custom'
    | 'pivot';
  parameters?: {
    dateRange?: { from: Date; to: Date };
    metrics?: string[];
    dimensions?: string[];
    filters?: unknown[];
    groupBy?: string;
    chartType?: string;
  };
  naturalLanguageQuery: string;
}

export interface ReportGenerationResult {
  success: boolean;
  reportId: string;
  reportType: string;
  message: string;
  data?: ReportRequest;
}

// AI-powered natural language query parsing using Vertex AI
export const parseReportQuery = async (
  query: string,
): Promise<ReportRequest> => {
  try {
    // Use the AI intelligence service for sophisticated query parsing
    const response = await fetch('/api/v1/intelligence/parse-report-query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query.trim(),
        context: 'financial_report_generation',
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to parse query: ${response.statusText}`);
    }

    const parsedQuery = (await response.json()) as {
      reportType?: ReportRequest['type'];
      parameters?: ReportRequest['parameters'];
    };

    return {
      type: parsedQuery.reportType || 'custom',
      parameters: parsedQuery.parameters || {},
      naturalLanguageQuery: query,
    };
  } catch {
    throw new Error('Unable to parse report query - AI service unavailable');
  }
};

// Generate report based on request
export const generateReportFromQuery = async (
  query: string,
  userId: string,
): Promise<ReportGenerationResult> => {
  try {
    const reportRequest = await parseReportQuery(query);
    const reportId = `report_${Date.now()}`;

    // Emit real-time event to trigger report generation in UI
    realtimeSync.emit(
      'report.generate',
      {
        reportId,
        request: reportRequest,
        userId,
        timestamp: Date.now(),
      },
      'agent',
    );

    // Generate response message based on report type
    let message = '';
    switch (reportRequest.type) {
      case 'spending_by_category':
        message =
          "I'm generating a spending by category report for you. It will show your expenses broken down by each category.";
        break;
      case 'income_vs_expense':
        message =
          "I'm creating an income vs expense comparison report. This will help you see your financial balance.";
        break;
      case 'monthly_trends':
        message =
          "I'm preparing a monthly trends report to show how your finances change over time.";
        break;
      case 'pivot':
        message =
          "I'm setting up a pivot table for you. You can drag and drop fields to analyze your data from different angles.";
        break;
      case 'custom':
        message = "I'm creating a custom report based on your specifications.";
        if (reportRequest.parameters?.chartType) {
          message += ` I'll display it as a ${reportRequest?.parameters?.chartType}.`;
        }
        break;
    }

    return {
      success: true,
      reportId,
      reportType: reportRequest.type,
      message,
      data: reportRequest,
    };
  } catch {
    return {
      success: false,
      reportId: '',
      reportType: 'error',
      message:
        'I encountered an error while generating the report. Please try again.',
    };
  }
};

// Example queries the agent can handle
export const REPORT_GENERATION_EXAMPLES = [
  'Show me my spending by category',
  'Generate a monthly trends report',
  'Compare my income vs expenses',
  'Create a pie chart of my expenses',
  'Show spending by category for last 3 months',
  'Generate a pivot table for transaction analysis',
  'Create a bar chart showing monthly expenses',
  'Show me average spending per category',
  'Generate a report of transactions by account',
  'Create a custom report with total income by month',
];
