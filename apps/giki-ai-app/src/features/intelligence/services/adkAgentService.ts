/**
 * ADK Agent Service
 *
 * Frontend service for interacting with backend ADK (Agent Development Kit) capabilities.
 * Provides agent discovery, coordination, memory management, and A2A protocol support.
 */

interface ADKAgent {
  id: string;
  name: string;
  type: string;
  status: 'available' | 'busy' | 'offline' | 'error';
  capabilities: string[];
  currentTask?: string;
  lastActivity: string;
  memorySize: number;
  toolsAvailable: string[];
}

interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
}

interface Artifact {
  id: string;
  type: string;
  data: unknown;
  metadata?: Record<string, unknown>;
}

interface AgentMemoryContext {
  conversationHistory: ConversationMessage[];
  userPreferences: Record<string, unknown>;
  contextData: Record<string, unknown>;
  artifacts: Artifact[];
}

interface AgentTransferRequest {
  fromAgentId: string;
  toAgentId: string;
  context: AgentMemoryContext;
  reason: string;
  preserveMemory?: boolean;
}

interface ADKToolInvocation {
  toolName: string;
  parameters: Record<string, unknown>;
  agentId: string;
  sessionId?: string;
}

interface AgentSession {
  id: string;
  agentId: string;
  conversationId: string;
  memoryContext: AgentMemoryContext;
  startTime: string;
  lastInteraction: string;
  isActive: boolean;
}

class ADKAgentService {
  private baseUrl: string;
  private currentSession: AgentSession | null = null;

  constructor() {
    this.baseUrl = process?.env?.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }

  /**
   * Discover available ADK agents in the system
   */
  async discoverAgents(): Promise<ADKAgent[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/agents/discover`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Agent discovery failed: ${response.statusText}`);
      }

      return (await response.json()) as ADKAgent[];
    } catch (error) {
      console.error('Failed to discover agents:', error);
      // Return mock data for development
      return this.getMockAgents();
    }
  }

  /**
   * Start a new agent session with memory preloading
   */
  async startAgentSession(
    agentId: string,
    preloadMemory: boolean = true,
  ): Promise<AgentSession> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/agents/${agentId}/sessions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            preloadMemory,
            contextSize: 'standard',
          }),
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to start agent session: ${response.statusText}`,
        );
      }

      const session = (await response.json()) as AgentSession;
      this.currentSession = session;
      return session;
    } catch (error) {
      console.error('Failed to start agent session:', error);
      throw error;
    }
  }

  /**
   * Transfer conversation to another agent using A2A protocol
   */
  async transferToAgent(
    transferRequest: AgentTransferRequest,
  ): Promise<AgentSession> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/agents/transfer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify(transferRequest),
        },
      );

      if (!response.ok) {
        throw new Error(`Agent transfer failed: ${response.statusText}`);
      }

      const newSession = (await response.json()) as AgentSession;
      this.currentSession = newSession;
      return newSession;
    } catch (error) {
      console.error('Failed to transfer to agent:', error);
      throw error;
    }
  }

  /**
   * Load persistent memory for an agent
   */
  async loadAgentMemory(agentId: string): Promise<AgentMemoryContext> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/agents/${agentId}/memory`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to load agent memory: ${response.statusText}`);
      }

      return (await response.json()) as AgentMemoryContext;
    } catch (error) {
      console.error('Failed to load agent memory:', error);
      throw error;
    }
  }

  /**
   * Preload memory context for faster agent responses
   */
  async preloadMemory(
    agentId: string,
    contextData: Record<string, unknown>,
  ): Promise<void> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/agents/${agentId}/memory/preload`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ contextData }),
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to preload memory: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to preload memory:', error);
      throw error;
    }
  }

  /**
   * Invoke advanced ADK tools (load_artifacts, openapi_tool, etc.)
   */
  async invokeADKTool(invocation: ADKToolInvocation): Promise<unknown> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/adk/tools/invoke`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(invocation),
      });

      if (!response.ok) {
        throw new Error(`Tool invocation failed: ${response.statusText}`);
      }

      return (await response.json()) as unknown;
    } catch (error) {
      console.error('Failed to invoke ADK tool:', error);
      throw error;
    }
  }

  /**
   * Get real-time agent network status
   */
  async getAgentNetworkStatus(): Promise<{
    totalAgents: number;
    availableAgents: number;
    busyAgents: number;
    offlineAgents: number;
    networkHealth: 'healthy' | 'degraded' | 'offline';
  }> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v1/adk/network/status`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to get network status: ${response.statusText}`);
      }

      return (await response.json()) as {
        totalAgents: number;
        availableAgents: number;
        busyAgents: number;
        offlineAgents: number;
        networkHealth: 'healthy' | 'degraded' | 'offline';
      };
    } catch (error) {
      console.error('Failed to get agent network status:', error);
      return {
        totalAgents: 0,
        availableAgents: 0,
        busyAgents: 0,
        offlineAgents: 0,
        networkHealth: 'offline',
      };
    }
  }

  /**
   * Load artifacts using ADK load_artifacts tool
   */
  async loadArtifacts(artifactIds: string[]): Promise<Artifact[]> {
    const result = await this.invokeADKTool({
      toolName: 'load_artifacts',
      parameters: { artifact_ids: artifactIds },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as Artifact[];
  }

  /**
   * Search knowledge base using Vertex AI search tool
   */
  async searchKnowledge(query: string): Promise<unknown[]> {
    const result = await this.invokeADKTool({
      toolName: 'vertex_ai_search_tool',
      parameters: {
        query,
        data_store_id: 'financial-knowledge-base',
      },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as unknown[];
  }

  /**
   * Use Google Search tool for real-time data
   */
  async googleSearch(query: string): Promise<unknown[]> {
    const result = await this.invokeADKTool({
      toolName: 'google_search_tool',
      parameters: { query },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as unknown[];
  }

  /**
   * Get user choice using interactive ADK tool
   */
  async getUserChoice(prompt: string, options: string[]): Promise<string> {
    const result = await this.invokeADKTool({
      toolName: 'get_user_choice',
      parameters: { prompt, options },
      agentId: this.currentSession?.agentId || 'system',
    });
    return (result as { choice: string }).choice;
  }

  /**
   * Mock agents for development/fallback
   */
  private getMockAgents(): ADKAgent[] {
    return [
      {
        id: 'gl-code-agent',
        name: 'GL Code Agent',
        type: 'gl_code',
        status: 'available',
        capabilities: [
          'assign_gl_codes',
          'create_category_hierarchy',
          'map_to_23_toplevel_structure',
        ],
        lastActivity: new Date().toISOString(),
        memorySize: 1024,
        toolsAvailable: ['load_memory', 'preload_memory', 'transfer_to_agent'],
      },
      {
        id: 'debit-credit-agent',
        name: 'Debit Credit Agent',
        type: 'debit_credit',
        status: 'available',
        capabilities: [
          'infer_transaction_type',
          'validate_accounting_rules',
          'detect_anomalies',
        ],
        lastActivity: new Date().toISOString(),
        memorySize: 512,
        toolsAvailable: [
          'load_artifacts',
          'openapi_tool',
          'vertex_ai_search_tool',
        ],
      },
      {
        id: 'reports-agent',
        name: 'Reports Agent',
        type: 'reports',
        status: 'busy',
        capabilities: ['generate_reports', 'financial_analysis', 'export_data'],
        currentTask: 'Generating financial report',
        lastActivity: new Date().toISOString(),
        memorySize: 2048,
        toolsAvailable: [
          'apihub_tool',
          'google_search_tool',
          'LongRunningFunctionTool',
        ],
      },
      {
        id: 'coordinator-agent',
        name: 'Coordinator Agent',
        type: 'coordinator',
        status: 'available',
        capabilities: [
          'orchestrate_workflows',
          'manage_agent_handoffs',
          'optimize_resources',
        ],
        lastActivity: new Date().toISOString(),
        memorySize: 4096,
        toolsAvailable: [
          'transfer_to_agent',
          'exit_loop_tool',
          'get_user_choice',
        ],
      },
      {
        id: 'customer-agent',
        name: 'Customer Agent',
        type: 'customer',
        status: 'available',
        capabilities: [
          'ui_equivalence',
          'conversation_handling',
          'user_assistance',
        ],
        lastActivity: new Date().toISOString(),
        memorySize: 1536,
        toolsAvailable: [
          'load_memory',
          'transfer_to_agent',
          'get_user_choice_tool',
        ],
      },
    ];
  }

  /**
   * Get current active session
   */
  getCurrentSession(): AgentSession | null {
    return this.currentSession;
  }

  /**
   * End current agent session
   */
  async endSession(): Promise<void> {
    if (!this.currentSession) return;

    try {
      await fetch(
        `${this.baseUrl}/api/v1/adk/sessions/${this?.currentSession?.id}/end`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );
    } catch (error) {
      console.error('Failed to end session:', error);
    } finally {
      this.currentSession = null;
    }
  }
}

// Export singleton instance
export const adkAgentService = new ADKAgentService();
export default adkAgentService;
