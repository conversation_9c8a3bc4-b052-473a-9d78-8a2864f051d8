/**
 * Chat service for AI assistant interactions
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  session_id?: string;
}

export interface ChatSession {
  id: string;
  created_at: string;
  updated_at: string;
  message_count: number;
  last_message_at: string;
}

export interface ChatRequest {
  message: string;
  session_id?: string;
  context?: {
    current_page?: string;
    filters?: Record<string, unknown>;
    selected_data?: Record<string, unknown>;
  };
}

export interface ChatResponse {
  session_id: string;
  assistant_message: {
    text_response: string;
    suggested_follow_ups?: string[];
    data?: Record<string, unknown> | unknown[];
  };
  user_message_content: string;
}

export interface ConversationHistory {
  session_id: string;
  messages: ChatMessage[];
  total_messages: number;
}

/**
 * Send a chat message to the AI assistant
 */
export const sendChatMessage = async (
  request: ChatRequest,
): Promise<ChatResponse | ApiError> => {
  try {
    const response = await apiClient.post<ChatResponse>('/chat', request);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'sendChatMessage',
      defaultMessage: 'Failed to send message to AI assistant.',
    });
  }
};

/**
 * Get conversation history for a session
 */
export const getConversationHistory = async (
  sessionId: string,
  page: number = 1,
  limit: number = 50,
): Promise<ConversationHistory | ApiError> => {
  try {
    const response = await apiClient.get<ConversationHistory>(
      `/chat/sessions/${sessionId}/history`,
      {
        params: {
          page,
          limit,
        },
      },
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getConversationHistory',
      defaultMessage: 'Failed to get conversation history.',
    });
  }
};

/**
 * Get list of chat sessions
 */
export const getChatSessions = async (
  page: number = 1,
  limit: number = 20,
): Promise<{ sessions: ChatSession[]; total: number } | ApiError> => {
  try {
    const response = await apiClient.get<{
      sessions: ChatSession[];
      total: number;
    }>('/chat/sessions', {
      params: {
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getChatSessions',
      defaultMessage: 'Failed to get chat sessions.',
    });
  }
};

/**
 * Create a new chat session
 */
export const createChatSession = async (): Promise<ChatSession | ApiError> => {
  try {
    const response = await apiClient.post<ChatSession>('/chat/sessions');
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'createChatSession',
      defaultMessage: 'Failed to create chat session.',
    });
  }
};

/**
 * Delete a chat session
 */
export const deleteChatSession = async (
  sessionId: string,
): Promise<void | ApiError> => {
  try {
    await apiClient.delete(`/chat/sessions/${sessionId}`);
  } catch (error) {
    return handleApiError(error, {
      context: 'deleteChatSession',
      defaultMessage: 'Failed to delete chat session.',
    });
  }
};

/**
 * Get suggested questions based on current context
 */
export const getSuggestedQuestions = async (context?: {
  current_page?: string;
  data_summary?: Record<string, unknown>;
}): Promise<string[] | ApiError> => {
  try {
    const response = await apiClient.post<{ suggestions: string[] }>(
      '/chat/suggestions',
      { context },
    );
    return response?.data?.suggestions;
  } catch (error) {
    return handleApiError(error, {
      context: 'getSuggestedQuestions',
      defaultMessage: 'Failed to get suggested questions.',
    });
  }
};

/**
 * Format message timestamp for display
 */
export const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) {
    return 'Just now';
  } else if (diffMins < 60) {
    return `${diffMins}m ago`;
  } else if (diffHours < 24) {
    return `${diffHours}h ago`;
  } else if (diffDays < 7) {
    return `${diffDays}d ago`;
  } else {
    return date.toLocaleDateString();
  }
};

/**
 * Format session title from first message or timestamp
 */
export const formatSessionTitle = (
  session: ChatSession,
  firstMessage?: string,
): string => {
  if (firstMessage && firstMessage.length > 0) {
    // Use first 50 characters of the first message
    return firstMessage.length > 50
      ? `${firstMessage.substring(0, 50)}...`
      : firstMessage;
  }

  // Fallback to date
  const date = new Date(session.created_at);
  return `Chat from ${date.toLocaleDateString()}`;
};

/**
 * Validate message content
 */
export const validateMessage = (
  message: string,
): { isValid: boolean; error?: string } => {
  if (!message || message.trim().length === 0) {
    return {
      isValid: false,
      error: 'Message cannot be empty',
    };
  }

  if (message.length > 4000) {
    return {
      isValid: false,
      error: 'Message is too long (max 4000 characters)',
    };
  }

  return { isValid: true };
};

/**
 * Get context-aware suggestions based on current page
 */
export const getContextualSuggestions = (currentPage: string): string[] => {
  const suggestions: Record<string, string[]> = {
    dashboard: [
      "What's my spending trend this month?",
      'Show me my top expense categories',
      'How does this month compare to last month?',
      'What are my largest transactions?',
    ],
    transactions: [
      'Categorize my recent transactions',
      'Find transactions over $500',
      'Show me all restaurant expenses',
      "What's my average monthly spending?",
    ],
    reports: [
      'Generate an income statement for this quarter',
      'Show me expense breakdown by category',
      'Create a cash flow report',
      'Compare this year to last year',
    ],
    upload: [
      'How do I format my CSV file?',
      'What columns are required?',
      'Help me map my bank statement columns',
      'Why did my upload fail?',
    ],
  };

  return (
    suggestions[currentPage] || [
      'How can I better categorize my expenses?',
      'What insights can you provide about my finances?',
      'Help me understand my spending patterns',
      'How do I generate a financial report?',
    ]
  );
};

/**
 * Extract actionable items from assistant response
 */
export const extractActionableItems = (
  response: string,
): { type: string; action: string; data?: Record<string, unknown> }[] => {
  const actions: {
    type: string;
    action: string;
    data?: Record<string, unknown>;
  }[] = [];

  // Look for common patterns in AI responses that suggest actions
  if (response.includes('generate') && response.includes('report')) {
    actions.push({
      type: 'generate_report',
      action: 'Generate Report',
    });
  }

  if (response.includes('filter') || response.includes('search')) {
    actions.push({
      type: 'apply_filter',
      action: 'Apply Filter',
    });
  }

  if (response.includes('categorize') || response.includes('category')) {
    actions.push({
      type: 'categorize',
      action: 'Categorize Transactions',
    });
  }

  if (response.includes('export') || response.includes('download')) {
    actions.push({
      type: 'export_data',
      action: 'Export Data',
    });
  }

  return actions;
};

/**
 * Check if response contains data that should be displayed
 */
export const hasDisplayableData = (response: ChatResponse): boolean => {
  return !!(
    response?.assistant_message?.data &&
    (Array.isArray(response?.assistant_message?.data) ||
      Object.keys(response?.assistant_message?.data).length > 0)
  );
};

/**
 * Generate session ID for new conversations
 */
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
