// apps/giki-ai-app/src/services/agentService.ts
import { apiClient } from '@/shared/services/api/apiClient';
import type { AgentResponse } from '@/shared/types/api';
import { handleApiError } from '@/shared/utils/errorHandling';
import {
  generateReportFromQuery,
  // parseReportQuery,
} from '../tools/reportGenerationTool';

// Two-Agent Architecture Integration
export interface CustomerQueryRequest {
  query: string;
  query_type?: 'transactions' | 'insights' | 'accuracy' | 'general';
  parameters?: Record<string, unknown>;
}

export interface AudioInputRequest {
  audio_data: Uint8Array;
  format?: string;
}

export interface DataProcessingRequest {
  operation: 'process_file' | 'categorize' | 'update_rag' | 'simulate_accuracy';
  parameters: Record<string, unknown>;
}

export interface AgentApiResponse {
  success: boolean;
  result: Record<string, unknown>;
  message: string;
  agent_type: 'data_processing' | 'customer_facing';
  cached?: boolean;
}

// Check if message is asking for a report
const isReportQuery = (message: string): boolean => {
  const reportKeywords = [
    'report',
    'show me',
    'generate',
    'create',
    'analyze',
    'spending',
    'income',
    'expense',
    'trend',
    'pivot',
    'chart',
    'graph',
    'table',
    'summary',
    'breakdown',
  ];

  const lowerMessage = message.toLowerCase();
  return reportKeywords.some((keyword) => lowerMessage.includes(keyword));
};

export const sendMessageToAgent = async (
  messageText: string,
  conversationId?: string,
): Promise<AgentResponse> => {
  const endpoint = '/intelligence/agent/customer/query';
  const queryType = 'general';

  console.log('[AgentService] sendMessageToAgent called with:', {
    messageText,
    conversationId,
    endpoint,
  });

  // Check if this is a report generation request
  if (isReportQuery(messageText)) {
    try {
      // Generate report through real-time sync
      const reportResult = await generateReportFromQuery(
        messageText,
        'current-user',
      );

      if (reportResult.success) {
        // Return a synthetic agent response
        return {
          message: {
            id: `agent-msg-${Date.now()}`,
            text: reportResult.message,
            sender: 'agent',
            timestamp: new Date().toISOString(),
            contentType: 'text',
            contentData: {},
          },
          conversation_id: conversationId || `conv-${Date.now()}`,
          metadata: {
            tokens_used: 0,
            confidence: 0.95,
          },
        };
      }
    } catch (error) {
      console.error('Error in report generation:', error);
      // Fall through to regular API call
    }
  }

  try {
    // Use new Customer-Facing Agent endpoint
    const payload: CustomerQueryRequest = {
      query: messageText,
      query_type: queryType,
      parameters: conversationId ? { session_id: conversationId } : undefined,
    };

    console.log(
      '[AgentService] Making API request to:',
      endpoint,
      'with payload:',
      payload,
    );

    const response = await apiClient.post<AgentApiResponse>(endpoint, payload);

    console.log('[AgentService] Received response:', response);

    // improve agent response to match expected AgentResponse format
    const agentResponse: AgentResponse = {
      message: {
        id: `agent-msg-${Date.now()}`,
        text: response?.data?.result.response || response?.data?.message,
        sender: 'agent',
        timestamp: new Date().toISOString(),
      },
      conversation_id: response?.data?.result.conversation_id || conversationId,
      metadata: {
        ...response?.data?.result,
        tokens_used:
          response?.data?.result.tokens_used ||
          Math.floor(messageText.length / 4),
        confidence: response?.data?.result.confidence || 0.85,
      },
    };

    console.log('[AgentService] Returning agent response:', agentResponse);
    return agentResponse;
  } catch (error) {
    console.error('[AgentService] Error in sendMessageToAgent:', error);
    throw handleApiError(error, {
      showToast: false, // Let the component handle the toast
      context: 'sendMessageToAgent',
      defaultMessage: 'Failed to send message to Customer-Facing Agent',
    });
  }
};

// Additional agent service functions for specialized queries
export const queryTransactions = async (
  queryParams: Record<string, unknown> = {},
): Promise<AgentApiResponse> => {
  const endpoint = '/intelligence/agent/customer/query';
  const queryType = 'transactions';

  try {
    const payload: CustomerQueryRequest = {
      query: 'Query transaction data',
      query_type: queryType,
      parameters: queryParams,
    };

    const response = await apiClient.post<AgentApiResponse>(endpoint, payload);

    return response.data;
  } catch (error) {
    console.error('Error in queryTransactions:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'queryTransactions',
      defaultMessage: 'Failed to query transaction data',
    });
  }
};

export const getAccuracyMetrics = async (
  period?: string,
): Promise<AgentApiResponse> => {
  try {
    const payload: CustomerQueryRequest = {
      query: 'Get accuracy metrics',
      query_type: 'accuracy',
      parameters: period ? { period } : undefined,
    };

    const response = await apiClient.post<AgentApiResponse>(
      '/intelligence/agent/customer/query',
      payload,
    );

    return response.data;
  } catch (error) {
    console.error('Error in getAccuracyMetrics:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'getAccuracyMetrics',
      defaultMessage: 'Failed to get accuracy metrics',
    });
  }
};

export const generateInsights = async (
  insightType: string = 'comprehensive',
): Promise<AgentApiResponse> => {
  try {
    const payload: CustomerQueryRequest = {
      query: 'Generate insights',
      query_type: 'insights',
      parameters: { insight_type: insightType },
    };

    const response = await apiClient.post<AgentApiResponse>(
      '/intelligence/agent/customer/query',
      payload,
    );

    return response.data;
  } catch (error) {
    console.error('Error in generateInsights:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'generateInsights',
      defaultMessage: 'Failed to generate insights',
    });
  }
};

export const processAudioInput = async (
  audioData: Uint8Array,
  format: string = 'wav',
): Promise<AgentApiResponse> => {
  try {
    // Create FormData for file upload (required by FastAPI UploadFile)
    const formData = new FormData();
    const audioBlob = new Blob([audioData], { type: `audio/${format}` });
    formData.append('audio_file', audioBlob, `audio.${format}`);

    const response = await apiClient.post<AgentApiResponse>(
      '/intelligence/agent/customer/audio',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return response.data;
  } catch (error) {
    console.error('Error in processAudioInput:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'processAudioInput',
      defaultMessage: 'Failed to process audio input with Gemini 2.0 Flash',
    });
  }
};

export const processDataOperation = async (
  operation: string,
  parameters: Record<string, unknown>,
): Promise<AgentApiResponse> => {
  const endpoint = '/intelligence/agent/data/process';

  try {
    const payload: DataProcessingRequest = {
      operation: operation as unknown,
      parameters,
    };

    const response = await apiClient.post<AgentApiResponse>(endpoint, payload);

    return response.data;
  } catch (error) {
    console.error('Error in processDataOperation:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'processDataOperation',
      defaultMessage: 'Failed to process data operation',
    });
  }
};

// Note: Performance monitoring functions removed as part of reorganization

// Preload common queries for better performance
export const preloadCommonQueries = async () => {
  const commonQueries = [
    { query: 'Show me my recent transactions', type: 'transactions' },
    { query: 'Generate spending insights', type: 'insights' },
    { query: 'Get accuracy metrics', type: 'accuracy' },
  ];

  const preloadPromises = commonQueries.map(async ({ type }) => {
    try {
      if (type === 'transactions') {
        await queryTransactions();
      } else if (type === 'insights') {
        await generateInsights();
      } else if (type === 'accuracy') {
        await getAccuracyMetrics();
      }
    } catch (error) {
      console.warn(`Failed to preload ${type} query:`, error);
    }
  });

  await Promise.allSettled(preloadPromises);
};
