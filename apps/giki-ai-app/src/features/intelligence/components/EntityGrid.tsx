import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shared/components/ui/avatar';
import type { Entity } from '@/shared/types/knowledgehub'; // Corrected import

interface EntityGridProps {
  entities: Entity[];
  onEntitySelect: (entity: Entity) => void;
  formatCurrency: (amount: number) => string;
}

export const EntityGrid: React.FC<EntityGridProps> = ({
  entities,
  onEntitySelect,
  formatCurrency,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
      {' '}
      {/* Increased gap to 24px (1.5rem) */}
      {entities.map((entity) => (
        // Applied card styling from design: White BG, optional light grey border, medium radius, subtle shadow
        <Card
          key={entity.entity}
          className="overflow-hidden cursor-pointer bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200" // Using rounded-lg (8px) and shadow-md
          onClick={() => onEntitySelect(entity)}
        >
          <CardHeader className="pb-3 pt-4 px-4">
            {' '}
            {/* Adjusted padding */}
            <div className="flex flex-wrap items-center gap-3">
              {' '}
              {/* Increased gap */}
              <Avatar className="h-10 w-10">
                {' '}
                {/* Slightly larger Avatar */}
                <AvatarImage src={entity.logo} alt={entity.entity} />
                <AvatarFallback>
                  {entity?.entity?.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {/* H4: 16px, Medium (500), Primary Text. Tailwind text-base is 16px. font-medium is 500. */}
              <CardTitle className="text-base font-medium text-primary">
                {entity.entity}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pb-3 px-4 overflow-hidden">
            {' '}
            {/* Adjusted padding */}
            <div className="space-y-2">
              <div className="flex flex-wrap justify-between items-center">
                {/* Secondary Text: 15px, Regular (400), Steel Grey. text-sm is 14px. */}
                <span className="text-sm text-secondary">Total Spending</span>
                <span className="font-semibold text-primary">
                  {formatCurrency(entity.spending)}
                </span>{' '}
                {/* Made amount bolder */}
              </div>
              <div className="flex flex-wrap justify-between items-center">
                <span className="text-sm text-secondary">Category</span>
                <Badge
                  variant="outline"
                  className="text-secondary border-secondary/40 max-w-[150px] truncate"
                >
                  {entity.category}
                </Badge>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-3 pb-4 px-4">
            {' '}
            {/* Adjusted padding */}
            {/* Secondary/Outline Button style: Transparent/White BG, Primary Text, Primary Border */}
            <Button
              className="max-w-full w-full border-primary text-primary hover:bg-primary/10"
              variant="outline" // Shadcn outline should align with theme
              size="sm"
              // Explicitly styling for secondary/outline
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click from also firing
                onEntitySelect(entity);
              }}
            >
              View Details
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default EntityGrid;
