import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Badge } from '@/shared/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import {
  Plus,
  RefreshCw,
  Search,
  Trash2,
  Database,
  FileText,
  Settings,
  Brain,
  TestTube,
} from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading'; // Import Loading component
import { Textarea } from '@/shared/components/ui/textarea';
import {
  RAGCorpus,
  RAGCorpusStatus,
  createRAGCorpus,
  listRAGCorpora,
  deleteRAGCorpus,
  categorizeWithRAG,
} from '../services/ragCorpusService';
// TODO: Uncomment when useRAGCategorization is available
// import { useRAGCategorization } from '@/shared/hooks/useApi';

interface RagCorpusManagementProps {
  onCorpusSelect?: (corpus: RAGCorpus) => void;
}

const RagCorpusManagement: React.FC<RagCorpusManagementProps> = ({
  onCorpusSelect,
}) => {
  const navigate = useNavigate();
  const [corpora, setCorpora] = useState<RAGCorpus[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newCorpusName, setNewCorpusName] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [corpusToDelete, setCorpusToDelete] = useState<RAGCorpus | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Enhanced RAG categorization testing
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [selectedCorpusForTest, setSelectedCorpusForTest] =
    useState<RAGCorpus | null>(null);
  const [testTransactionText, setTestTransactionText] = useState('');
  // RAG categorization state
  interface CategorizationResult {
    categorization_result?: {
      suggested_category?: string;
      confidence?: number;
      similar_transactions?: number;
      reasoning?: string;
    };
  }

  const [ragCategorizationState, setRagCategorizationState] = useState<{
    isLoading: boolean;
    data: CategorizationResult | null;
    error: string | null;
  }>({ isLoading: false, data: null, error: null });

  const testCategorization = async () => {
    if (!selectedCorpusForTest || !testTransactionText.trim()) {
      setRagCategorizationState((prev) => ({
        ...prev,
        error: 'Please select a corpus and enter transaction text',
      }));
      return;
    }

    setRagCategorizationState((prev) => ({
      ...prev,
      isLoading: true,
      error: null,
    }));

    try {
      const result = await categorizeWithRAG({
        query_text: testTransactionText,
        corpus_id: selectedCorpusForTest.id,
        top_k: 5,
      });

      setRagCategorizationState({
        isLoading: false,
        data: result,
        error: null,
      });
    } catch (error) {
      setRagCategorizationState({
        isLoading: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to categorize',
      });
    }
  };

  const resetCategorization = () => {
    setRagCategorizationState({ isLoading: false, data: null, error: null });
  };

  const fetchCorpora = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await listRAGCorpora();
      setCorpora(response.items);
    } catch {
      setError('Failed to fetch RAG corpora. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void fetchCorpora();
  }, []);

  const handleCreateCorpus = async () => {
    if (!newCorpusName.trim()) {
      setError('Corpus name cannot be empty');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      await createRAGCorpus(newCorpusName);
      setNewCorpusName('');
      setCreateDialogOpen(false);
      await fetchCorpora();
    } catch {
      setError('Failed to create RAG corpus. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCorpus = async () => {
    if (!corpusToDelete) return;

    setLoading(true);
    setError(null);
    try {
      await deleteRAGCorpus(corpusToDelete.id);
      setDeleteDialogOpen(false);
      setCorpusToDelete(null);
      await fetchCorpora();
    } catch {
      setError('Failed to delete RAG corpus. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCorpus = (corpus: RAGCorpus) => {
    if (onCorpusSelect) {
      onCorpusSelect(corpus);
    }
  };

  const handleTestCategorization = async () => {
    if (!selectedCorpusForTest || !testTransactionText.trim()) {
      setError('Please select a corpus and enter transaction text');
      return;
    }

    try {
      await testCategorization();
    } catch {
      setError('Failed to test RAG categorization. Please try again.');
    }
  };

  const openTestDialog = (corpus: RAGCorpus) => {
    setSelectedCorpusForTest(corpus);
    setTestDialogOpen(true);
    resetCategorization();
    setTestTransactionText('');
  };

  const filteredCorpora = corpora.filter((corpus) =>
    corpus?.display_name?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const getStatusBadgeColor = (status: RAGCorpusStatus) => {
    switch (status) {
      case RAGCorpusStatus.ACTIVE:
        return 'bg-green-500';
      case RAGCorpusStatus.CREATED:
        return 'bg-blue-500';
      case RAGCorpusStatus.IMPORTING:
        return 'bg-yellow-500';
      case RAGCorpusStatus.FAILED:
        return 'bg-red-500';
      case RAGCorpusStatus.DELETED:
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-wrap justify-between items-center">
          <div>
            <CardTitle className="text-xl">RAG Corpus Management</CardTitle>
            <CardDescription>
              Manage your RAG corpora for transaction categorization
            </CardDescription>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              onClick={() => void fetchCorpora()}
              disabled={loading}
            >
              {loading ? (
                <Loading size="sm" text="" className="mr-2 py-0" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              <span className="ml-2">Refresh</span>
            </Button>
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              onClick={() => navigate('/rag-corpus-management')}
            >
              <Settings className="h-4 w-4 mr-2" />
              Advanced Management
            </Button>
            <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="max-w-full" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Corpus
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New RAG Corpus</DialogTitle>
                  <DialogDescription>
                    Enter a name for your new RAG corpus.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <Input
                    placeholder="Corpus Name"
                    value={newCorpusName}
                    onChange={(e) => setNewCorpusName(e?.target?.value)}
                  />
                </div>
                <DialogFooter>
                  <Button
                    className="max-w-full"
                    variant="outline"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="max-w-full"
                    onClick={() => void handleCreateCorpus()}
                    disabled={loading}
                  >
                    {loading ? (
                      <Loading size="sm" text="" className="mr-2 py-0" />
                    ) : null}
                    Create
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent className="overflow-hidden">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="mb-4 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 truncate[hsl(var(--giki-input-placeholder))]" />
          <Input
            placeholder="Search corpora..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e?.target?.value)}
          />
        </div>

        {loading && corpora.length === 0 ? (
          <Loading text="Loading RAG corpora..." className="h-40" />
        ) : filteredCorpora.length === 0 ? (
          <div className="flex flex-wrap flex-col justify-center items-center h-40 text-center">
            <Database className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              {searchTerm
                ? 'No corpora match your search'
                : 'No RAG corpora found. Create one to get started.'}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCorpora.map((corpus) => (
                <TableRow key={corpus.id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-wrap items-center">
                      <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                      {corpus.display_name}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={`${getStatusBadgeColor(corpus.status)} max-w-[150px] truncate`}
                    >
                      {corpus.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {corpus.created_at
                      ? new Date(corpus.created_at).toLocaleDateString()
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        className="max-w-full"
                        variant="outline"
                        size="sm"
                        onClick={() => handleSelectCorpus(corpus)}
                      >
                        Select
                      </Button>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        size="sm"
                        onClick={() => openTestDialog(corpus)}
                        title="Test RAG Categorization"
                      >
                        <Brain className="h-4 w-4" />
                      </Button>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setCorpusToDelete(corpus);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete RAG Corpus</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete the corpus &quot;
                {corpusToDelete?.display_name}&quot;? This action cannot be
                undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                className="max-w-full"
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="max-w-full"
                variant="outline"
                onClick={() => void handleDeleteCorpus()}
                disabled={loading}
              >
                {loading ? (
                  <Loading size="sm" text="" className="mr-2 py-0" />
                ) : null}
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Enhanced RAG Categorization Test Dialog */}
        <Dialog open={testDialogOpen} onOpenChange={setTestDialogOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex flex-wrap items-center gap-2">
                <Brain className="h-5 w-5" />
                Test RAG Categorization
              </DialogTitle>
              <DialogDescription>
                Test how well the corpus &quot;
                {selectedCorpusForTest?.display_name}&quot; categorizes
                transactions using ADK RAGManagementService.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="truncatelabel">Transaction Description</label>
                <Textarea
                  placeholder="Enter a transaction description to categorize (e.g., 'Starbucks Coffee Shop Purchase $5.50')"
                  value={testTransactionText}
                  onChange={(e) => setTestTransactionText(e?.target?.value)}
                  rows={3}
                  className="mt-1"
                />
              </div>

              {ragCategorizationState.isLoading && (
                <div className="flex flex-wrap items-center gap-2 text-info">
                  <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">Analyzing with RAG...</span>
                </div>
              )}

              {ragCategorizationState.data && (
                <Card>
                  <CardContent className="p-4 overflow-hidden">
                    <h4 className="font-medium mb-3 flex flex-wrap items-center gap-2">
                      <TestTube className="w-4 h-4" />
                      Categorization Result
                    </h4>
                    <div className="space-y-2">
                      <div className="flex flex-wrap justify-between items-center">
                        <span className="truncatelabel">
                          Suggested Category:
                        </span>
                        <Badge
                          variant="outline"
                          className="max-w-[150px] truncate"
                        >
                          {ragCategorizationState.data?.categorization_result
                            ?.suggested_category || 'Unknown'}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap justify-between items-center">
                        <span className="truncatelabel">Confidence:</span>
                        <span className="text-sm">
                          {(
                            (ragCategorizationState.data?.categorization_result
                              ?.confidence || 0) * 100
                          ).toFixed(1)}
                          %
                        </span>
                      </div>
                      <div className="flex flex-wrap justify-between items-center">
                        <span className="truncatelabel">
                          Similar Transactions:
                        </span>
                        <span className="text-sm">
                          {ragCategorizationState.data?.categorization_result
                            ?.similar_transactions || 0}
                        </span>
                      </div>
                      {ragCategorizationState.data?.categorization_result
                        ?.reasoning && (
                        <div className="mt-3">
                          <span className="truncatelabel">Reasoning:</span>
                          <p className="truncatebody-small mt-1">
                            {
                              ragCategorizationState.data?.categorization_result
                                .reasoning
                            }
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {ragCategorizationState.error && (
                <Alert variant="destructive">
                  <AlertTitle>Categorization Failed</AlertTitle>
                  <AlertDescription>
                    {ragCategorizationState.error}
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <DialogFooter>
              <Button
                className="max-w-full"
                variant="outline"
                onClick={() => setTestDialogOpen(false)}
              >
                Close
              </Button>
              <Button
                className="max-w-full"
                onClick={() => void handleTestCategorization()}
                disabled={
                  !testTransactionText.trim() ||
                  ragCategorizationState.isLoading
                }
              >
                {ragCategorizationState.isLoading ? (
                  <Loading size="sm" text="" className="mr-2 py-0" />
                ) : (
                  <Brain className="h-4 w-4 mr-2" />
                )}
                Test Categorization
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default RagCorpusManagement;
