import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import type { Entity, EntitySortState } from '@/shared/types/knowledgehub';

interface EntityTableProps {
  entities: Entity[];
  onSortChange: (sortBy: EntitySortState['sortBy']) => void;
  currentSortBy: EntitySortState['sortBy'];
  currentSortOrder: 'asc' | 'desc';
  onEntitySelect: (entity: Entity) => void;
  formatCurrency: (amount: number) => string;
}

export const EntityTable: React.FC<EntityTableProps> = ({
  entities,
  onSortChange,
  currentSortBy,
  currentSortOrder,
  onEntitySelect,
  formatCurrency,
}) => {
  // Helper to determine if a column is the current sort column
  const isSortColumn = (columnName: EntitySortState['sortBy']) =>
    currentSortBy === columnName;

  return (
    <div className="rounded-md border bg-gradient-card shadow-premium">
      <Table>
        <TableHeader>
          <TableRow>
            {/* Updated TableHead to use font-semibold and primary text color from theme */}
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => onSortChange('name')} // Corrected to 'name'
            >
              Entity Name
              {isSortColumn('name') && ( // Corrected to 'name'
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="text-right cursor-pointer text-foreground font-semibold"
              onClick={() => onSortChange('spending')}
            >
              Total Spending
              {isSortColumn('spending') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => onSortChange('category')} // This is now a valid key
            >
              Category
              {isSortColumn('category') && ( // This is now a valid key
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => onSortChange('transactionCount')}
            >
              Transactions
              {isSortColumn('transactionCount') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => onSortChange('lastTransactionDate')}
            >
              Last Transaction
              {isSortColumn('lastTransactionDate') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead className="w-[100px] text-foreground font-semibold">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {entities.map((entity) => (
            <TableRow
              key={entity.id}
              onClick={() => onEntitySelect(entity)}
              className="cursor-pointer hover:bg-muted/50"
            >
              {' '}
              {/* Added hover consistent with design spec */}
              <TableCell className="text-left">{entity.entity}</TableCell>
              <TableCell className="text-right text-muted-foreground text-sm">
                {formatCurrency(entity.spending)}
              </TableCell>
              <TableCell>
                {entity.category && (
                  <Badge
                    variant="outline"
                    className="text-muted-foreground border-muted-foreground/40 max-w-[150px] truncate"
                  >
                    {entity.category}
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-center text-muted-foreground text-sm">
                {entity.transactionCount}
              </TableCell>
              <TableCell className="text-muted-foreground text-sm">
                {entity.lastTransactionDate
                  ? new Date(entity.lastTransactionDate).toLocaleDateString()
                  : 'N/A'}
              </TableCell>
              <TableCell>
                <Button
                  className="max-w-full"
                  variant="outline" // This should pick up themed styles
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEntitySelect(entity);
                  }}
                >
                  Details
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

// Re-eval trigger comment
export default EntityTable;
