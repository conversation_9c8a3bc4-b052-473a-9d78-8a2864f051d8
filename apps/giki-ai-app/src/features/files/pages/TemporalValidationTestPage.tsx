/**
 * Temporal Validation Test Page
 *
 * A standalone page to test and demonstrate the temporal validation workflow.
 */

import React from 'react';
import { TemporalValidationFlow } from '../components/temporal/TemporalValidationFlow';
import { useNavigate } from 'react-router-dom';

export const TemporalValidationTestPage: React.FC = () => {
  const navigate = useNavigate();

  const handleComplete = () => {
    // Navigate to dashboard after successful validation
    navigate('/dashboard');
  };

  const handleSkip = () => {
    // Allow skipping for testing
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-muted/50">
      <TemporalValidationFlow onComplete={handleComplete} onSkip={handleSkip} />
    </div>
  );
};

export default TemporalValidationTestPage;
