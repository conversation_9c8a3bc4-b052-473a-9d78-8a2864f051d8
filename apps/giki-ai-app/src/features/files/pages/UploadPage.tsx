/**
 * Work Page - Main Production Workspace
 * Unified desktop workspace for daily financial operations
 */
import React, { useState, useEffect } from 'react';
import DataUpload from '@/features/files/components/DataUpload';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import {
  AlertCircle,
  FileText,
  Upload,
  BarChart3,
  CheckCircle2,
  Clock,
  DollarSign,
  Receipt,
  Filter,
  ArrowRight,
  RefreshCw,
  Calendar,
  XCircle,
  FileBarChart,
  FileUp,
  Settings,
  Plus,
  Search,
  Download,
} from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading';
import { Button } from '@/shared/components/ui/button';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import { Badge } from '@/shared/components/ui/badge';
import { Scroll<PERSON><PERSON> } from '@/shared/components/ui/scroll-area';
import { Separator } from '@/shared/components/ui/separator';
import { CardFooter } from '@/shared/components/ui/card';
import { format } from 'date-fns';
import {
  getAllUploads,
  convertUploadRecord,
} from '@/features/files/services/uploadService';
import {
  getWorkspaceStats,
  getQuickReports,
  getPendingTransactions,
} from '@/features/files/services/workPageService';

// Workspace data interfaces
interface WorkspaceStats {
  totalTransactions: number;
  pendingReview: number;
  monthlySpending: number;
  categoriesCount: number;
}

interface QuickReport {
  id: string;
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
}

interface PendingTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  suggestedCategory: string;
  confidence: number;
}

interface RecentUpload {
  id: string;
  filename: string;
  uploadDate: Date;
  status: 'pending' | 'processing' | 'completed' | 'error';
  recordCount?: number;
  errorMessage?: string;
}

interface WorkPageProps {
  onUploadSuccess: (
    uploadId: string,
    processingCompleteCallback?: (
      uploadId: string,
      reportId: string | null,
      processingStats?: {
        totalRows?: number;
        successfulRows?: number;
        failedRows?: number;
        dataQualityScore?: number;
      },
    ) => void,
  ) => void;
}

const WorkPage: React.FC<WorkPageProps> = ({ onUploadSuccess }) => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('upload');
  const [recentUploads, setRecentUploads] = useState<RecentUpload[]>([]);
  const [workspaceStats, setWorkspaceStats] = useState<WorkspaceStats>({
    totalTransactions: 0,
    pendingReview: 0,
    monthlySpending: 0,
    categoriesCount: 0,
  });
  const [quickReports, setQuickReports] = useState<QuickReport[]>([]);
  const [pendingTransactions, setPendingTransactions] = useState<
    PendingTransaction[]
  >([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Mobile panel state management
  const [showReviewQueue, setShowReviewQueue] = useState(false);
  const [showQuickTools, setShowQuickTools] = useState(false);

  // Load workspace data including uploads, stats, and pending transactions
  useEffect(() => {
    const loadWorkspaceData = async () => {
      setIsLoadingHistory(true);
      setError(null);
      try {
        // Load all data in parallel for better performance
        const [uploadsResult, stats, reports, pending] = await Promise.all([
          getAllUploads(),
          getWorkspaceStats(),
          getQuickReports(),
          getPendingTransactions(5), // Get top 5 pending transactions
        ]);

        // Process uploads
        if (
          uploadsResult &&
          typeof uploadsResult === 'object' &&
          'isError' in uploadsResult &&
          uploadsResult.isError
        ) {
          throw new Error('Failed to fetch uploads');
        }

        const uploads = Array.isArray(uploadsResult) ? uploadsResult : [];
        const convertedUploads: RecentUpload[] =
          uploads.map(convertUploadRecord);
        convertedUploads.sort(
          (a, b) => b?.uploadDate?.getTime() - a?.uploadDate?.getTime(),
        );
        setRecentUploads(convertedUploads);

        // Set real workspace statistics
        setWorkspaceStats(stats);

        // Set real quick reports
        setQuickReports(reports);

        // Set real pending transactions
        setPendingTransactions(pending);
      } catch (err) {
        console.error('Error loading workspace data:', err);
        setError('Failed to load workspace data. Please try again.');
        // Set default values on error
        setWorkspaceStats({
          totalTransactions: 0,
          pendingReview: 0,
          monthlySpending: 0,
          categoriesCount: 0,
        });
        setQuickReports([]);
        setPendingTransactions([]);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    void loadWorkspaceData();
  }, []);

  // Handle file upload success (currently unused but may be needed for future features)
  // const handleFileUploadSuccess = (uploadId: string, headers?: string[]) => {
  //   console.log(
  //     'File upload success on UploadPage, Upload ID:',
  //     uploadId,
  //     'Headers:',
  //     headers,
  //   );

  //   setCurrentUploadId(uploadId);

  //   // Add the new upload to the recent uploads list
  //   const newUpload: RecentUpload = {
  //     id: uploadId,
  //     filename: 'new_upload.csv', // In a real app, this would come from the API response
  //     uploadDate: new Date(),
  //     status: 'processing',
  //   };

  //   setRecentUploads((prev) => [newUpload, ...prev]);
  //   setSuccessMessage('File uploaded successfully!');

  //   // Clear success message after 3 seconds
  //   setTimeout(() => {
  //     setSuccessMessage(null);
  //   }, 3000);

  //   if (headers && headers.length > 0) {
  //     setIsMappingModalOpen(true);
  //   } else {
  //     onUploadSuccess(uploadId);
  //   }
  // };

  // Handle success notification after file processing
  // const _handleFileProcessingSuccess = () => {
  //   setSuccessMessage('File processed successfully!');

  //   // Clear success message and navigate to review
  //   setTimeout(() => {
  //     setSuccessMessage(null);
  //     void navigate('/review');
  //   }, 1500);
  // };

  // Handle refresh of all workspace data
  const handleRefreshUploads = async () => {
    setIsLoadingHistory(true);
    setError(null);
    try {
      // Refresh all data in parallel
      const [uploadsResult, stats, reports, pending] = await Promise.all([
        getAllUploads(),
        getWorkspaceStats(),
        getQuickReports(),
        getPendingTransactions(5),
      ]);

      // Process uploads
      if (
        uploadsResult &&
        typeof uploadsResult === 'object' &&
        'isError' in uploadsResult &&
        uploadsResult.isError
      ) {
        throw new Error('Failed to refresh uploads');
      }

      // Convert API upload records to frontend format
      const uploads = Array.isArray(uploadsResult) ? uploadsResult : [];
      const convertedUploads: RecentUpload[] = uploads.map(convertUploadRecord);

      // Sort by upload date (newest first)
      convertedUploads.sort(
        (a, b) => b?.uploadDate?.getTime() - a?.uploadDate?.getTime(),
      );

      setRecentUploads(convertedUploads);
      setWorkspaceStats(stats);
      setQuickReports(reports);
      setPendingTransactions(pending);
      setSuccessMessage('Upload history refreshed');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error refreshing recent uploads:', err);
      setError('Failed to refresh upload history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Handle view details of an upload
  const handleViewUploadDetails = (_uploadId: string) => {
    // Viewing details for upload
    // In a real app, this would navigate to a details page or open a modal
    // For now, just navigate to the review page
    void navigate('/review');
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header Section */}
      <div className="border-b border-border bg-card/50 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-foreground">
              Work Dashboard
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              Manage transactions, review categorizations, and generate reports
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* Mobile Panel Toggles */}
            <div className="flex lg:hidden gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowReviewQueue(!showReviewQueue)}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Queue ({workspaceStats.pendingReview})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowQuickTools(!showQuickTools)}
                className="xl:hidden flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Tools
              </Button>
            </div>

            {/* Desktop Actions */}
            <Button
              onClick={() => setActiveTab('upload')}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              <span className="hidden sm:inline">Upload Data</span>
              <span className="sm:hidden">Upload</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/admin')}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Settings</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Dashboard Overview - All Interactive */}
      <div className="px-6 py-4 border-b border-border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('review')}
            className="w-full text-left"
          >
            <Card className="hover:shadow-md hover:border-primary/50 transition-all duration-200 cursor-pointer">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground group-hover:text-primary">
                      Total Transactions
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {workspaceStats.totalTransactions.toLocaleString()}
                    </p>
                    <p className="text-xs text-primary mt-1">View all →</p>
                  </div>
                  <Receipt className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>
          </button>

          <button
            onClick={() => {
              // Jump to review queue section
              const reviewSection = document.querySelector(
                '[data-section="review-queue"]',
              );
              reviewSection?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="w-full text-left"
          >
            <Card className="hover:shadow-md hover:border-orange-400 transition-all duration-200 cursor-pointer group">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground group-hover:text-orange-600">
                      Pending Review
                    </p>
                    <p className="text-2xl font-bold text-orange-600">
                      {workspaceStats.pendingReview}
                    </p>
                    <p className="text-xs text-orange-600 mt-1">Review now →</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </button>

          <button
            onClick={() => {
              // Navigate to spending report
              navigate('/reports?type=spending');
            }}
            className="w-full text-left"
          >
            <Card className="hover:shadow-md hover:border-green-400 transition-all duration-200 cursor-pointer group">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground group-hover:text-green-600">
                      Monthly Spending
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      ${workspaceStats.monthlySpending.toLocaleString()}
                    </p>
                    <p className="text-xs text-green-600 mt-1">View report →</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </button>

          <button
            onClick={() => navigate('/admin')}
            className="w-full text-left"
          >
            <Card className="hover:shadow-md hover:border-primary/50 transition-all duration-200 cursor-pointer group">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground group-hover:text-primary">
                      Categories
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {workspaceStats.categoriesCount}
                    </p>
                    <p className="text-xs text-primary mt-1">Manage →</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>
          </button>
        </div>
      </div>

      {/* Alerts Section */}
      {(successMessage || error) && (
        <div className="px-6 py-2">
          {successMessage && (
            <Alert className="mb-2 status-success">
              <CheckCircle2 className="h-4 w-4" />
              <AlertTitle className="font-semibold">Success</AlertTitle>
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}
          {error && (
            <Alert variant="destructive" className="mb-2">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle className="font-semibold">Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Responsive Three-Column Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Column: Transaction Review Queue - Responsive */}
        <div
          className="hidden lg:flex lg:w-1/3 xl:w-1/4 2xl:w-1/5 border-r border-border bg-card/30 flex-col"
          data-section="review-queue"
        >
          <div className="p-4 border-b border-border flex-shrink-0">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">
                Review Queue
              </h3>
              <button
                onClick={() => {
                  // Refresh pending transactions
                  setPendingTransactions([...pendingTransactions]);
                }}
                className="flex items-center gap-1 text-sm text-primary hover:text-primary/80"
              >
                <Badge variant="secondary">
                  {workspaceStats.pendingReview} pending
                </Badge>
                <RefreshCw className="h-3 w-3" />
              </button>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Click transactions to review and approve categorizations
            </p>
          </div>
          <div className="flex-1 p-4 space-y-3 overflow-y-auto min-h-0">
            {pendingTransactions.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle2 className="h-12 w-12 text-success mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">
                  All transactions reviewed!
                </p>
              </div>
            ) : (
              pendingTransactions.map((transaction) => (
                <button
                  key={transaction.id}
                  onClick={() => {
                    // Open transaction review modal or navigate to detail view
                    // For now, navigate to transactions page with filter
                    navigate(`/transactions?id=${transaction.id}`);
                  }}
                  className="w-full text-left"
                >
                  <Card className="p-3 hover:bg-accent/50 hover:border-primary/50 cursor-pointer transition-all duration-200 group">
                    <div className="space-y-2">
                      <div className="flex justify-between items-start">
                        <p className="text-sm font-medium text-foreground truncate group-hover:text-primary">
                          {transaction.description}
                        </p>
                        <span className="text-sm font-semibold text-foreground">
                          ${Math.abs(transaction.amount).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">
                          {transaction.date}
                        </span>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              transaction.confidence > 0.8
                                ? 'default'
                                : 'secondary'
                            }
                            className="text-xs"
                          >
                            {transaction.suggestedCategory}
                          </Badge>
                          <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                        </div>
                      </div>
                      <div className="w-full bg-secondary rounded-full h-1">
                        <div
                          className="bg-primary h-1 rounded-full transition-all duration-300"
                          style={{ width: `${transaction.confidence * 100}%` }}
                        />
                      </div>
                      <p className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                        Click to review and approve
                      </p>
                    </div>
                  </Card>
                </button>
              ))
            )}
          </div>
        </div>

        {/* Center Column: Main Work Area - Always Visible */}
        <div className="flex-1 flex flex-col min-w-0">
          <Tabs
            defaultValue="upload"
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <TabsList className="flex w-full justify-start p-1 bg-muted/50 border-b border-border">
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline">Upload</span>
              </TabsTrigger>
              <TabsTrigger value="review" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Activity</span>
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span className="hidden sm:inline">History</span>
              </TabsTrigger>
            </TabsList>

            {/* Upload Tab - Production Data Upload */}
            <TabsContent value="upload" className="flex-1 p-6">
              <div className="h-full flex flex-col">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Upload Production Data
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Upload new transaction data for AI categorization. These
                    transactions should NOT have category labels.
                  </p>
                </div>

                <Card className="flex-1">
                  <CardContent className="p-6 h-full">
                    <DataUpload onUploadSuccess={onUploadSuccess} />

                    <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            Production Upload Guidelines
                          </h4>
                          <ul className="text-xs text-blue-800 dark:text-blue-200 mt-1 space-y-1">
                            <li>
                              • Upload NEW transaction data without category
                              labels
                            </li>
                            <li>
                              • AI will automatically categorize based on your
                              training data
                            </li>
                            <li>
                              • Review suggested categories in the Review Queue
                            </li>
                            <li>
                              • For historical data with categories, use
                              onboarding instead
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Activity Tab - Recent Transactions Stream */}
            <TabsContent value="review" className="flex-1 p-6">
              <div className="h-full flex flex-col">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Recent Activity
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Latest transactions and categorization results
                  </p>
                </div>

                <Card className="flex-1">
                  <CardContent className="p-0 h-full">
                    <div className="p-4 border-b border-border">
                      <div className="flex items-center gap-4">
                        <div className="flex-1">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <input
                              type="text"
                              placeholder="Search transactions..."
                              className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                            />
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                      </div>
                    </div>

                    <ScrollArea className="h-full">
                      <div className="p-4 space-y-3">
                        {/* Sample recent transactions */}
                        {[
                          {
                            id: '1',
                            description: 'Microsoft Office 365 Subscription',
                            amount: -299.99,
                            category: 'Software',
                            confidence: 0.95,
                            date: '2024-01-15',
                            status: 'confirmed',
                          },
                          {
                            id: '2',
                            description: 'Starbucks Coffee',
                            amount: -12.5,
                            category: 'Meals & Entertainment',
                            confidence: 0.88,
                            date: '2024-01-15',
                            status: 'confirmed',
                          },
                          {
                            id: '3',
                            description: 'Client Payment - ABC Corp',
                            amount: 5000.0,
                            category: 'Revenue',
                            confidence: 0.92,
                            date: '2024-01-14',
                            status: 'confirmed',
                          },
                        ].map((transaction) => (
                          <Card
                            key={transaction.id}
                            className="p-4 hover:bg-accent/50"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <p className="font-medium text-foreground">
                                  {transaction.description}
                                </p>
                                <div className="flex items-center gap-4 mt-2">
                                  <span className="text-sm text-muted-foreground">
                                    {transaction.date}
                                  </span>
                                  <Badge
                                    variant={
                                      transaction.confidence > 0.9
                                        ? 'default'
                                        : 'secondary'
                                    }
                                  >
                                    {transaction.category}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground">
                                    {Math.round(transaction.confidence * 100)}%
                                    confidence
                                  </span>
                                </div>
                              </div>
                              <div className="text-right">
                                <span
                                  className={`text-lg font-semibold ${
                                    transaction.amount > 0
                                      ? 'text-green-600'
                                      : 'text-foreground'
                                  }`}
                                >
                                  {transaction.amount > 0 ? '+' : ''}$
                                  {Math.abs(transaction.amount).toFixed(2)}
                                </span>
                                <div className="mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {transaction.status}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* History Tab */}
            <TabsContent value="history" className="mt-0">
              <Card className="w-full shadow-md">
                <CardHeader className="pb-4">
                  <div className="flex flex-wrap justify-between items-center">
                    <div>
                      <CardTitle className="section-title">
                        Recent Uploads
                      </CardTitle>
                      <CardDescription className="truncatebody text-secondary">
                        View and manage your uploaded transaction files
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => void handleRefreshUploads()}
                      disabled={isLoadingHistory}
                      className="max-w-full text-button text-primary border-primary hover:bg-primary/5"
                    >
                      {isLoadingHistory ? (
                        <Loading size="sm" className="py-0 mr-2" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Refresh
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="px-6 py-4 overflow-hidden">
                  {isLoadingHistory ? (
                    <div className="flex flex-wrap items-center justify-center py-12">
                      <Loading text="Loading upload history..." />
                    </div>
                  ) : recentUploads.length === 0 ? (
                    <div className="text-center py-12">
                      <FileText className="h-12 w-12 text-secondary mx-auto mb-4" />
                      <h3 className="truncateh3">No uploads found</h3>
                      <p className="truncatebody-small text-muted max-w-md mx-auto">
                        You haven&apos;t uploaded any transaction files yet. Go
                        to Upload tab to get started.
                      </p>
                      <Button
                        onClick={() => setActiveTab('upload')}
                        className="max-w-full mt-4 text-button bg-secondary text-secondary-foreground hover:bg-secondary/90 focus-visible:ring-ring"
                      >
                        <FileUp className="mr-2 h-4 w-4" />
                        Upload New File
                      </Button>
                    </div>
                  ) : (
                    <ScrollArea className="h-[400px] overflow-y-auto pr-4">
                      <div className="space-y-4">
                        {recentUploads.map((upload) => (
                          <div
                            key={upload.id}
                            className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                          >
                            <div className="flex flex-wrap justify-between items-start">
                              <div className="flex flex-wrap items-start space-x-3">
                                <div className="mt-1">
                                  <FileText className="h-5 w-5 text-secondary" />
                                </div>
                                <div>
                                  <h3 className="truncatebody">
                                    {upload.filename}
                                  </h3>
                                  <div className="flex flex-wrap items-center mt-1 truncatebody-small text-muted">
                                    <Calendar className="h-3.5 w-3.5 mr-1" />
                                    <span>
                                      {format(
                                        upload.uploadDate,
                                        'MMM d, yyyy h:mm a',
                                      )}
                                    </span>
                                  </div>
                                  <div className="flex flex-wrap items-center mt-2">
                                    <Badge
                                      variant={
                                        upload.status === 'completed'
                                          ? 'default' // 'default' often maps to primary or a neutral success
                                          : upload.status === 'processing'
                                            ? 'secondary' // 'secondary' for in-progress
                                            : upload.status === 'error'
                                              ? 'destructive'
                                              : 'outline' // 'outline' for pending
                                      }
                                      className={`max-w-[150px] truncate ${
                                        upload.status === 'completed'
                                          ? 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-400'
                                          : upload.status === 'processing'
                                            ? 'bg-primary/20 text-primary-foreground hover:bg-primary/30'
                                            : upload.status === 'error'
                                              ? 'bg-destructive/20 text-destructive-foreground hover:bg-destructive/30'
                                              : ''
                                      }`}
                                    >
                                      {upload.status === 'completed' && (
                                        <CheckCircle2 className="h-3 w-3 mr-1" />
                                      )}
                                      {upload.status === 'processing' && (
                                        <Loading
                                          size="sm"
                                          className="py-0 mr-1"
                                        />
                                      )}
                                      {upload.status === 'error' && (
                                        <XCircle className="h-3 w-3 mr-1" />
                                      )}
                                      {upload.status === 'pending' && (
                                        <Clock className="h-3 w-3 mr-1" />
                                      )}
                                      {upload?.status?.charAt(0).toUpperCase() +
                                        upload?.status?.slice(1)}
                                    </Badge>

                                    {upload.recordCount && (
                                      <span className="truncate text-caption text-muted ml-2">
                                        {upload.recordCount} records
                                      </span>
                                    )}
                                  </div>

                                  {upload.errorMessage && (
                                    <div className="mt-2 flex flex-wrap items-start truncateerror">
                                      <AlertCircle className="h-4 w-4 mr-2 flex flex-wrap-shrink-0" />
                                      <AlertDescription className="truncate text-caption">
                                        {upload.errorMessage}
                                      </AlertDescription>
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="flex flex-wrap gap-2">
                                {upload.status === 'completed' && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      navigate(
                                        `/reports/processing/${upload.id}`,
                                      )
                                    }
                                    className="max-w-full text-primary hover:bg-primary/10"
                                  >
                                    <FileBarChart className="h-4 w-4 mr-1" />
                                    Report
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleViewUploadDetails(upload.id)
                                  }
                                  disabled={
                                    upload.status === 'processing' ||
                                    upload.status === 'error'
                                  }
                                  className={`max-w-full ${
                                    upload.status === 'processing' ||
                                    upload.status === 'error'
                                      ? 'opacity-50 cursor-not-allowed'
                                      : 'text-primary hover:bg-primary/10'
                                  }`}
                                >
                                  <ArrowRight className="h-4 w-4 mr-1" />
                                  View
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </CardContent>
                <CardFooter className="pt-4">
                  <div className="w-full">
                    <Separator className="mb-4" />
                    <div className="flex flex-wrap justify-between items-center">
                      <p className="truncate text-caption text-muted">
                        Showing {recentUploads.length} recent uploads
                      </p>
                      <Button
                        size="sm"
                        onClick={() => setActiveTab('upload')}
                        className="max-w-full text-button bg-secondary text-secondary-foreground hover:bg-secondary/90 focus-visible:ring-ring"
                      >
                        <FileUp className="mr-2 h-4 w-4" />
                        Upload New File
                      </Button>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column: Quick Tools - Responsive */}
        <div className="hidden xl:flex xl:w-80 2xl:w-96 border-l border-border bg-card/30 flex-col">
          <div className="p-4 border-b border-border flex-shrink-0">
            <h3 className="text-lg font-semibold text-foreground">
              Quick Tools
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              Common actions and reports
            </p>
          </div>
          <div className="flex-1 p-4 space-y-4 overflow-y-auto min-h-0">
            {/* Quick Actions */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-foreground">
                Quick Actions
              </h4>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-3 hover:bg-primary hover:text-primary-foreground"
                  onClick={() => setActiveTab('upload')}
                >
                  <Plus className="h-4 w-4" />
                  <span className="text-xs">Upload</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-3 hover:bg-primary hover:text-primary-foreground"
                  onClick={() => {
                    // Export current data
                    window.open(
                      '/api/v1/transactions/export?format=csv',
                      '_blank',
                    );
                  }}
                >
                  <Download className="h-4 w-4" />
                  <span className="text-xs">Export</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-3 hover:bg-primary hover:text-primary-foreground"
                  onClick={() => navigate('/reports')}
                >
                  <BarChart3 className="h-4 w-4" />
                  <span className="text-xs">Report</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-3 hover:bg-primary hover:text-primary-foreground"
                  onClick={() => {
                    // Focus search input in activity tab
                    setActiveTab('review');
                    setTimeout(() => {
                      const searchInput = document.querySelector(
                        'input[placeholder="Search transactions..."]',
                      );
                      searchInput?.focus();
                    }, 100);
                  }}
                >
                  <Search className="h-4 w-4" />
                  <span className="text-xs">Search</span>
                </Button>
              </div>
            </div>

            {/* Quick Reports */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-foreground">
                Quick Reports
              </h4>
              <div className="space-y-2">
                {quickReports.length === 0 ? (
                  <p className="text-xs text-muted-foreground">
                    No recent reports
                  </p>
                ) : (
                  quickReports.map((report) => (
                    <button
                      key={report.id}
                      onClick={() => {
                        // Navigate to detailed report based on report type
                        if (report.title.includes('Spend')) {
                          navigate('/reports?type=spending&period=monthly');
                        } else if (report.title.includes('Category')) {
                          navigate('/reports?type=category-breakdown');
                        } else {
                          navigate('/reports?type=transaction-analysis');
                        }
                      }}
                      className="w-full text-left"
                    >
                      <Card className="p-3 hover:bg-accent/50 hover:border-primary/50 cursor-pointer transition-all duration-200 group">
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-foreground group-hover:text-primary">
                            {report.title}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-lg font-bold text-foreground">
                              {report.value}
                            </span>
                            <div className="flex items-center gap-1">
                              <span
                                className={`text-xs flex items-center gap-1 ${
                                  report.trend === 'up'
                                    ? 'text-green-600'
                                    : report.trend === 'down'
                                      ? 'text-red-600'
                                      : 'text-muted-foreground'
                                }`}
                              >
                                {report.trend === 'up'
                                  ? '↗'
                                  : report.trend === 'down'
                                    ? '↘'
                                    : '→'}
                                {report.change}
                              </span>
                              <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                            </div>
                          </div>
                          <p className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                            View detailed report
                          </p>
                        </div>
                      </Card>
                    </button>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Review Queue Overlay */}
      {showReviewQueue && (
        <div className="lg:hidden fixed inset-0 z-50 bg-background">
          <div className="flex flex-col h-full">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">
                Review Queue
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReviewQueue(false)}
              >
                ✕
              </Button>
            </div>
            <div className="flex-1 p-4 space-y-3 overflow-y-auto">
              {pendingTransactions.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle2 className="h-12 w-12 text-success mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">
                    All transactions reviewed!
                  </p>
                </div>
              ) : (
                pendingTransactions.map((transaction) => (
                  <button
                    key={transaction.id}
                    onClick={() => {
                      setShowReviewQueue(false);
                      navigate(`/transactions?id=${transaction.id}`);
                    }}
                    className="w-full text-left"
                  >
                    <Card className="p-3 hover:bg-accent/50 hover:border-primary/50 cursor-pointer transition-all duration-200 group">
                      <div className="space-y-2">
                        <div className="flex justify-between items-start">
                          <p className="text-sm font-medium text-foreground truncate group-hover:text-primary">
                            {transaction.description}
                          </p>
                          <span className="text-sm font-semibold text-foreground">
                            ${Math.abs(transaction.amount).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-muted-foreground">
                            {transaction.date}
                          </span>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                transaction.confidence > 0.8
                                  ? 'default'
                                  : 'secondary'
                              }
                              className="text-xs"
                            >
                              {transaction.suggestedCategory}
                            </Badge>
                            <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                          </div>
                        </div>
                        <div className="w-full bg-secondary rounded-full h-1">
                          <div
                            className="bg-primary h-1 rounded-full transition-all duration-300"
                            style={{
                              width: `${transaction.confidence * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                    </Card>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>
      )}

      {/* Mobile Quick Tools Overlay */}
      {showQuickTools && (
        <div className="xl:hidden fixed inset-0 z-50 bg-background">
          <div className="flex flex-col h-full">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">
                Quick Tools
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowQuickTools(false)}
              >
                ✕
              </Button>
            </div>
            <div className="flex-1 p-4 space-y-4 overflow-y-auto">
              {/* Quick Actions */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-foreground">
                  Quick Actions
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-primary hover:text-primary-foreground"
                    onClick={() => {
                      setShowQuickTools(false);
                      setActiveTab('upload');
                    }}
                  >
                    <Plus className="h-6 w-6" />
                    <span className="text-sm">Upload</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-primary hover:text-primary-foreground"
                    onClick={() => {
                      setShowQuickTools(false);
                      window.open(
                        '/api/v1/transactions/export?format=csv',
                        '_blank',
                      );
                    }}
                  >
                    <Download className="h-6 w-6" />
                    <span className="text-sm">Export</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-primary hover:text-primary-foreground"
                    onClick={() => {
                      setShowQuickTools(false);
                      navigate('/reports');
                    }}
                  >
                    <BarChart3 className="h-6 w-6" />
                    <span className="text-sm">Report</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="flex flex-col items-center gap-2 h-auto py-4 hover:bg-primary hover:text-primary-foreground"
                    onClick={() => {
                      setShowQuickTools(false);
                      setActiveTab('review');
                    }}
                  >
                    <Search className="h-6 w-6" />
                    <span className="text-sm">Search</span>
                  </Button>
                </div>
              </div>

              {/* Quick Reports */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-foreground">
                  Quick Reports
                </h4>
                <div className="space-y-3">
                  {quickReports.length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      No recent reports
                    </p>
                  ) : (
                    quickReports.map((report) => (
                      <button
                        key={report.id}
                        onClick={() => {
                          setShowQuickTools(false);
                          if (report.title.includes('Spend')) {
                            navigate('/reports?type=spending&period=monthly');
                          } else if (report.title.includes('Category')) {
                            navigate('/reports?type=category-breakdown');
                          } else {
                            navigate('/reports?type=transaction-analysis');
                          }
                        }}
                        className="w-full text-left"
                      >
                        <Card className="p-4 hover:bg-accent/50 hover:border-primary/50 cursor-pointer transition-all duration-200 group">
                          <div className="space-y-2">
                            <p className="text-sm font-medium text-foreground group-hover:text-primary">
                              {report.title}
                            </p>
                            <div className="flex justify-between items-center">
                              <span className="text-xl font-bold text-foreground">
                                {report.value}
                              </span>
                              <div className="flex items-center gap-2">
                                <span
                                  className={`text-sm flex items-center gap-1 ${
                                    report.trend === 'up'
                                      ? 'text-green-600'
                                      : report.trend === 'down'
                                        ? 'text-red-600'
                                        : 'text-muted-foreground'
                                  }`}
                                >
                                  {report.trend === 'up'
                                    ? '↗'
                                    : report.trend === 'down'
                                      ? '↘'
                                      : '→'}
                                  {report.change}
                                </span>
                                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                              </div>
                            </div>
                          </div>
                        </Card>
                      </button>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkPage;
