/**
 * Batch Upload Dialog Component
 *
 * Provides an option to batch upload all input files at once for comprehensive
 * temporal validation with maximum data coverage.
 */

import React, { useState } from 'react';
import {
  Upload,
  CheckCircle2,
  AlertTriangle,
  FileText,
  Clock,
  Database,
  BarChart3,
  Eye,
  Copy,
} from 'lucide-react';
import { batchUploadFiles } from '../../services/temporalValidationService';
import { useToast } from '@/shared/components/ui/use-toast';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';

interface BatchUploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (uploadId: string, totalTransactions: number) => void;
}

interface UploadResult {
  filename: string;
  status: string;
  transactions_imported: number;
  report_id?: string | null;
  error?: string;
}

export const BatchUploadDialog: React.FC<BatchUploadDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [summary, setSummary] = useState<{
    total_files_processed: number;
    successful_uploads: number;
    failed_uploads: number;
    total_transactions_imported: number;
    rag_corpus_built: boolean;
  } | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const { toast } = useToast();

  const handleBatchUpload = async () => {
    if (selectedFiles.length === 0) {
      toast({
        variant: 'destructive',
        title: 'No Files Selected',
        description: 'Please select files to upload',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadResults([]);
    setSummary(null);

    try {
      const result = await batchUploadFiles(
        selectedFiles,
        '2024',
        true,
        (progress) => setUploadProgress(progress),
      );

      setUploadResults(result.upload_results);
      setSummary(result.summary);

      if (result.summary.successful_uploads > 0) {
        toast({
          title: 'Batch Upload Successful',
          description: `${result.summary.total_transactions_imported} transactions imported from ${result.summary.successful_uploads} files`,
        });

        // Generate upload ID for progress tracking
        const uploadId = `batch_${Date.now()}`;
        onSuccess(uploadId, result.summary.total_transactions_imported);
      } else {
        toast({
          variant: 'destructive',
          title: 'Upload Failed',
          description: 'No files were uploaded successfully',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Batch Upload Failed',
        description:
          error instanceof Error ? error.message : 'Failed to upload files',
      });
    } finally {
      setIsUploading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex flex-wrap items-center justify-center z-modal">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-border">
          <div className="flex flex-wrap items-center justify-between">
            <div>
              <h2 className="truncate text-heading-4 text-gray-900">
                Batch Upload All Files
              </h2>
              <p className="text-sm text-muted-foreground mt-1">
                Upload all input files for comprehensive temporal validation
              </p>
            </div>
            <button
              onClick={onClose}
              className="truncategray-400 hover:text-muted-foreground"
              disabled={isUploading}
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {!summary && !isUploading && (
            <div className="space-y-4">
              <div className="bg-info/5 rounded-lg p-4">
                <div className="flex flex-wrap items-start space-x-3">
                  <Database className="h-6 w-6 text-info mt-1" />
                  <div>
                    <h3 className="font-medium text-info-foreground">
                      Comprehensive Data Upload
                    </h3>
                    <p className="text-sm text-info mt-1">
                      Select multiple financial data files to upload for
                      comprehensive analysis. Each file will be processed with:
                    </p>
                    <ul className="text-sm text-info mt-2 space-y-1">
                      <li>• column mapping</li>
                      <li>• Row-by-row validation</li>
                      <li>• Detailed processing reports</li>
                      <li>• Automatic categorization</li>
                    </ul>

                    <div className="mt-4">
                      <label
                        htmlFor="file-upload"
                        className="block truncatelabel text-info-foreground mb-2"
                      >
                        Select Files
                      </label>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        accept=".xlsx,.xls,.csv"
                        onChange={(e) => {
                          const files = Array.from(e.target.files || []);
                          setSelectedFiles(files);
                        }}
                        className="block w-full text-sm text-muted-foreground
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-md file:border-0
                          file:text-sm file:font-semibold
                          file:bg-info/5 file:text-info
                          hover:file:bg-info/10"
                      />
                      {selectedFiles.length > 0 && (
                        <div className="mt-2 text-sm text-info">
                          {selectedFiles.length} file
                          {selectedFiles.length > 1 ? 's' : ''} selected:
                          <ul className="mt-1 space-y-1">
                            {selectedFiles.map((file, idx) => (
                              <li
                                key={idx}
                                className="flex flex-wrap items-center space-x-2"
                              >
                                <FileText className="h-3 w-3" />
                                <span>{file.name}</span>
                                <span className="truncate text-caption text-muted-foreground">
                                  ({(file.size / 1024).toFixed(1)} KB)
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Estimated time: 2-3 minutes</span>
              </div>
            </div>
          )}

          {isUploading && (
            <div className="space-y-4">
              <div className="text-center">
                <Upload className="h-12 w-12 text-info mx-auto mb-3 animate-pulse" />
                <h3 className="truncate text-heading-5 text-gray-900 mb-2">
                  Processing Files...
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Uploading and analyzing transaction data
                </p>
                <ProgressBar value={uploadProgress} className="h-3" />
                <p className="truncate text-caption text-muted-foreground mt-2">
                  {uploadProgress}% complete
                </p>
              </div>
            </div>
          )}

          {summary && uploadResults.length > 0 && (
            <div className="space-y-4">
              {/* Summary Stats */}
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex flex-wrap items-center space-x-3">
                  <CheckCircle2 className="h-6 w-6 text-success" />
                  <div>
                    <h3 className="font-medium truncategreen-900">
                      Upload Complete
                    </h3>
                    <p className="text-sm text-success">
                      {summary.total_transactions_imported} transactions
                      imported from {summary.successful_uploads} files
                    </p>
                  </div>
                </div>
              </div>

              {/* File Results */}
              <div className="space-y-2">
                <h4 className="font-medium truncategray-900">File Results:</h4>
                {uploadResults.map((result, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      result.status === 'success'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex flex-wrap items-center space-x-3">
                      {result.status === 'success' ? (
                        <CheckCircle2 className="h-5 w-5 text-success" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-destructive" />
                      )}
                      <div>
                        <div className="flex flex-wrap items-center space-x-2">
                          <FileText className="h-4 w-4 truncategray-400" />
                          <span className="font-medium truncategray-900">
                            {result.filename}
                          </span>
                        </div>
                        {result.error && (
                          <p className="truncate text-caption text-destructive mt-1">
                            {result.error}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="truncatelabel text-gray-900">
                        {result.transactions_imported}
                      </span>
                      <p className="truncate text-caption text-muted-foreground">
                        transactions
                      </p>
                      {result.report_id && (
                        <div className="mt-1 flex flex-wrap space-x-1">
                          <button
                            onClick={() =>
                              void navigator.clipboard.writeText(
                                result.report_id,
                              )
                            }
                            className="p-1 truncategray-400 hover:text-muted-foreground rounded"
                            title="Copy Report ID"
                          >
                            <Copy className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() =>
                              window.open(
                                `/reports/${result.report_id}`,
                                '_blank',
                              )
                            }
                            className="p-1 truncateblue-400 hover:text-info rounded"
                            title="View Detailed Report"
                          >
                            <Eye className="h-3 w-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Enhanced Reporting Section */}
              {uploadResults.some((r) => r.report_id) && (
                <div className="bg-info/5 rounded-lg p-4">
                  <div className="flex flex-wrap items-center space-x-3 mb-3">
                    <BarChart3 className="h-5 w-5 text-info" />
                    <h4 className="font-medium text-info-foreground">
                      Detailed Processing Reports Available
                    </h4>
                  </div>
                  <p className="text-sm text-info mb-3">
                    Enhanced reports are available for each file showing:
                  </p>
                  <ul className="text-sm text-info space-y-1 mb-4">
                    <li>
                      • Row-by-row processing results with validation details
                    </li>
                    <li>• Column mapping confidence and statistics</li>
                    <li>• Data quality metrics and issue identification</li>
                    <li>• Schema discovery and category mappings</li>
                  </ul>
                  <div className="space-y-2">
                    {uploadResults
                      .filter((r) => r.report_id && r.status === 'success')
                      .map((result, index) => (
                        <div
                          key={index}
                          className="flex flex-wrap items-center justify-between bg-white rounded p-2 border border-blue-200"
                        >
                          <div className="flex flex-wrap items-center space-x-2">
                            <FileText className="h-4 w-4 truncateblue-500" />
                            <span className="truncatelabel text-gray-900">
                              {result.filename}
                            </span>
                            <span className="truncate text-caption text-muted-foreground">
                              (Report ID: {result.report_id?.slice(-8)})
                            </span>
                          </div>
                          <button
                            onClick={() =>
                              window.open(
                                `/reports/processing/${result.report_id}`,
                                '_blank',
                              )
                            }
                            className="truncate text-caption px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                          >
                            View Report
                          </button>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* RAG Corpus Status */}
              {summary.rag_corpus_built && (
                <div className="bg-info/5 rounded-lg p-3">
                  <div className="flex flex-wrap items-center space-x-2">
                    <Database className="h-5 w-5 text-info" />
                    <span className="truncatelabel text-info-foreground">
                      RAG Corpus Built Successfully
                    </span>
                  </div>
                  <p className="truncate text-caption text-info mt-1">
                    AI knowledge base created for temporal validation
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-border flex flex-wrap justify-end space-x-3">
          {!summary && !isUploading && (
            <>
              <button
                onClick={onClose}
                className="px-4 py-2 truncatelabel text-gray-700 bg-white border border-border rounded-md hover:bg-muted/50"
              >
                Cancel
              </button>
              <button
                onClick={() => void handleBatchUpload()}
                disabled={selectedFiles.length === 0}
                className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md ${
                  selectedFiles.length === 0
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                Upload {selectedFiles.length} File
                {selectedFiles.length !== 1 ? 's' : ''}
              </button>
            </>
          )}

          {summary && (
            <button
              onClick={onClose}
              className="px-4 py-2 truncatelabel text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
            >
              Continue to Validation
            </button>
          )}

          {isUploading && (
            <button
              disabled
              className="px-4 py-2 truncatelabel text-gray-400 bg-muted border border-border rounded-md cursor-not-allowed"
            >
              Uploading...
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
