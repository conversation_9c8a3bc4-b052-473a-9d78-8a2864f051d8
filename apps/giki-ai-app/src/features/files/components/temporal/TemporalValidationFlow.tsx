/**
 * Temporal Validation Flow Component
 *
 * Orchestrates the complete temporal validation workflow from file upload
 * through RAG corpus building to accuracy validation.
 */

import React, { useState } from 'react';
import { CheckCircle, ChevronRight } from 'lucide-react';
import { HistoricalDataUpload } from './HistoricalDataUpload';
import { TemporalValidationDashboard } from './TemporalValidationDashboard';
import {
  buildRAGCorpus,
  runTemporalValidation,
  completeTemporalValidation,
} from '../../services/temporalValidationService';
import { useToast } from '@/shared/components/ui/use-toast';

type ValidationPhase = 'upload' | 'validation' | 'complete';

interface TemporalValidationFlowProps {
  onComplete: () => void;
  onSkip?: () => void;
}

export const TemporalValidationFlow: React.FC<TemporalValidationFlowProps> = ({
  onComplete,
  onSkip,
}) => {
  const [phase, setPhase] = useState<ValidationPhase>('upload');
  const [uploadId, setUploadId] = useState<string>('');
  const [filename, setFilename] = useState<string>('');
  const [_validationResult, setValidationResult] = useState<unknown>(null);
  const { toast } = useToast();

  const handleUploadSuccess = async (id: string, name: string) => {
    setUploadId(id);
    setFilename(name);

    try {
      // Build RAG corpus synchronously
      toast({
        title: 'Building AI Knowledge Base',
        description: 'Processing your historical data patterns...',
      });

      const corpusResult = await buildRAGCorpus();

      toast({
        title: 'AI Knowledge Base Ready',
        description: `Successfully processed ${corpusResult.total_patterns} patterns across ${corpusResult.categories_found} categories`,
      });

      // Run temporal validation synchronously
      toast({
        title: 'Running Temporal Validation',
        description: 'Validating AI accuracy across time periods...',
      });

      const validationResult = await runTemporalValidation();

      // Store validation result for display
      setValidationResult(validationResult);

      // Move directly to complete phase if successful
      if (validationResult.meets_threshold) {
        setPhase('complete');
      } else {
        setPhase('validation');
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Process failed',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to build AI knowledge base',
      });
    }
  };

  // Corpus building is now synchronous - no separate phase needed

  const handleValidationComplete = async () => {
    try {
      // Save the validated model
      await completeTemporalValidation(uploadId);
      setPhase('complete');

      toast({
        title: 'Success',
        description: 'AI model successfully trained and saved!',
      });

      // Give user time to see success message
      setTimeout(() => {
        onComplete();
      }, 2000);
    } catch {
      toast({
        variant: 'destructive',
        title: 'Validation failed',
        description: 'Failed to complete validation',
      });
    }
  };

  const getPhaseNumber = (p: ValidationPhase): number => {
    const phases: ValidationPhase[] = ['upload', 'validation', 'complete'];
    return phases.indexOf(p) + 1;
  };

  const currentPhaseNumber = getPhaseNumber(phase);

  return (
    <div className="space-y-4">
      {/* Phase Content */}
      <div>
        {phase === 'upload' && (
          <HistoricalDataUpload
            onUploadSuccess={(id, name) => void handleUploadSuccess(id, name)}
            onCancel={onSkip}
          />
        )}

        {phase === 'validation' && uploadId && (
          <TemporalValidationDashboard
            uploadId={uploadId}
            onComplete={() => void handleValidationComplete()}
            targetAccuracy={85}
          />
        )}

        {phase === 'complete' && (
          <div className="p-4 text-center">
            <CheckCircle className="h-12 w-12 text-success mx-auto mb-2" />
            <h2 className="text-lg font-bold text-gray-900 mb-1">
              Training Complete
            </h2>
            <p className="text-xs text-muted-foreground mb-4">
              Your AI has been successfully trained on {filename} with {'>'}85%
              accuracy. The model is now ready to categorize new transactions.
            </p>
            <button
              onClick={() => void onComplete()}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Continue to Dashboard
              <ChevronRight className="ml-1 h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Skip Option */}
      {phase === 'upload' && onSkip && (
        <div className="text-center">
          <button
            onClick={() => void onSkip()}
            className="text-xs text-muted-foreground hover:text-gray-800 underline"
          >
            Skip this step (use default AI model)
          </button>
        </div>
      )}
    </div>
  );
};
