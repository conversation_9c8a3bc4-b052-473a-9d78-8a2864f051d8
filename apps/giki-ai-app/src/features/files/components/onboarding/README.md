# Onboarding UI Components

This directory contains a comprehensive set of onboarding UI components designed to guide new users through the Giki AI application setup process. These components provide a smooth, educational, and engaging first-time user experience.

## Components Overview

### 1. ProgressiveUploadWizard.tsx
**Purpose**: Multi-step wizard that breaks down the file upload process into manageable steps with clear guidance.

**Features**:
- Step-by-step file upload guidance
- Interactive progress indicators
- File preparation tips and best practices
- Real-time upload status
- Automatic progression with user confirmations

**Usage**:
```typescript
import { ProgressiveUploadWizard } from '@/features/files';

<ProgressiveUploadWizard
  onComplete={() => console.log('Wizard completed')}
  isFirstTime={true}
/>
```

### 2. WelcomeTutorialModal.tsx
**Purpose**: Interactive tutorial explaining AI categorization concepts and application features.

**Features**:
- Multi-step educational content
- Feature explanations with visual aids
- Progress tracking through tutorial steps
- Skip option for experienced users
- Comprehensive overview of AI capabilities

**Usage**:
```typescript
import { WelcomeTutorialModal } from '@/features/files';

<WelcomeTutorialModal
  isOpen={showTutorial}
  onClose={() => setShowTutorial(false)}
  onStartOnboarding={() => startOnboarding()}
/>
```

### 3. RealTimeProcessingStatus.tsx
**Purpose**: Live status updates during AI categorization with detailed progress and statistics.

**Features**:
- Real-time processing stages
- Live statistics (transactions processed, confidence scores)
- Estimated completion times
- Error handling and retry functionality
- Performance metrics display

**Usage**:
```typescript
import { RealTimeProcessingStatus } from '@/features/files';

<RealTimeProcessingStatus
  uploadId="upload-123"
  onComplete={(stats) => handleProcessingComplete(stats)}
  onError={(error) => handleError(error)}
  autoStart={true}
/>
```

### 4. InteractiveCategoryPreview.tsx
**Purpose**: Preview AI categorization results with ability to review and adjust before final processing.

**Features**:
- Category suggestion review
- Confidence score visualization
- Transaction preview with examples
- Category modification interface
- Approval/rejection workflow
- Analytics and distribution charts

**Usage**:
```typescript
import { InteractiveCategoryPreview } from '@/features/files';

<InteractiveCategoryPreview
  uploadId="upload-123"
  onApprove={(categories) => approveCategories(categories)}
  onReject={(feedback) => handleRejection(feedback)}
  onModify={(modifications) => applyModifications(modifications)}
/>
```

### 5. OnboardingCompletionFlow.tsx
**Purpose**: Celebration and next steps guidance after successful onboarding completion.

**Features**:
- Success celebration with achievements
- Next steps recommendations
- Feature discovery tour
- Statistics summary
- Navigation to key application areas

**Usage**:
```typescript
import { OnboardingCompletionFlow } from '@/features/files';

<OnboardingCompletionFlow
  stats={{
    totalTransactions: 1546,
    categorizedTransactions: 1546,
    averageConfidence: 0.87,
    categoriesCreated: 12,
    processingTime: 45,
    uploadedFiles: 3
  }}
  onComplete={() => navigateToDashboard()}
  isOpen={true}
/>
```

### 6. FirstTimeUserGuide.tsx
**Purpose**: Contextual tooltips and interface guidance for first-time users.

**Features**:
- Interactive element highlighting
- Contextual tooltips
- Step-by-step interface walkthrough
- Skip/pause functionality
- Persistent help access

**Usage**:
```typescript
import { FirstTimeUserGuide } from '@/features/files';

<FirstTimeUserGuide
  isActive={showGuide}
  onComplete={() => setShowGuide(false)}
  onSkip={() => setShowGuide(false)}
  guideName="Getting Started"
  autoStart={true}
/>
```

### 7. OnboardingOrchestrator.tsx
**Purpose**: Coordinates the complete onboarding flow using all components together.

**Features**:
- Phase management and transitions
- Component coordination
- State management across flow
- Error handling and recovery
- Skip options and alternative paths

**Usage**:
```typescript
import { OnboardingOrchestrator } from '@/features/files';

<OnboardingOrchestrator
  isFirstTime={true}
  autoStart={true}
  onComplete={() => completeOnboarding()}
/>
```

## Integration Guide

### Complete Onboarding Flow
The recommended integration approach uses the `OnboardingOrchestrator` which coordinates all components:

```typescript
// App.tsx or main routing component
import { OnboardingOrchestrator } from '@/features/files';

const App = () => {
  const [showOnboarding, setShowOnboarding] = useState(true);
  
  if (showOnboarding) {
    return (
      <OnboardingOrchestrator
        isFirstTime={true}
        onComplete={() => setShowOnboarding(false)}
      />
    );
  }
  
  return <MainApplication />;
};
```

### Individual Component Usage
Components can also be used individually for specific use cases:

```typescript
// For just the upload wizard
import { ProgressiveUploadWizard } from '@/features/files';

// For processing status only
import { RealTimeProcessingStatus } from '@/features/files';

// For tutorial modal
import { WelcomeTutorialModal } from '@/features/files';
```

## Component Flow

The typical onboarding flow follows this sequence:

1. **Welcome** → User arrives at application
2. **Tutorial** → `WelcomeTutorialModal` explains features
3. **Wizard** → `ProgressiveUploadWizard` guides file upload
4. **Processing** → `RealTimeProcessingStatus` shows AI work
5. **Preview** → `InteractiveCategoryPreview` allows review
6. **Completion** → `OnboardingCompletionFlow` celebrates success
7. **Guide** → `FirstTimeUserGuide` provides interface help

## Styling and Theming

All components use:
- Tailwind CSS for styling
- shadcn/ui components for consistency
- Responsive design patterns
- Accessible color schemes and interactions
- Animation and transition effects

## State Management

Components manage their own internal state but provide callbacks for:
- Progress updates
- Completion events
- Error handling
- User interactions
- Data modifications

## Testing

Each component includes:
- TypeScript interfaces for props
- Error boundary compatibility
- Loading state management
- Responsive behavior testing
- Accessibility compliance

## Performance Considerations

- Components use React.memo where appropriate
- Large datasets are paginated or virtualized
- Images and assets are optimized
- Animation performance is optimized
- Memory leaks are prevented with proper cleanup

## Customization

Components can be customized through:
- Props for behavior modification
- CSS classes for styling overrides
- Callback functions for custom logic
- Configuration objects for content

## Browser Support

Components work across:
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Tablet interfaces
- Screen readers and accessibility tools

## Future Enhancements

Planned improvements include:
- Internationalization support
- Advanced animation options
- More granular progress tracking
- Integration with analytics
- A/B testing capabilities
- Enhanced accessibility features