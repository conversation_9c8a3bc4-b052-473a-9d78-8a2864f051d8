import React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  TrendingUp,
  Target,
  Calendar,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

interface AccuracyMetric {
  month: number;
  training_months: number;
  training_transactions: number;
  test_transactions: number;
  accuracy: number;
  confidence_avg: number;
}

interface OverallMetrics {
  average_accuracy: number;
  min_accuracy: number;
  max_accuracy: number;
  months_above_target: number;
  total_months_tested: number;
  target_achievement_rate: number;
}

interface PerformanceSummary {
  meets_requirements: boolean;
  consistent_performance: boolean;
  improvement_trend: string;
  recommendation: string;
}

interface AccuracySimulationData {
  monthly_results: Record<string, AccuracyMetric>;
  overall_metrics: OverallMetrics;
  performance_summary: PerformanceSummary;
  target_accuracy: number;
}

interface AccuracySimulationChartProps {
  data: AccuracySimulationData;
}

export const AccuracySimulationChart: React.FC<
  AccuracySimulationChartProps
> = ({ data }) => {
  const {
    monthly_results,
    overall_metrics,
    performance_summary,
    target_accuracy,
  } = data;

  const monthlyData = Object.entries(monthly_results)
    .map(([key, metric]) => ({
      period: key,
      ...metric,
    }))
    .sort((a, b) => a.month - b.month);

  const getAccuracyColor = (accuracy: number): string => {
    if (accuracy >= target_accuracy) return 'text-green-600';
    if (accuracy >= 0.8) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyBadgeVariant = (accuracy: number) => {
    if (accuracy >= target_accuracy) return 'default';
    if (accuracy >= 0.8) return 'secondary';
    return 'destructive';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 truncategreen-500" />;
      case 'declining':
        return <AlertCircle className="h-4 w-4 truncatered-500" />;
      default:
        return <Target className="h-4 w-4 truncateblue-500" />;
    }
  };

  // const getRecommendationLevel = (recommendation: string) => {
  //   if (recommendation.includes('excellent')) return 'success';
  //   if (recommendation.includes('good')) return 'info';
  //   if (recommendation.includes('moderate')) return 'warning';
  //   return 'error';
  // };

  return (
    <div className="space-y-6">
      {/* Overall Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center gap-2">
            <Target className="h-5 w-5" />
            Progressive Accuracy Simulation Results
          </CardTitle>
          <CardDescription>
            July-December 2024 monthly accuracy validation with expanding
            training corpus
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="truncate text-heading-1 text-success">
                {(overall_metrics.average_accuracy * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-[hsl(var(--giki-text-muted))]">
                Average Accuracy
              </div>
              <div className="truncate text-caption mt-1">
                Target: {(target_accuracy * 100).toFixed(0)}%
              </div>
            </div>
            <div className="text-center">
              <div className="truncate text-heading-1">
                {overall_metrics.months_above_target}/
                {overall_metrics.total_months_tested}
              </div>
              <div className="text-sm text-[hsl(var(--giki-text-muted))]">
                Months Above Target
              </div>
              <div className="truncate text-caption mt-1">
                Success Rate:{' '}
                {(overall_metrics.target_achievement_rate * 100).toFixed(0)}%
              </div>
            </div>
            <div className="text-center">
              <div className="truncate text-heading-1 flex flex-wrap items-center justify-center gap-2">
                {getTrendIcon(performance_summary.improvement_trend)}
                {performance_summary.improvement_trend}
              </div>
              <div className="text-sm text-[hsl(var(--giki-text-muted))]">
                Performance Trend
              </div>
            </div>
          </div>

          {/* Performance Alert */}
          <Alert
            className={`mb-4 ${performance_summary.meets_requirements ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}`}
          >
            <div className="flex flex-wrap items-center gap-2">
              {performance_summary.meets_requirements ? (
                <CheckCircle className="h-4 w-4 text-success" />
              ) : (
                <AlertCircle className="h-4 w-4 text-warning" />
              )}
              <AlertDescription>
                <strong>
                  {performance_summary.meets_requirements
                    ? 'Requirements Met: '
                    : 'Attention Required: '}
                </strong>
                {performance_summary.recommendation
                  .replace(/_/g, ' ')
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
              </AlertDescription>
            </div>
          </Alert>
        </CardContent>
      </Card>

      {/* Monthly Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Accuracy Results
          </CardTitle>
          <CardDescription>
            Progressive training simulation: each month uses expanded training
            data
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="space-y-3">
            {monthlyData.map((month) => (
              <div key={month.period} className="border rounded-lg p-4">
                <div className="flex flex-wrap items-center justify-between mb-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge variant="outline" className="max-w-[150px] truncate">
                      {new Date(2024, month.month - 1).toLocaleDateString(
                        'en-US',
                        { month: 'long', year: 'numeric' },
                      )}
                    </Badge>
                    <Badge
                      variant={getAccuracyBadgeVariant(month.accuracy)}
                      className="max-w-[150px] truncate"
                    >
                      {(month.accuracy * 100).toFixed(1)}% Accuracy
                    </Badge>
                  </div>
                  <div className="text-sm text-[hsl(var(--giki-text-muted))]">
                    {month.test_transactions} test transactions
                  </div>
                </div>

                <div className="space-y-2">
                  <Progress value={month.accuracy * 100} className="h-2" />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 truncate text-caption text-[hsl(var(--giki-text-muted))]">
                    <div>
                      Training: {month.training_months} months (
                      {month.training_transactions} transactions)
                    </div>
                    <div>
                      Confidence: {(month.confidence_avg * 100).toFixed(1)}%
                    </div>
                    <div className={getAccuracyColor(month.accuracy)}>
                      {month.accuracy >= target_accuracy
                        ? '✓ Target Met'
                        : '⚠ Below Target'}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Analysis</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Accuracy Range</h4>
              <div className="space-y-1 text-sm">
                <div>
                  Highest: {(overall_metrics.max_accuracy * 100).toFixed(1)}%
                </div>
                <div>
                  Lowest: {(overall_metrics.min_accuracy * 100).toFixed(1)}%
                </div>
                <div>
                  Average: {(overall_metrics.average_accuracy * 100).toFixed(1)}
                  %
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Performance Indicators</h4>
              <div className="space-y-1 text-sm">
                <div className="flex flex-wrap items-center gap-2">
                  {performance_summary.meets_requirements ? (
                    <CheckCircle className="h-4 w-4 truncategreen-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 truncatered-500" />
                  )}
                  Requirements Met
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  {performance_summary.consistent_performance ? (
                    <CheckCircle className="h-4 w-4 truncategreen-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 truncateyellow-500" />
                  )}
                  Consistent Performance
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  {getTrendIcon(performance_summary.improvement_trend)}
                  {performance_summary?.improvement_trend?.replace(
                    '_',
                    ' ',
                  )}{' '}
                  Trend
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccuracySimulationChart;
