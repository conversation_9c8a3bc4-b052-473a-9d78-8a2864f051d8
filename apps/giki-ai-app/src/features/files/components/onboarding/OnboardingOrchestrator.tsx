/**
 * Onboarding Orchestrator Component
 *
 * Coordinates the complete onboarding flow using all the individual onboarding components.
 * This demonstrates how all the components work together for a streamlined experience.
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// Import all onboarding components
import ProgressiveUploadWizard from './ProgressiveUploadWizard';
import WelcomeTutorialModal from './WelcomeTutorialModal';
import { RealTimeProcessingStatus } from './RealTimeProcessingStatus';
import InteractiveCategoryPreview from './InteractiveCategoryPreview';
import OnboardingCompletionFlow from './OnboardingCompletionFlow';
import FirstTimeUserGuide from './FirstTimeUserGuide';
import { TemporalValidationFlow } from '../temporal/TemporalValidationFlow';

type OnboardingPhase =
  | 'welcome'
  | 'tutorial'
  | 'temporal_validation'
  | 'wizard'
  | 'processing'
  | 'preview'
  | 'completion'
  | 'guide'
  | 'finished';

interface ProcessingStats {
  totalTransactions: number;
  categorizedTransactions: number;
  averageConfidence: number;
  categoriesDetected: number;
  estimatedTimeRemaining?: number;
}

interface OnboardingOrchestratorProps {
  isFirstTime?: boolean;
  autoStart?: boolean;
  onComplete?: () => void;
}

export const OnboardingOrchestrator: React.FC<OnboardingOrchestratorProps> = ({
  isFirstTime = true,
  autoStart: _autoStart = true,
  onComplete,
}) => {
  const navigate = useNavigate();
  const [currentPhase, setCurrentPhase] = useState<OnboardingPhase>('welcome');
  const [uploadId] = useState<string | null>(null);
  const [onboardingStats, setOnboardingStats] = useState({
    totalTransactions: 0,
    categorizedTransactions: 0,
    averageConfidence: 0,
    categoriesCreated: 0,
    processingTime: 0,
    uploadedFiles: 0,
  });

  // Phase transition handlers
  const handleWelcomeComplete = useCallback(() => {
    if (isFirstTime) {
      setCurrentPhase('tutorial');
    } else {
      setCurrentPhase('temporal_validation');
    }
  }, [isFirstTime]);

  const handleTutorialComplete = useCallback(() => {
    setCurrentPhase('temporal_validation');
  }, []);

  const handleTemporalValidationComplete = useCallback(() => {
    setCurrentPhase('wizard');
  }, []);

  const handleTemporalValidationSkip = useCallback(() => {
    setCurrentPhase('wizard');
  }, []);

  const handleWizardComplete = useCallback(() => {
    setCurrentPhase('processing');
  }, []);

  const handleProcessingComplete = useCallback((stats: ProcessingStats) => {
    setOnboardingStats((prev) => ({
      ...prev,
      totalTransactions: stats.totalTransactions || 0,
      categorizedTransactions: stats.categorizedTransactions || 0,
      averageConfidence: stats.averageConfidence || 0,
      categoriesCreated: stats.categoriesDetected || 0,
      processingTime: stats.estimatedTimeRemaining || 45,
    }));
    setCurrentPhase('preview');
  }, []);

  const handlePreviewApprove = useCallback(() => {
    setCurrentPhase('completion');
  }, []);

  const handlePreviewReject = useCallback(() => {
    // Go back to processing or allow manual adjustments
    setCurrentPhase('processing');
  }, []);

  const handleCompletionFinish = useCallback(() => {
    setCurrentPhase('guide');
  }, []);

  const handleGuideComplete = useCallback(() => {
    setCurrentPhase('finished');
    onComplete?.();
  }, [onComplete]);

  const handleSkip = useCallback(() => {
    setCurrentPhase('finished');
    onComplete?.();
    navigate('/dashboard');
  }, [onComplete, navigate]);

  // Render current phase
  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'welcome':
        return (
          <div className="min-h-screen flex flex-wrap items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
            <div className="text-center space-y-6 p-8">
              <div className="animate-bounce">
                <h1 className="text-4xl font-bold text-foreground mb-2">
                  Welcome to Giki AI
                </h1>
                <p className="text-xl text-muted-foreground mb-4">
                  Let&apos;s train your AI assistant with your historical
                  transaction data
                </p>
                <p className="text-lg text-muted-foreground mb-8">
                  <strong>Important:</strong> You&apos;ll need 12+ months of
                  transactions that{' '}
                  <span className="text-info font-semibold">
                    already include your category labels
                  </span>
                </p>
              </div>

              <div className="flex flex-wrap flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => void handleWelcomeComplete()}
                  className="px-8 py-3 bg-blue-600 truncatewhite rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Get Started
                </button>
                <button
                  onClick={() => void handleSkip()}
                  className="px-8 py-3 border border-border text-muted-foreground rounded-lg hover:bg-muted/50 transition-colors"
                >
                  Skip Setup
                </button>
              </div>
            </div>
          </div>
        );

      case 'tutorial':
        return (
          <WelcomeTutorialModal
            isOpen={true}
            onClose={handleTutorialComplete}
            onStartOnboarding={handleTutorialComplete}
          />
        );

      case 'temporal_validation':
        return (
          <TemporalValidationFlow
            onComplete={handleTemporalValidationComplete}
            onSkip={handleTemporalValidationSkip}
          />
        );

      case 'wizard':
        return (
          <ProgressiveUploadWizard
            onComplete={handleWizardComplete}
            isFirstTime={isFirstTime}
          />
        );

      case 'processing':
        return (
          <div className="min-h-screen flex flex-wrap items-center justify-center bg-muted/30">
            <div className="max-w-4xl w-full p-6">
              <RealTimeProcessingStatus
                taskId={uploadId || 'demo-upload-' + Date.now()}
                onComplete={handleProcessingComplete}
                onError={(error) => {
                  console.error('Processing error:', error);
                  // Handle error - maybe show retry option or go back to upload
                }}
              />
            </div>
          </div>
        );

      case 'preview':
        return (
          <div className="min-h-screen bg-muted/30 py-8">
            <div className="max-w-6xl mx-auto px-6">
              <div className="mb-6 text-center">
                <h1 className="truncate text-heading-1 text-foreground mb-2">
                  Review AI Categorization Results
                </h1>
                <p className="text-muted-foreground">
                  Check the AI suggestions and approve or adjust categories
                  before finalizing
                </p>
              </div>

              <InteractiveCategoryPreview
                uploadId={uploadId || 'demo-upload'}
                onApprove={handlePreviewApprove}
                onReject={handlePreviewReject}
                onModify={(_modifications) => {
                  // Category modifications applied
                }}
              />
            </div>
          </div>
        );

      case 'completion':
        return (
          <OnboardingCompletionFlow
            stats={onboardingStats}
            onComplete={handleCompletionFinish}
            isOpen={true}
          />
        );

      case 'guide':
        return (
          <div className="min-h-screen bg-white">
            <FirstTimeUserGuide
              isActive={true}
              onComplete={handleGuideComplete}
              onSkip={handleGuideComplete}
              guideName="Interface Guide"
              autoStart={true}
            />

            {/* Show a basic interface for the guide to work with */}
            <div className="p-8">
              <nav className="mb-8 flex flex-wrap space-x-4">
                <a href="/dashboard" className="text-info hover:text-blue-800">
                  Dashboard
                </a>
                <a href="/review" className="text-info hover:text-blue-800">
                  Review
                </a>
                <a href="/reports" className="text-info hover:text-blue-800">
                  Reports
                </a>
              </nav>

              <div className="space-y-6">
                <h1 className="text-2xl font-bold">File Upload Interface</h1>

                <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                  <input
                    type="file"
                    data-testid="file-input"
                    className="hidden"
                  />
                  <p>Drag & drop files here or click to browse</p>
                </div>

                <button
                  data-testid="upload-button"
                  className="px-6 py-2 bg-blue-600 truncatewhite rounded-lg"
                >
                  Upload Files
                </button>
              </div>
            </div>
          </div>
        );

      case 'finished':
      default:
        return (
          <div className="min-h-screen flex flex-wrap items-center justify-center bg-green-50">
            <div className="text-center space-y-4">
              <div className="truncate6xl">🎉</div>
              <h1 className="truncate text-heading-1 text-success-foreground">
                Onboarding Complete
              </h1>
              <p className="text-success mb-6">
                You&apos;re now ready to use Giki AI for intelligent financial
                management
              </p>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-8 py-3 bg-green-600 truncatewhite rounded-lg hover:bg-green-700 transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="onboarding-orchestrator">
      {renderCurrentPhase()}

      {/* Debug Info - Remove in production */}
      {process?.env?.NODE_ENV === 'development' && (
        <div className="fixed top-4 left-4 bg-black truncatewhite p-2 rounded text-caption z-modal">
          Phase: {currentPhase} | Upload ID: {uploadId}
        </div>
      )}
    </div>
  );
};

export default OnboardingOrchestrator;
