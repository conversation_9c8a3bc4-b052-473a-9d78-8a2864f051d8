/**
 * Onboarding Completion Flow Component
 *
 * Celebrates successful onboarding completion and guides users to their next steps
 * with feature discovery and recommended actions.
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  CheckCircle,
  Sparkles,
  TrendingUp,
  BarChart3,
  Settings,
  BookOpen,
  Target,
  Zap,
  ArrowRight,
  Star,
  Trophy,
  Rocket,
  Eye,
  Edit,
  Download,
  Share,
  Calendar,
  Bell,
  Clock,
  FileText,
} from 'lucide-react';

interface OnboardingStats {
  totalTransactions: number;
  categorizedTransactions: number;
  averageConfidence: number;
  categoriesCreated: number;
  processingTime: number;
  uploadedFiles: number;
}

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  action: string;
  route: string;
  isRecommended?: boolean;
  completionTime?: string;
}

interface OnboardingCompletionFlowProps {
  stats: OnboardingStats;
  onComplete?: () => void;
  isOpen?: boolean;
}

export const OnboardingCompletionFlow: React.FC<
  OnboardingCompletionFlowProps
> = ({ stats, onComplete, isOpen = true }) => {
  const navigate = useNavigate();
  const [currentView, setCurrentView] = useState<
    'celebration' | 'next-steps' | 'feature-tour'
  >('celebration');
  const [showConfetti, setShowConfetti] = useState(true);

  useEffect(() => {
    // Hide confetti after animation
    const timer = setTimeout(() => setShowConfetti(false), 3000);
    return () => clearTimeout(timer);
  }, []);

  const nextStepFeatures: FeatureCard[] = [
    {
      id: 'view-processing-report',
      title: 'View Processing Report',
      description:
        'See detailed analysis of your file upload and data quality metrics',
      icon: FileText,
      action: 'View Report',
      route: '/reports/processing', // Go to the list of all processing reports
      isRecommended: true,
      completionTime: '2-3 min',
    },
    {
      id: 'review-transactions',
      title: 'Review Your Transactions',
      description:
        'Check AI categorizations and make any necessary adjustments',
      icon: Eye,
      action: 'Start Review',
      route: '/review',
      isRecommended: true,
      completionTime: '5-10 min',
    },
    {
      id: 'explore-dashboard',
      title: 'Explore Dashboard',
      description: 'View insights and analytics from your categorized data',
      icon: BarChart3,
      action: 'View Dashboard',
      route: '/dashboard',
      isRecommended: true,
      completionTime: '2-5 min',
    },
    {
      id: 'customize-categories',
      title: 'Customize Categories',
      description: 'Create custom categories and manage your chart of accounts',
      icon: Edit,
      action: 'Manage Categories',
      route: '/categories',
      completionTime: '10-15 min',
    },
    {
      id: 'generate-reports',
      title: 'Generate Reports',
      description:
        'Create custom reports and export data for accounting software',
      icon: Download,
      action: 'Create Reports',
      route: '/reports',
      completionTime: '5-10 min',
    },
    {
      id: 'setup-automation',
      title: 'Setup Automation',
      description: 'Configure automated processing for future uploads',
      icon: Zap,
      action: 'Configure',
      route: '/settings',
      completionTime: '5-10 min',
    },
    {
      id: 'knowledge-hub',
      title: 'Knowledge Hub',
      description: 'Explore AI insights and business intelligence features',
      icon: BookOpen,
      action: 'Explore',
      route: '/knowledge-hub',
      completionTime: '5-15 min',
    },
  ];

  const achievementBadges = [
    {
      title: 'First Upload',
      description: 'Successfully uploaded your first file',
      icon: Trophy,
      earned: true,
    },
    {
      title: 'AI Categorized',
      description: `${stats.categorizedTransactions} transactions categorized`,
      icon: Sparkles,
      earned: stats.categorizedTransactions > 0,
    },
    {
      title: 'High Accuracy',
      description: `${Math.round(stats.averageConfidence * 100)}% average confidence`,
      icon: Target,
      earned: stats.averageConfidence >= 0.85,
    },
    {
      title: 'Speed Demon',
      description: 'Completed setup in under 2 minutes',
      icon: Rocket,
      earned: stats.processingTime < 120,
    },
  ];

  const CelebrationView = () => (
    <div className="text-center space-y-6">
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="animate-bounce text-4xl">🎉</div>
        </div>
      )}

      <div className="relative">
        <div className="mx-auto w-24 h-24 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex flex-wrap items-center justify-center mb-4">
          <CheckCircle className="h-12 w-12 truncatewhite" />
        </div>
        {showConfetti && (
          <div className="absolute -top-2 -right-2 animate-spin text-2xl">
            ✨
          </div>
        )}
      </div>

      <div>
        <h2 className="truncate text-heading-1 mb-2">Complete</h2>
        <p className="text-xl text-muted-foreground mb-4">
          Your financial AI assistant is ready to use
        </p>
        <p className="text-muted-foreground max-w-md mx-auto">
          You&apos;ve successfully uploaded and categorized your transaction
          data. Giki AI is now ready to help you manage your finances
          intelligently.
        </p>
      </div>

      {/* Achievement Badges */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {achievementBadges.map((badge, index) => (
          <div
            key={index}
            className={`p-3 rounded-lg border-2 ${
              badge.earned
                ? 'border-yellow-300 bg-yellow-50'
                : 'border-border bg-muted opacity-60'
            }`}
          >
            <badge.icon
              className={`h-6 w-6 mx-auto mb-2 ${
                badge.earned ? 'text-warning' : 'text-muted-foreground/50'
              }`}
            />
            <div className="truncate text-caption font-medium">
              {badge.title}
            </div>
            <div className="truncate text-caption text-muted-foreground">
              {badge.description}
            </div>
          </div>
        ))}
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
        <div className="text-center">
          <div className="text-2xl font-bold text-success">
            {stats.totalTransactions}
          </div>
          <div className="truncatebody-small">Transactions</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-info">
            {stats.categoriesCreated}
          </div>
          <div className="truncatebody-small">Categories</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {Math.round(stats.averageConfidence * 100)}%
          </div>
          <div className="truncatebody-small">Accuracy</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {stats.processingTime}s
          </div>
          <div className="truncatebody-small">Processing Time</div>
        </div>
      </div>

      <Button
        onClick={() => setCurrentView('next-steps')}
        size="lg"
        className="max-w-full mt-6"
      >
        What&apos;s Next?
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  );

  const NextStepsView = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">
          What Would You Like To Do Next?
        </h2>
        <p className="text-muted-foreground">
          Choose from these recommended actions to get the most out of Giki AI
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {nextStepFeatures.map((feature) => (
          <Card
            key={feature.id}
            className={`hover:shadow-lg transition-all cursor-pointer ${
              feature.isRecommended ? 'border-blue-500 border-2' : ''
            }`}
            onClick={() => navigate(feature.route)}
          >
            <CardHeader className="pb-3">
              <div className="flex flex-wrap items-center justify-between">
                <CardTitle className="flex flex-wrap items-center text-lg">
                  <feature.icon className="h-5 w-5 mr-2" />
                  {feature.title}
                  {feature.isRecommended && (
                    <Badge
                      variant="default"
                      className="ml-2 max-w-[150px] truncate"
                    >
                      <Star className="h-3 w-3 mr-1" />
                      Recommended
                    </Badge>
                  )}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <CardDescription className="mb-3">
                {feature.description}
              </CardDescription>
              <div className="flex flex-wrap items-center justify-between">
                <Button className="max-w-full" size="sm" variant="outline">
                  {feature.action}
                  <ArrowRight className="ml-2 h-3 w-3" />
                </Button>
                {feature.completionTime && (
                  <Badge
                    variant="secondary"
                    className="truncate text-caption max-w-[150px]"
                  >
                    <Clock className="h-3 w-3 mr-1" />
                    {feature.completionTime}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex flex-wrap justify-center space-x-3">
        <Button
          className="max-w-full"
          variant="outline"
          onClick={() => setCurrentView('feature-tour')}
        >
          Feature Tour
        </Button>
        <Button
          className="max-w-full"
          onClick={() => {
            onComplete?.();
            navigate('/dashboard');
          }}
        >
          Go to Dashboard
        </Button>
      </div>
    </div>
  );

  const FeatureTourView = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Explore Giki AI Features</h2>
        <p className="text-muted-foreground">
          Access features for financial management
        </p>
      </div>

      <Tabs defaultValue="analytics" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
          <TabsTrigger value="intelligence">Intelligence</TabsTrigger>
        </TabsList>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Financial Analytics
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="space-y-3">
                <div className="flex flex-wrap items-center space-x-3">
                  <TrendingUp className="h-5 w-5 truncategreen-500" />
                  <div>
                    <div className="font-medium">Spending Trends</div>
                    <div className="truncatebody-small">
                      Track spending patterns across time periods and categories
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap items-center space-x-3">
                  <Target className="h-5 w-5 truncateblue-500" />
                  <div>
                    <div className="font-medium">Budget Analysis</div>
                    <div className="truncatebody-small">
                      Compare actual vs planned spending with variance analysis
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center">
                <Zap className="h-5 w-5 mr-2" />
                Smart Automation
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="space-y-3">
                <div className="flex flex-wrap items-center space-x-3">
                  <Calendar className="h-5 w-5 truncatepurple-500" />
                  <div>
                    <div className="font-medium">Scheduled Processing</div>
                    <div className="truncatebody-small">
                      Automatically process new files on a schedule
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap items-center space-x-3">
                  <Bell className="h-5 w-5 truncateorange-500" />
                  <div>
                    <div className="font-medium">Smart Alerts</div>
                    <div className="truncatebody-small">
                      Get notified about unusual spending patterns
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reporting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center">
                <Download className="h-5 w-5 mr-2" />
                Advanced Reporting
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="space-y-3">
                <div className="flex flex-wrap items-center space-x-3">
                  <Share className="h-5 w-5 truncateteal-500" />
                  <div>
                    <div className="font-medium">Export Integration</div>
                    <div className="truncatebody-small">
                      Export to QuickBooks, Excel, and other accounting software
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap items-center space-x-3">
                  <Settings className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Custom Reports</div>
                    <div className="truncatebody-small">
                      Build tailored reports for specific business needs
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="intelligence" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                Business Intelligence
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="space-y-3">
                <div className="flex flex-wrap items-center space-x-3">
                  <Sparkles className="h-5 w-5 truncatepink-500" />
                  <div>
                    <div className="font-medium">AI Insights</div>
                    <div className="truncatebody-small">
                      Get intelligent recommendations based on your data
                      patterns
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap items-center space-x-3">
                  <BookOpen className="h-5 w-5 truncateindigo-500" />
                  <div>
                    <div className="font-medium">Knowledge Base</div>
                    <div className="truncatebody-small">
                      Access curated financial knowledge and best practices
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex flex-wrap justify-center space-x-3">
        <Button
          className="max-w-full"
          variant="outline"
          onClick={() => setCurrentView('next-steps')}
        >
          Back to Next Steps
        </Button>
        <Button
          className="max-w-full"
          onClick={() => {
            onComplete?.();
            navigate('/dashboard');
          }}
        >
          Start Exploring
        </Button>
      </div>
    </div>
  );

  const getCurrentView = () => {
    switch (currentView) {
      case 'celebration':
        return <CelebrationView />;
      case 'next-steps':
        return <NextStepsView />;
      case 'feature-tour':
        return <FeatureTourView />;
      default:
        return <CelebrationView />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-wrap items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>
              {currentView === 'celebration' && 'Onboarding Complete'}
              {currentView === 'next-steps' && "What's Next?"}
              {currentView === 'feature-tour' && 'Feature Tour'}
            </span>
          </DialogTitle>
          <DialogDescription>
            {currentView === 'celebration' &&
              'Congratulations on setting up your AI assistant'}
            {currentView === 'next-steps' &&
              'Choose your next action to get started'}
            {currentView === 'feature-tour' && 'Explore the power of Giki AI'}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex flex-wrap justify-center space-x-2 mb-6">
          {['celebration', 'next-steps', 'feature-tour'].map((view, index) => (
            <button
              key={view}
              onClick={() =>
                setCurrentView(
                  view as 'celebration' | 'next-steps' | 'feature-tour',
                )
              }
              className={`w-3 h-3 rounded-full transition-colors ${
                currentView === view
                  ? 'bg-info'
                  : index <
                      ['celebration', 'next-steps', 'feature-tour'].indexOf(
                        currentView,
                      )
                    ? 'bg-success'
                    : 'bg-muted'
              }`}
              aria-label={`Go to ${view} view`}
            />
          ))}
        </div>

        <div className="min-h-[400px] overflow-y-auto">{getCurrentView()}</div>
      </DialogContent>
    </Dialog>
  );
};

export default OnboardingCompletionFlow;
