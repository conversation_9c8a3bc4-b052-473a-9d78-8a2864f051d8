/**
 * Progressive File Upload Wizard
 *
 * Provides step-by-step guidance for new users during the file upload and onboarding process.
 * Breaks down the complex upload workflow into manageable steps with clear explanations.
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Upload,
  FileText,
  Brain,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Info,
  Clock,
  Zap,
  Target,
  BookOpen,
  Lightbulb,
} from 'lucide-react';
import DataUpload from '../DataUpload';

interface ProgressiveUploadWizardProps {
  onComplete?: () => void;
  isFirstTime?: boolean;
}

// Step Components
const WelcomeStep: React.FC<{ onNext: () => void }> = ({ onNext }) => (
  <div className="space-y-6 text-center">
    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex flex-wrap items-center justify-center">
      <Zap className="h-10 w-10 truncatewhite" />
    </div>
    <div>
      <h2 className="text-2xl font-bold mb-2">Welcome to Giki AI</h2>
      <p className="text-muted-foreground max-w-md mx-auto">
        Let&apos;s get your financial data set up in just a few simple steps.
        Our AI will help categorize your transactions automatically.
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
      <Card className="border-2">
        <CardContent className="p-4 text-center overflow-hidden">
          <Upload className="h-8 w-8 mx-auto mb-2 truncateblue-500" />
          <h3 className="font-semibold">Upload Files</h3>
          <p className="text-sm text-muted-foreground">
            CSV, Excel files supported
          </p>
        </CardContent>
      </Card>

      <Card className="border-2">
        <CardContent className="p-4 text-center overflow-hidden">
          <Brain className="h-8 w-8 mx-auto mb-2 truncategreen-500" />
          <h3 className="font-semibold">AI Processing</h3>
          <p className="text-sm text-muted-foreground">Smart categorization</p>
        </CardContent>
      </Card>

      <Card className="border-2">
        <CardContent className="p-4 text-center overflow-hidden">
          <CheckCircle className="h-8 w-8 mx-auto mb-2 truncatepurple-500" />
          <h3 className="font-semibold">Review & Refine</h3>
          <p className="text-sm text-muted-foreground">
            Perfect your categories
          </p>
        </CardContent>
      </Card>
    </div>

    <Button onClick={() => void onNext()} size="lg" className="mt-6">
      Get Started
      <ArrowRight className="ml-2 h-4 w-4" />
    </Button>
  </div>
);

const FilePreparationStep: React.FC<{ onNext: () => void }> = ({ onNext }) => (
  <div className="space-y-6">
    <div className="text-center mb-6">
      <FileText className="h-16 w-16 sm:w-16 mx-auto mb-4 truncateblue-500" />
      <h2 className="text-xl font-bold mb-2">Prepare Your Files</h2>
      <p className="text-muted-foreground">
        Make sure your transaction files are ready for upload
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center text-lg">
            <CheckCircle className="h-5 w-5 mr-2 truncategreen-500" />
            Supported Formats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 overflow-hidden">
          <div className="flex flex-wrap items-center">
            <Badge variant="outline" className="mr-2 max-w-[150px] truncate">
              .CSV
            </Badge>
            <span className="text-sm">Comma-separated values</span>
          </div>
          <div className="flex flex-wrap items-center">
            <Badge variant="outline" className="mr-2 max-w-[150px] truncate">
              .XLSX
            </Badge>
            <span className="text-sm">Excel spreadsheet</span>
          </div>
          <div className="flex flex-wrap items-center">
            <Badge variant="outline" className="mr-2 max-w-[150px] truncate">
              .XLS
            </Badge>
            <span className="text-sm">Legacy Excel format</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center text-lg">
            <Lightbulb className="h-5 w-5 mr-2 truncateyellow-500" />
            Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm overflow-hidden">
          <div className="flex flex-wrap items-start">
            <div className="w-2 h-2 bg-info rounded-full mt-2 mr-2 flex flex-wrap-shrink-0"></div>
            <span>Include date, description, and amount columns</span>
          </div>
          <div className="flex flex-wrap items-start">
            <div className="w-2 h-2 bg-info rounded-full mt-2 mr-2 flex flex-wrap-shrink-0"></div>
            <span>One transaction per row</span>
          </div>
          <div className="flex flex-wrap items-start">
            <div className="w-2 h-2 bg-info rounded-full mt-2 mr-2 flex flex-wrap-shrink-0"></div>
            <span>Clear, descriptive transaction descriptions</span>
          </div>
          <div className="flex flex-wrap items-start">
            <div className="w-2 h-2 bg-info rounded-full mt-2 mr-2 flex flex-wrap-shrink-0"></div>
            <span>Files under 5MB for best performance</span>
          </div>
        </CardContent>
      </Card>
    </div>

    <Alert>
      <Info className="h-4 w-4" />
      <AlertDescription>
        <strong>Tip:</strong> Our AI works best with detailed transaction
        descriptions. The more descriptive your transaction details, the more
        accurate the categorization will be.
      </AlertDescription>
    </Alert>

    <div className="flex flex-wrap justify-center">
      <Button className="max-w-full" onClick={() => void onNext()} size="lg">
        I&apos;m Ready to Upload
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  </div>
);

const UploadStep: React.FC<{
  onUploadSuccess: (uploadId: string) => void;
  onNext: () => void;
}> = ({ onUploadSuccess, onNext }) => {
  const [uploadComplete, setUploadComplete] = useState(false);

  const handleUploadSuccess = useCallback(
    (uploadId: string) => {
      setUploadComplete(true);
      onUploadSuccess(uploadId);
    },
    [onUploadSuccess],
  );

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <Upload className="h-16 w-16 sm:w-16 mx-auto mb-4 truncateblue-500" />
        <h2 className="text-xl font-bold mb-2">Upload Your Files</h2>
        <p className="text-muted-foreground">
          Drag and drop your transaction files or click to browse
        </p>
      </div>

      <DataUpload onUploadSuccess={handleUploadSuccess} />

      {uploadComplete && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-success" />
          <AlertDescription className="text-success-foreground">
            <strong>Upload successful!</strong> Your files are now being
            processed by our AI. You&apos;ll be able to review the results in
            the next step.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-wrap justify-center">
        <Button
          className="max-w-full"
          onClick={() => void onNext()}
          disabled={!uploadComplete}
          size="lg"
          variant={uploadComplete ? 'default' : 'outline'}
        >
          {uploadComplete ? 'Continue to Processing' : 'Upload Files First'}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const ProcessingStep: React.FC<{ uploadId?: string; onNext: () => void }> = ({
  uploadId,
  onNext,
}) => {
  const [processingStage, setProcessingStage] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(
    'Initializing AI processing...',
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingError, setProcessingError] = useState<string | null>(null);

  React.useEffect(() => {
    const processFiles = async () => {
      if (isProcessing || !uploadId) return;
      setIsProcessing(true);
      setProcessingError(null);

      try {
        // Import real processing service
        const { createProcessingService } = await import(
          '../../services/processingService'
        );

        // Create processing service for this upload
        const processingService = createProcessingService(uploadId);

        // Subscribe to status updates
        processingService.onStatusUpdate((status) => {
          setProcessingStage(status.currentStage);
          if (status.stages[status.currentStage]) {
            setCurrentMessage(status.stages[status.currentStage].description);
          }

          if (status.failed) {
            setProcessingError(status.error || 'Processing failed');
          } else if (status.completed) {
            // Processing completed successfully - move to next step
            setTimeout(() => {
              onNext();
            }, 500); // Small delay to show completion state
          }
        });

        // Start real AI processing
        const result = await processingService.startProcessing();

        if (!result.success) {
          setProcessingError(result.error || 'Processing failed');
        }

        // Clean up
        processingService.dispose();
      } catch (error) {
        console.error('Real AI processing error:', error);
        setProcessingError(
          error instanceof Error ? error.message : 'Processing failed',
        );
      } finally {
        setIsProcessing(false);
      }
    };

    void processFiles();
  }, [uploadId, onNext, isProcessing]);

  const stages = [
    'File Validation',
    'Column Detection',
    'AI Schema Interpretation',
    'Data Validation',
    'Processing Complete',
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <Brain
          className={`h-16 w-16 sm:w-16 mx-auto mb-4 ${processingError ? 'text-red-500' : 'text-purple-500'} ${isProcessing && !processingError ? 'animate-pulse' : ''}`}
        />
        <h2 className="text-xl font-bold mb-2">
          {processingError ? 'Processing Error' : 'AI Processing Your Data'}
        </h2>
        <p className="text-muted-foreground">
          {processingError
            ? 'There was an issue processing your file'
            : 'Our intelligent system is analyzing your file structure and data patterns'}
        </p>
      </div>

      {processingError ? (
        <Alert className="border-red-200 bg-red-50">
          <Info className="h-4 w-4 text-destructive" />
          <AlertDescription className="truncatered-800">
            <strong>Processing Failed:</strong> {processingError}
            <br />
            Please try uploading your file again or contact support if the issue
            persists.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-4">
          <Progress
            value={((processingStage + 1) / stages.length) * 100}
            className="w-full"
          />

          <div className="space-y-2">
            <div className="text-center text-heading-5 text-primary">
              {currentMessage}
            </div>
            {stages.map((stage, index) => (
              <div
                key={index}
                className="flex flex-wrap items-center space-x-3"
              >
                {index < processingStage ? (
                  <CheckCircle className="h-5 w-5 truncategreen-500" />
                ) : index === processingStage ? (
                  <Clock className="h-5 w-5 truncateblue-500 animate-spin" />
                ) : (
                  <div className="h-5 w-5 rounded-full border-2 border-muted" />
                )}
                <span
                  className={
                    index <= processingStage
                      ? 'text-foreground'
                      : 'text-muted-foreground'
                  }
                >
                  {stage}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          {processingError
            ? 'Check your file format and ensure it contains transaction data with date, description, and amount columns.'
            : "This process uses real AI analysis and typically takes 15-30 seconds. We're examining your data patterns for accurate schema interpretation."}
        </AlertDescription>
      </Alert>
    </div>
  );
};

const CompletionStep: React.FC<{ onComplete: () => void }> = ({
  onComplete,
}) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6 text-center">
      <div className="mx-auto w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex flex-wrap items-center justify-center">
        <CheckCircle className="h-10 w-10 truncatewhite" />
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-2">Setup Complete</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Your transactions have been processed and categorized. You&apos;re
          ready to start using Giki AI!
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
        <Card
          className="border-2 hover:border-blue-500 transition-colors cursor-pointer"
          onClick={() => navigate('/review')}
        >
          <CardContent className="p-6 text-center overflow-hidden">
            <Target className="h-8 w-8 mx-auto mb-3 truncateblue-500" />
            <h3 className="font-semibold mb-2">Review Transactions</h3>
            <p className="text-sm text-muted-foreground">
              Check AI categorization results and make adjustments
            </p>
          </CardContent>
        </Card>

        <Card
          className="border-2 hover:border-green-500 transition-colors cursor-pointer"
          onClick={() => navigate('/dashboard')}
        >
          <CardContent className="p-6 text-center overflow-hidden">
            <BookOpen className="h-8 w-8 mx-auto mb-3 truncategreen-500" />
            <h3 className="font-semibold mb-2">Explore Dashboard</h3>
            <p className="text-sm text-muted-foreground">
              View insights and analytics from your data
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-wrap flex-col sm:flex-row gap-3 justify-center mt-6">
        <Button
          className="max-w-full"
          onClick={() => navigate('/review')}
          size="lg"
        >
          Review My Transactions
        </Button>
        <Button
          className="max-w-full"
          onClick={() => void onComplete()}
          variant="outline"
          size="lg"
        >
          Go to Dashboard
        </Button>
      </div>
    </div>
  );
};

export const ProgressiveUploadWizard: React.FC<
  ProgressiveUploadWizardProps
> = ({ onComplete, isFirstTime: _isFirstTime = true }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [uploadId, setUploadId] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleUploadSuccess = useCallback(
    (uploadId: string) => {
      // Store upload ID for processing step
      setUploadId(uploadId);
      // Mark upload step as complete
      setCompletedSteps((prev) => new Set(prev).add(currentStep));
    },
    [currentStep],
  );

  const steps: Array<{
    id: string;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    component: React.ComponentType<Record<string, never>>;
  }> = [
    {
      id: 'welcome',
      title: 'Welcome',
      description: 'Introduction to Giki AI',
      icon: Zap,
      component: () => <WelcomeStep onNext={() => setCurrentStep(1)} />,
    },
    {
      id: 'preparation',
      title: 'Prepare Files',
      description: 'Get your data ready',
      icon: FileText,
      component: () => <FilePreparationStep onNext={() => setCurrentStep(2)} />,
    },
    {
      id: 'upload',
      title: 'Upload',
      description: 'Upload your transaction files',
      icon: Upload,
      component: () => (
        <UploadStep
          onUploadSuccess={handleUploadSuccess}
          onNext={() => setCurrentStep(3)}
        />
      ),
    },
    {
      id: 'processing',
      title: 'AI Processing',
      description: 'AI analyzes your data',
      icon: Brain,
      component: () => (
        <ProcessingStep
          uploadId={uploadId || undefined}
          onNext={() => setCurrentStep(4)}
        />
      ),
    },
    {
      id: 'completion',
      title: 'Complete',
      description: 'Ready to use Giki AI',
      icon: CheckCircle,
      component: () => (
        <CompletionStep
          onComplete={() => {
            onComplete?.();
            navigate('/dashboard');
          }}
        />
      ),
    },
  ];

  const currentStepData = steps[currentStep];
  const CurrentStepComponent = currentStepData.component;

  return (
    <Dialog open={true} onOpenChange={() => {}}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-wrap items-center space-x-2">
            <currentStepData.icon className="h-5 w-5" />
            <span>{currentStepData.title}</span>
          </DialogTitle>
          <DialogDescription>
            Step {currentStep + 1} of {steps.length}:{' '}
            {currentStepData.description}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="mb-6">
          <Progress
            value={(currentStep / (steps.length - 1)) * 100}
            className="w-full mb-4"
          />
          <div className="flex flex-wrap justify-between truncate text-caption text-muted-foreground">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className="flex flex-wrap flex-col items-center"
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                    index <= currentStep
                      ? 'border-blue-500 bg-info text-white'
                      : 'border-muted text-muted-foreground'
                  }`}
                >
                  {completedSteps.has(index) ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <step.icon className="h-4 w-4" />
                  )}
                </div>
                <span className="mt-1 text-center">{step.title}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="min-h-[400px] overflow-y-auto">
          <CurrentStepComponent />
        </div>

        {/* Navigation */}
        <div className="flex flex-wrap justify-between mt-6">
          <Button
            className="max-w-full"
            variant="outline"
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          <div className="text-sm text-muted-foreground">
            Step {currentStep + 1} of {steps.length}
          </div>

          <Button
            className="max-w-full"
            onClick={() => {
              if (currentStep === steps.length - 1) {
                onComplete?.();
                navigate('/dashboard');
              } else {
                setCurrentStep(Math.min(steps.length - 1, currentStep + 1));
              }
            }}
            disabled={currentStep === 2 && !completedSteps.has(2)} // Disable on upload step until complete
          >
            {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProgressiveUploadWizard;
