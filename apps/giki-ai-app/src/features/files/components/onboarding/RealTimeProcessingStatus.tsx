/**
 * Real-time Processing Status Component
 *
 * Uses actual Server-Sent Events (SSE) for real-time progress tracking
 * instead of simulated progress. Connects to the backend progress API.
 */
import React, { useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Brain,
  CheckCircle,
  FileText,
  Zap,
  AlertCircle,
  RefreshCw,
  Database,
  Loader2,
} from 'lucide-react';
import { useProgressTracking } from '@/shared/hooks/useProgressTracking';

interface ProcessingResult {
  transactions_processed?: number;
  categorization_accuracy?: number;
  processing_time?: number;
}

interface ProcessingMetadata {
  transaction_count?: number;
  transactions_processed?: number;
  columns_mapped?: number;
  progress?: number;
}

interface StageInfo {
  status: 'completed' | 'in_progress' | 'pending';
  message?: string;
  metadata?: ProcessingMetadata;
}

interface RealTimeProcessingStatusProps {
  taskId: string | null;
  onComplete?: (result: ProcessingResult) => void;
  onError?: (error: string) => void;
}

const stageIcons: Record<
  string,
  React.ComponentType<{ className?: string }>
> = {
  'Column Mapping': Database,
  'Data Parsing': FileText,
  'Data Validation': CheckCircle,
  'AI Categorization': Brain,
  'Saving Results': Zap,
};

export const RealTimeProcessingStatus: React.FC<
  RealTimeProcessingStatusProps
> = ({ taskId, onComplete, onError }) => {
  const {
    isConnected,
    isLoading,
    status,
    progress,
    currentStage,
    message,
    error,
    updates,
    result,
    connect,
  } = useProgressTracking(taskId, {
    onComplete,
    onError,
    autoConnect: true,
  });

  const progressPercentage = useMemo(
    () => Math.round(progress * 100),
    [progress],
  );

  const currentStageIcon = useMemo(() => {
    const stage = updates.find((u) => u.stage === currentStage);
    if (stage?.stage && stageIcons[stage.stage]) {
      const Icon = stageIcons[stage.stage];
      return <Icon className="h-5 w-5" />;
    }
    return <Loader2 className="h-5 w-5 animate-spin" />;
  }, [currentStage, updates]);

  const stageSummary = useMemo(() => {
    const stages = new Map<string, StageInfo>();

    // Build stage summary from updates
    updates.forEach((update) => {
      if (update.stage) {
        const existing = stages.get(update.stage);
        const currentProgress = update.progress || 0;
        const existingProgress = existing?.metadata?.progress || 0;
        if (!existing || currentProgress > existingProgress) {
          stages.set(update.stage, {
            status: currentProgress === 1 ? 'completed' : 'in_progress',
            message: update.message,
            metadata: {
              ...(update.metadata as ProcessingMetadata),
              progress: currentProgress,
            },
          });
        }
      }
    });

    return Array.from(stages.entries());
  }, [updates]);

  if (!taskId) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between">
          <div>
            <CardTitle className="flex flex-wrap items-center gap-2">
              Processing Your File
              {isConnected && (
                <Badge
                  variant="outline"
                  className="animate-pulse max-w-[150px] truncate"
                >
                  <span className="h-2 w-2 rounded-full bg-success mr-1" />
                  Live
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Real-time progress updates as we process your data
            </CardDescription>
          </div>
          {status === 'failed' && (
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              onClick={() => connect()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6 overflow-hidden">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex flex-wrap items-center justify-between text-sm">
            <span className="font-medium flex flex-wrap items-center gap-2">
              {currentStageIcon}
              {currentStage || 'Initializing...'}
            </span>
            <span className="text-muted-foreground">{progressPercentage}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          {message && <p className="truncatebody-small">{message}</p>}
        </div>

        {/* Stage Details */}
        <div className="space-y-3">
          {stageSummary.map((stageEntry) => {
            const stageName = stageEntry[0];
            const stageInfo = stageEntry[1];
            const Icon = stageIcons[stageName] || CheckCircle;
            return (
              <div
                key={stageName}
                className={`flex items-start gap-3 p-3 rounded-lg transition-colors ${
                  stageInfo.status === 'completed'
                    ? 'bg-green-50 dark:bg-green-950/20'
                    : stageInfo.status === 'in_progress'
                      ? 'bg-info/5 dark:bg-blue-950/20'
                      : 'bg-muted/50 dark:bg-gray-950/20'
                }`}
              >
                <div
                  className={`mt-0.5 ${
                    stageInfo.status === 'completed'
                      ? 'text-success'
                      : stageInfo.status === 'in_progress'
                        ? 'text-info'
                        : 'text-muted-foreground/50'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                </div>
                <div className="flex flex-wrap-1 min-w-0">
                  <div className="flex flex-wrap items-center justify-between">
                    <p
                      className={`font-medium ${
                        stageInfo.status === 'completed'
                          ? 'text-green-900 dark:text-green-100'
                          : stageInfo.status === 'in_progress'
                            ? 'text-info-foreground dark:text-blue-100'
                            : 'text-muted-foreground dark:text-gray-400'
                      }`}
                    >
                      {stageName}
                    </p>
                    {stageInfo.status === 'completed' && (
                      <CheckCircle className="h-4 w-4 text-success" />
                    )}
                  </div>
                  {stageInfo.message && (
                    <p className="truncatebody-small mt-1">
                      {stageInfo.message}
                    </p>
                  )}
                  {stageInfo.metadata && (
                    <div className="flex flex-wrap gap-4 mt-2 truncate text-caption text-muted-foreground">
                      {typeof stageInfo?.metadata?.transaction_count ===
                        'number' && (
                        <span>
                          {stageInfo?.metadata?.transaction_count} transactions
                        </span>
                      )}
                      {typeof stageInfo?.metadata?.transactions_processed ===
                        'number' && (
                        <span>
                          {stageInfo?.metadata?.transactions_processed}{' '}
                          processed
                        </span>
                      )}
                      {typeof stageInfo?.metadata?.columns_mapped ===
                        'number' && (
                        <span>
                          {stageInfo?.metadata?.columns_mapped} columns mapped
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success Result */}
        {status === 'completed' && result && (
          <Alert className="border-green-200 bg-green-50 dark:bg-green-950/20">
            <CheckCircle className="h-4 w-4 text-success" />
            <AlertDescription className="text-success-foreground dark:text-green-200">
              <div className="font-medium mb-2">Processing Complete</div>
              <div className="space-y-1 text-sm">
                {(() => {
                  const processedResult = result as ProcessingResult;
                  return (
                    <>
                      {typeof processedResult.transactions_processed ===
                        'number' && (
                        <div>
                          Transactions processed:{' '}
                          {processedResult.transactions_processed}
                        </div>
                      )}
                      {typeof processedResult.categorization_accuracy ===
                        'number' && (
                        <div>
                          Categorization accuracy:{' '}
                          {(
                            processedResult.categorization_accuracy * 100
                          ).toFixed(1)}
                          %
                        </div>
                      )}
                      {typeof processedResult.processing_time === 'number' && (
                        <div>
                          Processing time:{' '}
                          {processedResult?.processing_time?.toFixed(1)}s
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Connection Status */}
        {!isConnected && !isLoading && status !== 'completed' && (
          <div className="text-center">
            <p className="truncatebody-small mb-2">
              Connection lost. Click to reconnect.
            </p>
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              onClick={() => connect()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reconnect
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
