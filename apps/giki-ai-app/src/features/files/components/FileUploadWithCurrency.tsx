import React, { useState, useRef } from 'react';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Card, CardContent } from '@/shared/components/ui/card';
import {
  Upload,
  FileSpreadsheet,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Info,
} from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { useId, announce, focusClasses } from '@/shared/utils/accessibility';
// File validation services temporarily disabled - using basic validation only

interface ValidationResult {
  is_valid: boolean;
  confidence: number;
  validation_errors?: string[];
  validation_warnings?: string[];
}

interface FileMetadata {
  row_count: number;
  column_count: number;
  file_type: string;
  file_size: number;
  headers: string[];
}

interface ValidationData {
  validation_result?: ValidationResult;
}

interface MetadataData {
  metadata?: FileMetadata;
}

interface FileUploadWithCurrencyProps {
  onFileUpload: (file: File, currency: string) => Promise<void>;
  isUploading: boolean;
}

export const FileUploadWithCurrency: React.FC<FileUploadWithCurrencyProps> = ({
  onFileUpload,
  isUploading,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast, dismiss } = useToast();
  const dropzoneId = useId('dropzone');
  const fileInputId = useId('file-input');
  const currencySelectId = useId('currency-select');

  // Advanced validation hooks disabled - using basic validation only

  const currencies = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
    { value: 'AUD', label: 'AUD (A$)' },
    { value: 'JPY', label: 'JPY (¥)' },
    { value: 'INR', label: 'INR (₹)' },
  ];

  const handleFileSelect = (file: File) => {
    // Clear any previous toast notifications
    dismiss();

    // Validation and metadata reset disabled

    // Basic client-side validation first
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
    ];

    if (
      !allowedTypes.includes(file.type) &&
      !file?.name?.toLowerCase().endsWith('.csv') &&
      !file?.name?.toLowerCase().endsWith('.xlsx') &&
      !file?.name?.toLowerCase().endsWith('.xls')
    ) {
      toast({
        title: 'Invalid File Type',
        description: 'Please select a CSV or Excel file (.csv, .xlsx, .xls)',
        variant: 'destructive',
      });
      announce(
        'Invalid file type. Please select a CSV or Excel file',
        'assertive',
      );
      return;
    }

    // Basic size check (will be validated more thoroughly by ADK service)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: 'File Too Large',
        description: 'Please select a file smaller than 10MB',
        variant: 'destructive',
      });
      announce(
        'File too large. Please select a file smaller than 10MB',
        'assertive',
      );
      return;
    }

    setSelectedFile(file);
    announce(`File selected: ${file.name}`, 'polite');

    // Enhanced validation disabled - using basic file validation only
    toast({
      title: 'File Selected',
      description: 'File selected for upload. Basic validation applied.',
    });
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e?.dataTransfer?.files && e?.dataTransfer?.files[0]) {
      void handleFileSelect(e?.dataTransfer?.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e?.target?.files && e?.target?.files[0]) {
      void handleFileSelect(e?.target?.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !selectedCurrency) return;

    try {
      await onFileUpload(selectedFile, selectedCurrency);
      // Reset form after successful upload
      setSelectedFile(null);
      setSelectedCurrency('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch {
      // Error handling is done in the parent component
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setSelectedCurrency('');
    // Validation reset disabled
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Helper function to render validation status
  const renderValidationStatus = () => {
    if (!selectedFile) return null;

    // Advanced validation disabled - using fallback values
    const isValidating = false;
    const hasValidation: ValidationData | null = null;
    const hasMetadata: MetadataData | null = null;
    const hasErrors = false;

    return (
      <Card className="mt-4">
        <CardContent className="p-4 overflow-hidden">
          <div className="space-y-3">
            <h4 className="font-medium flex flex-wrap items-center gap-2">
              <Info className="w-4 h-4" />
              File Analysis
            </h4>

            {isValidating && (
              <div className="flex flex-wrap items-center gap-2 text-info">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Analyzing file...</span>
              </div>
            )}

            {hasValidation && (
              <div className="space-y-2">
                <div className="flex flex-wrap items-center gap-2">
                  {hasValidation.validation_result?.is_valid ? (
                    <CheckCircle className="w-4 h-4 text-success" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-destructive" />
                  )}
                  <span className="text-sm font-medium">
                    Validation:{' '}
                    {hasValidation.validation_result?.is_valid
                      ? 'Passed'
                      : 'Failed'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({(hasValidation.validation_result?.confidence || 0) * 100}%
                    confidence)
                  </span>
                </div>

                {hasValidation.validation_result?.validation_errors?.length &&
                  hasValidation?.validation_result?.validation_errors.length >
                    0 && (
                    <div className="text-sm text-destructive">
                      Errors:{' '}
                      {hasValidation?.validation_result?.validation_errors.join(
                        ', ',
                      )}
                    </div>
                  )}

                {hasValidation.validation_result?.validation_warnings?.length &&
                  hasValidation?.validation_result?.validation_warnings.length >
                    0 && (
                    <div className="text-sm text-warning">
                      Warnings:{' '}
                      {hasValidation?.validation_result?.validation_warnings.join(
                        ', ',
                      )}
                    </div>
                  )}
              </div>
            )}

            {hasMetadata && hasMetadata.metadata && (
              <div className="space-y-2">
                <div className="flex flex-wrap items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span className="text-sm font-medium">
                    Metadata Extracted
                  </span>
                </div>
                <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
                  <div>Rows: {hasMetadata?.metadata?.row_count}</div>
                  <div>Columns: {hasMetadata?.metadata?.column_count}</div>
                  <div>Type: {hasMetadata?.metadata?.file_type}</div>
                  <div>
                    Size:{' '}
                    {(hasMetadata?.metadata?.file_size / 1024 / 1024).toFixed(
                      2,
                    )}{' '}
                    MB
                  </div>
                </div>
                {hasMetadata?.metadata?.headers.length > 0 && (
                  <div className="text-sm">
                    <span className="font-medium">Headers: </span>
                    <span className="text-muted-foreground">
                      {hasMetadata?.metadata?.headers.slice(0, 3).join(', ')}
                      {hasMetadata?.metadata?.headers.length > 3 &&
                        ` +${hasMetadata?.metadata?.headers.length - 3} more`}
                    </span>
                  </div>
                )}
              </div>
            )}

            {hasErrors && (
              <div className="flex flex-wrap items-center gap-2 text-destructive">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">
                  Analysis failed: {/* Validation error details disabled */}
                  &quot;Validation service unavailable&quot;
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {/* File Drop Zone */}
      <Card
        id={dropzoneId}
        className={`border-2 border-dashed transition-colors ${
          dragActive
            ? 'border-[hsl(var(--giki-primary))] bg-[hsl(var(--giki-success))]/10'
            : selectedFile
              ? 'border-success bg-success/10'
              : 'border-[hsl(var(--giki-border))] hover:border-[hsl(var(--giki-border-hover))]'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        role="region"
        aria-label="File upload drop zone"
        aria-describedby={`${dropzoneId}-description`}
      >
        <CardContent className="p-8 overflow-hidden">
          <div className="text-center">
            {selectedFile ? (
              <div className="space-y-3">
                <FileSpreadsheet
                  className="w-12 h-12 text-success mx-auto"
                  aria-hidden="true"
                />
                <div>
                  <p className="truncate text-heading-5 text-success">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB •{' '}
                    {selectedFile?.type?.includes('csv')
                      ? 'CSV File'
                      : 'Excel File'}
                  </p>
                </div>
                <Button
                  onClick={() => void resetForm()}
                  variant="outline"
                  size="sm"
                  className={`text-destructive hover:text-destructive/80 ${focusClasses}`}
                  aria-label="Remove selected file"
                >
                  Remove File
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <Upload
                  className="w-12 h-12 text-muted-foreground mx-auto"
                  aria-hidden="true"
                />
                <div id={`${dropzoneId}-description`}>
                  <p className="truncate text-heading-5">
                    Drop your file here or click to browse
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Supports CSV and Excel files (.csv, .xlsx, .xls) up to 10MB
                  </p>
                </div>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className={`gap-2 ${focusClasses}`}
                  aria-label="Choose file to upload"
                >
                  <Upload className="w-4 h-4" aria-hidden="true" />
                  Choose File
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <label htmlFor={fileInputId} className="sr-only">
        Select file to upload
      </label>
      <input
        ref={fileInputRef}
        id={fileInputId}
        type="file"
        accept=".csv,.xlsx,.xls"
        onChange={handleFileInputChange}
        className="hidden"
        data-testid="file-input"
        aria-describedby={`${dropzoneId}-description`}
      />

      {/* Currency Selection */}
      {selectedFile && (
        <Card>
          <CardContent className="p-4 overflow-hidden">
            <div className="flex flex-wrap items-center justify-between">
              <div className="flex flex-wrap items-center gap-3">
                <DollarSign className="w-5 h-5 text-success" />
                <div>
                  <p className="font-medium">Select Currency</p>
                  <p className="text-sm text-muted-foreground">
                    Choose the currency used in this file
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-3">
                <label htmlFor={currencySelectId} className="sr-only">
                  Select currency for uploaded file
                </label>
                <Select
                  value={selectedCurrency}
                  onValueChange={(value) => {
                    setSelectedCurrency(value);
                    announce(`Currency selected: ${value}`, 'polite');
                  }}
                >
                  <SelectTrigger
                    id={currencySelectId}
                    className={`w-40 ${focusClasses}`}
                  >
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  className="max-w-full gap-2"
                  onClick={() => void handleUpload()}
                  disabled={!selectedFile || !selectedCurrency || isUploading}
                >
                  {isUploading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Upload File
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced File Analysis Display */}
      {renderValidationStatus()}
    </div>
  );
};

export default FileUploadWithCurrency;
