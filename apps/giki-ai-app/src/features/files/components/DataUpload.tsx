import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import {
  UploadCloud,
  X,
  File as FileIcon,
  BarChart3,
  CheckCircle2,
  AlertCircle,
  RotateCcw,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { uploadFile, UploadResponse } from '../services/fileService';
import { validateFile } from '../services/uploadService';

interface FileWithStatus {
  file: File;
  id: string; // Unique ID for each file instance
  status:
    | 'waiting'
    | 'uploading'
    | 'column_mapping_required'
    | 'processing'
    | 'completed'
    | 'error';
  progress: number; // 0-100
  error?: string;
  uploadId?: string; // To store the ID received from the backend after initial upload
  // jobId removed - no longer needed with synchronous processing
  currency?: string; // Currency for this specific file
  reportId?: string; // To store the processing report ID for detailed reports
  processingStats?: {
    totalRows?: number;
    successfulRows?: number;
    failedRows?: number;
    dataQualityScore?: number;
  };
  // Backend might tell us where to go next
  nextStep?: 'interpretation-review' | 'categorization-review';
}

interface DataUploadProps {
  onUploadSuccess?: (
    uploadId: string,
    processingCompleteCallback?: (
      uploadId: string,
      reportId: string | null,
      processingStats?: {
        totalRows?: number;
        successfulRows?: number;
        failedRows?: number;
        dataQualityScore?: number;
      },
    ) => void,
  ) => void; // Callback for successful upload needing column mapping
}

const acceptedFileTypes = '.xlsx,.xls,.csv';
// const acceptedMimeTypes = [
//   'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet', // .xlsx
//   'application/vnd.ms-excel', // .xls
//   'text/csv', // .csv
// ];

const DataUpload: React.FC<DataUploadProps> = ({ onUploadSuccess }) => {
  const [queuedFiles, setQueuedFiles] = useState<FileWithStatus[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const addFilesToQueue = useCallback(
    (files: FileList | File[]) => {
      setError(null);
      const newFiles: FileWithStatus[] = [];
      Array.from(files).forEach((file) => {
        const validation = validateFile(file);
        if (validation.isValid) {
          if (
            !queuedFiles.some(
              (f) => f?.file?.name === file.name && f?.file?.size === file.size,
            )
          ) {
            newFiles.push({
              file,
              id: `${file.name}-${file.lastModified}-${file.size}`, // Basic unique ID
              status: 'waiting',
              progress: 0,
              currency: 'USD', // Default currency
            });
          }
        } else {
          setError(validation.error || 'Invalid file type');
        }
      });
      if (newFiles.length > 0) {
        setQueuedFiles((prevFiles) => [...prevFiles, ...newFiles]);
      }
    },
    [queuedFiles],
  ); // Added validateFile to dependencies, setError and setQueuedFiles are stable

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event?.target?.files;
    if (files) {
      addFilesToQueue(files);
    }
    // Reset file input to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragEnter = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    },
    [],
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    },
    [],
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      // You can add custom drag over effects here if needed
    },
    [],
  );

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
      const files = event?.dataTransfer?.files;
      if (files && files.length > 0) {
        addFilesToQueue(files);
        event?.dataTransfer?.clearData();
      }
    },
    [addFilesToQueue],
  );

  const removeFileFromQueue = (fileIdToRemove: string) => {
    setQueuedFiles((prevFiles) => {
      const updatedFiles = prevFiles.filter((f) => f.id !== fileIdToRemove);

      // Only clear global error if no files remain or no errors remain
      if (
        updatedFiles.length === 0 ||
        !updatedFiles.some((f) => f.status === 'error')
      ) {
        setError(null);
      }

      return updatedFiles;
    });
  };

  // Retry failed upload for a specific file
  const retryFileUpload = async (fileId: string) => {
    const fileToRetry = queuedFiles.find((f) => f.id === fileId);
    if (!fileToRetry || fileToRetry.status !== 'error') return;

    // Reset file status to waiting and clear error
    setQueuedFiles((prev) =>
      prev.map((f) =>
        f.id === fileId
          ? {
              ...f,
              status: 'waiting',
              error: undefined,
              progress: 0,
            }
          : f,
      ),
    );

    // Trigger upload for this specific file
    await handleUploadAll();
  };

  // Function to update file with processing statistics and report ID
  const updateFileProcessingResults = (
    fileId: string,
    reportId: string | null | undefined,
    processingStats?: {
      totalRows?: number;
      successfulRows?: number;
      failedRows?: number;
      dataQualityScore?: number;
    },
  ) => {
    setQueuedFiles((prev) =>
      prev.map((f) =>
        f.id === fileId
          ? {
              ...f,
              status: 'completed' as const,
              progress: 100,
              reportId: reportId || undefined,
              processingStats,
            }
          : f,
      ),
    );
  };

  const handleUploadAll = async () => {
    if (queuedFiles.filter((f) => f.status === 'waiting').length === 0) {
      setError('No files are waiting for upload.');
      return;
    }
    setError(null);

    const filesToUpload = queuedFiles.filter((f) => f.status === 'waiting');

    // Limit concurrent uploads to prevent resource exhaustion
    const MAX_CONCURRENT_UPLOADS = 3;
    const uploadQueue = [...filesToUpload];

    // Process uploads in batches to prevent ERR_INSUFFICIENT_RESOURCES
    while (uploadQueue.length > 0) {
      const batch = uploadQueue.splice(0, MAX_CONCURRENT_UPLOADS);
      await Promise.all(
        batch.map(async (fileWithStatus) => {
          try {
            // Optimistically update status
            setQueuedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWithStatus.id
                  ? { ...f, status: 'uploading', progress: 10 }
                  : f,
              ),
            );

            // Upload file with progress tracking
            const uploadResult = await uploadFile(fileWithStatus.file);

            if (uploadResult instanceof Error || 'error' in uploadResult) {
              throw uploadResult instanceof Error
                ? uploadResult
                : new Error('Upload failed');
            }

            const uploadResponse: UploadResponse =
              uploadResult as UploadResponse;

            console.log('📤 Upload response:', uploadResponse);
            console.log('📤 Upload ID:', uploadResponse.upload_id);

            setQueuedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWithStatus.id
                  ? {
                      ...f,
                      status: 'processing',
                      progress: 50,
                      uploadId: uploadResponse.upload_id,
                    }
                  : f,
              ),
            );

            // Mark file as ready for column mapping
            setQueuedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWithStatus.id
                  ? {
                      ...f,
                      status: 'column_mapping_required',
                      progress: 75,
                      uploadId: uploadResponse.upload_id,
                      nextStep: 'interpretation-review',
                    }
                  : f,
              ),
            );

            // Trigger the column mapping modal via callback to App.tsx
            if (onUploadSuccess) {
              // Pass the processing completion callback to handle report_id updates
              onUploadSuccess(
                uploadResponse.upload_id,
                (uploadId, reportId, processingStats) => {
                  updateFileProcessingResults(
                    fileWithStatus.id,
                    reportId,
                    processingStats,
                  );
                },
              );
            }
          } catch (err) {
            // Upload failed for file - preserve progress to show how far upload got
            setQueuedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWithStatus.id
                  ? {
                      ...f,
                      status: 'error',
                      error:
                        err instanceof Error
                          ? err.message
                          : 'Unknown upload error',
                      // Keep current progress to show how far upload progressed before failing
                      // Don't reset to 0 - this preserves user context
                    }
                  : f,
              ),
            );
          }
        }),
      ); // Close Promise.all
    } // Close while loop
  };

  // ADK Agent processes files synchronously - no polling needed
  // Removed job status polling useEffect since ADK agent returns results immediately

  // Navigate when a file is completed and has a next step
  useEffect(() => {
    const completedFileWithNav = queuedFiles.find(
      (f) => f.status === 'completed' && f.nextStep,
    );
    if (completedFileWithNav) {
      // For simplicity, navigate on the first completed file with a next step.
      // In a real app, you might want to wait for all or provide a summary.
      if (completedFileWithNav.nextStep === 'interpretation-review') {
        // Assuming uploadId is relevant for the review page
        void navigate(
          `/review/interpretation/${completedFileWithNav.uploadId}`,
        );
      } else if (completedFileWithNav.nextStep === 'categorization-review') {
        void navigate(
          `/review/categorization/${completedFileWithNav.uploadId}`,
        );
      }
      // Optionally, remove the file from queue or mark as navigated
      // setQueuedFiles(prev => prev.filter(f => f.id !== completedFileWithNav.id));
    }
  }, [queuedFiles, navigate]);

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const getFileStatusColor = (status: FileWithStatus['status']) => {
    switch (status) {
      case 'uploading':
        return 'text-info';
      case 'column_mapping_required':
        return 'text-warning';
      case 'processing':
        return 'text-warning';
      case 'completed':
        return 'text-success';
      case 'error':
        return 'text-destructive';
      case 'waiting':
      default:
        return 'text-muted-foreground';
    }
  };

  const getFileStatusIcon = (status: FileWithStatus['status']) => {
    switch (status) {
      case 'uploading':
        return 'text-info';
      case 'column_mapping_required':
        return 'text-warning';
      case 'processing':
        return 'text-warning';
      case 'completed':
        return <CheckCircle2 className="w-3 h-3 text-success" />;
      case 'error':
        return <AlertCircle className="w-3 h-3 text-destructive" />;
      case 'waiting':
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {/* Error Message */}
      {error && (
        <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md border border-destructive/20">
          {error}
        </div>
      )}

      {/* File Input - Visible for E2E tests */}
      <Input
        id="file-upload-input"
        type="file"
        accept={acceptedFileTypes}
        onChange={handleFileChange}
        className="hidden"
        ref={fileInputRef}
        multiple
        data-testid="file-input"
      />

      {/* Compact Drag and Drop Area */}
      <div
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={`flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-lg cursor-pointer
                    ${
                      isDragging
                        ? 'border-primary bg-primary/10'
                        : 'border-border hover:border-primary/60'
                    }
                    transition-colors duration-200 ease-in-out min-h-[160px]`}
        onClick={() => void handleBrowseClick()}
      >
        <UploadCloud
          className={`w-10 h-10 mb-2 ${
            isDragging ? 'text-primary' : 'text-muted-foreground'
          }`}
        />
        <p
          className={`text-base font-medium ${
            isDragging ? 'text-primary' : 'text-foreground'
          }`}
        >
          Drag & drop files here, or{' '}
          <span className="text-primary hover:underline">click to browse</span>
        </p>
        <p className="truncate text-caption text-muted-foreground/70 mt-1">
          Max 5MB each • .xlsx, .xls, .csv
        </p>
      </div>

      {/* Compact File Queue */}
      {queuedFiles.length > 0 && (
        <div className="space-y-2">
          <h3 className="truncatelabel text-foreground">
            Selected Files ({queuedFiles.length})
          </h3>
          <div className="space-y-1">
            {queuedFiles.map((item) => (
              <div
                key={item.id}
                className="flex flex-wrap items-center justify-between p-2 bg-muted rounded border"
              >
                <div className="flex flex-wrap items-center space-x-2 flex-grow min-w-0">
                  <FileIcon className="w-4 h-4 truncate text-muted flex flex-wrap-shrink-0" />
                  <div className="flex flex-wrap-grow min-w-0">
                    <p
                      className="truncatelabel text-foreground truncate"
                      title={item?.file?.name}
                    >
                      {item?.file?.name}
                    </p>
                  </div>
                </div>
                <div className="flex flex-wrap items-center space-x-2 flex-shrink-0">
                  {/* Show processing stats if available */}
                  {item.processingStats && (
                    <div className="truncate text-caption text-muted-foreground mr-2">
                      <div className="flex flex-wrap items-center space-x-1">
                        <BarChart3 className="w-3 h-3" />
                        <span>
                          {item.processingStats.successfulRows}/
                          {item.processingStats.totalRows}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Status indicator with icon */}
                  <div className="flex flex-wrap items-center space-x-1">
                    {getFileStatusIcon(item.status)}
                    <span
                      className={`text-xs font-medium ${getFileStatusColor(item.status)}`}
                    >
                      {item.status === 'uploading'
                        ? `${item.progress}%`
                        : item.status === 'completed' &&
                            item.processingStats &&
                            item.processingStats.successfulRows &&
                            item.processingStats.totalRows
                          ? `${Math.round((item.processingStats.successfulRows / item.processingStats.totalRows) * 100)}% Success`
                          : item?.status?.charAt(0).toUpperCase() +
                            item?.status?.slice(1)}
                    </span>
                  </div>

                  {/* View Report button for completed files */}
                  {item.status === 'completed' && item.reportId && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        navigate(`/reports/processing/${item.reportId}`)
                      }
                      className="text-info hover:text-info/80 h-6 px-2 text-caption"
                    >
                      <BarChart3 className="w-3 h-3 mr-1" />
                      Report
                    </Button>
                  )}

                  {/* Retry button for failed files */}
                  {item.status === 'error' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => retryFileUpload(item.id)}
                      className="text-orange-600 hover:text-orange-800 h-6 px-2 text-caption"
                      title={`Retry upload: ${item.error || 'Unknown error'}`}
                    >
                      <RotateCcw className="w-3 h-3 mr-1" />
                      Retry
                    </Button>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFileFromQueue(item.id)}
                    className="text-destructive hover:text-destructive/80 h-6 w-6 p-0"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="mb-4 text-sm text-destructive bg-destructive/10 p-3 rounded-md border border-destructive/20">
          {error}
        </p>
      )}

      {/* Upload Button */}
      <Button
        onClick={() => {
          handleUploadAll().catch((err: unknown) => {
            // Error initiating upload process - handled by internal error states
            // setError is already used within handleUploadAll for specific errors,
            // this catch is for unexpected errors from handleUploadAll itself.
            const errorMessage =
              err instanceof Error ? err.message : 'Unknown error occurred';
            setError(errorMessage);
          });
        }}
        disabled={
          queuedFiles.filter((f) => f.status === 'waiting').length === 0 ||
          queuedFiles.some(
            (f) => f.status === 'uploading' || f.status === 'processing',
          )
        }
        variant="default"
        className="w-full"
        size="lg"
        data-testid="upload-button"
        type="submit"
      >
        {queuedFiles.some(
          (f) => f.status === 'uploading' || f.status === 'processing',
        )
          ? 'Processing...'
          : 'Upload'}
      </Button>
    </div>
  );
};

export default DataUpload;
