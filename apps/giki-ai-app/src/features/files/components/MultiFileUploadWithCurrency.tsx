import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Upload, FileSpreadsheet, X, Plus } from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { Badge } from '@/shared/components/ui/badge';

interface FileWithCurrency {
  file: File;
  currency: string;
  id: string;
}

interface MultiFileUploadWithCurrencyProps {
  onFilesUpload: (
    files: Array<{ file: File; currency: string }>,
  ) => Promise<void>;
  isUploading: boolean;
  maxFiles?: number;
}

export const MultiFileUploadWithCurrency: React.FC<
  MultiFileUploadWithCurrencyProps
> = ({ onFilesUpload, isUploading, maxFiles = 10 }) => {
  const [selectedFiles, setSelectedFiles] = useState<FileWithCurrency[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const currencies = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
    { value: 'AUD', label: 'AUD (A$)' },
    { value: 'JPY', label: 'JPY (¥)' },
    { value: 'INR', label: 'INR (₹)' },
  ];

  const validateFile = (file: File): boolean => {
    // Validate file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
    ];

    if (
      !allowedTypes.includes(file.type) &&
      !file?.name?.toLowerCase().endsWith('.csv') &&
      !file?.name?.toLowerCase().endsWith('.xlsx') &&
      !file?.name?.toLowerCase().endsWith('.xls')
    ) {
      toast({
        title: 'Invalid File Type',
        description: `${file.name}: Please select a CSV or Excel file (.csv, .xlsx, .xls)`,
        variant: 'destructive',
      });
      return false;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: 'File Too Large',
        description: `${file.name}: Please select a file smaller than 10MB`,
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const handleFilesSelect = (files: FileList) => {
    const newFiles: FileWithCurrency[] = [];

    // Check if adding these files would exceed the limit
    if (selectedFiles.length + files.length > maxFiles) {
      toast({
        title: 'Too Many Files',
        description: `Maximum ${maxFiles} files allowed. You can upload ${maxFiles - selectedFiles.length} more files.`,
        variant: 'destructive',
      });
      return;
    }

    Array.from(files).forEach((file) => {
      // Check if file is already selected
      if (
        selectedFiles.some(
          (f) => f?.file?.name === file.name && f?.file?.size === file.size,
        )
      ) {
        toast({
          title: 'Duplicate File',
          description: `${file.name} is already selected.`,
          variant: 'destructive',
        });
        return;
      }

      if (validateFile(file)) {
        newFiles.push({
          file,
          currency: '', // Will be set by user
          id: `${file.name}-${file.lastModified}-${file.size}`,
        });
      }
    });

    if (newFiles.length > 0) {
      setSelectedFiles((prev) => [...prev, ...newFiles]);
      toast({
        title: 'Files Added',
        description: `${newFiles.length} file${newFiles.length !== 1 ? 's' : ''} added. Set currency for each file.`,
      });
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e?.dataTransfer?.files && e?.dataTransfer?.files.length > 0) {
      handleFilesSelect(e?.dataTransfer?.files);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e?.target?.files && e?.target?.files.length > 0) {
      handleFilesSelect(e?.target?.files);
    }
  };

  const updateFileCurrency = (fileId: string, currency: string) => {
    setSelectedFiles((prev) =>
      prev.map((f) => (f.id === fileId ? { ...f, currency } : f)),
    );
  };

  const removeFile = (fileId: string) => {
    setSelectedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleUploadAll = async () => {
    // Check if all files have currencies set
    const filesWithoutCurrency = selectedFiles.filter((f) => !f.currency);
    if (filesWithoutCurrency.length > 0) {
      toast({
        title: 'Missing Currency',
        description: `Please set currency for ${filesWithoutCurrency.length} file${filesWithoutCurrency.length !== 1 ? 's' : ''}.`,
        variant: 'destructive',
      });
      return;
    }

    try {
      await onFilesUpload(
        selectedFiles.map((f) => ({ file: f.file, currency: f.currency })),
      );
      // Reset form after successful upload
      setSelectedFiles([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch {
      // Error handling is done in the parent component
    }
  };

  const resetForm = () => {
    setSelectedFiles([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const allFilesHaveCurrency =
    selectedFiles.length > 0 && selectedFiles.every((f) => f.currency);

  return (
    <div className="space-y-4">
      {/* File Drop Zone */}
      <Card
        className={`border-2 border-dashed transition-colors ${
          dragActive
            ? 'border-primary bg-emerald-50'
            : selectedFiles.length > 0
              ? 'border-green-500 bg-green-50'
              : 'border-border hover:border-border/80'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <CardContent className="p-8 overflow-hidden">
          <div className="text-center">
            <div className="space-y-3">
              <Upload className="w-12 h-12 truncate text-secondary mx-auto" />
              <div>
                <p className="truncate text-heading-5">
                  Drop multiple files here or click to browse
                </p>
                <p className="text-sm text-text-secondary">
                  Supports CSV and Excel files (.csv, .xlsx, .xls) up to 10MB
                  each
                </p>
                <p className="truncate text-caption text-text-secondary mt-1">
                  Maximum {maxFiles} files • {selectedFiles.length} selected
                </p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  disabled={selectedFiles.length >= maxFiles}
                  className="max-w-full gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Files
                </Button>
                {selectedFiles.length > 0 && (
                  <Button
                    onClick={() => void resetForm()}
                    variant="outline"
                    className="max-w-full gap-2 text-destructive hover:text-destructive"
                  >
                    <X className="w-4 h-4" />
                    Clear All
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv,.xlsx,.xls"
        onChange={handleFileInputChange}
        className="hidden"
        multiple
      />

      {/* Selected Files List */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className="p-4 overflow-hidden">
            <div className="space-y-3">
              <div className="flex flex-wrap items-center justify-between">
                <h4 className="font-medium">
                  Selected Files ({selectedFiles.length})
                </h4>
                <Badge
                  variant={allFilesHaveCurrency ? 'default' : 'secondary'}
                  className="max-w-[150px] truncate"
                >
                  {selectedFiles.filter((f) => f.currency).length} of{' '}
                  {selectedFiles.length} have currency
                </Badge>
              </div>

              {selectedFiles.map((fileWithCurrency) => (
                <div
                  key={fileWithCurrency.id}
                  className="flex flex-wrap items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex flex-wrap items-center gap-3">
                    <FileSpreadsheet className="w-5 h-5 text-info" />
                    <div>
                      <div className="font-medium">
                        {fileWithCurrency?.file?.name}
                      </div>
                      <div className="truncatebody-small">
                        {(fileWithCurrency?.file?.size / 1024 / 1024).toFixed(
                          2,
                        )}{' '}
                        MB •{' '}
                        {fileWithCurrency?.file?.type.includes('csv')
                          ? 'CSV'
                          : 'Excel'}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center gap-2">
                    <Select
                      value={fileWithCurrency.currency}
                      onValueChange={(currency) =>
                        updateFileCurrency(fileWithCurrency.id, currency)
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Currency" />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map((currency) => (
                          <SelectItem
                            key={currency.value}
                            value={currency.value}
                          >
                            {currency.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Button
                      onClick={() => removeFile(fileWithCurrency.id)}
                      variant="outline"
                      size="sm"
                      className="max-w-full text-destructive hover:text-destructive"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {/* Upload All Button */}
              <div className="pt-3 border-t">
                <Button
                  onClick={() => void handleUploadAll()}
                  disabled={!allFilesHaveCurrency || isUploading}
                  size="lg"
                  className="max-w-full w-full gap-2"
                >
                  {isUploading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Uploading {selectedFiles.length} files...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Upload All {selectedFiles.length} Files
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MultiFileUploadWithCurrency;
