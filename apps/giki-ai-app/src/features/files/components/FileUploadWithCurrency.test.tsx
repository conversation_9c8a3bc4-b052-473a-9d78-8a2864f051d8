/**
 * FileUploadWithCurrency Component Tests
 * Critical file upload component tests for financial data onboarding workflow
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFile, mockExcelFile } from '../../../test-utils';
import { FileUploadWithCurrency } from './FileUploadWithCurrency';

// Mock useToast
const mockToast = vi.fn();
vi.mock('@/shared/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}));

describe('FileUploadWithCurrency - Financial Data Upload Component', () => {
  const mockOnFileUpload = vi.fn();

  const defaultProps = {
    onFileUpload: mockOnFileUpload,
    isUploading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockOnFileUpload.mockClear();
    mockToast.mockClear();
  });

  it('renders file upload interface with proper messaging', () => {
    render(<FileUploadWithCurrency {...defaultProps} />);

    expect(
      screen.getByText('Drop your file here or click to browse'),
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'Supports CSV and Excel files (.csv, .xlsx, .xls) up to 10MB',
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /choose file/i }),
    ).toBeInTheDocument();
  });

  it('handles file selection via click', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    const chooseFileButton = screen.getByRole('button', {
      name: /choose file/i,
    });

    await user.click(chooseFileButton);

    // Simulate file selection
    await user.upload(fileInput, mockFile);

    // Should show selected file
    expect(screen.getByText('test.csv')).toBeInTheDocument();
    expect(screen.getByText(/CSV File/)).toBeInTheDocument();
  });

  it('validates supported file types', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

    await user.upload(fileInput, invalidFile);

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Invalid File Type',
      description: 'Please select a CSV or Excel file (.csv, .xlsx, .xls)',
      variant: 'destructive',
    });
  });

  it('validates file size limits', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    // Create large file (11MB)
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.csv', {
      type: 'text/csv',
    });

    await user.upload(fileInput, largeFile);

    expect(mockToast).toHaveBeenCalledWith({
      title: 'File Too Large',
      description: 'Please select a file smaller than 10MB',
      variant: 'destructive',
    });
  });

  it('handles drag and drop file selection', async () => {
    render(<FileUploadWithCurrency {...defaultProps} />);

    const dropZone = screen
      .getByText('Drop your file here or click to browse')
      .closest('div');

    // Create drag event
    const dragEvent = new Event('drop', { bubbles: true });
    Object.defineProperty(dragEvent, 'dataTransfer', {
      value: {
        files: [mockFile],
      },
    });

    fireEvent(dropZone, dragEvent);

    await waitFor(() => {
      expect(screen.getByText('test.csv')).toBeInTheDocument();
    });
  });

  it('shows drag active state during drag over', () => {
    render(<FileUploadWithCurrency {...defaultProps} />);

    const dropZone = screen
      .getByText('Drop your file here or click to browse')
      .closest('div');

    fireEvent.dragEnter(dropZone);

    // Check for active drag styling (implementation specific)
    expect(dropZone).toHaveClass('border-[hsl(var(--giki-primary))]');
  });

  it('shows currency selection after file is selected', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // Currency selection should appear
    expect(screen.getByText('Select Currency')).toBeInTheDocument();
    expect(
      screen.getByText('Choose the currency used in this file'),
    ).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('lists all supported currencies', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // Open currency selector
    const currencySelect = screen.getByRole('combobox');
    await user.click(currencySelect);

    // Check for major currencies
    expect(screen.getByText('USD ($)')).toBeInTheDocument();
    expect(screen.getByText('EUR (€)')).toBeInTheDocument();
    expect(screen.getByText('GBP (£)')).toBeInTheDocument();
    expect(screen.getByText('INR (₹)')).toBeInTheDocument();
    expect(screen.getByText('JPY (¥)')).toBeInTheDocument();
  });

  it('enables upload button only when file and currency are selected', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // Upload button should be disabled initially
    const uploadButton = screen.getByRole('button', { name: /upload file/i });
    expect(uploadButton).toBeDisabled();

    // Select currency
    const currencySelect = screen.getByRole('combobox');
    await user.click(currencySelect);
    await user.click(screen.getByText('USD ($)'));

    // Upload button should now be enabled
    expect(uploadButton).toBeEnabled();
  });

  it('handles successful file upload', async () => {
    const user = userEvent.setup();
    mockOnFileUpload.mockResolvedValue(undefined);

    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // Select currency and upload
    const currencySelect = screen.getByRole('combobox');
    await user.click(currencySelect);
    await user.click(screen.getByText('USD ($)'));

    const uploadButton = screen.getByRole('button', { name: /upload file/i });
    await user.click(uploadButton);

    expect(mockOnFileUpload).toHaveBeenCalledWith(mockFile, 'USD');

    // Form should reset after successful upload
    await waitFor(() => {
      expect(
        screen.getByText('Drop your file here or click to browse'),
      ).toBeInTheDocument();
    });
  });

  it('shows loading state during upload', () => {
    render(<FileUploadWithCurrency {...defaultProps} isUploading={true} />);

    // If there's a file selected, it should show uploading state
    // For this test, we'll check the loading state when isUploading is true
    expect(screen.queryByText('Uploading...')).toBeInTheDocument();
  });

  it('handles upload errors gracefully', async () => {
    const user = userEvent.setup();
    mockOnFileUpload.mockRejectedValue(new Error('Upload failed'));

    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    const currencySelect = screen.getByRole('combobox');
    await user.click(currencySelect);
    await user.click(screen.getByText('USD ($)'));

    const uploadButton = screen.getByRole('button', { name: /upload file/i });
    await user.click(uploadButton);

    expect(mockOnFileUpload).toHaveBeenCalledWith(mockFile, 'USD');
    // Error handling is expected to be done in parent component
  });

  it('allows file removal and form reset', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // File should be selected
    expect(screen.getByText('test.csv')).toBeInTheDocument();

    // Click remove file button
    const removeButton = screen.getByRole('button', { name: /remove file/i });
    await user.click(removeButton);

    // Should return to initial state
    expect(
      screen.getByText('Drop your file here or click to browse'),
    ).toBeInTheDocument();
    expect(screen.queryByText('test.csv')).not.toBeInTheDocument();
  });

  it('displays file information correctly for CSV files', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    expect(screen.getByText('test.csv')).toBeInTheDocument();
    expect(screen.getByText(/CSV File/)).toBeInTheDocument();
    expect(screen.getByText(/MB/)).toBeInTheDocument(); // File size display
  });

  it('displays file information correctly for Excel files', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockExcelFile);

    expect(screen.getByText('test.xlsx')).toBeInTheDocument();
    expect(screen.getByText(/Excel File/)).toBeInTheDocument();
  });

  it('validates file extensions when MIME type is not reliable', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    // File with empty MIME type but valid extension
    const fileWithValidExtension = new File(['data'], 'data.csv', { type: '' });

    await user.upload(fileInput, fileWithValidExtension);

    // Should accept file based on extension
    expect(screen.getByText('data.csv')).toBeInTheDocument();
  });

  it('rejects files with invalid extensions even with valid MIME types', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    const invalidFile = new File(['data'], 'data.pdf', {
      type: 'application/pdf',
    });

    await user.upload(fileInput, invalidFile);

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Invalid File Type',
      description: 'Please select a CSV or Excel file (.csv, .xlsx, .xls)',
      variant: 'destructive',
    });
  });

  it('handles multiple currency selection changes', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    const currencySelect = screen.getByRole('combobox');

    // Select USD
    await user.click(currencySelect);
    await user.click(screen.getByText('USD ($)'));

    // Change to EUR
    await user.click(currencySelect);
    await user.click(screen.getByText('EUR (€)'));

    // Upload with EUR
    const uploadButton = screen.getByRole('button', { name: /upload file/i });
    await user.click(uploadButton);

    expect(mockOnFileUpload).toHaveBeenCalledWith(mockFile, 'EUR');
  });

  it('maintains accessibility standards', () => {
    render(<FileUploadWithCurrency {...defaultProps} />);

    // Check file input has proper attributes
    const fileInput = screen.getByTestId('file-input');
    expect(fileInput).toHaveAttribute('type', 'file');
    expect(fileInput).toHaveAttribute('accept', '.csv,.xlsx,.xls');

    // Check buttons are properly labeled
    const chooseFileButton = screen.getByRole('button', {
      name: /choose file/i,
    });
    expect(chooseFileButton).toBeInTheDocument();
  });

  it('prevents drag default behavior', () => {
    render(<FileUploadWithCurrency {...defaultProps} />);

    const dropZone = screen
      .getByText('Drop your file here or click to browse')
      .closest('div');

    const dragEvent = new Event('dragover', { bubbles: true });
    const preventDefaultSpy = vi.spyOn(dragEvent, 'preventDefault');

    fireEvent(dropZone, dragEvent);

    expect(preventDefaultSpy).toHaveBeenCalled();
  });

  it('shows proper visual feedback for selected files', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // Should show green styling for selected file
    const dropZone = screen.getByText('test.csv').closest('[class*="border"]');
    expect(dropZone).toHaveClass('border-green-500');
  });

  it('disables upload during uploading state', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} isUploading={true} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    const currencySelect = screen.getByRole('combobox');
    await user.click(currencySelect);
    await user.click(screen.getByText('USD ($)'));

    const uploadButton = screen.getByRole('button', { name: /uploading/i });
    expect(uploadButton).toBeDisabled();
  });

  it('resets file input value on form reset', async () => {
    const user = userEvent.setup();
    render(<FileUploadWithCurrency {...defaultProps} />);

    const fileInput = screen.getByTestId('file-input');
    await user.upload(fileInput, mockFile);

    // File input should have files
    expect(fileInput.files).toHaveLength(1);

    const removeButton = screen.getByRole('button', { name: /remove file/i });
    await user.click(removeButton);

    // File input should be cleared
    expect(fileInput.value).toBe('');
  });
});
