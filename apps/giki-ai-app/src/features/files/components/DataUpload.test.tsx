/**
 * DataUpload Component Tests
 * Critical onboarding component tests for financial data ingestion
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFile, mockExcelFile } from '../../../test-utils';
import DataUpload from './DataUpload';

// Mock file reader for CSV preview
Object.defineProperty(global, 'FileReader', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    readAsText: vi.fn(),
    readAsArrayBuffer: vi.fn(),
    onload: null,
    onerror: null,
    result: 'Date,Description,Amount\n2024-01-15,STARBUCKS,-4.95',
  })),
});

describe('DataUpload - Financial Data Ingestion Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders upload interface with proper instructions', () => {
    render(<DataUpload />);

    expect(screen.getByText(/upload your financial data/i)).toBeInTheDocument();
    expect(screen.getByText(/drag.*drop.*csv.*excel/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /browse files/i }),
    ).toBeInTheDocument();
  });

  it('accepts CSV files via drag and drop', async () => {
    render(<DataUpload />);

    const dropzone = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div');

    // Simulate drag and drop
    const file = mockFile;
    Object.defineProperty(file, 'type', { value: 'text/csv' });

    fireEvent.dragEnter(dropzone);
    fireEvent.dragOver(dropzone);
    fireEvent.drop(dropzone, {
      dataTransfer: {
        files: [file],
        types: ['Files'],
      },
    });

    await waitFor(() => {
      expect(screen.getByText(/test\.csv/i)).toBeInTheDocument();
    });
  });

  it('accepts Excel files via file input', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockExcelFile);

    await waitFor(() => {
      expect(screen.getByText(/test\.xlsx/i)).toBeInTheDocument();
    });
  });

  it('rejects invalid file types', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, invalidFile);

    await waitFor(() => {
      expect(screen.getByText(/only csv and excel files/i)).toBeInTheDocument();
    });
  });

  it('validates file size limits', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    // Create a large file (>10MB)
    const largeContent = 'x'.repeat(11 * 1024 * 1024); // 11MB
    const largeFile = new File([largeContent], 'large.csv', {
      type: 'text/csv',
    });

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, largeFile);

    await waitFor(() => {
      expect(screen.getByText(/file size exceeds/i)).toBeInTheDocument();
    });
  });

  it('shows file preview after upload', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockFile);

    await waitFor(() => {
      expect(screen.getByText(/file preview/i)).toBeInTheDocument();
      expect(screen.getByText(/date/i)).toBeInTheDocument();
      expect(screen.getByText(/description/i)).toBeInTheDocument();
      expect(screen.getByText(/amount/i)).toBeInTheDocument();
    });
  });

  it('allows file removal after upload', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockFile);

    await waitFor(() => {
      expect(screen.getByText(/test\.csv/i)).toBeInTheDocument();
    });

    const removeButton = screen.getByRole('button', { name: /remove/i });
    await user.click(removeButton);

    expect(screen.queryByText(/test\.csv/i)).not.toBeInTheDocument();
  });

  it('shows column mapping after file upload', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockFile);

    // Wait for file processing UI
    await waitFor(() => {
      expect(screen.getByText(/test\.csv/i)).toBeInTheDocument();
    });
  });

  it('shows file size and type information', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockFile);

    await waitFor(() => {
      // Should display file name
      expect(screen.getByText(/test\.csv/i)).toBeInTheDocument();
      // Should show file type indicator
      expect(screen.getByText(/csv/i)).toBeInTheDocument();
    });
  });

  it('supports multiple file formats', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    // Should accept CSV and Excel files
    expect(fileInput).toHaveAttribute('accept', '.csv,.xlsx,.xls');

    // Test Excel file upload
    await user.upload(fileInput, mockExcelFile);

    await waitFor(() => {
      expect(screen.getByText(/test\.xlsx/i)).toBeInTheDocument();
    });
  });

  it('maintains financial data security during upload', async () => {
    // const user = userEvent.setup();
    render(<DataUpload />);

    // Check for security indicators
    expect(screen.getByText(/secure upload/i)).toBeInTheDocument();
    expect(screen.getByText(/encrypted/i)).toBeInTheDocument();

    // Verify secure file handling
    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    expect(fileInput).toHaveAttribute('accept', '.csv,.xlsx,.xls');
  });

  it('provides clear validation feedback for financial data', async () => {
    const user = userEvent.setup();
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    await user.upload(fileInput, mockFile);

    await waitFor(() => {
      expect(screen.getByText(/3 columns detected/i)).toBeInTheDocument();
      expect(
        screen.getByText(/transaction data appears valid/i),
      ).toBeInTheDocument();
    });
  });

  it('meets accessibility standards for file upload', () => {
    render(<DataUpload />);

    const fileInput = screen
      .getByRole('button', { name: /browse files/i })
      .closest('div')
      .querySelector('input[type="file"]');

    // Check proper labeling
    expect(fileInput).toHaveAttribute('aria-label');
    expect(fileInput).toHaveAttribute('accept');

    // Check keyboard navigation
    expect(
      screen.getByRole('button', { name: /browse files/i }),
    ).toHaveAttribute('tabIndex');

    // Check screen reader support
    expect(screen.getByText(/drag.*drop.*files/i)).toBeInTheDocument();
  });
});
