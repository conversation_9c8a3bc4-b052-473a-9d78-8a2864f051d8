/**
 * useFileUpload Hook Tests
 * Critical file upload hook tests for production reliability
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useFileUpload } from './useFileUpload';
import type { UploadResponse, UploadError } from '../types/upload';

// Mock the upload service
const mockUploadFile = vi.fn();
vi.mock('../services/uploadService', () => ({
  uploadFile: mockUploadFile,
}));

describe('useFileUpload - File Upload Management Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('returns correct initial state', () => {
      const { result } = renderHook(() => useFileUpload());

      expect(result?.current?.isUploading).toBe(false);
      expect(result?.current?.progress).toBeNull();
      expect(result?.current?.error).toBeNull();
      expect(typeof result?.current?.uploadFile).toBe('function');
      expect(typeof result?.current?.reset).toBe('function');
    });

    it('maintains stable function references', () => {
      const { result, rerender } = renderHook(() => useFileUpload());

      const initialUploadFile = result?.current?.uploadFile;
      const initialReset = result?.current?.reset;

      rerender();

      expect(result?.current?.uploadFile).toBe(initialUploadFile);
      expect(result?.current?.reset).toBe(initialReset);
    });
  });

  describe('Successful File Upload', () => {
    it('handles successful upload without currency', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockApiResponse = {
        upload_id: 'upload_123',
        filename: 'test.xlsx',
        size: 1024,
        status: 'uploaded',
      };

      mockUploadFile.mockImplementation((file, onProgress, _currency) => {
        if (onProgress) {
          onProgress(50);
          onProgress(100);
        }
        return Promise.resolve(mockApiResponse);
      });

      const { result } = renderHook(() => useFileUpload());

      expect(result?.current?.isUploading).toBe(false);

      let uploadPromise: Promise<UploadResponse>;
      act(() => {
        uploadPromise = result?.current?.uploadFile(mockFile);
      });

      expect(result?.current?.isUploading).toBe(true);
      expect(result?.current?.error).toBeNull();
      expect(result?.current?.progress).toEqual({
        loaded: 0,
        total: mockFile.size,
        percentage: 0,
      });

      const uploadResult = await uploadPromise;

      await waitFor(() => {
        expect(result?.current?.isUploading).toBe(false);
      });

      expect(result?.current?.progress).toEqual({
        loaded: mockFile.size,
        total: mockFile.size,
        percentage: 100,
      });

      expect(uploadResult).toEqual({
        success: true,
        uploadId: 'upload_123',
        filename: 'test.xlsx',
        size: 1024,
        mimeType: mockFile.type,
        data: mockApiResponse,
      });

      expect(mockUploadFile).toHaveBeenCalledWith(
        mockFile,
        expect.any(Function),
        undefined,
      );
    });

    it('handles successful upload with currency', async () => {
      const mockFile = new File(['test content'], 'financial_data.csv', {
        type: 'text/csv',
      });

      const mockApiResponse = {
        upload_id: 'upload_456',
        filename: 'financial_data.csv',
        size: 2048,
        status: 'uploaded',
      };

      mockUploadFile.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useFileUpload());

      let uploadPromise: Promise<UploadResponse>;
      act(() => {
        uploadPromise = result?.current?.uploadFile(mockFile, 'EUR');
      });

      const uploadResult = await uploadPromise;

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.uploadId).toBe('upload_456');

      expect(mockUploadFile).toHaveBeenCalledWith(
        mockFile,
        expect.any(Function),
        'EUR',
      );
    });

    it('tracks progress updates correctly', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const mockApiResponse = {
        upload_id: 'upload_progress',
        filename: 'test.xlsx',
        size: 1000,
        status: 'uploaded',
      };

      mockUploadFile.mockImplementation((file, onProgress) => {
        setTimeout(() => onProgress?.(25), 10);
        setTimeout(() => onProgress?.(50), 20);
        setTimeout(() => onProgress?.(75), 30);
        setTimeout(() => onProgress?.(100), 40);
        return Promise.resolve(mockApiResponse);
      });

      const { result } = renderHook(() => useFileUpload());

      act(() => {
        void result?.current?.uploadFile(mockFile);
      });

      // Initial progress
      expect(result?.current?.progress).toEqual({
        loaded: 0,
        total: mockFile.size,
        percentage: 0,
      });

      // Wait for progress updates
      await waitFor(() => {
        expect(result?.current?.progress?.percentage).toBe(100);
      });

      expect(result?.current?.progress).toEqual({
        loaded: mockFile.size,
        total: mockFile.size,
        percentage: 100,
      });
    });

    it('handles alternative API response formats', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      // Test with uploadId instead of upload_id
      const alternativeApiResponse = {
        uploadId: 'alt_upload_123',
        filename: 'test.xlsx',
        status: 'uploaded',
      };

      mockUploadFile.mockResolvedValue(alternativeApiResponse);

      const { result } = renderHook(() => useFileUpload());

      const uploadResult = await act(async () => {
        return await result?.current?.uploadFile(mockFile);
      });

      expect(uploadResult.uploadId).toBe('alt_upload_123');
      expect(uploadResult.size).toBe(mockFile.size); // Should fallback to file size
    });
  });

  describe('Upload Errors', () => {
    it('handles API error responses', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const apiError = {
        error: true,
        message: 'File size exceeds maximum limit',
        code: 'FILE_TOO_LARGE',
      };

      mockUploadFile.mockResolvedValue(apiError);

      const { result } = renderHook(() => useFileUpload());

      let uploadError: UploadError | undefined;
      try {
        await act(async () => {
          await result?.current?.uploadFile(mockFile);
        });
      } catch (error) {
        uploadError = error as UploadError;
      }

      expect(uploadError).toBeDefined();
      expect(uploadError?.code).toBe('UPLOAD_FAILED');
      expect(uploadError?.message).toBe('File size exceeds maximum limit');

      expect(result?.current?.error).toEqual({
        code: 'UPLOAD_FAILED',
        message: 'File size exceeds maximum limit',
        details: expect.any(Error),
      });

      expect(result?.current?.isUploading).toBe(false);
    });

    it('handles network errors', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const networkError = new Error('Network connection failed');
      mockUploadFile.mockRejectedValue(networkError);

      const { result } = renderHook(() => useFileUpload());

      let uploadError: UploadError | undefined;
      try {
        await act(async () => {
          await result?.current?.uploadFile(mockFile);
        });
      } catch (error) {
        uploadError = error as UploadError;
      }

      expect(uploadError).toBeDefined();
      expect(uploadError?.message).toBe('Network connection failed');

      expect(result?.current?.error).toEqual({
        code: 'UPLOAD_FAILED',
        message: 'Network connection failed',
        details: networkError,
      });
    });

    it('handles timeout errors', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const timeoutError = new Error('Request timeout');
      mockUploadFile.mockRejectedValue(timeoutError);

      const { result } = renderHook(() => useFileUpload());

      await expect(
        act(async () => {
          await result?.current?.uploadFile(mockFile);
        }),
      ).rejects.toThrow('Request timeout');

      expect(result?.current?.error?.message).toBe('Request timeout');
      expect(result?.current?.isUploading).toBe(false);
    });

    it('handles non-Error exceptions', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      mockUploadFile.mockRejectedValue('String error');

      const { result } = renderHook(() => useFileUpload());

      await expect(
        act(async () => {
          await result?.current?.uploadFile(mockFile);
        }),
      ).rejects.toEqual({
        code: 'UPLOAD_FAILED',
        message: 'Upload failed',
        details: 'String error',
      });

      expect(result?.current?.error?.message).toBe('Upload failed');
    });

    it('clears previous errors on new upload attempt', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      // First upload fails
      mockUploadFile.mockRejectedValueOnce(new Error('First error'));

      const { result } = renderHook(() => useFileUpload());

      try {
        await act(async () => {
          await result?.current?.uploadFile(mockFile);
        });
      } catch (error) {
        void error;
      }

      expect(result?.current?.error).toBeDefined();

      // Second upload succeeds
      const successResponse = {
        upload_id: 'success_123',
        filename: 'test.xlsx',
        status: 'uploaded',
      };

      mockUploadFile.mockResolvedValue(successResponse);

      await act(async () => {
        await result?.current?.uploadFile(mockFile);
      });

      expect(result?.current?.error).toBeNull();
    });
  });

  describe('Reset Functionality', () => {
    it('resets all state to initial values', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      // Set up upload that fails
      mockUploadFile.mockRejectedValue(new Error('Upload failed'));

      const { result } = renderHook(() => useFileUpload());

      try {
        await act(async () => {
          await result?.current?.uploadFile(mockFile);
        });
      } catch (error) {
        void error;
      }

      // State should have error and progress
      expect(result?.current?.error).toBeDefined();
      expect(result?.current?.progress).toBeDefined();
      expect(result?.current?.isUploading).toBe(false);

      // Reset state
      act(() => {
        result?.current?.reset();
      });

      expect(result?.current?.isUploading).toBe(false);
      expect(result?.current?.progress).toBeNull();
      expect(result?.current?.error).toBeNull();
    });

    it('resets state during active upload', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      // Mock upload that takes time
      mockUploadFile.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ upload_id: 'test', filename: 'test.xlsx' }),
              100,
            ),
          ),
      );

      const { result } = renderHook(() => useFileUpload());

      act(() => {
        void result?.current?.uploadFile(mockFile);
      });

      expect(result?.current?.isUploading).toBe(true);

      // Reset during upload
      act(() => {
        result?.current?.reset();
      });

      expect(result?.current?.isUploading).toBe(false);
      expect(result?.current?.progress).toBeNull();
      expect(result?.current?.error).toBeNull();
    });
  });

  describe('Edge Cases and File Handling', () => {
    it('handles empty files', async () => {
      const emptyFile = new File([], 'empty.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse = {
        upload_id: 'empty_123',
        filename: 'empty.xlsx',
        size: 0,
        status: 'uploaded',
      };

      mockUploadFile.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useFileUpload());

      const uploadResult = await act(async () => {
        return await result?.current?.uploadFile(emptyFile);
      });

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.size).toBe(0);
    });

    it('handles very large files', async () => {
      const largeFile = new File(
        [new ArrayBuffer(100 * 1024 * 1024)],
        'large.xlsx',
        {
          type: 'application/xlsx',
        },
      );

      const mockResponse = {
        upload_id: 'large_123',
        filename: 'large.xlsx',
        size: largeFile.size,
        status: 'uploaded',
      };

      mockUploadFile.mockImplementation((file, onProgress) => {
        // Simulate gradual progress for large file
        const intervals = [10, 25, 50, 75, 90, 100];
        intervals.forEach((progress, index) => {
          setTimeout(() => onProgress?.(progress), index * 10);
        });
        return Promise.resolve(mockResponse);
      });

      const { result } = renderHook(() => useFileUpload());

      const uploadResult = await act(async () => {
        return await result?.current?.uploadFile(largeFile);
      });

      expect(uploadResult.success).toBe(true);
      expect(uploadResult.size).toBe(largeFile.size);
    });

    it('handles files with special characters in name', async () => {
      const specialFile = new File(['test'], 'файл тест (1) & more.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse = {
        upload_id: 'special_123',
        filename: 'файл тест (1) & more.xlsx',
        size: specialFile.size,
        status: 'uploaded',
      };

      mockUploadFile.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useFileUpload());

      const uploadResult = await act(async () => {
        return await result?.current?.uploadFile(specialFile);
      });

      expect(uploadResult.filename).toBe('файл тест (1) & more.xlsx');
    });

    it('handles different file types', async () => {
      const csvFile = new File(['col1,col2\nval1,val2'], 'data.csv', {
        type: 'text/csv',
      });

      const xlsxFile = new File(['excel data'], 'data.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockCsvResponse = {
        upload_id: 'csv_123',
        filename: 'data.csv',
        status: 'uploaded',
      };

      const mockXlsxResponse = {
        upload_id: 'xlsx_123',
        filename: 'data.xlsx',
        status: 'uploaded',
      };

      mockUploadFile
        .mockResolvedValueOnce(mockCsvResponse)
        .mockResolvedValueOnce(mockXlsxResponse);

      const { result } = renderHook(() => useFileUpload());

      const csvResult = await act(async () => {
        return await result?.current?.uploadFile(csvFile);
      });

      const xlsxResult = await act(async () => {
        return await result?.current?.uploadFile(xlsxFile);
      });

      expect(csvResult.mimeType).toBe('text/csv');
      expect(xlsxResult.mimeType).toBe(
        'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      );
    });
  });

  describe('Progress Tracking', () => {
    it('handles progress updates correctly', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const progressUpdates: number[] = [];

      mockUploadFile.mockImplementation((file, onProgress) => {
        const progressValues = [0, 25, 50, 75, 100];
        progressValues.forEach((progress, index) => {
          setTimeout(() => {
            onProgress?.(progress);
            progressUpdates.push(progress);
          }, index * 10);
        });
        return Promise.resolve({
          upload_id: 'progress_test',
          filename: 'test.xlsx',
        });
      });

      const { result } = renderHook(() => useFileUpload());

      await act(async () => {
        await result?.current?.uploadFile(mockFile);
      });

      await waitFor(() => {
        expect(progressUpdates).toContain(100);
      });

      expect(result?.current?.progress?.percentage).toBe(100);
      expect(result?.current?.progress?.loaded).toBe(mockFile.size);
      expect(result?.current?.progress?.total).toBe(mockFile.size);
    });

    it('calculates loaded bytes correctly based on percentage', async () => {
      const mockFile = new File([new ArrayBuffer(1000)], 'test.xlsx', {
        type: 'application/xlsx',
      });

      mockUploadFile.mockImplementation((file, onProgress) => {
        onProgress?.(30); // 30% of 1000 bytes = 300 bytes
        return Promise.resolve({
          upload_id: 'bytes_test',
          filename: 'test.xlsx',
        });
      });

      const { result } = renderHook(() => useFileUpload());

      await act(async () => {
        await result?.current?.uploadFile(mockFile);
      });

      expect(result?.current?.progress?.loaded).toBe(300);
      expect(result?.current?.progress?.total).toBe(1000);
      expect(result?.current?.progress?.percentage).toBe(30);
    });
  });

  describe('Concurrent Uploads', () => {
    it('handles multiple upload calls gracefully', async () => {
      const file1 = new File(['test1'], 'test1.xlsx', {
        type: 'application/xlsx',
      });
      const file2 = new File(['test2'], 'test2.xlsx', {
        type: 'application/xlsx',
      });

      mockUploadFile
        .mockResolvedValueOnce({ upload_id: 'upload1', filename: 'test1.xlsx' })
        .mockResolvedValueOnce({
          upload_id: 'upload2',
          filename: 'test2.xlsx',
        });

      const { result } = renderHook(() => useFileUpload());

      const [result1, result2] = await Promise.all([
        act(async () => await result?.current?.uploadFile(file1)),
        act(async () => await result?.current?.uploadFile(file2)),
      ]);

      expect(result1.uploadId).toBe('upload1');
      expect(result2.uploadId).toBe('upload2');
    });
  });

  describe('Memory and Performance', () => {
    it('does not leak memory during multiple uploads', async () => {
      const files = Array.from(
        { length: 10 },
        (_, i) =>
          new File([`test${i}`], `test${i}.xlsx`, { type: 'application/xlsx' }),
      );

      files.forEach((_, i) => {
        mockUploadFile.mockResolvedValueOnce({
          upload_id: `upload_${i}`,
          filename: `test${i}.xlsx`,
        });
      });

      const { result } = renderHook(() => useFileUpload());

      // Upload files sequentially
      for (const file of files) {
        await act(async () => {
          await result?.current?.uploadFile(file);
        });

        // Reset after each upload to simulate real usage
        act(() => {
          result?.current?.reset();
        });
      }

      expect(mockUploadFile).toHaveBeenCalledTimes(10);
    });

    it('handles rapid reset calls', async () => {
      const { result } = renderHook(() => useFileUpload());

      // Call reset multiple times rapidly
      act(() => {
        result?.current?.reset();
        result?.current?.reset();
        result?.current?.reset();
      });

      expect(result?.current?.isUploading).toBe(false);
      expect(result?.current?.progress).toBeNull();
      expect(result?.current?.error).toBeNull();
    });
  });
});
