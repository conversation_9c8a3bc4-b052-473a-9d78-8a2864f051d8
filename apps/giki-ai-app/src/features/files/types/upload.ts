/**
 * Upload Feature Types
 *
 * Type definitions for file upload functionality.
 */

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResponse {
  success: boolean;
  uploadId: string;
  filename: string;
  size: number;
  mimeType: string;
  data?: {
    upload_id: string;
    filename: string;
    content_type?: string;
    size: number;
    status: string;
    message?: string;
    headers?: string[];
  }; // Include the full API response with proper typing
}

export interface UploadError {
  code: string;
  message: string;
  details?: unknown;
}

export interface FileUploadProps {
  onUploadSuccess?: (uploadId: string) => void;
  onUploadError?: (error: UploadError) => void;
  onUploadProgress?: (progress: UploadProgress) => void;
  acceptedFileTypes?: string[];
  maxFileSize?: number;
  multiple?: boolean;
}

// Backend upload record structure from API
export interface UploadRecord {
  upload_id: string;
  filename: string;
  content_type: string;
  size: number;
  status: string;
  headers: string[] | null;
  message: string;
  created_at?: string;
}

// Frontend RecentUpload interface for the History tab
export interface RecentUpload {
  id: string;
  filename: string;
  uploadDate: Date;
  status: 'completed' | 'processing' | 'error' | 'pending';
  recordCount?: number;
  errorMessage?: string;
}
