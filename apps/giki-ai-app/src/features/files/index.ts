/**
 * Files Feature Barrel Exports
 *
 * This file provides clean imports for all file processing-related
 * components, hooks, and services.
 */

// Components
export { default as DataUpload } from './components/DataUpload';
export { default as ColumnMappingModal } from './components/ColumnMappingModal';
export { default as FileUploadWithCurrency } from './components/FileUploadWithCurrency';
export { default as MultiFileUploadWithCurrency } from './components/MultiFileUploadWithCurrency';

// Onboarding Components
export { default as AccuracySimulationChart } from './components/onboarding/AccuracySimulationChart';
export { default as ProgressiveUploadWizard } from './components/onboarding/ProgressiveUploadWizard';
export { default as WelcomeTutorialModal } from './components/onboarding/WelcomeTutorialModal';
export { RealTimeProcessingStatus } from './components/onboarding/RealTimeProcessingStatus';
export { default as InteractiveCategoryPreview } from './components/onboarding/InteractiveCategoryPreview';
export { default as OnboardingCompletionFlow } from './components/onboarding/OnboardingCompletionFlow';
export { default as FirstTimeUserGuide } from './components/onboarding/FirstTimeUserGuide';

// Pages
export { default as WorkPage } from './pages/UploadPage';
export { default as OnboardingPage } from './pages/OnboardingPage';
export { default as TemporalValidationTestPage } from './pages/TemporalValidationTestPage';

// Services
export * from '@/features/files/services/fileService';
export { validateFile, formatFileSize } from './services/uploadService';

// Types
export type { UploadProgress } from './types/upload';
// Note: UploadResponse is exported from fileService to avoid conflicts

// Hooks
export * from './hooks/useFileUpload';
