/**
 * UploadService Tests
 * Critical file upload service tests for production reliability
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  uploadFile,
  getFileColumns,
  getSchemaInterpretation,
} from './uploadService';
import type { UploadResponse } from '@/shared/types/categorization';
import type { SchemaInterpretationResponse } from './uploadService';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
  },
}));

// Mock error handling
vi.mock('@/shared/utils/errorHandling', () => ({
  handleApiError: vi.fn((error) => ({
    type: 'api_error',
    message: error.message || 'API Error',
    statusCode: 500,
  })),
}));

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

describe('UploadService - File Upload and Processing Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('uploadFile Function', () => {
    it('successfully uploads a file without currency', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_123',
        filename: 'test.xlsx',
        status: 'uploaded',
        message: 'File uploaded successfully',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(mockFile);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/uploads/upload',
        expect.any(FormData),
        expect.objectContaining({
          onUploadProgress: expect.any(Function),
        }),
      );

      expect(result).toEqual(mockResponse);
    });

    it('successfully uploads a file with currency', async () => {
      const mockFile = new File(['test content'], 'test.csv', {
        type: 'text/csv',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_456',
        filename: 'test.csv',
        status: 'uploaded',
        message: 'File uploaded successfully',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(mockFile, undefined, 'USD');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/uploads/upload',
        expect.any(FormData),
        expect.objectContaining({
          onUploadProgress: expect.any(Function),
        }),
      );

      // Check that FormData includes currency
      const formDataCall = (apiClient.post as unknown).mock.calls[0][1];
      expect(formDataCall).toBeInstanceOf(FormData);

      expect(result).toEqual(mockResponse);
    });

    it('tracks upload progress correctly', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_789',
        filename: 'test.xlsx',
        status: 'uploaded',
        message: 'File uploaded successfully',
      };

      const mockOnProgress = vi.fn();

      (apiClient.post as unknown).mockImplementation(
        async (url, data, config) => {
          // Simulate progress events
          if (config.onUploadProgress) {
            config.onUploadProgress({ loaded: 50, total: 100 });
            config.onUploadProgress({ loaded: 100, total: 100 });
          }
          return { data: mockResponse };
        },
      );

      await uploadFile(mockFile, mockOnProgress);

      expect(mockOnProgress).toHaveBeenCalledWith(50);
      expect(mockOnProgress).toHaveBeenCalledWith(100);
    });

    it('handles upload progress without total size', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_abc',
        filename: 'test.xlsx',
        status: 'uploaded',
        message: 'File uploaded successfully',
      };

      const mockOnProgress = vi.fn();

      (apiClient.post as unknown).mockImplementation(
        async (url, data, config) => {
          if (config.onUploadProgress) {
            config.onUploadProgress({ loaded: 50 }); // No total property
          }
          return { data: mockResponse };
        },
      );

      await uploadFile(mockFile, mockOnProgress);

      // Should not call onProgress when total is undefined
      expect(mockOnProgress).not.toHaveBeenCalled();
    });

    it('handles network errors during upload', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const networkError = new Error('Network connection failed');
      (apiClient.post as unknown).mockRejectedValue(networkError);

      const result = await uploadFile(mockFile);

      expect(handleApiError).toHaveBeenCalledWith(networkError, {
        context: 'uploadFile',
        defaultMessage: 'Failed to upload file',
      });

      expect(result).toEqual({
        type: 'api_error',
        message: 'Network connection failed',
        statusCode: 500,
      });
    });

    it('handles server errors during upload', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const serverError = new Error('Internal server error');
      (apiClient.post as unknown).mockRejectedValue(serverError);

      const result = await uploadFile(mockFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Internal server error',
        statusCode: 500,
      });
    });

    it('handles large files correctly', async () => {
      const largeMockFile = new File(
        [new ArrayBuffer(5 * 1024 * 1024)],
        'large.xlsx',
        {
          type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
        },
      );

      const mockResponse: UploadResponse = {
        upload_id: 'upload_large',
        filename: 'large.xlsx',
        status: 'uploaded',
        message: 'Large file uploaded successfully',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(largeMockFile);

      expect(result).toEqual(mockResponse);
      expect(apiClient.post).toHaveBeenCalledWith(
        '/uploads/upload',
        expect.any(FormData),
        expect.objectContaining({
          onUploadProgress: expect.any(Function),
        }),
      );
    });

    it('handles special characters in filename', async () => {
      const mockFile = new File(['test content'], 'test file (1) & more.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_special',
        filename: 'test file (1) & more.xlsx',
        status: 'uploaded',
        message: 'File uploaded successfully',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(mockFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles empty files', async () => {
      const emptyMockFile = new File([], 'empty.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_empty',
        filename: 'empty.xlsx',
        status: 'uploaded',
        message: 'Empty file uploaded',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(emptyMockFile);

      expect(result).toEqual(mockResponse);
    });
  });

  describe('getFileColumns Function', () => {
    it('successfully retrieves file columns', async () => {
      const mockColumns = ['Date', 'Description', 'Amount', 'Category'];
      const mockResponse = { columns: mockColumns };

      (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

      const result = await getFileColumns('upload_123');

      expect(apiClient.get).toHaveBeenCalledWith('/uploads/upload_123/columns');
      expect(result).toEqual(mockColumns);
    });

    it('handles empty columns response', async () => {
      const mockResponse = { columns: [] };

      (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

      const result = await getFileColumns('upload_456');

      expect(result).toEqual([]);
    });

    it('handles missing columns property', async () => {
      const mockResponse = {}; // No columns property

      (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

      const result = await getFileColumns('upload_789');

      expect(result).toEqual([]);
    });

    it('handles network errors when getting columns', async () => {
      const networkError = new Error('Connection timeout');
      (apiClient.get as unknown).mockRejectedValue(networkError);

      await expect(getFileColumns('upload_error')).rejects.toEqual({
        type: 'api_error',
        message: 'Connection timeout',
        statusCode: 500,
      });

      expect(handleApiError).toHaveBeenCalledWith(networkError, {
        showToast: false,
        context: 'getFileColumns',
        defaultMessage: 'Failed to get file columns',
      });
    });

    it('handles server errors when getting columns', async () => {
      const serverError = new Error('Upload not found');
      (apiClient.get as unknown).mockRejectedValue(serverError);

      await expect(getFileColumns('nonexistent_upload')).rejects.toEqual({
        type: 'api_error',
        message: 'Upload not found',
        statusCode: 500,
      });
    });

    it('handles invalid upload IDs', async () => {
      const invalidError = new Error('Invalid upload ID format');
      (apiClient.get as unknown).mockRejectedValue(invalidError);

      await expect(getFileColumns('')).rejects.toEqual({
        type: 'api_error',
        message: 'Invalid upload ID format',
        statusCode: 500,
      });
    });

    it('handles very long column names', async () => {
      const longColumnNames = [
        'This is a very long column name that might cause issues in some systems',
        'Another extremely long column name with special characters !@#$%^&*()',
        'Date',
        'Amount',
      ];
      const mockResponse = { columns: longColumnNames };

      (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

      const result = await getFileColumns('upload_long');

      expect(result).toEqual(longColumnNames);
    });
  });

  describe('getSchemaInterpretation Function', () => {
    it('successfully retrieves schema interpretation', async () => {
      const mockInterpretation: SchemaInterpretationResponse = {
        upload_id: 'upload_123',
        filename: 'financial_data.xlsx',
        column_mappings: [
          {
            original_name: 'Date',
            mapped_field: 'transaction_date',
            confidence: 0.95,
            reasoning: 'Column contains date values in MM/DD/YYYY format',
          },
          {
            original_name: 'Amount',
            mapped_field: 'transaction_amount',
            confidence: 0.98,
            reasoning: 'Column contains numeric currency values',
          },
        ],
        overall_confidence: 0.96,
        required_fields_mapped: {
          transaction_date: true,
          transaction_amount: true,
          description: false,
        },
        interpretation_summary:
          'High confidence mapping found for financial transaction data',
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: mockInterpretation,
      });

      const result = await getSchemaInterpretation('upload_123');

      expect(apiClient.get).toHaveBeenCalledWith(
        '/uploads/upload_123/schema-interpretation',
      );
      expect(result).toEqual(mockInterpretation);
    });

    it('handles low confidence interpretations', async () => {
      const lowConfidenceInterpretation: SchemaInterpretationResponse = {
        upload_id: 'upload_456',
        filename: 'unclear_data.csv',
        column_mappings: [
          {
            original_name: 'Col1',
            mapped_field: 'unknown',
            confidence: 0.2,
            reasoning: 'Unable to determine data type with confidence',
          },
        ],
        overall_confidence: 0.2,
        required_fields_mapped: {
          transaction_date: false,
          transaction_amount: false,
          description: false,
        },
        interpretation_summary: 'Low confidence - manual review required',
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: lowConfidenceInterpretation,
      });

      const result = await getSchemaInterpretation('upload_456');

      expect(result).toEqual(lowConfidenceInterpretation);
      expect(result.overall_confidence).toBe(0.2);
    });

    it('handles empty column mappings', async () => {
      const emptyInterpretation: SchemaInterpretationResponse = {
        upload_id: 'upload_empty',
        filename: 'empty_file.xlsx',
        column_mappings: [],
        overall_confidence: 0,
        required_fields_mapped: {},
        interpretation_summary: 'No columns found in file',
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: emptyInterpretation,
      });

      const result = await getSchemaInterpretation('upload_empty');

      expect(result).toEqual(emptyInterpretation);
      expect(result.column_mappings).toHaveLength(0);
    });

    it('handles network errors during schema interpretation', async () => {
      const networkError = new Error('Service unavailable');
      (apiClient.get as unknown).mockRejectedValue(networkError);

      await expect(getSchemaInterpretation('upload_error')).rejects.toEqual({
        type: 'api_error',
        message: 'Service unavailable',
        statusCode: 500,
      });

      expect(handleApiError).toHaveBeenCalledWith(networkError, {
        context: 'getSchemaInterpretation',
        defaultMessage: 'Failed to get schema interpretation',
      });
    });

    it('handles malformed interpretation responses', async () => {
      const malformedResponse = {
        upload_id: 'upload_malformed',
        // Missing required fields
      };

      (apiClient.get as unknown).mockResolvedValue({ data: malformedResponse });

      const result = await getSchemaInterpretation('upload_malformed');

      expect(result).toEqual(malformedResponse);
    });

    it('handles very complex column mappings', async () => {
      const complexInterpretation: SchemaInterpretationResponse = {
        upload_id: 'upload_complex',
        filename: 'complex_financial_data.xlsx',
        column_mappings: Array.from({ length: 50 }, (_, i) => ({
          original_name: `Column_${i + 1}`,
          mapped_field: `field_${i + 1}`,
          confidence: Math.random(),
          reasoning: `Auto-detected pattern for column ${i + 1}`,
        })),
        overall_confidence: 0.75,
        required_fields_mapped: {
          transaction_date: true,
          transaction_amount: true,
          description: true,
          category: true,
        },
        interpretation_summary:
          'Complex file with many columns successfully interpreted',
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: complexInterpretation,
      });

      const result = await getSchemaInterpretation('upload_complex');

      expect(result).toEqual(complexInterpretation);
      expect(result.column_mappings).toHaveLength(50);
    });

    it('handles special characters in interpretations', async () => {
      const specialCharInterpretation: SchemaInterpretationResponse = {
        upload_id: 'upload_special',
        filename: 'données_financières_éñ.xlsx',
        column_mappings: [
          {
            original_name: 'Montánt (€)',
            mapped_field: 'transaction_amount',
            confidence: 0.9,
            reasoning: 'Currency symbol detected: €',
          },
        ],
        overall_confidence: 0.9,
        required_fields_mapped: {
          transaction_amount: true,
        },
        interpretation_summary:
          'Fichier avec caractères spéciaux traité avec succès',
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: specialCharInterpretation,
      });

      const result = await getSchemaInterpretation('upload_special');

      expect(result).toEqual(specialCharInterpretation);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('logs errors appropriately without exposing sensitive data', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const sensitiveError = new Error('Database password: secret123');
      (apiClient.post as unknown).mockRejectedValue(sensitiveError);

      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });
      await uploadFile(mockFile);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Error in uploadFile:',
        sensitiveError,
      );

      consoleSpy.mockRestore();
    });

    it('handles timeout errors gracefully', async () => {
      const timeoutError = new Error('Request timeout');
      (apiClient.post as unknown).mockRejectedValue(timeoutError);

      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });
      const result = await uploadFile(mockFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Request timeout',
        statusCode: 500,
      });
    });

    it('handles concurrent upload attempts', async () => {
      const mockFile1 = new File(['test1'], 'test1.xlsx', {
        type: 'application/xlsx',
      });
      const mockFile2 = new File(['test2'], 'test2.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse1: UploadResponse = {
        upload_id: 'upload_1',
        filename: 'test1.xlsx',
        status: 'uploaded',
        message: 'File 1 uploaded',
      };

      const mockResponse2: UploadResponse = {
        upload_id: 'upload_2',
        filename: 'test2.xlsx',
        status: 'uploaded',
        message: 'File 2 uploaded',
      };

      (apiClient.post as unknown)
        .mockResolvedValueOnce({ data: mockResponse1 })
        .mockResolvedValueOnce({ data: mockResponse2 });

      const [result1, result2] = await Promise.all([
        uploadFile(mockFile1),
        uploadFile(mockFile2),
      ]);

      expect(result1).toEqual(mockResponse1);
      expect(result2).toEqual(mockResponse2);
      expect(apiClient.post).toHaveBeenCalledTimes(2);
    });

    it('handles invalid file types', async () => {
      const invalidFile = new File(['test'], 'test.txt', {
        type: 'text/plain',
      });
      const validationError = new Error('Unsupported file type');
      (apiClient.post as unknown).mockRejectedValue(validationError);

      const result = await uploadFile(invalidFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Unsupported file type',
        statusCode: 500,
      });
    });

    it('handles corrupted file uploads', async () => {
      const corruptedFile = new File(['corrupted data'], 'corrupted.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const corruptionError = new Error('File appears to be corrupted');
      (apiClient.post as unknown).mockRejectedValue(corruptionError);

      const result = await uploadFile(corruptedFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'File appears to be corrupted',
        statusCode: 500,
      });
    });
  });

  describe('Performance and Memory', () => {
    it('handles rapid sequential uploads efficiently', async () => {
      const files = Array.from(
        { length: 5 },
        (_, i) =>
          new File([`test${i}`], `test${i}.xlsx`, { type: 'application/xlsx' }),
      );

      const responses = files.map((_, i) => ({
        upload_id: `upload_${i}`,
        filename: `test${i}.xlsx`,
        status: 'uploaded',
        message: `File ${i} uploaded`,
      }));

      responses.forEach((response) => {
        (apiClient.post as unknown).mockResolvedValueOnce({ data: response });
      });

      const startTime = performance.now();
      const results = [];

      for (const file of files) {
        results.push(await uploadFile(file));
      }

      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(results).toHaveLength(5);
      expect(apiClient.post).toHaveBeenCalledTimes(5);
    });

    it('does not leak memory during large file uploads', async () => {
      const largeFile = new File(
        [new ArrayBuffer(10 * 1024 * 1024)],
        'large.xlsx',
        {
          type: 'application/xlsx',
        },
      );

      const mockResponse: UploadResponse = {
        upload_id: 'upload_large_memory',
        filename: 'large.xlsx',
        status: 'uploaded',
        message: 'Large file uploaded successfully',
      };

      (apiClient.post as unknown).mockResolvedValue({ data: mockResponse });

      const result = await uploadFile(largeFile);

      expect(result).toEqual(mockResponse);
      // File should be processed without memory issues
    });
  });
});
