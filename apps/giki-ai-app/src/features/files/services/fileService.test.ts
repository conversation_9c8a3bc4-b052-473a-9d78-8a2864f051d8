/**
 * FileService Tests
 * Critical file validation and utility service tests for production
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  formatFileSize,
  uploadFile,
  type UploadResponse,
  type FileValidationResult,
  type FileMetadata,
  type FileSignatureCheck,
} from './fileService';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    postFormData: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
  },
}));

// Mock error handling
vi.mock('@/shared/utils/errorHandling', () => ({
  handleApiError: vi.fn((error) => ({
    type: 'api_error',
    message: error.message || 'API Error',
    statusCode: error.status || 500,
  })),
}));

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

describe('FileService - File Processing and Validation Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('formatFileSize Utility', () => {
    it('formats zero bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
    });

    it('formats bytes correctly', () => {
      expect(formatFileSize(500)).toBe('500 Bytes');
      expect(formatFileSize(1023)).toBe('1023 Bytes');
    });

    it('formats kilobytes correctly', () => {
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(2048)).toBe('2 KB');
      expect(formatFileSize(1048575)).toBe('1024 KB');
    });

    it('formats megabytes correctly', () => {
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1572864)).toBe('1.5 MB');
      expect(formatFileSize(5242880)).toBe('5 MB');
      expect(formatFileSize(1073741823)).toBe('1024 MB');
    });

    it('formats gigabytes correctly', () => {
      expect(formatFileSize(1073741824)).toBe('1 GB');
      expect(formatFileSize(1610612736)).toBe('1.5 GB');
      expect(formatFileSize(5368709120)).toBe('5 GB');
    });

    it('handles very large numbers', () => {
      expect(formatFileSize(1099511627776)).toBe('1024 GB'); // 1 TB displayed as GB
    });

    it('formats decimal precision correctly', () => {
      expect(formatFileSize(1234567)).toBe('1.18 MB');
      expect(formatFileSize(12345678)).toBe('11.77 MB');
      expect(formatFileSize(123456789)).toBe('117.74 MB');
    });

    it('handles edge cases', () => {
      expect(formatFileSize(1)).toBe('1 Bytes');
      expect(formatFileSize(1025)).toBe('1 KB');
      expect(formatFileSize(1048577)).toBe('1 MB');
    });
  });

  describe('uploadFile Function', () => {
    it('successfully uploads a file', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_123',
        filename: 'test.xlsx',
        size: 1024,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(mockFile);

      expect(apiClient.postFormData).toHaveBeenCalledWith(
        '/uploads/upload',
        expect.any(FormData),
      );

      // Verify FormData contains the file
      const formDataCall = (apiClient.postFormData as unknown).mock.calls[0][1];
      expect(formDataCall).toBeInstanceOf(FormData);
      expect(formDataCall.get('file')).toBe(mockFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles upload errors correctly', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const uploadError = new Error('File too large');
      uploadError.status = 413;
      (apiClient.postFormData as unknown).mockRejectedValue(uploadError);

      const result = await uploadFile(mockFile);

      expect(handleApiError).toHaveBeenCalledWith(uploadError, {
        context: 'uploadFile',
        defaultMessage: 'Failed to upload file.',
      });

      expect(result).toEqual({
        type: 'api_error',
        message: 'File too large',
        statusCode: 413,
      });
    });

    it('handles network connectivity issues', async () => {
      const mockFile = new File(['test content'], 'test.csv', {
        type: 'text/csv',
      });

      const networkError = new Error('Network Error');
      (apiClient.postFormData as unknown).mockRejectedValue(networkError);

      const result = await uploadFile(mockFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Network Error',
        statusCode: 500,
      });
    });

    it('handles server timeout errors', async () => {
      const mockFile = new File(['test content'], 'large_file.xlsx', {
        type: 'application/xlsx',
      });

      const timeoutError = new Error('Request timeout');
      timeoutError.status = 408;
      (apiClient.postFormData as unknown).mockRejectedValue(timeoutError);

      const result = await uploadFile(mockFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Request timeout',
        statusCode: 408,
      });
    });

    it('handles empty files', async () => {
      const emptyFile = new File([], 'empty.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_empty',
        filename: 'empty.xlsx',
        size: 0,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(emptyFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles files with special characters in name', async () => {
      const specialFile = new File(['content'], 'test file (1) & more.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'upload_special',
        filename: 'test file (1) & more.xlsx',
        size: 7,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(specialFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles different file types', async () => {
      const csvFile = new File(['col1,col2\nval1,val2'], 'data.csv', {
        type: 'text/csv',
      });

      const xlsxFile = new File(['excel data'], 'data.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      });

      const csvResponse: UploadResponse = {
        upload_id: 'csv_123',
        filename: 'data.csv',
        size: 19,
        status: 'uploaded',
      };

      const xlsxResponse: UploadResponse = {
        upload_id: 'xlsx_123',
        filename: 'data.xlsx',
        size: 10,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown)
        .mockResolvedValueOnce({ data: csvResponse })
        .mockResolvedValueOnce({ data: xlsxResponse });

      const csvResult = await uploadFile(csvFile);
      const xlsxResult = await uploadFile(xlsxFile);

      expect(csvResult).toEqual(csvResponse);
      expect(xlsxResult).toEqual(xlsxResponse);
    });

    it('handles very large files', async () => {
      const largeFile = new File(
        [new ArrayBuffer(50 * 1024 * 1024)],
        'large.xlsx',
        {
          type: 'application/xlsx',
        },
      );

      const mockResponse: UploadResponse = {
        upload_id: 'upload_large',
        filename: 'large.xlsx',
        size: 50 * 1024 * 1024,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(largeFile);

      expect(result).toEqual(mockResponse);
    });
  });

  describe('File Validation Types and Interfaces', () => {
    it('defines correct FileValidationResult structure', () => {
      const mockValidation: FileValidationResult = {
        is_valid: true,
        file_type: 'xlsx',
        file_size: 1024,
        mime_type:
          'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
        validation_errors: [],
        validation_warnings: ['Missing header row'],
        confidence: 0.95,
      };

      expect(mockValidation.is_valid).toBe(true);
      expect(mockValidation.file_type).toBe('xlsx');
      expect(mockValidation.validation_errors).toHaveLength(0);
      expect(mockValidation.validation_warnings).toHaveLength(1);
      expect(mockValidation.confidence).toBe(0.95);
    });

    it('defines correct FileMetadata structure', () => {
      const mockMetadata: FileMetadata = {
        filename: 'financial_data.xlsx',
        file_size: 2048,
        mime_type: 'application/xlsx',
        file_type: 'xlsx',
        encoding: 'UTF-8',
        headers: ['Date', 'Description', 'Amount'],
        row_count: 100,
        column_count: 3,
        sample_data: [
          ['2024-01-01', 'Purchase', '50.00'],
          ['2024-01-02', 'Sale', '100.00'],
        ],
        extracted_at: '2024-06-14T12:00:00Z',
      };

      expect(mockMetadata.filename).toBe('financial_data.xlsx');
      expect(mockMetadata.headers).toHaveLength(3);
      expect(mockMetadata.sample_data).toHaveLength(2);
      expect(mockMetadata.row_count).toBe(100);
      expect(mockMetadata.column_count).toBe(3);
    });

    it('defines correct FileSignatureCheck structure', () => {
      const mockSignature: FileSignatureCheck = {
        signature_hash: 'abc123def456',
        file_type: 'xlsx',
        confidence: 0.98,
        headers: ['Date', 'Amount', 'Description'],
        data_patterns: {
          date_columns: ['Date'],
          numeric_columns: ['Amount'],
          text_columns: ['Description'],
        },
        schema_match: {
          matched: true,
          schema_id: 'financial_schema_v1',
          confidence: 0.92,
          reason: 'Matches known financial transaction pattern',
        },
        can_reuse_schema: true,
        checked_at: '2024-06-14T12:00:00Z',
      };

      expect(mockSignature.confidence).toBe(0.98);
      expect(mockSignature.schema_match?.matched).toBe(true);
      expect(mockSignature.can_reuse_schema).toBe(true);
      expect(mockSignature?.data_patterns?.date_columns).toContain('Date');
    });

    it('handles FileSignatureCheck with errors', () => {
      const errorSignature: FileSignatureCheck = {
        file_type: 'unknown',
        confidence: 0.1,
        headers: [],
        data_patterns: {},
        schema_match: {
          matched: false,
          confidence: 0.1,
          reason: 'No recognizable patterns found',
        },
        can_reuse_schema: false,
        checked_at: '2024-06-14T12:00:00Z',
        error: 'Unable to parse file structure',
      };

      expect(errorSignature.error).toBe('Unable to parse file structure');
      expect(errorSignature.confidence).toBe(0.1);
      expect(errorSignature.schema_match?.matched).toBe(false);
    });
  });

  describe('File Processing Edge Cases', () => {
    it('handles corrupted file uploads', async () => {
      const corruptedFile = new File(['corrupted data'], 'corrupted.xlsx', {
        type: 'application/xlsx',
      });

      const corruptionError = new Error(
        'File appears to be corrupted or invalid',
      );
      corruptionError.status = 422;
      (apiClient.postFormData as unknown).mockRejectedValue(corruptionError);

      const result = await uploadFile(corruptedFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'File appears to be corrupted or invalid',
        statusCode: 422,
      });
    });

    it('handles unsupported file types', async () => {
      const unsupportedFile = new File(['test content'], 'document.pdf', {
        type: 'application/pdf',
      });

      const typeError = new Error('Unsupported file type');
      typeError.status = 415;
      (apiClient.postFormData as unknown).mockRejectedValue(typeError);

      const result = await uploadFile(unsupportedFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Unsupported file type',
        statusCode: 415,
      });
    });

    it('handles file size limit exceeded', async () => {
      const oversizedFile = new File(
        [new ArrayBuffer(100 * 1024 * 1024)],
        'huge.xlsx',
        {
          type: 'application/xlsx',
        },
      );

      const sizeError = new Error('File size exceeds maximum allowed limit');
      sizeError.status = 413;
      (apiClient.postFormData as unknown).mockRejectedValue(sizeError);

      const result = await uploadFile(oversizedFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'File size exceeds maximum allowed limit',
        statusCode: 413,
      });
    });

    it('handles malformed FormData', async () => {
      const mockFile = new File(['test'], 'test.xlsx', {
        type: 'application/xlsx',
      });

      const formDataError = new Error('Invalid form data');
      formDataError.status = 400;
      (apiClient.postFormData as unknown).mockRejectedValue(formDataError);

      const result = await uploadFile(mockFile);

      expect(result).toEqual({
        type: 'api_error',
        message: 'Invalid form data',
        statusCode: 400,
      });
    });
  });

  describe('File Validation Scenarios', () => {
    it('handles valid financial data file validation', () => {
      const validationResult: FileValidationResult = {
        is_valid: true,
        file_type: 'xlsx',
        file_size: 5120,
        mime_type:
          'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
        validation_errors: [],
        validation_warnings: [],
        confidence: 0.99,
      };

      expect(validationResult.is_valid).toBe(true);
      expect(validationResult.validation_errors).toHaveLength(0);
      expect(validationResult.confidence).toBeGreaterThan(0.9);
    });

    it('handles file with validation warnings', () => {
      const warningValidation: FileValidationResult = {
        is_valid: true,
        file_type: 'csv',
        file_size: 2048,
        mime_type: 'text/csv',
        validation_errors: [],
        validation_warnings: [
          'Some date formats may need adjustment',
          'Currency symbols detected but not standardized',
        ],
        confidence: 0.85,
      };

      expect(warningValidation.is_valid).toBe(true);
      expect(warningValidation.validation_warnings).toHaveLength(2);
      expect(warningValidation.confidence).toBe(0.85);
    });

    it('handles file with validation errors', () => {
      const errorValidation: FileValidationResult = {
        is_valid: false,
        file_type: 'xlsx',
        file_size: 1024,
        mime_type: 'application/xlsx',
        validation_errors: [
          'Required columns missing: Date, Amount',
          'File appears to be empty or corrupted',
        ],
        validation_warnings: [],
        confidence: 0.2,
      };

      expect(errorValidation.is_valid).toBe(false);
      expect(errorValidation.validation_errors).toHaveLength(2);
      expect(errorValidation.confidence).toBeLessThan(0.5);
    });

    it('handles complex file metadata extraction', () => {
      const complexMetadata: FileMetadata = {
        filename: 'bank_statements_2024.xlsx',
        file_size: 15360,
        mime_type: 'application/xlsx',
        file_type: 'xlsx',
        encoding: 'UTF-8',
        headers: [
          'Date',
          'Transaction ID',
          'Description',
          'Debit Amount',
          'Credit Amount',
          'Balance',
          'Category',
        ],
        row_count: 1250,
        column_count: 7,
        sample_data: [
          [
            '2024-01-01',
            'TXN001',
            'ATM Withdrawal',
            '100.00',
            '',
            '1500.00',
            'Cash',
          ],
          [
            '2024-01-02',
            'TXN002',
            'Salary Deposit',
            '',
            '3000.00',
            '4500.00',
            'Income',
          ],
          [
            '2024-01-03',
            'TXN003',
            'Grocery Store',
            '75.50',
            '',
            '4424.50',
            'Food',
          ],
        ],
        extracted_at: '2024-06-14T12:00:00Z',
      };

      expect(complexMetadata.row_count).toBe(1250);
      expect(complexMetadata.headers).toHaveLength(7);
      expect(complexMetadata.sample_data).toHaveLength(3);
      expect(complexMetadata.sample_data[0]).toContain('ATM Withdrawal');
    });
  });

  describe('Performance and Memory Management', () => {
    it('handles concurrent file uploads efficiently', async () => {
      const files = Array.from(
        { length: 5 },
        (_, i) =>
          new File([`content${i}`], `file${i}.xlsx`, {
            type: 'application/xlsx',
          }),
      );

      const responses = files.map((_, i) => ({
        upload_id: `upload_${i}`,
        filename: `file${i}.xlsx`,
        size: 8,
        status: 'uploaded',
      }));

      responses.forEach((response) => {
        (apiClient.postFormData as unknown).mockResolvedValueOnce({
          data: response,
        });
      });

      const startTime = performance.now();
      const results = await Promise.all(files.map((file) => uploadFile(file)));
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete quickly
      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect((result as UploadResponse).upload_id).toBe(`upload_${index}`);
      });
    });

    it('handles memory-intensive file operations', () => {
      const largeSizes = [
        100 * 1024 * 1024, // 100 MB
        500 * 1024 * 1024, // 500 MB
        1024 * 1024 * 1024, // 1 GB
      ];

      largeSizes.forEach((size) => {
        const formatted = formatFileSize(size);
        expect(formatted).toMatch(/^\d+(\.\d+)?\s(MB|GB)$/);
      });
    });

    it('handles rapid formatFileSize calls efficiently', () => {
      const sizes = Array.from({ length: 1000 }, (_, i) => i * 1024);

      const startTime = performance.now();
      const results = sizes.map((size) => formatFileSize(size));
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should be very fast
      expect(results).toHaveLength(1000);
      expect(results[0]).toBe('0 Bytes');
      expect(results[1]).toBe('1 KB');
    });
  });

  describe('Unicode and Internationalization', () => {
    it('handles files with unicode names', async () => {
      const unicodeFile = new File(['content'], '测试文件.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'unicode_123',
        filename: '测试文件.xlsx',
        size: 7,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(unicodeFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles files with emoji names', async () => {
      const emojiFile = new File(['content'], '📊财务数据📈.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'emoji_123',
        filename: '📊财务数据📈.xlsx',
        size: 7,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(emojiFile);

      expect(result).toEqual(mockResponse);
    });

    it('handles files with accented characters', async () => {
      const accentFile = new File(['contenu'], 'données_financières_été.xlsx', {
        type: 'application/xlsx',
      });

      const mockResponse: UploadResponse = {
        upload_id: 'accent_123',
        filename: 'données_financières_été.xlsx',
        size: 7,
        status: 'uploaded',
      };

      (apiClient.postFormData as unknown).mockResolvedValue({
        data: mockResponse,
      });

      const result = await uploadFile(accentFile);

      expect(result).toEqual(mockResponse);
    });
  });
});
