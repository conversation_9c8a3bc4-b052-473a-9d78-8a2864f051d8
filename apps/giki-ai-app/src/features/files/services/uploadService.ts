// apps/giki-ai-app/src/services/uploadService.ts
import { apiClient } from '@/shared/services/api/apiClient';
import type {
  UploadResponse,
  ProcessedFileResponse,
  JobStatus,
} from '@/shared/types/categorization';
import type { UploadRecord, RecentUpload } from '../types/upload';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

// This was GetFileColumnsResponse in lib/api.ts, aliasing for clarity if needed or use directly
interface GetFileColumnsResponse {
  columns: string[];
}

// This was ColumnListResponse in lib/api.ts, often it's the same as GetFileColumnsResponse
// If it's truly different, define it, otherwise, GetFileColumnsResponse can be used.
// For now, assuming it's the same for simplicity based on typical usage.
// export interface ColumnListResponse extends GetFileColumnsResponse {}

// File upload for AI categorization (NEW transactions WITHOUT categories)
export const uploadFiles = async (
  files: File[],
  options?: {
    onProgress?: (progress: number) => void;
  },
): Promise<{
  uploads: { upload_id: string; filename: string; transaction_count: number }[];
  failed: number;
}> => {
  try {
    // Validate files
    for (const file of files) {
      const validation = validateFile(file);
      if (!validation.isValid) {
        throw new Error(`File ${file.name}: ${validation.error}`);
      }
    }

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await apiClient.postFormData<{
      uploads: {
        upload_id: string;
        filename: string;
        transaction_count: number;
      }[];
      failed: number;
    }>('/api/v1/files/upload-production-data', formData, {
      onUploadProgress: (() => {
        let lastProgressUpdate = 0;
        return (progressEvent) => {
          const now = Date.now();
          if (
            options?.onProgress &&
            progressEvent.total &&
            now - lastProgressUpdate > 100
          ) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            );
            options.onProgress(progress);
            lastProgressUpdate = now;
          }
        };
      })(),
      timeout: 300000, // 5 minutes for large file uploads
    });

    return response.data;
  } catch (error) {
    console.error('Error in uploadProductionFiles:', error);
    throw error;
  }
};

export const uploadFile = async (
  file: File,
  onProgress?: (progress: number) => void,
  currency?: string,
): Promise<UploadResponse | ApiError> => {
  try {
    // Validate file before upload to prevent resource issues
    const validation = validateFile(file);
    if (!validation.isValid) {
      return handleApiError(new Error(validation.error), {
        context: 'uploadFile',
        defaultMessage: validation.error || 'File validation failed',
      });
    }

    const formData = new FormData();
    formData.append('file', file);
    if (currency) {
      formData.append('currency', currency);
    }

    const response = await apiClient.postFormData<UploadResponse>(
      '/api/v1/uploads/upload',
      formData,
      {
        onUploadProgress: (() => {
          let lastProgressUpdate = 0;
          return (progressEvent) => {
            const now = Date.now();
            if (
              onProgress &&
              progressEvent.total &&
              now - lastProgressUpdate > 100
            ) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              onProgress(progress);
              lastProgressUpdate = now;
            }
          };
        })(),
        timeout: 300000, // 5 minutes for large file uploads
      },
    );
    return response.data;
  } catch (error) {
    console.error('Error in uploadFile:', error);

    // Extract detailed error information for better user feedback
    let specificMessage = 'Failed to upload file';

    // Handle specific resource errors
    if (
      error instanceof Error &&
      error.message.includes('ERR_INSUFFICIENT_RESOURCES')
    ) {
      return handleApiError(error, {
        context: 'uploadFile',
        defaultMessage:
          'Upload failed due to file size or network limitations. Please try a smaller file or check your connection.',
      });
    }

    // Handle timeout errors specifically for uploads
    if (
      error instanceof Error &&
      (error.message.includes('timeout') || error.message.includes('abort'))
    ) {
      return handleApiError(error, {
        context: 'uploadFile',
        defaultMessage:
          'Upload timed out. Please try again with a smaller file or better network connection.',
      });
    }

    // Extract specific error details from axios response
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error;
      const status = axiosError.response?.status;
      const data = axiosError.response?.data;

      if (status) {
        switch (status) {
          case 400:
            specificMessage =
              data?.detail ||
              data?.message ||
              'Invalid file format or data. Please check your file and try again.';
            break;
          case 401:
            specificMessage = 'Your session has expired. Please log in again.';
            break;
          case 403:
            specificMessage = 'You do not have permission to upload files.';
            break;
          case 413:
            specificMessage = 'File is too large. Please try a smaller file.';
            break;
          case 415:
            specificMessage =
              'File type not supported. Please use CSV, Excel, or PDF files.';
            break;
          case 422:
            specificMessage =
              data?.detail ||
              'File validation failed. Please check the file format and data.';
            break;
          case 429:
            specificMessage =
              'Too many upload attempts. Please wait a moment and try again.';
            break;
          case 500:
            specificMessage =
              'Server error during upload. Please try again or contact support.';
            break;
          case 503:
            specificMessage =
              'Service temporarily unavailable. Please try again in a few minutes.';
            break;
          default:
            // Try to extract backend error message
            specificMessage =
              data?.detail ||
              data?.message ||
              data?.error ||
              `Upload failed with status ${status}`;
        }
      }
    }

    return handleApiError(error, {
      context: 'uploadFile',
      defaultMessage: specificMessage,
    });
  }
};

export const getFileColumns = async (uploadId: string): Promise<string[]> => {
  try {
    const response = await apiClient.get<GetFileColumnsResponse>(
      `/api/v1/uploads/${uploadId}/columns`,
    );
    return response?.data?.columns || [];
  } catch (error) {
    console.error('Error in getFileColumns:', error); // Keep console logs
    throw handleApiError(error, {
      showToast: false,
      context: 'getFileColumns',
      defaultMessage: 'Failed to get file columns',
    });
  }
};

// Schema interpretation types
export interface ColumnMapping {
  original_name: string;
  mapped_field: string;
  confidence: number;
  reasoning: string;
}

export interface SchemaInterpretationResponse {
  upload_id: string;
  filename: string;
  column_mappings: ColumnMapping[];
  overall_confidence: number;
  required_fields_mapped: Record<string, boolean>;
  interpretation_summary: string;
}

export const getSchemaInterpretation = async (
  uploadId: string,
): Promise<SchemaInterpretationResponse> => {
  try {
    const response = await apiClient.get<SchemaInterpretationResponse>(
      `/api/v1/uploads/${uploadId}/schema-interpretation`,
    );
    return response.data;
  } catch (error) {
    console.error('Error in getSchemaInterpretation:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'getSchemaInterpretation',
      defaultMessage: 'Failed to get schema interpretation',
    });
  }
};

export const submitColumnMapping = async (
  uploadId: string,
  mapping: Record<string, string | null>,
): Promise<ProcessedFileResponse> => {
  try {
    // Use the working /map endpoint that creates transactions and updates onboarding status
    // This endpoint DOES persist data permanently and triggers category inference
    const response = await apiClient.post<ProcessedFileResponse>(
      `/api/v1/uploads/${uploadId}/map`,
      { mapping },
    );
    return response.data;
  } catch (error) {
    console.error('Error in submitColumnMapping:', error); // Keep console logs
    throw handleApiError(error, {
      showToast: false,
      context: 'submitColumnMapping',
      defaultMessage: 'Failed to process file with column mapping',
    });
  }
};

export const getCategorizationJobStatus = async (
  jobId: string,
): Promise<JobStatus> => {
  try {
    const response = await apiClient.get<JobStatus>(
      `/api/v1/categorization/jobs/${jobId}`,
    );
    return response.data;
  } catch (error) {
    console.error('Error in getCategorizationJobStatus:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'getCategorizationJobStatus',
      defaultMessage: 'Failed to get job status',
    });
  }
};

/**
 * Get upload status by ID
 */
export const getUploadStatus = async (
  uploadId: string,
): Promise<{ status: string; progress?: number } | ApiError> => {
  try {
    const response = await apiClient.get(`/api/v1/uploads/${uploadId}/status`);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getUploadStatus',
      defaultMessage: 'Failed to get upload status.',
    });
  }
};

/**
 * Get all uploads for the current tenant
 */
export const getAllUploads = async (): Promise<UploadRecord[] | ApiError> => {
  try {
    const response = await apiClient.get<UploadRecord[]>('/api/v1/uploads');
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getAllUploads',
      defaultMessage: 'Failed to get uploads.',
    });
  }
};

/**
 * Get upload history with pagination
 */
export const getUploadHistory = async (
  page: number = 1,
  perPage: number = 20,
): Promise<{ uploads: unknown[]; total: number; page: number } | ApiError> => {
  try {
    const response = await apiClient.get('/api/v1/uploads/history', {
      params: {
        page,
        per_page: perPage,
      },
    });
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getUploadHistory',
      defaultMessage: 'Failed to get upload history.',
    });
  }
};

/**
 * Validate file before upload
 */
export const validateFile = (
  file: File,
  supportedFormats: string[] = ['csv', 'xlsx', 'xls'],
  maxSizeBytes: number = 10 * 1024 * 1024, // 10MB default - align with backend limit
): { isValid: boolean; error?: string } => {
  // Check file size
  if (file.size > maxSizeBytes) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSizeBytes / (1024 * 1024))}MB limit`,
    };
  }

  // Check file extension
  const fileExtension = file?.name?.split('.').pop()?.toLowerCase();
  if (!fileExtension || !supportedFormats.includes(fileExtension)) {
    return {
      isValid: false,
      error: `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`,
    };
  }

  return { isValid: true };
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Convert UploadRecord from API to RecentUpload for frontend display
 */
export const convertUploadRecord = (record: UploadRecord): RecentUpload => {
  return {
    id: record.upload_id,
    filename: record.filename,
    uploadDate: record.created_at ? new Date(record.created_at) : new Date(),
    status:
      record.status === 'completed'
        ? 'completed'
        : record.status === 'processing'
          ? 'processing'
          : record.status === 'pending'
            ? 'pending'
            : 'error',
    recordCount: undefined, // Would need additional field from API
    errorMessage: record.status === 'error' ? record.message : undefined,
  };
};

/**
 * Upload file for onboarding flow with categorization labels
 */
export const uploadFileForOnboarding = async (
  file: File,
  currency: string,
  onProgress?: (progress: number) => void,
): Promise<UploadResponse | ApiError> => {
  try {
    // Validate file before upload
    const validation = validateFile(file);
    if (!validation.isValid) {
      return handleApiError(new Error(validation.error), {
        context: 'uploadFileForOnboarding',
        defaultMessage: validation.error || 'File validation failed',
      });
    }

    const formData = new FormData();
    formData.append('files', file); // Note: 'files' not 'file' for batch endpoint
    formData.append('year', new Date().getFullYear().toString());
    formData.append('has_category_labels', 'true'); // Onboarding always has labels
    if (currency) {
      formData.append('currency', currency);
    }

    const response = await apiClient.postFormData<{
      status: string;
      summary: {
        total_files_processed: number;
        successful_uploads: number;
        failed_uploads: number;
        total_transactions_imported: number;
        rag_corpus_built: boolean;
      };
      upload_results: Array<{
        filename: string;
        status: string;
        transactions_imported: number;
        report_id: string | null;
        column_mapping?: any;
        file_path?: string;
        error?: string;
      }>;
      message: string;
    }>('/api/v1/onboarding/batch-upload-files', formData, {
      onUploadProgress: (() => {
        let lastProgressUpdate = 0;
        return (progressEvent) => {
          const now = Date.now();
          if (
            onProgress &&
            progressEvent.total &&
            now - lastProgressUpdate > 100
          ) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            );
            onProgress(progress);
            lastProgressUpdate = now;
          }
        };
      })(),
      timeout: 300000, // 5 minutes for large file uploads
    });

    // Extract the first upload result (since we're uploading single file)
    const uploadResult = response.data.upload_results[0];

    if (!uploadResult || uploadResult.status !== 'success') {
      const error = uploadResult?.error || 'Upload failed';
      return handleApiError(new Error(error), {
        context: 'uploadFileForOnboarding',
        defaultMessage: error,
      });
    }

    // Convert to UploadResponse format
    const uploadResponse: UploadResponse = {
      upload_id: uploadResult.upload_id || uploadResult.report_id || '',
      filename: uploadResult.filename,
      size: file.size,
      content_type: file.type,
      status: 'completed',
      transaction_count: uploadResult.transactions_imported,
      column_mapping: uploadResult.column_mapping,
      report_id: uploadResult.report_id,
    };

    return uploadResponse;
  } catch (error) {
    console.error('Error in uploadFileForOnboarding:', error);
    return handleApiError(error as Error, {
      context: 'uploadFileForOnboarding',
      defaultMessage: 'Failed to upload file for onboarding',
    });
  }
};
