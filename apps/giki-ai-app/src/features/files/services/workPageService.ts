import { apiClient } from '@/shared/services/api/apiClient';
import type { Transaction } from '@/shared/types/transaction';

/**
 * Work Page Service
 * Handles data fetching for the main production workspace
 */

// Interfaces for Work Page data
export interface WorkspaceStats {
  totalTransactions: number;
  pendingReview: number;
  monthlySpending: number;
  categoriesCount: number;
}

export interface QuickReport {
  id: string;
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
}

export interface PendingTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  suggestedCategory: string;
  confidence: number;
}

export interface ProcessingStatusResponse {
  total_uploads: number;
  uploads: Array<{
    upload_id: string;
    filename: string;
    file_size: number;
    file_type: string;
    created_at: string;
    transaction_count: number;
    status: 'processing' | 'completed' | 'failed';
  }>;
  summary: {
    processing: number;
    completed: number;
    failed: number;
  };
}

export interface DashboardMetricsResponse {
  total_transactions: number;
  total_income: number;
  total_expenses: number;
  net_income: number;
  categorized_transactions: number;
  uncategorized_transactions: number;
  categorization_rate: number;
  date_range: {
    start: string;
    end: string;
  };
}

export interface CategoryBreakdownResponse {
  items: Array<{
    category: string;
    amount: number;
    transaction_count: number;
    percentage: number;
  }>;
  total_categories: number;
  total_spending: number;
}

// Get workspace statistics from dashboard metrics
export async function getWorkspaceStats(): Promise<WorkspaceStats> {
  try {
    const response = await apiClient.get<DashboardMetricsResponse>(
      '/api/v1/dashboard/metrics',
    );

    const metrics = response.data;

    // Get category count from category breakdown
    const categoryResponse = await apiClient.get<CategoryBreakdownResponse>(
      '/api/v1/dashboard/category-breakdown',
    );

    return {
      totalTransactions: metrics.total_transactions,
      pendingReview: metrics.uncategorized_transactions,
      monthlySpending: Math.abs(metrics.total_expenses),
      categoriesCount: categoryResponse.data.total_categories,
    };
  } catch (error) {
    console.error('Failed to fetch workspace stats:', error);
    // Return default values on error
    return {
      totalTransactions: 0,
      pendingReview: 0,
      monthlySpending: 0,
      categoriesCount: 0,
    };
  }
}

// Get quick reports data
export async function getQuickReports(): Promise<QuickReport[]> {
  try {
    // Fetch current month and previous month data for comparison
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    const startOfPrevMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1,
    );
    const endOfPrevMonth = new Date(today.getFullYear(), today.getMonth(), 0);

    // Get current month metrics
    const currentMonthResponse = await apiClient.get<DashboardMetricsResponse>(
      `/api/v1/dashboard/metrics?start_date=${startOfMonth.toISOString().split('T')[0]}&end_date=${endOfMonth.toISOString().split('T')[0]}`,
    );

    // Get previous month metrics for comparison
    const prevMonthResponse = await apiClient.get<DashboardMetricsResponse>(
      `/api/v1/dashboard/metrics?start_date=${startOfPrevMonth.toISOString().split('T')[0]}&end_date=${endOfPrevMonth.toISOString().split('T')[0]}`,
    );

    // Get category breakdown for top category
    const categoryResponse = await apiClient.get<CategoryBreakdownResponse>(
      '/api/v1/dashboard/category-breakdown',
    );

    const currentMetrics = currentMonthResponse.data;
    const prevMetrics = prevMonthResponse.data;
    const topCategory = categoryResponse.data.items[0];

    // Calculate changes
    const spendingChange =
      prevMetrics.total_expenses !== 0
        ? ((currentMetrics.total_expenses - prevMetrics.total_expenses) /
            Math.abs(prevMetrics.total_expenses)) *
          100
        : 0;

    const avgTransactionCurrent =
      currentMetrics.total_transactions > 0
        ? currentMetrics.total_expenses / currentMetrics.total_transactions
        : 0;
    const avgTransactionPrev =
      prevMetrics.total_transactions > 0
        ? prevMetrics.total_expenses / prevMetrics.total_transactions
        : 0;
    const avgTransactionChange =
      avgTransactionPrev !== 0
        ? ((avgTransactionCurrent - avgTransactionPrev) /
            Math.abs(avgTransactionPrev)) *
          100
        : 0;

    return [
      {
        id: '1',
        title: 'Monthly Spend',
        value: `$${(Math.abs(currentMetrics.total_expenses) / 1000).toFixed(1)}K`,
        change: `${spendingChange >= 0 ? '+' : ''}${spendingChange.toFixed(0)}%`,
        trend:
          spendingChange > 0 ? 'up' : spendingChange < 0 ? 'down' : 'neutral',
      },
      {
        id: '2',
        title: 'Avg Transaction',
        value: `$${Math.abs(avgTransactionCurrent).toFixed(0)}`,
        change: `${avgTransactionChange >= 0 ? '+' : ''}${avgTransactionChange.toFixed(0)}%`,
        trend:
          avgTransactionChange > 0
            ? 'up'
            : avgTransactionChange < 0
              ? 'down'
              : 'neutral',
      },
      {
        id: '3',
        title: 'Top Category',
        value: topCategory?.category || 'No data',
        change: `${topCategory?.percentage.toFixed(0)}%`,
        trend: 'neutral',
      },
    ];
  } catch (error) {
    console.error('Failed to fetch quick reports:', error);
    return [];
  }
}

// Get pending transactions that need review
export async function getPendingTransactions(
  limit: number = 10,
): Promise<PendingTransaction[]> {
  try {
    const response = await apiClient.get<{
      data: Transaction[];
      total: number;
    }>(
      `/api/v1/transactions/?status=uncategorized&page_size=${limit}&skip_count=true`,
    );

    const transactions = response.data.data || [];

    return transactions.map((transaction) => ({
      id: transaction.id,
      description: transaction.description,
      amount: transaction.amount,
      date: transaction.date,
      suggestedCategory:
        transaction.ai_suggested_category_path || 'Uncategorized',
      confidence: transaction.ai_confidence || 0,
    }));
  } catch (error) {
    console.error('Failed to fetch pending transactions:', error);
    return [];
  }
}

// Get recent file processing status
export async function getProcessingStatus(): Promise<ProcessingStatusResponse | null> {
  try {
    const response = await apiClient.get<ProcessingStatusResponse>(
      '/api/v1/files/processing-status',
    );
    return response.data;
  } catch (error) {
    console.error('Failed to fetch processing status:', error);
    return null;
  }
}
