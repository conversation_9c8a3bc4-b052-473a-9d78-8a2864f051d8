import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import { Textarea } from '@/shared/components/ui/textarea';
import { Label } from '@/shared/components/ui/label';
import { AlertCircle, Folder, Info, Plus, RefreshCw } from 'lucide-react';
import { toast } from '@/shared/components/ui/use-toast';
import {
  fetchCategories,
  categoryService,
} from '@/features/categories/services/categoryService';
import { Category } from '@/shared/types/categorization';
import { HierarchicalCategory } from '@/shared/types/category';
import { AppError, isAppError } from '@/shared/types/errors';
import CategoryManagementView from '@/features/categories/components/CategoryManagementView';

// Using HierarchicalCategory from types/category.ts

const CategoryManagementPage: React.FC = () => {
  const [categories, setCategories] = useState<HierarchicalCategory[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [expandedCategories] = useState<Set<number>>(new Set());
  const [newCategoryName, setNewCategoryName] = useState<string>('');
  const [newCategoryDescription, setNewCategoryDescription] =
    useState<string>('');
  const [isNewCategoryDialogOpen, setIsNewCategoryDialogOpen] =
    useState<boolean>(false);
  const [newParentCategoryId, setNewParentCategoryId] = useState<number | null>(
    null,
  );
  const [categoryStats, setCategoryStats] = useState<{
    total: number;
    active: number;
    withTransactions: number;
  }>({
    total: 0,
    active: 0,
    withTransactions: 0,
  });

  // Function to build hierarchical categories
  const buildHierarchy = useCallback(
    (flatCategories: Category[]): HierarchicalCategory[] => {
      // Create a map of categories by ID
      const categoryMap: Record<string, HierarchicalCategory> = {};

      // First pass: create category objects with level = 0
      flatCategories.forEach((category) => {
        categoryMap[category.id] = {
          ...category,
          children: [],
          level: 0,
          isExpanded: expandedCategories.has(category.id),
        };
      });

      // Second pass: build the tree structure
      const rootCategories: HierarchicalCategory[] = [];

      flatCategories.forEach((category) => {
        const categoryWithLevel = categoryMap[category.id];

        if (category.parent_id && categoryMap[category.parent_id]) {
          // This is a child category
          const parent = categoryMap[category.parent_id];
          categoryWithLevel.level = (parent.level ?? 0) + 1;
          categoryWithLevel.parent = parent;
          parent?.children?.push(categoryWithLevel);
        } else {
          // This is a root category
          rootCategories.push(categoryWithLevel);
        }
      });

      // Compute statistics
      let activeCount = 0;
      let withTransactionsCount = 0;

      flatCategories.forEach((category) => {
        if (category.active !== false) {
          activeCount++;
        }

        if (category.transaction_count && category.transaction_count > 0) {
          withTransactionsCount++;
        }
      });

      setCategoryStats({
        total: flatCategories.length,
        active: activeCount,
        withTransactions: withTransactionsCount,
      });

      // Sort root categories and their children
      const sortCategories = (
        cats: HierarchicalCategory[],
      ): HierarchicalCategory[] => {
        // Sort by name
        const sorted = [...cats].sort((a, b) => a?.name?.localeCompare(b.name));

        // Recursively sort children
        sorted.forEach((cat) => {
          if (cat.children && cat?.children?.length > 0) {
            cat.children = sortCategories(cat.children);
          }
        });

        return sorted;
      };

      return sortCategories(rootCategories);
    },
    [expandedCategories, setCategoryStats],
  );

  // Fetch categories from API
  const loadCategories = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const categoriesData: Category[] | AppError = await fetchCategories();

      if (isAppError(categoriesData)) {
        setError(categoriesData.message);
        toast({
          title: 'Error',
          description:
            categoriesData.message ||
            'Failed to load categories. Please try again.',
          variant: 'destructive',
        });
        setCategories([]); // Clear categories on error
        return; // Exit if there's an error
      }

      // If we are here, categoriesData is Category[]
      // Add dummy transaction_count for sample data if it doesn't exist
      const categoriesWithTransactionCount = categoriesData.map(
        (category: Category) => ({
          ...category,
          transaction_count:
            category.transaction_count ?? Math.floor(Math.random() * 500),
          active: category.active === undefined ? true : category.active,
        }),
      );

      const hierarchicalCategories = buildHierarchy(
        categoriesWithTransactionCount,
      );
      setCategories(hierarchicalCategories);
    } catch (err) {
      // This catch block will now primarily handle unexpected errors
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'An unknown error occurred while loading categories.';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [buildHierarchy]);

  // Initial data load
  useEffect(() => {
    void loadCategories();
  }, [loadCategories]);

  // Handle category refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    void loadCategories();
  };

  // Create a new category
  const handleCreateCategory = () => {
    setNewCategoryName('');
    setNewCategoryDescription('');
    setNewParentCategoryId(null);
    setIsNewCategoryDialogOpen(true);
  };

  // Save a new category
  const handleSaveNewCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: 'Error',
        description: 'Category name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      const result = await categoryService.createCategory({
        name: newCategoryName.trim(),
        parentId: newParentCategoryId,
      });

      if (isAppError(result)) {
        toast({
          title: 'Error',
          description: result.message || 'Failed to create category',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Success',
        description: `Category "${newCategoryName}" created successfully`,
      });

      setIsNewCategoryDialogOpen(false);
      setNewCategoryName('');
      setNewCategoryDescription('');
      setNewParentCategoryId(null);

      // Reload categories to show the new one
      void loadCategories();
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to create category. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Get all categories flattened for searching
  const getAllCategories = (
    cats: HierarchicalCategory[],
  ): HierarchicalCategory[] => {
    let allCats: HierarchicalCategory[] = [];

    cats.forEach((cat) => {
      allCats.push(cat);
      if (cat.children && cat?.children?.length > 0) {
        allCats = [...allCats, ...getAllCategories(cat.children)];
      }
    });

    return allCats;
  };

  // Filter categories based on search term - handled in CategoryTreeView component

  const flatCategories = categories.flatMap((cat) => getAllCategories([cat]));

  return (
    <div className="container mx-auto py-8 px-6">
      {/* Professional Header with giki.ai Branding */}
      <div className="mb-8">
        <div className="flex flex-wrap flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          <div className="space-y-2">
            <div className="flex flex-wrap items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[hsl(var(--giki-primary))] to-[hsl(var(--giki-primary))]/80 flex flex-wrap items-center justify-center">
                <Folder className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="page-title">Category Management</h1>
                <p className="text-sm text-muted-foreground mt-1">
                  Organize and manage your transaction categorization hierarchy
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => void handleCreateCategory()}
              className="max-w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2.5 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
            >
              <Plus className="mr-2 h-4 w-4" />
              New Category
            </Button>

            <Button
              variant="outline"
              onClick={() => void handleRefresh()}
              disabled={isRefreshing}
              className="max-w-full border-border hover:border-primary hover:text-primary px-4 py-2.5 rounded-lg transition-all duration-200"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Category Management View */}
      {isLoading ? (
        <div className="flex flex-wrap flex-col items-center justify-center h-80 space-y-4">
          <div className="w-12 h-12 rounded-full bg-[hsl(var(--giki-primary))]/10 flex flex-wrap items-center justify-center">
            <RefreshCw className="h-6 w-6 text-primary animate-spin" />
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-primary mb-1">
              Loading Categories
            </h3>
            <p className="text-sm text-muted-foreground">
              Please wait while we fetch your category data...
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="flex flex-wrap flex-col items-center justify-center h-80 space-y-4">
          <div className="w-12 h-12 rounded-full bg-[hsl(var(--giki-destructive))]/10 flex flex-wrap items-center justify-center">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <div className="text-center space-y-3">
            <h3 className="text-lg font-semibold text-primary">
              Unable to Load Categories
            </h3>
            <p className="text-sm text-muted-foreground max-w-md">{error}</p>
            <Button
              onClick={() => void handleRefresh()}
              className="max-w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      ) : (
        <CategoryManagementView
          categories={flatCategories}
          onAddCategory={async (name: string, parentId: number | null) => {
            try {
              const result = await categoryService.createCategory({
                name: name.trim(),
                parentId: parentId,
              });

              if (isAppError(result)) {
                toast({
                  title: 'Error',
                  description: result.message || 'Failed to create category',
                  variant: 'destructive',
                });
                return;
              }

              toast({
                title: 'Success',
                description: `Category "${name}" created successfully`,
              });
              await loadCategories();
            } catch {
              toast({
                title: 'Error',
                description: 'Failed to create category. Please try again.',
                variant: 'destructive',
              });
            }
          }}
          onRenameCategory={async (categoryId: number, newName: string) => {
            try {
              const result = await categoryService.updateCategory(categoryId, {
                name: newName.trim(),
              });

              if (isAppError(result)) {
                toast({
                  title: 'Error',
                  description: result.message || 'Failed to update category',
                  variant: 'destructive',
                });
                return;
              }

              toast({
                title: 'Success',
                description: `Category updated successfully`,
              });
              await loadCategories();
            } catch {
              toast({
                title: 'Error',
                description: 'Failed to update category. Please try again.',
                variant: 'destructive',
              });
            }
          }}
          onUpdateCategory={async (
            categoryId: number,
            updates: Partial<Category>,
          ) => {
            try {
              const result = await categoryService.updateCategory(
                categoryId,
                updates,
              );

              if (isAppError(result)) {
                toast({
                  title: 'Error',
                  description: result.message || 'Failed to update category',
                  variant: 'destructive',
                });
                return;
              }

              toast({
                title: 'Success',
                description: `Category updated successfully`,
              });
              await loadCategories();
            } catch {
              toast({
                title: 'Error',
                description: 'Failed to update category. Please try again.',
                variant: 'destructive',
              });
            }
          }}
          onDeleteCategory={async (categoryId: number) => {
            if (
              confirm(
                'Are you sure you want to delete this category? This action cannot be undone.',
              )
            ) {
              try {
                const result = await categoryService.deleteCategory(categoryId);

                if (isAppError(result)) {
                  toast({
                    title: 'Error',
                    description: result.message || 'Failed to delete category',
                    variant: 'destructive',
                  });
                  return;
                }

                toast({
                  title: 'Success',
                  description: `Category deleted successfully`,
                });
                await loadCategories();
              } catch {
                toast({
                  title: 'Error',
                  description: 'Failed to delete category. Please try again.',
                  variant: 'destructive',
                });
              }
            }
          }}
          onMoveCategory={async (
            categoryId: number,
            newParentId: number | null,
          ) => {
            try {
              const result = await categoryService.updateCategory(categoryId, {
                parent_id: newParentId,
              });

              if (isAppError(result)) {
                toast({
                  title: 'Error',
                  description: result.message || 'Failed to move category',
                  variant: 'destructive',
                });
                return;
              }

              toast({
                title: 'Success',
                description: `Category moved successfully`,
              });
              await loadCategories();
            } catch {
              toast({
                title: 'Error',
                description: 'Failed to move category. Please try again.',
                variant: 'destructive',
              });
            }
          }}
        />
      )}

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mt-8">
        {/* Stats and Info */}
        <div className="xl:col-span-1">
          <Card className="border-0 shadow-lg bg-white">
            <CardHeader className="bg-gradient-to-r from-[hsl(var(--giki-bg-secondary))] to-[hsl(var(--giki-bg-primary))] border-b border-border pb-6">
              <div className="flex flex-wrap items-center gap-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary to-primary/80 flex flex-wrap items-center justify-center">
                  <Info className="h-4 w-4 text-white" />
                </div>
                <div>
                  <CardTitle className="section-title">
                    Category Overview
                  </CardTitle>
                  <CardDescription className="text-sm text-muted-foreground mt-1">
                    System insights and statistics
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6 space-y-6 overflow-hidden">
              {/* Category Stats */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100">
                <div className="flex flex-wrap items-center gap-2 mb-4">
                  <div className="w-6 h-6 rounded-md bg-info flex flex-wrap items-center justify-center">
                    <Folder className="h-3 w-3 text-white" />
                  </div>
                  <h3 className="form-label">Category Statistics</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Total Categories
                    </span>
                    <Badge
                      variant="outline"
                      className="bg-white border-blue-200 text-info max-w-[150px] truncate"
                    >
                      {categoryStats.total}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm text-secondary">
                      Active Categories
                    </span>
                    <Badge
                      variant="outline"
                      className="bg-success/10 text-success border-green-200 font-medium max-w-[150px] truncate"
                    >
                      {categoryStats.active}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm text-secondary">
                      With Transactions
                    </span>
                    <Badge
                      variant="outline"
                      className="bg-primary/10 text-primary border-primary/20 font-medium max-w-[150px] truncate"
                    >
                      {categoryStats.withTransactions}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* New Category Dialog */}
      <Dialog
        open={isNewCategoryDialogOpen}
        onOpenChange={setIsNewCategoryDialogOpen}
      >
        <DialogContent className="sm:max-w-full sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {newParentCategoryId
                ? 'Create Subcategory'
                : 'Create New Category'}
            </DialogTitle>
            <DialogDescription>
              {newParentCategoryId
                ? 'Add a new subcategory to the selected parent category'
                : 'Add a new top-level category to your hierarchy'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="form-label">
                Category Name
              </Label>
              <Input
                id="name"
                placeholder="Enter category name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e?.target?.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="form-label">
                Description (Optional)
              </Label>
              <Textarea
                id="description"
                placeholder="Enter category description"
                value={newCategoryDescription}
                onChange={(e) => setNewCategoryDescription(e?.target?.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewCategoryDialogOpen(false)}
              className="max-w-full"
            >
              Cancel
            </Button>
            <Button
              onClick={() => void handleSaveNewCategory()}
              className="max-w-full"
            >
              Create Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CategoryManagementPage;
