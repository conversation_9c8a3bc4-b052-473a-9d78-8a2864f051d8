/**
 * Category Service Tests
 * Critical service layer tests for production category management
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  categoryService,
  addCategory,
  fetchCategories,
  updateCategory,
  deleteCategory,
  type Category,
  type AddCategoryPayload,
} from './categoryService';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock error handling
vi.mock('@/shared/utils/errorHandling', () => ({
  handleApiError: vi.fn((error, context) => ({
    isApiError: true,
    message: `API Error: ${context.defaultMessage}`,
    context: context.context,
    originalError: error,
  })),
}));

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

describe('CategoryService - Category Management API Layer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Individual Category Functions', () => {
    describe('addCategory', () => {
      it('successfully adds a category', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'Test Category',
          parent_id: null,
          path: 'Test Category',
        };

        const payload: AddCategoryPayload = {
          name: 'Test Category',
          parentId: null,
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await addCategory(payload);

        expect(apiClient.post).toHaveBeenCalledWith('/categories', {
          name: 'Test Category',
          parent_id: null,
        });
        expect(result).toEqual(mockCategory);
      });

      it('handles addCategory API errors', async () => {
        const error = new Error('Network error');
        const payload: AddCategoryPayload = {
          name: 'Test Category',
          parentId: null,
        };

        (apiClient.post as unknown).mockRejectedValue(error);

        const result = await addCategory(payload);

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'addCategory',
          defaultMessage: 'Failed to add category.',
        });
        expect(result).toEqual({
          isApiError: true,
          message: 'API Error: Failed to add category.',
          context: 'addCategory',
          originalError: error,
        });
      });

      it('correctly maps parentId to parent_id', async () => {
        const payload: AddCategoryPayload = {
          name: 'Child Category',
          parentId: 5,
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: {
            id: 2,
            name: 'Child Category',
            parent_id: 5,
            path: 'Parent > Child Category',
          },
        });

        await addCategory(payload);

        expect(apiClient.post).toHaveBeenCalledWith('/categories', {
          name: 'Child Category',
          parent_id: 5,
        });
      });
    });

    describe('fetchCategories', () => {
      it('successfully fetches categories', async () => {
        const mockCategories: Category[] = [
          { id: 1, name: 'Category 1', parent_id: null, path: 'Category 1' },
          {
            id: 2,
            name: 'Category 2',
            parent_id: 1,
            path: 'Category 1 > Category 2',
          },
        ];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockCategories,
        });

        const result = await fetchCategories();

        expect(apiClient.get).toHaveBeenCalledWith('/categories');
        expect(result).toEqual(mockCategories);
      });

      it('handles fetchCategories API errors', async () => {
        const error = new Error('Server error');

        (apiClient.get as unknown).mockRejectedValue(error);

        const result = await fetchCategories();

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'fetchCategories',
          defaultMessage: 'Failed to fetch categories.',
        });
        expect(result).toEqual({
          isApiError: true,
          message: 'API Error: Failed to fetch categories.',
          context: 'fetchCategories',
          originalError: error,
        });
      });
    });

    describe('updateCategory', () => {
      it('successfully updates a category', async () => {
        const mockUpdatedCategory: Category = {
          id: 1,
          name: 'Updated Category',
          parent_id: null,
          path: 'Updated Category',
        };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockUpdatedCategory,
        });

        const result = await updateCategory(1, 'Updated Category');

        expect(apiClient.put).toHaveBeenCalledWith('/categories/1', {
          name: 'Updated Category',
        });
        expect(result).toEqual(mockUpdatedCategory);
      });

      it('handles updateCategory API errors', async () => {
        const error = new Error('Update failed');

        (apiClient.put as unknown).mockRejectedValue(error);

        const result = await updateCategory(1, 'Updated Category');

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'updateCategory',
          defaultMessage: 'Failed to update category.',
        });
        expect(result).toEqual({
          isApiError: true,
          message: 'API Error: Failed to update category.',
          context: 'updateCategory',
          originalError: error,
        });
      });
    });

    describe('deleteCategory', () => {
      it('successfully deletes a category', async () => {
        (apiClient.delete as unknown).mockResolvedValue({});

        const result = await deleteCategory(1);

        expect(apiClient.delete).toHaveBeenCalledWith('/categories/1');
        expect(result).toBeUndefined();
      });

      it('handles deleteCategory API errors', async () => {
        const error = new Error('Delete failed');

        (apiClient.delete as unknown).mockRejectedValue(error);

        const result = await deleteCategory(1);

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'deleteCategory',
          defaultMessage: 'Failed to delete category.',
        });
        expect(result).toEqual({
          isApiError: true,
          message: 'API Error: Failed to delete category.',
          context: 'deleteCategory',
          originalError: error,
        });
      });
    });
  });

  describe('CategoryService Object Methods', () => {
    describe('getCategories', () => {
      it('fetches all categories without filter', async () => {
        const mockCategories: Category[] = [
          { id: 1, name: 'Category 1', parent_id: null, path: 'Category 1' },
        ];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockCategories,
        });

        const result = await categoryService.getCategories();

        expect(apiClient.get).toHaveBeenCalledWith('/categories', {
          params: undefined,
        });
        expect(result).toEqual(mockCategories);
      });

      it('fetches active categories only when specified', async () => {
        const mockCategories: Category[] = [
          {
            id: 1,
            name: 'Active Category',
            parent_id: null,
            path: 'Active Category',
          },
        ];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockCategories,
        });

        const result = await categoryService.getCategories(true);

        expect(apiClient.get).toHaveBeenCalledWith('/categories', {
          params: { active_only: true },
        });
        expect(result).toEqual(mockCategories);
      });

      it('handles getCategories API errors', async () => {
        const error = new Error('Fetch error');

        (apiClient.get as unknown).mockRejectedValue(error);

        await categoryService.getCategories();

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'getCategories',
          defaultMessage: 'Failed to fetch categories.',
        });
      });
    });

    describe('getCategory', () => {
      it('fetches a single category by ID', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'Single Category',
          parent_id: null,
          path: 'Single Category',
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await categoryService.getCategory(1);

        expect(apiClient.get).toHaveBeenCalledWith('/categories/1');
        expect(result).toEqual(mockCategory);
      });

      it('handles getCategory API errors', async () => {
        const error = new Error('Category not found');

        (apiClient.get as unknown).mockRejectedValue(error);

        await categoryService.getCategory(999);

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'getCategory',
          defaultMessage: 'Failed to fetch category.',
        });
      });
    });

    describe('createCategory', () => {
      it('creates a new category', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'New Category',
          parent_id: null,
          path: 'New Category',
        };

        const categoryData = {
          name: 'New Category',
          parent_id: null,
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await categoryService.createCategory(categoryData);

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories',
          categoryData,
        );
        expect(result).toEqual(mockCategory);
      });
    });

    describe('updateCategory', () => {
      it('updates an existing category', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'Updated Category',
          parent_id: null,
          path: 'Updated Category',
        };

        const updateData = { name: 'Updated Category' };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await categoryService.updateCategory(1, updateData);

        expect(apiClient.put).toHaveBeenCalledWith('/categories/1', updateData);
        expect(result).toEqual(mockCategory);
      });
    });

    describe('deleteCategory', () => {
      it('deletes a category', async () => {
        (apiClient.delete as unknown).mockResolvedValue({});

        const result = await categoryService.deleteCategory(1);

        expect(apiClient.delete).toHaveBeenCalledWith('/categories/1');
        expect(result).toBeUndefined();
      });
    });

    describe('suggestCategory', () => {
      it('gets category suggestions', async () => {
        const mockSuggestion = {
          suggested_category: 'Food & Dining',
          confidence: 0.95,
          reasons: ['Transaction description matches food keywords'],
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockSuggestion,
        });

        const result = await categoryService.suggestCategory(
          'Restaurant meal',
          25.5,
        );

        expect(apiClient.post).toHaveBeenCalledWith('/categories/suggest', {
          description: 'Restaurant meal',
          amount: 25.5,
        });
        expect(result).toEqual(mockSuggestion);
      });

      it('handles suggestCategory API errors', async () => {
        const error = new Error('Suggestion failed');

        (apiClient.post as unknown).mockRejectedValue(error);

        await categoryService.suggestCategory('Test', 100);

        expect(handleApiError).toHaveBeenCalledWith(error, {
          context: 'suggestCategory',
          defaultMessage: 'Failed to suggest category.',
        });
      });
    });

    describe('getCategoryStats', () => {
      it('fetches category statistics without date filter', async () => {
        const mockStats = [
          { category: 'Food', count: 25, total_amount: 500.0 },
          { category: 'Transport', count: 10, total_amount: 200.0 },
        ];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockStats,
        });

        await categoryService.getCategoryStats();

        expect(apiClient.get).toHaveBeenCalledWith('/categories/stats', {
          params: undefined,
        });
      });

      it('fetches category statistics with date filter', async () => {
        const mockStats = [{ category: 'Food', count: 5, total_amount: 100.0 }];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockStats,
        });

        await categoryService.getCategoryStats('2024-01-01', '2024-01-31');

        expect(apiClient.get).toHaveBeenCalledWith('/categories/stats', {
          params: { date_from: '2024-01-01', date_to: '2024-01-31' },
        });
      });
    });

    describe('bulkCategorizeTransactions', () => {
      it('bulk categorizes transactions', async () => {
        const mockResult = {
          updated_count: 3,
          transactions: [1, 2, 3],
        };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.bulkCategorizeTransactions(
          [1, 2, 3],
          5,
        );

        expect(apiClient.put).toHaveBeenCalledWith(
          '/categories/bulk-categorize',
          {
            transaction_ids: [1, 2, 3],
            category_id: 5,
          },
        );
        expect(result).toEqual(mockResult);
      });

      it('handles empty transaction list', async () => {
        const mockResult = { updated_count: 0, transactions: [] };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.bulkCategorizeTransactions([], 5);

        expect(apiClient.put).toHaveBeenCalledWith(
          '/categories/bulk-categorize',
          {
            transaction_ids: [],
            category_id: 5,
          },
        );
        expect(result).toEqual(mockResult);
      });
    });
  });

  describe('GL Code Management Methods', () => {
    describe('validateGLCode', () => {
      it('validates a GL code successfully', async () => {
        const mockValidation = {
          is_valid: true,
          gl_code: '4001',
          account_name: 'Revenue',
          account_type: 'Income',
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockValidation,
        });

        const result = await categoryService.validateGLCode('4001');

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories/validate-gl-code',
          {
            gl_code: '4001',
          },
        );
        expect(result).toEqual(mockValidation);
      });

      it('handles invalid GL code', async () => {
        const mockValidation = {
          is_valid: false,
          gl_code: '9999',
          error: 'GL code not found',
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockValidation,
        });

        const result = await categoryService.validateGLCode('9999');

        expect(result).toEqual(mockValidation);
      });
    });

    describe('suggestGLCodes', () => {
      it('gets GL code suggestions', async () => {
        const mockSuggestions = [
          {
            gl_code: '6001',
            account_name: 'Office Supplies',
            account_type: 'Expense',
            confidence: 0.9,
          },
          {
            gl_code: '6002',
            account_name: 'Office Equipment',
            account_type: 'Expense',
            confidence: 0.8,
          },
        ];

        (apiClient.post as unknown).mockResolvedValue({
          data: mockSuggestions,
        });

        const result = await categoryService.suggestGLCodes(
          'Office Supplies',
          'Business > Office > Supplies',
          'Expense',
        );

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories/suggest-gl-codes',
          {
            category_name: 'Office Supplies',
            category_path: 'Business > Office > Supplies',
            account_type: 'Expense',
          },
        );
        expect(result).toEqual(mockSuggestions);
      });

      it('handles suggestions without account type', async () => {
        const mockSuggestions = [
          {
            gl_code: '5001',
            account_name: 'General Expense',
            confidence: 0.7,
          },
        ];

        (apiClient.post as unknown).mockResolvedValue({
          data: mockSuggestions,
        });

        const result = await categoryService.suggestGLCodes(
          'Miscellaneous',
          'Other > Miscellaneous',
        );

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories/suggest-gl-codes',
          {
            category_name: 'Miscellaneous',
            category_path: 'Other > Miscellaneous',
            account_type: undefined,
          },
        );
        expect(result).toEqual(mockSuggestions);
      });
    });

    describe('updateGLCodeMapping', () => {
      it('updates GL code mapping successfully', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'Office Supplies',
          parent_id: null,
          path: 'Business > Office Supplies',
        };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await categoryService.updateGLCodeMapping(
          1,
          '6001',
          'Office Supplies',
          'Expense',
        );

        expect(apiClient.put).toHaveBeenCalledWith('/categories/1/gl-mapping', {
          gl_code: '6001',
          gl_account_name: 'Office Supplies',
          gl_account_type: 'Expense',
        });
        expect(result).toEqual(mockCategory);
      });

      it('clears GL code mapping when no values provided', async () => {
        const mockCategory: Category = {
          id: 1,
          name: 'Office Supplies',
          parent_id: null,
          path: 'Business > Office Supplies',
        };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockCategory,
        });

        const result = await categoryService.updateGLCodeMapping(1);

        expect(apiClient.put).toHaveBeenCalledWith('/categories/1/gl-mapping', {
          gl_code: undefined,
          gl_account_name: undefined,
          gl_account_type: undefined,
        });
        expect(result).toEqual(mockCategory);
      });
    });

    describe('getGLCodeAnalytics', () => {
      it('fetches GL code analytics', async () => {
        const mockAnalytics = {
          total_categories: 25,
          mapped_categories: 20,
          unmapped_categories: 5,
          coverage_percentage: 80,
          account_types: {
            Income: 5,
            Expense: 15,
          },
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: mockAnalytics,
        });

        const result = await categoryService.getGLCodeAnalytics();

        expect(apiClient.get).toHaveBeenCalledWith('/categories/gl-analytics');
        expect(result).toEqual(mockAnalytics);
      });
    });

    describe('autoAssignGLCodes', () => {
      it('performs dry run auto-assignment by default', async () => {
        const mockResult = {
          assignments: [
            { category_id: 1, suggested_gl_code: '6001', confidence: 0.9 },
            { category_id: 2, suggested_gl_code: '6002', confidence: 0.8 },
          ],
          total_assignments: 2,
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.autoAssignGLCodes();

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories/auto-assign-gl-codes',
          {
            dry_run: true,
          },
        );
        expect(result).toEqual(mockResult);
      });

      it('performs actual assignment when dry_run is false', async () => {
        const mockResult = {
          assignments: [],
          updated_count: 2,
        };

        (apiClient.post as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.autoAssignGLCodes(false);

        expect(apiClient.post).toHaveBeenCalledWith(
          '/categories/auto-assign-gl-codes',
          {
            dry_run: false,
          },
        );
        expect(result).toEqual(mockResult);
      });
    });

    describe('bulkUpdateGLMappings', () => {
      it('bulk updates GL mappings', async () => {
        const mappings = [
          {
            category_id: 1,
            gl_code: '6001',
            gl_account_name: 'Office Supplies',
            gl_account_type: 'Expense',
          },
          {
            category_id: 2,
            gl_code: '6002',
            gl_account_name: 'Office Equipment',
            gl_account_type: 'Expense',
          },
        ];

        const mockResult = {
          updated_count: 2,
          updated_categories: [1, 2],
        };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.bulkUpdateGLMappings(mappings);

        expect(apiClient.put).toHaveBeenCalledWith(
          '/categories/bulk-gl-mappings',
          {
            mappings,
          },
        );
        expect(result).toEqual(mockResult);
      });

      it('handles empty mappings array', async () => {
        const mockResult = { updated_count: 0, updated_categories: [] };

        (apiClient.put as unknown).mockResolvedValue({
          data: mockResult,
        });

        const result = await categoryService.bulkUpdateGLMappings([]);

        expect(apiClient.put).toHaveBeenCalledWith(
          '/categories/bulk-gl-mappings',
          {
            mappings: [],
          },
        );
        expect(result).toEqual(mockResult);
      });
    });

    describe('exportGLMappings', () => {
      it('exports GL mappings in CSV format by default', async () => {
        const mockCSV =
          'Category,GL Code,Account Name,Account Type\nOffice Supplies,6001,Office Supplies,Expense';

        (apiClient.get as unknown).mockResolvedValue({
          data: mockCSV,
        });

        const result = await categoryService.exportGLMappings();

        expect(apiClient.get).toHaveBeenCalledWith(
          '/categories/export-gl-mappings?format=csv',
        );
        expect(result).toEqual(mockCSV);
      });

      it('exports GL mappings in specified format', async () => {
        const mockJSON =
          '{"mappings": [{"category": "Office Supplies", "gl_code": "6001"}]}';

        (apiClient.get as unknown).mockResolvedValue({
          data: mockJSON,
        });

        const result = await categoryService.exportGLMappings('json');

        expect(apiClient.get).toHaveBeenCalledWith(
          '/categories/export-gl-mappings?format=json',
        );
        expect(result).toEqual(mockJSON);
      });
    });
  });

  describe('Error Handling Across All Methods', () => {
    it('handles network errors consistently', async () => {
      const networkError = new Error('Network unavailable');

      (apiClient.get as unknown).mockRejectedValue(networkError);
      (apiClient.post as unknown).mockRejectedValue(networkError);
      (apiClient.put as unknown).mockRejectedValue(networkError);
      (apiClient.delete as unknown).mockRejectedValue(networkError);

      const methods = [
        () => categoryService.getCategories(),
        () => categoryService.createCategory({}),
        () => categoryService.updateCategory(1, {}),
        () => categoryService.deleteCategory(1),
        () => categoryService.validateGLCode('6001'),
        () => categoryService.getGLCodeAnalytics(),
      ];

      for (const method of methods) {
        const result = await method();
        expect(result).toHaveProperty('isApiError', true);
        expect(result).toHaveProperty('originalError', networkError);
      }
    });

    it('maintains error context across all methods', async () => {
      const error = new Error('Server error');
      (apiClient.get as unknown).mockRejectedValue(error);

      await categoryService.getCategories();
      await categoryService.getCategory(1);
      await categoryService.getCategoryStats();
      await categoryService.getGLCodeAnalytics();

      const calls = (handleApiError as unknown).mock.calls;
      expect(calls).toHaveLength(4);
      expect(calls[0][1].context).toBe('getCategories');
      expect(calls[1][1].context).toBe('getCategory');
      expect(calls[2][1].context).toBe('getCategoryStats');
      expect(calls[3][1].context).toBe('getGLCodeAnalytics');
    });
  });

  describe('Edge Cases and Performance', () => {
    it('handles large category lists efficiently', async () => {
      const largeList = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `Category ${i + 1}`,
        parent_id: null,
        path: `Category ${i + 1}`,
      }));

      (apiClient.get as unknown).mockResolvedValue({
        data: largeList,
      });

      const startTime = performance.now();
      const result = await categoryService.getCategories();
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100);
      expect(result).toHaveLength(1000);
    });

    it('handles concurrent API calls', async () => {
      const mockCategory: Category = {
        id: 1,
        name: 'Test Category',
        parent_id: null,
        path: 'Test Category',
      };

      (apiClient.get as unknown).mockResolvedValue({ data: mockCategory });

      const promises = Array.from({ length: 10 }, () =>
        categoryService.getCategory(1),
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result).toEqual(mockCategory);
      });
    });

    it('handles malformed API responses gracefully', async () => {
      (apiClient.get as unknown).mockResolvedValue({
        data: null,
      });

      const result = await categoryService.getCategories();

      expect(result).toBeNull();
    });
  });
});
