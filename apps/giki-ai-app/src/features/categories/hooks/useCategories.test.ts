/**
 * useCategories Hook Tests
 * Critical hook tests for production category state management
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useCategories } from './useCategories';
import type { Category } from '@/shared/types/categorization';

// Mock the category service
const mockFetchCategories = vi.fn();
const mockAddCategory = vi.fn();
const mockUpdateCategory = vi.fn();
const mockDeleteCategory = vi.fn();

vi.mock('../services/categoryService', () => ({
  fetchCategories: () => mockFetchCategories(),
  addCategory: (payload: unknown) => mockAddCategory(payload),
  updateCategory: (id: number, payload: unknown) =>
    mockUpdateCategory(id, payload),
  deleteCategory: (id: number) => mockDeleteCategory(id),
}));

describe('useCategories Hook - Category State Management', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('returns correct initial state', () => {
      const { result } = renderHook(() => useCategories());

      expect(result?.current?.categories).toEqual([]);
      expect(result?.current?.isLoading).toBe(false);
      expect(result?.current?.error).toBeNull();
      expect(typeof result?.current?.fetchCategories).toBe('function');
      expect(typeof result?.current?.createCategory).toBe('function');
      expect(typeof result?.current?.updateCategory).toBe('function');
      expect(typeof result?.current?.deleteCategory).toBe('function');
    });

    it('maintains stable function references across renders', () => {
      const { result, rerender } = renderHook(() => useCategories());

      const initialFunctions = {
        fetchCategories: result?.current?.fetchCategories,
        createCategory: result?.current?.createCategory,
        updateCategory: result?.current?.updateCategory,
        deleteCategory: result?.current?.deleteCategory,
      };

      rerender();

      expect(result?.current?.fetchCategories).toBe(
        initialFunctions.fetchCategories,
      );
      expect(result?.current?.createCategory).toBe(
        initialFunctions.createCategory,
      );
      expect(result?.current?.updateCategory).toBe(
        initialFunctions.updateCategory,
      );
      expect(result?.current?.deleteCategory).toBe(
        initialFunctions.deleteCategory,
      );
    });
  });

  describe('fetchCategories', () => {
    it('successfully fetches and maps categories', async () => {
      const mockApiCategories = [
        {
          id: 1,
          name: 'Food & Dining',
          path: 'Food & Dining',
          parent_id: null,
          level: 0,
          is_active: true,
          gl_code: '6001',
        },
        {
          id: 2,
          name: 'Transportation',
          path: 'Transportation',
          parent_id: null,
          level: 0,
          active: true,
        },
      ];

      mockFetchCategories.mockResolvedValue(mockApiCategories);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.fetchCategories();
      });

      expect(result?.current?.isLoading).toBe(false);
      expect(result?.current?.error).toBeNull();
      expect(result?.current?.categories).toHaveLength(2);

      expect(result?.current?.categories[0]).toEqual({
        id: 1,
        name: 'Food & Dining',
        path: 'Food & Dining',
        parent_id: undefined,
        level: 0,
        active: true,
        gl_code: '6001',
      });

      expect(result?.current?.categories[1]).toEqual({
        id: 2,
        name: 'Transportation',
        path: 'Transportation',
        parent_id: undefined,
        level: 0,
        active: true,
        gl_code: undefined,
      });
    });

    it('sets loading state during fetch', async () => {
      let resolvePromise: (value: unknown) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockFetchCategories.mockReturnValue(promise);

      const { result } = renderHook(() => useCategories());

      act(() => {
        void result?.current?.fetchCategories();
      });

      expect(result?.current?.isLoading).toBe(true);
      expect(result?.current?.error).toBeNull();

      await act(async () => {
        resolvePromise([]);
      });

      expect(result?.current?.isLoading).toBe(false);
    });

    it('handles API error responses', async () => {
      const errorResponse = {
        error: true,
        message: 'Failed to fetch categories from server',
      };

      mockFetchCategories.mockResolvedValue(errorResponse);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.fetchCategories();
      });

      expect(result?.current?.isLoading).toBe(false);
      expect(result?.current?.error).toBe(
        'Failed to fetch categories from server',
      );
      expect(result?.current?.categories).toEqual([]);
      expect(console.error).toHaveBeenCalled();
    });

    it('handles network errors', async () => {
      const networkError = new Error('Network connection failed');
      mockFetchCategories.mockRejectedValue(networkError);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.fetchCategories();
      });

      expect(result?.current?.isLoading).toBe(false);
      expect(result?.current?.error).toBe('Network connection failed');
      expect(result?.current?.categories).toEqual([]);
    });

    it('maps categories with missing fields correctly', async () => {
      const mockApiCategories = [
        {
          id: 1,
          name: 'Basic Category',
          // Missing optional fields
        },
        {
          id: 2,
          name: 'Category with nulls',
          path: null,
          parent_id: null,
          level: null,
          is_active: false,
          gl_code: null,
        },
      ];

      mockFetchCategories.mockResolvedValue(mockApiCategories);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.fetchCategories();
      });

      expect(result?.current?.categories[0]).toEqual({
        id: 1,
        name: 'Basic Category',
        path: 'Basic Category',
        parent_id: undefined,
        level: 0,
        active: true,
        gl_code: undefined,
      });

      expect(result?.current?.categories[1]).toEqual({
        id: 2,
        name: 'Category with nulls',
        path: 'Category with nulls',
        parent_id: undefined,
        level: 0,
        active: false,
        gl_code: undefined,
      });
    });

    it('clears previous error on successful fetch', async () => {
      const { result } = renderHook(() => useCategories());

      // First call fails
      mockFetchCategories.mockRejectedValue(new Error('First error'));
      await act(async () => {
        await result?.current?.fetchCategories();
      });
      expect(result?.current?.error).toBe('First error');

      // Second call succeeds
      mockFetchCategories.mockResolvedValue([]);
      await act(async () => {
        await result?.current?.fetchCategories();
      });
      expect(result?.current?.error).toBeNull();
    });
  });

  describe('createCategory', () => {
    it('successfully creates a new category', async () => {
      const newCategoryData: Omit<Category, 'id'> = {
        name: 'New Category',
        path: 'New Category',
        parent_id: undefined,
        level: 0,
        active: true,
      };

      const mockApiResponse = {
        id: 3,
        name: 'New Category',
        path: 'New Category',
        parent_id: null,
        level: 0,
        is_active: true,
      };

      mockAddCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      let createdCategory: Category;
      await act(async () => {
        createdCategory =
          await result?.current?.createCategory(newCategoryData);
      });

      expect(mockAddCategory).toHaveBeenCalledWith({
        name: 'New Category',
        parentId: null,
      });

      expect(createdCategory).toEqual({
        id: 3,
        name: 'New Category',
        path: 'New Category',
        parent_id: undefined,
        level: 0,
        active: true,
        gl_code: undefined,
      });

      expect(result?.current?.categories).toContainEqual(createdCategory);
    });

    it('creates category with parent_id', async () => {
      const newCategoryData: Omit<Category, 'id'> = {
        name: 'Child Category',
        path: 'Parent > Child Category',
        parent_id: 1,
        level: 1,
        active: true,
      };

      const mockApiResponse = {
        id: 4,
        name: 'Child Category',
        path: 'Parent > Child Category',
        parent_id: 1,
        level: 1,
        is_active: true,
      };

      mockAddCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.createCategory(newCategoryData);
      });

      expect(mockAddCategory).toHaveBeenCalledWith({
        name: 'Child Category',
        parentId: 1,
      });
    });

    it('handles createCategory API error', async () => {
      const newCategoryData: Omit<Category, 'id'> = {
        name: 'Failed Category',
        path: 'Failed Category',
        parent_id: undefined,
        level: 0,
        active: true,
      };

      const errorResponse = {
        error: true,
        message: 'Category name already exists',
      };

      mockAddCategory.mockResolvedValue(errorResponse);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.createCategory(newCategoryData);
        });
      }).rejects.toThrow('Category name already exists');

      expect(result?.current?.error).toBe('Category name already exists');
      expect(result?.current?.categories).toEqual([]);
    });

    it('handles createCategory network error', async () => {
      const newCategoryData: Omit<Category, 'id'> = {
        name: 'Network Failed Category',
        path: 'Network Failed Category',
        parent_id: undefined,
        level: 0,
        active: true,
      };

      const networkError = new Error('Network timeout');
      mockAddCategory.mockRejectedValue(networkError);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.createCategory(newCategoryData);
        });
      }).rejects.toThrow('Network timeout');

      expect(result?.current?.error).toBe('Network timeout');
    });

    it('maps created category with missing fields', async () => {
      const newCategoryData: Omit<Category, 'id'> = {
        name: 'Minimal Category',
        path: 'Minimal Category',
        parent_id: undefined,
        level: 0,
        active: true,
      };

      const mockApiResponse = {
        id: 5,
        name: 'Minimal Category',
        // Missing optional fields
      };

      mockAddCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      let createdCategory: Category;
      await act(async () => {
        createdCategory =
          await result?.current?.createCategory(newCategoryData);
      });

      expect(createdCategory).toEqual({
        id: 5,
        name: 'Minimal Category',
        path: 'Minimal Category',
        parent_id: undefined,
        level: 0,
        active: true,
        gl_code: undefined,
      });
    });
  });

  describe('updateCategory', () => {
    it('successfully updates an existing category', async () => {
      const initialCategories = [
        {
          id: 1,
          name: 'Original Name',
          path: 'Original Name',
          parent_id: undefined,
          level: 0,
          active: true,
        },
      ];

      const updateData: Partial<Category> = {
        name: 'Updated Name',
        active: false,
      };

      const mockApiResponse = {
        id: 1,
        name: 'Updated Name',
        path: 'Updated Name',
        parent_id: null,
        level: 0,
        is_active: false,
      };

      mockUpdateCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      // Set initial categories
      act(() => {
        result?.current?.categories.push(...(initialCategories as Category[]));
      });

      let updatedCategory: Category;
      await act(async () => {
        updatedCategory = await result?.current?.updateCategory(1, updateData);
      });

      expect(mockUpdateCategory).toHaveBeenCalledWith(1, {
        name: 'Updated Name',
        parent_id: undefined,
        level: undefined,
        active: false,
        gl_code: undefined,
      });

      expect(updatedCategory).toEqual({
        id: 1,
        name: 'Updated Name',
        path: 'Updated Name',
        parent_id: undefined,
        level: 0,
        active: false,
        gl_code: undefined,
      });

      expect(result?.current?.categories[0]).toEqual(updatedCategory);
    });

    it('updates category in state correctly', async () => {
      const initialCategories: Category[] = [
        {
          id: 1,
          name: 'Category 1',
          path: 'Category 1',
          parent_id: undefined,
          level: 0,
          active: true,
        },
        {
          id: 2,
          name: 'Category 2',
          path: 'Category 2',
          parent_id: undefined,
          level: 0,
          active: true,
        },
      ];

      const mockApiResponse = {
        id: 1,
        name: 'Updated Category 1',
        path: 'Updated Category 1',
        parent_id: null,
        level: 0,
        is_active: true,
      };

      mockUpdateCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      // Set initial state
      act(() => {
        result?.current?.categories.splice(0, 0, ...initialCategories);
      });

      await act(async () => {
        await result?.current?.updateCategory(1, {
          name: 'Updated Category 1',
        });
      });

      expect(result?.current?.categories).toHaveLength(2);
      expect(result?.current?.categories[0].name).toBe('Updated Category 1');
      expect(result?.current?.categories[1].name).toBe('Category 2');
    });

    it('handles updateCategory API error', async () => {
      const errorResponse = {
        error: true,
        message: 'Category update failed',
      };

      mockUpdateCategory.mockResolvedValue(errorResponse);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.updateCategory(1, { name: 'Failed Update' });
        });
      }).rejects.toThrow('Category update failed');

      expect(result?.current?.error).toBe('Category update failed');
    });

    it('handles updateCategory network error', async () => {
      const networkError = new Error('Update network error');
      mockUpdateCategory.mockRejectedValue(networkError);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.updateCategory(1, { name: 'Network Failed' });
        });
      }).rejects.toThrow('Update network error');

      expect(result?.current?.error).toBe('Update network error');
    });

    it('sends only provided update fields to API', async () => {
      const partialUpdate: Partial<Category> = {
        name: 'New Name Only',
      };

      const mockApiResponse = {
        id: 1,
        name: 'New Name Only',
        path: 'New Name Only',
        parent_id: null,
        level: 0,
        is_active: true,
      };

      mockUpdateCategory.mockResolvedValue(mockApiResponse);

      const { result } = renderHook(() => useCategories());

      await act(async () => {
        await result?.current?.updateCategory(1, partialUpdate);
      });

      expect(mockUpdateCategory).toHaveBeenCalledWith(1, {
        name: 'New Name Only',
        parent_id: undefined,
        level: undefined,
        active: undefined,
        gl_code: undefined,
      });
    });
  });

  describe('deleteCategory', () => {
    it('successfully deletes a category', async () => {
      const initialCategories: Category[] = [
        {
          id: 1,
          name: 'Category 1',
          path: 'Category 1',
          parent_id: undefined,
          level: 0,
          active: true,
        },
        {
          id: 2,
          name: 'Category 2',
          path: 'Category 2',
          parent_id: undefined,
          level: 0,
          active: true,
        },
      ];

      mockDeleteCategory.mockResolvedValue(undefined);

      const { result } = renderHook(() => useCategories());

      // Set initial state
      act(() => {
        result?.current?.categories.splice(0, 0, ...initialCategories);
      });

      await act(async () => {
        await result?.current?.deleteCategory(1);
      });

      expect(mockDeleteCategory).toHaveBeenCalledWith(1);
      expect(result?.current?.categories).toHaveLength(1);
      expect(result?.current?.categories[0].id).toBe(2);
    });

    it('handles deleteCategory API error', async () => {
      const errorResponse = {
        error: true,
        message: 'Cannot delete category with children',
      };

      mockDeleteCategory.mockResolvedValue(errorResponse);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.deleteCategory(1);
        });
      }).rejects.toThrow('Cannot delete category with children');

      expect(result?.current?.error).toBe(
        'Cannot delete category with children',
      );
    });

    it('handles deleteCategory network error', async () => {
      const networkError = new Error('Delete network error');
      mockDeleteCategory.mockRejectedValue(networkError);

      const { result } = renderHook(() => useCategories());

      await expect(async () => {
        await act(async () => {
          await result?.current?.deleteCategory(1);
        });
      }).rejects.toThrow('Delete network error');

      expect(result?.current?.error).toBe('Delete network error');
    });

    it('does not modify state if category not found', async () => {
      const initialCategories: Category[] = [
        {
          id: 1,
          name: 'Category 1',
          path: 'Category 1',
          parent_id: undefined,
          level: 0,
          active: true,
        },
      ];

      mockDeleteCategory.mockResolvedValue(undefined);

      const { result } = renderHook(() => useCategories());

      // Set initial state
      act(() => {
        result?.current?.categories.splice(0, 0, ...initialCategories);
      });

      await act(async () => {
        await result?.current?.deleteCategory(999); // Non-existent ID
      });

      expect(result?.current?.categories).toHaveLength(1);
      expect(result?.current?.categories[0].id).toBe(1);
    });
  });

  describe('Error Handling and State Management', () => {
    it('maintains separate error states for different operations', async () => {
      const { result } = renderHook(() => useCategories());

      // Fetch error
      mockFetchCategories.mockRejectedValue(new Error('Fetch error'));
      await act(async () => {
        await result?.current?.fetchCategories();
      });
      expect(result?.current?.error).toBe('Fetch error');

      // Create error should update error state
      mockAddCategory.mockRejectedValue(new Error('Create error'));
      await expect(async () => {
        await act(async () => {
          await result?.current?.createCategory({
            name: 'Test',
            path: 'Test',
            parent_id: undefined,
            level: 0,
            active: true,
          });
        });
      }).rejects.toThrow('Create error');
      expect(result?.current?.error).toBe('Create error');
    });

    it('handles concurrent operations', async () => {
      const { result } = renderHook(() => useCategories());

      mockFetchCategories.mockResolvedValue([]);
      mockAddCategory.mockResolvedValue({
        id: 1,
        name: 'Test Category',
        path: 'Test Category',
        parent_id: null,
        level: 0,
        is_active: true,
      });

      const promises = [
        act(() => result?.current?.fetchCategories()),
        act(() =>
          result?.current?.createCategory({
            name: 'Test Category',
            path: 'Test Category',
            parent_id: undefined,
            level: 0,
            active: true,
          }),
        ),
      ];

      await Promise.all(promises);

      expect(mockFetchCategories).toHaveBeenCalled();
      expect(mockAddCategory).toHaveBeenCalled();
    });

    it('preserves categories state across errors', async () => {
      const initialCategories: Category[] = [
        {
          id: 1,
          name: 'Existing Category',
          path: 'Existing Category',
          parent_id: undefined,
          level: 0,
          active: true,
        },
      ];

      const { result } = renderHook(() => useCategories());

      // Set initial state
      mockFetchCategories.mockResolvedValue(initialCategories);
      await act(async () => {
        await result?.current?.fetchCategories();
      });
      expect(result?.current?.categories).toHaveLength(1);

      // Failed operation should not clear existing categories
      mockAddCategory.mockRejectedValue(new Error('Create failed'));
      await expect(async () => {
        await act(async () => {
          await result?.current?.createCategory({
            name: 'Failed Category',
            path: 'Failed Category',
            parent_id: undefined,
            level: 0,
            active: true,
          });
        });
      }).rejects.toThrow('Create failed');

      expect(result?.current?.categories).toHaveLength(1);
      expect(result?.current?.categories[0].name).toBe('Existing Category');
    });
  });

  describe('Performance and Memory', () => {
    it('handles large numbers of categories efficiently', async () => {
      const largeList = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `Category ${i + 1}`,
        path: `Category ${i + 1}`,
        parent_id: null,
        level: 0,
        is_active: true,
      }));

      mockFetchCategories.mockResolvedValue(largeList);

      const { result } = renderHook(() => useCategories());

      const startTime = performance.now();
      await act(async () => {
        await result?.current?.fetchCategories();
      });
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(200);
      expect(result?.current?.categories).toHaveLength(1000);
    });

    it('cleans up properly on unmount', () => {
      const { unmount } = renderHook(() => useCategories());

      expect(() => unmount()).not.toThrow();
    });

    it('handles rapid state updates without memory leaks', async () => {
      const { result } = renderHook(() => useCategories());

      mockAddCategory.mockResolvedValue({
        id: 1,
        name: 'Test',
        path: 'Test',
        parent_id: null,
        level: 0,
        is_active: true,
      });

      // Rapid operations
      const operations = Array.from({ length: 50 }, async (_, i) => {
        return act(async () => {
          await result?.current?.createCategory({
            name: `Category ${i}`,
            path: `Category ${i}`,
            parent_id: undefined,
            level: 0,
            active: true,
          });
        });
      });

      await Promise.all(operations);

      // Should handle all operations without issues
      expect(result?.current?.categories).toHaveLength(50);
    });
  });
});
