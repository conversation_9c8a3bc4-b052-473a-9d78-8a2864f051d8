import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { categoryService } from '@/features/categories/services/categoryService';
import type { GLCodeAnalytics } from '@/shared/types/category';

export const GLCodeAnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<GLCodeAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadAnalytics = async () => {
    setIsLoading(true);
    const result = await categoryService.getGLCodeAnalytics();

    if ('overview' in result) {
      setAnalytics(result);
    }

    setIsLoading(false);
  };

  const exportMappings = async (format: string = 'csv') => {
    const result = await categoryService.exportGLMappings(format);

    if (typeof result === 'string') {
      // Create download
      const blob = new Blob([result], { type: 'text/csv' });
      const url = window?.URL?.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `gl_mappings.${format}`;
      a.click();
      window?.URL?.revokeObjectURL(url);
    }
  };

  useEffect(() => {
    void loadAnalytics();
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6 overflow-hidden">
          <div className="text-center">Loading analytics...</div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="p-6 overflow-hidden">
          <div className="text-center">Failed to load analytics</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="truncatelabel">Total Categories</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">
              {analytics?.overview?.total_categories}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="truncatelabel">With GL Codes</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">
              {analytics?.overview?.categories_with_gl_codes}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="truncatelabel">Coverage</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">
              {analytics?.overview?.coverage_percentage.toFixed(1)}%
            </div>
            <Progress
              value={analytics?.overview?.coverage_percentage}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="truncatelabel">Missing Codes</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">
              {analytics?.overview?.missing_gl_codes}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Account Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Account Type Distribution</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(analytics.account_type_distribution).map(
              ([type, count]) => (
                <div key={type} className="text-center">
                  <div className="text-card-foreground-title">{count}</div>
                  <Badge variant="outline" className="max-w-[150px] truncate">
                    {type}
                  </Badge>
                </div>
              ),
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="space-y-2">
            {analytics?.recommendations?.map((recommendation, index) => (
              <Alert key={index}>
                <AlertDescription>{recommendation}</AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export GL Mappings</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap gap-2">
            <Button
              className="max-w-full"
              onClick={() => void exportMappings('csv')}
            >
              Export CSV
            </Button>
            <Button
              className="max-w-full"
              onClick={() => void exportMappings('json')}
              variant="outline"
            >
              Export JSON
            </Button>
            <Button
              className="max-w-full"
              onClick={() => void loadAnalytics()}
              variant="outline"
            >
              Refresh Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GLCodeAnalyticsDashboard;
