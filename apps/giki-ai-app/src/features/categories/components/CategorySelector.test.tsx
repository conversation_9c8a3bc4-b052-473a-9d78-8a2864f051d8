/**
 * CategorySelector Component Tests
 * Critical categorization workflow component tests for transaction categorization
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import { CategorySelector } from './CategorySelector';
import type { Category } from '@/shared/types/categorization';

describe('CategorySelector - Financial Categorization Component', () => {
  const mockOnSelectCategory = vi.fn();

  // Mock hierarchical category data
  const mockCategories: Category[] = [
    {
      id: 1,
      name: 'Dining',
      path: 'Dining',
      parent_id: null,
      level: 1,
      gl_code: '6100',
      gl_account_name: 'Meals & Entertainment',
      gl_account_type: 'Expense',
      children: [
        {
          id: 2,
          name: 'Coffee',
          path: 'Dining > Coffee',
          parent_id: 1,
          level: 2,
          gl_code: '6101',
          gl_account_name: 'Coffee Expenses',
          gl_account_type: 'Expense',
          children: [],
        },
        {
          id: 3,
          name: 'Restaurant',
          path: 'Dining > Restaurant',
          parent_id: 1,
          level: 2,
          gl_code: '6102',
          gl_account_name: 'Restaurant Expenses',
          gl_account_type: 'Expense',
          children: [],
        },
      ],
    },
    {
      id: 4,
      name: 'Transportation',
      path: 'Transportation',
      parent_id: null,
      level: 1,
      gl_code: '6200',
      gl_account_name: 'Transportation',
      gl_account_type: 'Expense',
      children: [
        {
          id: 5,
          name: 'Taxi',
          path: 'Transportation > Taxi',
          parent_id: 4,
          level: 2,
          gl_code: '6201',
          gl_account_name: 'Taxi Expenses',
          gl_account_type: 'Expense',
          children: [],
        },
      ],
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders category selector with placeholder', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    expect(screen.getByText('-- Select Category --')).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('renders custom placeholder text', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
        placeholder="Choose a category"
      />,
    );

    expect(screen.getByText('Choose a category')).toBeInTheDocument();
  });

  it('displays all categories in hierarchical order', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Open the select dropdown
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    // Check that all categories are listed
    await waitFor(() => {
      expect(screen.getByText('Dining')).toBeInTheDocument();
      expect(screen.getByText('Dining > Coffee')).toBeInTheDocument();
      expect(screen.getByText('Dining > Restaurant')).toBeInTheDocument();
      expect(screen.getByText('Transportation')).toBeInTheDocument();
      expect(screen.getByText('Transportation > Taxi')).toBeInTheDocument();
    });
  });

  it('handles category selection correctly', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Open dropdown and select a category
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    const coffeeOption = screen.getByText('Dining > Coffee');
    await user.click(coffeeOption);

    expect(mockOnSelectCategory).toHaveBeenCalledWith(2); // ID of Coffee category
  });

  it('displays selected category correctly', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={2}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Should display the selected category path
    expect(screen.getByDisplayValue('Dining > Coffee')).toBeInTheDocument();
  });

  it('handles clearing selection', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={2}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Open dropdown and select placeholder to clear
    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    const placeholderOption = screen.getByText('-- Select Category --');
    await user.click(placeholderOption);

    expect(mockOnSelectCategory).toHaveBeenCalledWith(null);
  });

  it('handles categories without path property', () => {
    const categoriesWithoutPath: Category[] = [
      {
        id: 1,
        name: 'Simple Category',
        path: 'Simple Category',
        parent_id: null,
        level: 1,
        gl_code: '6000',
        gl_account_name: 'Simple',
        gl_account_type: 'Expense',
        children: [],
      },
    ];

    render(
      <CategorySelector
        categories={categoriesWithoutPath}
        selectedCategoryId={1}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Should fall back to name when path is not available
    expect(screen.getByDisplayValue('Simple Category')).toBeInTheDocument();
  });

  it('handles empty categories array', () => {
    render(
      <CategorySelector
        categories={[]}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    expect(screen.getByText('-- Select Category --')).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('handles invalid selected category ID', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={999} // Non-existent ID
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    // Should show placeholder since category doesn't exist
    expect(screen.getByText('-- Select Category --')).toBeInTheDocument();
  });

  it('applies custom className correctly', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
        className="custom-category-selector"
      />,
    );

    const trigger = screen.getByRole('combobox');
    expect(trigger).toHaveClass('custom-category-selector');
  });

  it('handles deep hierarchical nesting', async () => {
    const deepCategories: Category[] = [
      {
        id: 1,
        name: 'Level 1',
        path: 'Level 1',
        parent_id: null,
        level: 1,
        gl_code: '1000',
        gl_account_name: 'Level 1',
        gl_account_type: 'Expense',
        children: [
          {
            id: 2,
            name: 'Level 2',
            path: 'Level 1 > Level 2',
            parent_id: 1,
            level: 2,
            gl_code: '1001',
            gl_account_name: 'Level 2',
            gl_account_type: 'Expense',
            children: [
              {
                id: 3,
                name: 'Level 3',
                path: 'Level 1 > Level 2 > Level 3',
                parent_id: 2,
                level: 3,
                gl_code: '1002',
                gl_account_name: 'Level 3',
                gl_account_type: 'Expense',
                children: [],
              },
            ],
          },
        ],
      },
    ];

    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={deepCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    await waitFor(() => {
      expect(screen.getByText('Level 1')).toBeInTheDocument();
      expect(screen.getByText('Level 1 > Level 2')).toBeInTheDocument();
      expect(
        screen.getByText('Level 1 > Level 2 > Level 3'),
      ).toBeInTheDocument();
    });
  });

  it('handles string to number conversion correctly', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    const transportationOption = screen.getByText('Transportation');
    await user.click(transportationOption);

    // Should convert string "4" to number 4
    expect(mockOnSelectCategory).toHaveBeenCalledWith(4);
    expect(mockOnSelectCategory).toHaveBeenCalledWith(expect.any(Number));
  });

  it('handles rapid selection changes', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    const trigger = screen.getByRole('combobox');

    // Multiple rapid selections
    await user.click(trigger);
    await user.click(screen.getByText('Dining'));

    await user.click(trigger);
    await user.click(screen.getByText('Transportation'));

    expect(mockOnSelectCategory).toHaveBeenCalledTimes(2);
    expect(mockOnSelectCategory).toHaveBeenNthCalledWith(1, 1);
    expect(mockOnSelectCategory).toHaveBeenNthCalledWith(2, 4);
  });

  it('maintains selection state across re-renders', () => {
    const { rerender } = render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={2}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    expect(screen.getByDisplayValue('Dining > Coffee')).toBeInTheDocument();

    // Re-render with same props
    rerender(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={2}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    expect(screen.getByDisplayValue('Dining > Coffee')).toBeInTheDocument();
  });

  it('meets accessibility standards', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    const combobox = screen.getByRole('combobox');
    expect(combobox).toBeInTheDocument();
    expect(combobox).toHaveAttribute('aria-expanded');

    // Test keyboard navigation
    combobox.focus();
    await user.keyboard('{Enter}');

    // Should open dropdown
    await waitFor(() => {
      expect(screen.getByText('Dining')).toBeInTheDocument();
    });

    // Test escape key
    await user.keyboard('{Escape}');
    await waitFor(() => {
      expect(combobox).toHaveAttribute('aria-expanded', 'false');
    });
  });

  it('handles edge case with undefined selectedCategoryId', () => {
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={undefined}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    expect(screen.getByText('-- Select Category --')).toBeInTheDocument();
  });

  it('preserves category order in hierarchical display', async () => {
    const user = userEvent.setup();
    render(
      <CategorySelector
        categories={mockCategories}
        selectedCategoryId={null}
        onSelectCategory={mockOnSelectCategory}
      />,
    );

    const trigger = screen.getByRole('combobox');
    await user.click(trigger);

    // Get all option elements and verify they maintain hierarchy order
    await waitFor(() => {
      const options = screen.getAllByRole('option');
      const optionTexts = options.map((option) => option.textContent);

      // Verify parent appears before children
      const diningIndex = optionTexts.findIndex((text) => text === 'Dining');
      const coffeeIndex = optionTexts.findIndex(
        (text) => text === 'Dining > Coffee',
      );
      const restaurantIndex = optionTexts.findIndex(
        (text) => text === 'Dining > Restaurant',
      );

      expect(diningIndex).toBeLessThan(coffeeIndex);
      expect(diningIndex).toBeLessThan(restaurantIndex);
      expect(coffeeIndex).toBeLessThan(restaurantIndex);
    });
  });
});
