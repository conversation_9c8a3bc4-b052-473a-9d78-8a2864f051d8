import React, { useState } from 'react';
import { Category } from '@/shared/types/categorization';
// import { Button } from '@/shared/components/ui/button';
// import { Input } from '@/shared/components/ui/input';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import CategoryTreeView from './CategoryTreeView';
import GLCodeManager from './GLCodeManager';
import GLCodeBulkManager from './GLCodeBulkManager';

interface CategoryManagementViewProps {
  categories: Category[];
  onAddCategory: (name: string, parentId: number | null) => Promise<void>;
  onRenameCategory: (categoryId: number, newName: string) => Promise<void>;
  onUpdateCategory: (
    categoryId: number,
    updates: Partial<Category>,
  ) => Promise<void>;
  onDeleteCategory: (categoryId: number) => Promise<void>;
  onMoveCategory?: (
    categoryId: number,
    newParentId: number | null,
  ) => Promise<void>;
}

const CategoryManagementView: React.FC<CategoryManagementViewProps> = ({
  categories,
  onAddCategory,
  onRenameCategory: _onRenameCategory,
  onUpdateCategory,
  onDeleteCategory,
  onMoveCategory,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null,
  );

  return (
    <div className="category-management-view space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Category Management</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <Tabs defaultValue="hierarchy" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="hierarchy">Category Hierarchy</TabsTrigger>
              <TabsTrigger value="gl-codes">GL Code Management</TabsTrigger>
              <TabsTrigger value="bulk-operations">Bulk Operations</TabsTrigger>
            </TabsList>

            <TabsContent value="hierarchy" className="space-y-4">
              <CategoryTreeView
                categories={categories}
                onAddCategory={onAddCategory}
                onUpdateCategory={onUpdateCategory}
                onDeleteCategory={onDeleteCategory}
                onMoveCategory={onMoveCategory}
                selectedCategoryId={selectedCategory?.id}
                onSelectCategory={setSelectedCategory}
                showUsageStats={true}
                enableInlineEditing={true}
                maxDepth={5}
              />
            </TabsContent>

            <TabsContent value="gl-codes" className="space-y-4">
              {selectedCategory ? (
                <GLCodeManager
                  category={selectedCategory}
                  onUpdate={(updatedCategory) => {
                    setSelectedCategory(updatedCategory);
                    void onUpdateCategory(updatedCategory.id, updatedCategory);
                  }}
                />
              ) : (
                <Card>
                  <CardContent className="flex flex-wrap items-center justify-center py-8 overflow-hidden">
                    <p className="truncate text-muted">
                      Select a category from the hierarchy to manage its GL
                      codes
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="bulk-operations" className="space-y-4">
              <GLCodeBulkManager />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default CategoryManagementView;
