import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { categoryService } from '@/features/categories/services/categoryService';
import type { Category } from '@/shared/types/categorization';
import type {
  GLCodeSuggestion,
  GLCodeValidation,
} from '@/shared/types/category';

interface GLCodeManagerProps {
  category: Category;
  onUpdate: (updatedCategory: Category) => void;
}

export const GLCodeManager: React.FC<GLCodeManagerProps> = ({
  category,
  onUpdate,
}) => {
  const [glCode, setGlCode] = useState(category.gl_code || '');
  const [glAccountName, setGlAccountName] = useState(
    category.gl_account_name || '',
  );
  const [glAccountType, setGlAccountType] = useState(
    category.gl_account_type || 'Expense',
  );
  const [validation, setValidation] = useState<GLCodeValidation | null>(null);
  const [suggestions, setSuggestions] = useState<GLCodeSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const validateGLCode = async (code: string) => {
    if (!code) return;

    const result = await categoryService.validateGLCode(code);
    if ('is_valid' in result) {
      setValidation(result);
    }
  };

  const getSuggestions = async () => {
    setIsLoading(true);
    const result = await categoryService.suggestGLCodes(
      category.name,
      category.path,
      glAccountType,
    );

    if (Array.isArray(result)) {
      setSuggestions(result);
    }
    setIsLoading(false);
  };

  const handleSave = async () => {
    setIsLoading(true);
    const result = await categoryService.updateGLCodeMapping(
      category.id,
      glCode,
      glAccountName,
      glAccountType,
    );

    if ('id' in result) {
      onUpdate(result);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (glCode) {
      void validateGLCode(glCode);
    }
  }, [glCode]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>GL Code Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="gl-code">GL Code</Label>
            <Input
              id="gl-code"
              value={glCode}
              onChange={(e) => setGlCode(e?.target?.value)}
              placeholder="e.g., 6001"
            />
            {validation && !validation.is_valid && (
              <Alert className="mt-2">
                <AlertDescription>
                  {validation.errors?.join(', ') || 'Invalid GL code'}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div>
            <Label htmlFor="account-name">Account Name</Label>
            <Input
              id="account-name"
              value={glAccountName}
              onChange={(e) => setGlAccountName(e?.target?.value)}
              placeholder="e.g., Office Supplies"
            />
          </div>

          <div>
            <Label htmlFor="account-type">Account Type</Label>
            <select
              id="account-type"
              value={glAccountType}
              onChange={(e) => setGlAccountType(e?.target?.value)}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="Asset">Asset</option>
              <option value="Liability">Liability</option>
              <option value="Equity">Equity</option>
              <option value="Revenue">Revenue</option>
              <option value="Expense">Expense</option>
            </select>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            className="max-w-full"
            onClick={() => void getSuggestions()}
            disabled={isLoading}
          >
            Get AI Suggestions
          </Button>
          <Button
            className="max-w-full"
            onClick={() => void handleSave()}
            disabled={isLoading}
          >
            Save GL Mapping
          </Button>
        </div>

        {suggestions.length > 0 && (
          <div className="space-y-2">
            <Label>AI Suggestions:</Label>
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="flex flex-wrap items-center gap-2 p-2 border rounded"
              >
                <Badge className="max-w-[150px] truncate">
                  {suggestion.gl_code}
                </Badge>
                <span className="flex flex-wrap-1">
                  {suggestion.gl_account_name}
                </span>
                <Badge variant="secondary" className="max-w-[150px] truncate">
                  {(suggestion.confidence * 100).toFixed(0)}%
                </Badge>
                <Button
                  className="max-w-full"
                  size="sm"
                  onClick={() => {
                    setGlCode(suggestion.gl_code);
                    setGlAccountName(suggestion.gl_account_name);
                    setGlAccountType(suggestion.gl_account_type);
                  }}
                >
                  Apply
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GLCodeManager;
