/**
 * ConfidenceIndicator Component Tests
 * Critical confidence display component tests for production reliability
 */

import { describe, it, expect } from 'vitest';
import { screen } from '@testing-library/react';
import { render } from '../../../test-utils';
import ConfidenceIndicator from './ConfidenceIndicator';

describe('ConfidenceIndicator - Confidence Level Display Component', () => {
  describe('Level-based Display', () => {
    it('renders high confidence level', () => {
      render(<ConfidenceIndicator level="high" />);

      const badge = screen.getByText('High');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-emerald-900',
        'bg-emerald-50',
        'border-emerald-300',
        'font-semibold',
      );
    });

    it('renders medium confidence level', () => {
      render(<ConfidenceIndicator level="medium" />);

      const badge = screen.getByText('Medium');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-amber-900',
        'bg-amber-50',
        'border-amber-300',
        'font-semibold',
      );
    });

    it('renders low confidence level', () => {
      render(<ConfidenceIndicator level="low" />);

      const badge = screen.getByText('Low');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-red-900',
        'bg-red-50',
        'border-red-300',
        'font-semibold',
      );
    });

    it('renders default level when no level provided', () => {
      render(<ConfidenceIndicator />);

      const badge = screen.getByText('Low');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-red-900',
        'bg-red-50',
        'border-red-300',
        'font-semibold',
      );
    });
  });

  describe('Confidence-based Display', () => {
    it('converts high confidence (>= 0.8) to high level', () => {
      render(<ConfidenceIndicator confidence={0.95} />);

      const badge = screen.getByText('High (95%)');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-emerald-900',
        'bg-emerald-50',
        'border-emerald-300',
      );
    });

    it('converts medium confidence (0.6-0.79) to medium level', () => {
      render(<ConfidenceIndicator confidence={0.75} />);

      const badge = screen.getByText('Medium (75%)');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass(
        'text-amber-900',
        'bg-amber-50',
        'border-amber-300',
      );
    });

    it('converts low confidence (< 0.6) to low level', () => {
      render(<ConfidenceIndicator confidence={0.45} />);

      const badge = screen.getByText('Low (45%)');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('text-red-900', 'bg-red-50', 'border-red-300');
    });

    it('handles edge case confidence values', () => {
      // Exactly 0.8 - should be high
      render(<ConfidenceIndicator confidence={0.8} />);
      expect(screen.getByText('High (80%)')).toBeInTheDocument();
    });

    it('handles edge case confidence values at 0.6', () => {
      // Exactly 0.6 - should be medium
      render(<ConfidenceIndicator confidence={0.6} />);
      expect(screen.getByText('Medium (60%)')).toBeInTheDocument();
    });

    it('handles zero confidence', () => {
      render(<ConfidenceIndicator confidence={0} />);

      const badge = screen.getByText('Low (0%)');
      expect(badge).toBeInTheDocument();
    });

    it('handles maximum confidence', () => {
      render(<ConfidenceIndicator confidence={1} />);

      const badge = screen.getByText('High (100%)');
      expect(badge).toBeInTheDocument();
    });
  });

  describe('Value-based Display', () => {
    it('displays value as percentage when provided', () => {
      render(<ConfidenceIndicator level="high" value={0.87} />);

      expect(screen.getByText('High (87%)')).toBeInTheDocument();
    });

    it('prefers value over confidence when both provided', () => {
      render(
        <ConfidenceIndicator confidence={0.5} value={0.9} level="medium" />,
      );

      expect(screen.getByText('Medium (90%)')).toBeInTheDocument();
    });

    it('uses confidence when value is not provided', () => {
      render(<ConfidenceIndicator confidence={0.65} />);

      expect(screen.getByText('Medium (65%)')).toBeInTheDocument();
    });

    it('rounds percentage values correctly', () => {
      render(<ConfidenceIndicator confidence={0.876} />);

      expect(screen.getByText('High (88%)')).toBeInTheDocument();
    });

    it('handles decimal rounding edge cases', () => {
      render(<ConfidenceIndicator confidence={0.555} />);

      expect(screen.getByText('Low (56%)')).toBeInTheDocument();
    });
  });

  describe('Level Priority and Overrides', () => {
    it('uses explicit level over confidence calculation', () => {
      render(<ConfidenceIndicator level="high" confidence={0.3} />);

      expect(screen.getByText('High (30%)')).toBeInTheDocument();
      expect(screen.getByText('High (30%)')).toHaveClass('text-emerald-900');
    });

    it('uses explicit level over value calculation', () => {
      render(<ConfidenceIndicator level="low" value={0.95} />);

      expect(screen.getByText('Low (95%)')).toBeInTheDocument();
      expect(screen.getByText('Low (95%)')).toHaveClass('text-red-900');
    });

    it('calculates level from confidence when level not provided', () => {
      render(<ConfidenceIndicator confidence={0.85} />);

      expect(screen.getByText('High (85%)')).toBeInTheDocument();
      expect(screen.getByText('High (85%)')).toHaveClass('text-emerald-900');
    });
  });

  describe('Styling and Appearance', () => {
    it('applies custom className', () => {
      render(<ConfidenceIndicator level="high" className="custom-class" />);

      const badge = screen.getByText('High');
      expect(badge).toHaveClass('custom-class');
    });

    it('applies correct badge variants', () => {
      const { rerender } = render(<ConfidenceIndicator level="high" />);
      expect(screen.getByText('High')).toBeInTheDocument();

      rerender(<ConfidenceIndicator level="medium" />);
      expect(screen.getByText('Medium')).toBeInTheDocument();

      rerender(<ConfidenceIndicator level="low" />);
      expect(screen.getByText('Low')).toBeInTheDocument();
    });

    it('combines custom className with default styling', () => {
      render(<ConfidenceIndicator level="high" className="extra-padding" />);

      const badge = screen.getByText('High');
      expect(badge).toHaveClass('extra-padding');
      expect(badge).toHaveClass(
        'text-emerald-900',
        'bg-emerald-50',
        'border-emerald-300',
      );
    });

    it('handles undefined className gracefully', () => {
      render(<ConfidenceIndicator level="high" className={undefined} />);

      const badge = screen.getByText('High');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('text-emerald-900');
    });

    it('applies font weight consistently', () => {
      render(<ConfidenceIndicator level="high" />);
      render(<ConfidenceIndicator level="medium" />);
      render(<ConfidenceIndicator level="low" />);

      const badges = screen.getAllByText(/High|Medium|Low/);
      badges.forEach((badge) => {
        expect(badge).toHaveClass('font-semibold');
      });
    });
  });

  describe('Text Formatting', () => {
    it('capitalizes level text correctly', () => {
      render(<ConfidenceIndicator level="high" />);
      render(<ConfidenceIndicator level="medium" />);
      render(<ConfidenceIndicator level="low" />);

      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument();
      expect(screen.getByText('Low')).toBeInTheDocument();
    });

    it('displays percentage in parentheses when value provided', () => {
      render(<ConfidenceIndicator level="high" confidence={0.92} />);

      expect(screen.getByText('High (92%)')).toBeInTheDocument();
    });

    it('omits percentage when no value or confidence provided', () => {
      render(<ConfidenceIndicator level="high" />);

      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.queryByText(/\(\d+%\)/)).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles invalid level values gracefully', () => {
      // @ts-expect-error - Testing runtime behavior with invalid props
      render(<ConfidenceIndicator level="invalid" />);

      const badge = screen.getByText('Invalid');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('text-foreground', 'bg-muted', 'border-border');
    });

    it('handles negative confidence values', () => {
      render(<ConfidenceIndicator confidence={-0.5} />);

      expect(screen.getByText('Low (-50%)')).toBeInTheDocument();
    });

    it('handles confidence values greater than 1', () => {
      render(<ConfidenceIndicator confidence={1.5} />);

      expect(screen.getByText('High (150%)')).toBeInTheDocument();
    });

    it('handles very small confidence values', () => {
      render(<ConfidenceIndicator confidence={0.001} />);

      expect(screen.getByText('Low (0%)')).toBeInTheDocument();
    });

    it('handles very large confidence values', () => {
      render(<ConfidenceIndicator confidence={5.789} />);

      expect(screen.getByText('High (579%)')).toBeInTheDocument();
    });

    it('handles NaN confidence values', () => {
      render(<ConfidenceIndicator confidence={NaN} />);

      expect(screen.getByText('Low (NaN%)')).toBeInTheDocument();
    });

    it('handles Infinity confidence values', () => {
      render(<ConfidenceIndicator confidence={Infinity} />);

      expect(screen.getByText('High (Infinity%)')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('maintains proper color contrast for high confidence', () => {
      render(<ConfidenceIndicator level="high" />);

      const badge = screen.getByText('High');
      expect(badge).toHaveClass('text-emerald-900', 'bg-emerald-50');
    });

    it('maintains proper color contrast for medium confidence', () => {
      render(<ConfidenceIndicator level="medium" />);

      const badge = screen.getByText('Medium');
      expect(badge).toHaveClass('text-amber-900', 'bg-amber-50');
    });

    it('maintains proper color contrast for low confidence', () => {
      render(<ConfidenceIndicator level="low" />);

      const badge = screen.getByText('Low');
      expect(badge).toHaveClass('text-red-900', 'bg-red-50');
    });

    it('uses semantic colors for confidence levels', () => {
      render(<ConfidenceIndicator level="high" />);
      render(<ConfidenceIndicator level="medium" />);
      render(<ConfidenceIndicator level="low" />);

      // Green for high, amber for medium, red for low
      expect(screen.getByText('High')).toHaveClass('text-emerald-900');
      expect(screen.getByText('Medium')).toHaveClass('text-amber-900');
      expect(screen.getByText('Low')).toHaveClass('text-red-900');
    });

    it('provides clear visual hierarchy', () => {
      render(<ConfidenceIndicator level="high" confidence={0.95} />);

      const badge = screen.getByText('High (95%)');
      expect(badge).toHaveClass('font-semibold');
    });
  });

  describe('Performance', () => {
    it('renders efficiently with complex props', () => {
      const startTime = performance.now();

      for (let i = 0; i < 100; i++) {
        render(<ConfidenceIndicator confidence={Math.random()} />);
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(200);
    });

    it('handles frequent prop changes efficiently', () => {
      const { rerender } = render(<ConfidenceIndicator confidence={0.5} />);

      for (let i = 0; i < 20; i++) {
        rerender(<ConfidenceIndicator confidence={i / 20} />);
      }

      expect(screen.getByText(/\d+%/)).toBeInTheDocument();
    });

    it('cleans up properly on unmount', () => {
      const { unmount } = render(<ConfidenceIndicator level="high" />);

      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Prop Combinations', () => {
    it('handles all props provided simultaneously', () => {
      render(
        <ConfidenceIndicator
          level="medium"
          confidence={0.9}
          value={0.7}
          size="lg"
          className="test-class"
        />,
      );

      expect(screen.getByText('Medium (70%)')).toBeInTheDocument();
      expect(screen.getByText('Medium (70%)')).toHaveClass('test-class');
    });

    it('handles minimal props correctly', () => {
      render(<ConfidenceIndicator />);

      expect(screen.getByText('Low')).toBeInTheDocument();
    });

    it('handles only confidence prop', () => {
      render(<ConfidenceIndicator confidence={0.85} />);

      expect(screen.getByText('High (85%)')).toBeInTheDocument();
    });

    it('handles only value prop', () => {
      render(<ConfidenceIndicator value={0.65} />);

      expect(screen.getByText('Low (65%)')).toBeInTheDocument();
    });
  });
});
