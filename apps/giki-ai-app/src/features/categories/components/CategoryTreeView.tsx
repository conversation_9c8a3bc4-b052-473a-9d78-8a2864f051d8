import React, { useState, useCallback, useMemo } from 'react';
import { Category } from '@/shared/types/categorization';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import {
  ChevronDown,
  ChevronRight,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  GripVertical,
  FolderPlus,
  BarChart3,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

// Types

interface CategoryTreeViewProps {
  categories: Category[];
  onAddCategory: (name: string, parentId: number | null) => Promise<void>;
  onUpdateCategory: (
    categoryId: number,
    updates: Partial<Category>,
  ) => Promise<void>;
  onDeleteCategory: (categoryId: number) => Promise<void>;
  onMoveCategory?: (
    categoryId: number,
    newParentId: number | null,
  ) => Promise<void>;
  showUsageStats?: boolean;
  enableInlineEditing?: boolean;
  maxDepth?: number;
  selectedCategoryId?: number;
  onSelectCategory?: (category: Category) => void;
}

interface CategoryNodeProps {
  category: Category;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  showUsageStats: boolean;
  enableInlineEditing: boolean;
  maxDepth: number;
  onToggleExpand: (categoryId: number) => void;
  onSelect: (category: Category) => void;
  onAddChild: (parentId: number) => void;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: number) => void;
  onMove?: (categoryId: number, newParentId: number | null) => void;
}

// Build hierarchical tree from flat array
const buildCategoryTree = (categories: Category[]): Category[] => {
  const categoryMap = new Map<number, Category>();
  const rootCategories: Category[] = [];

  // First pass: create map and initialize children arrays
  categories.forEach((category) => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Second pass: build tree structure
  categories.forEach((category) => {
    const categoryNode = categoryMap.get(category.id);
    if (category.parent_id === null) {
      rootCategories.push(categoryNode);
    } else {
      const parent = category.parent_id
        ? categoryMap.get(category.parent_id)
        : null;
      if (parent) {
        parent?.children?.push(categoryNode);
      }
    }
  });

  return rootCategories;
};

// Individual category node component
const CategoryNode: React.FC<CategoryNodeProps> = ({
  category,
  level,
  isExpanded,
  isSelected,
  showUsageStats,
  enableInlineEditing,
  maxDepth,
  onToggleExpand,
  onSelect,
  onAddChild,
  onEdit,
  onDelete,
  onMove,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const hasChildren = category.children && category?.children?.length > 0;
  const canAddChildren = level < maxDepth;

  const handleDragStart = (e: React.DragEvent) => {
    e?.dataTransfer?.setData('text/plain', category?.id?.toString());
    if (e.dataTransfer) {
      e.dataTransfer.effectAllowed = 'move';
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = 'move';
    }
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const draggedCategoryId = parseInt(e?.dataTransfer?.getData('text/plain'));
    if (draggedCategoryId !== category.id && onMove) {
      onMove(draggedCategoryId, category.id);
    }
  };

  return (
    <div className="relative">
      {/* Main category row */}
      <div
        className={cn(
          'flex items-center gap-2 py-2 px-3 rounded-md transition-colors hover:bg-muted/50 group',
          isSelected && 'bg-primary/10 border border-primary/30',
          isDragOver && 'bg-info/5 border-2 border-blue-300',
        )}
        style={{ marginLeft: `${level * 20}px` }}
        onClick={() => onSelect(category)}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Drag handle */}
        <div
          draggable
          onDragStart={handleDragStart}
          className="opacity-0 group-hover:opacity-100 cursor-grab active:cursor-grabbing transition-opacity"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>

        {/* Expand/collapse button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            if (hasChildren) {
              onToggleExpand(category.id);
            }
          }}
          className="h-6 w-6 p-0 max-w-full"
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )
          ) : (
            <div className="h-4 w-4" />
          )}
        </Button>

        {/* Category name and path */}
        <div className="flex flex-wrap-1 min-w-0">
          <div className="flex flex-wrap items-center gap-2">
            <span className="font-medium truncate">{category.name}</span>
            {category.gl_code && (
              <Badge
                variant="outline"
                className="text-xs max-w-[150px] truncate"
              >
                {category.gl_code}
              </Badge>
            )}
            {showUsageStats && category.transaction_count !== undefined && (
              <Badge
                variant="secondary"
                className="text-xs max-w-[150px] truncate"
              >
                {category.transaction_count} txn
              </Badge>
            )}
          </div>
          <div className="text-xs text-muted-foreground truncate">
            {category.path}
          </div>
        </div>

        {/* Actions menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 max-w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(category)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenuItem>
            {canAddChildren && (
              <DropdownMenuItem onClick={() => onAddChild(category.id)}>
                <FolderPlus className="h-4 w-4 mr-2" />
                Add Subcategory
              </DropdownMenuItem>
            )}
            {showUsageStats && (
              <DropdownMenuItem>
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => onDelete(category.id)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-2">
          {category?.children?.map((child) => (
            <CategoryNode
              key={child.id}
              category={child}
              level={level + 1}
              isExpanded={isExpanded}
              isSelected={isSelected}
              showUsageStats={showUsageStats}
              enableInlineEditing={enableInlineEditing}
              maxDepth={maxDepth}
              onToggleExpand={onToggleExpand}
              onSelect={onSelect}
              onAddChild={onAddChild}
              onEdit={onEdit}
              onDelete={onDelete}
              onMove={onMove}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Main CategoryTreeView component
export const CategoryTreeView: React.FC<CategoryTreeViewProps> = ({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory,
  onMoveCategory,
  showUsageStats = false,
  enableInlineEditing = true,
  maxDepth = 5,
  selectedCategoryId,
  onSelectCategory,
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [addingChildTo, setAddingChildTo] = useState<number | null>(null);
  const [newCategoryName, setNewCategoryName] = useState('');

  // Build hierarchical tree
  const hierarchicalCategories = useMemo(() => {
    return buildCategoryTree(categories);
  }, [categories]);

  const handleToggleExpand = useCallback((categoryId: number) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  }, []);

  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) return;

    try {
      await onAddCategory(newCategoryName.trim(), addingChildTo);
      setNewCategoryName('');
      setAddingChildTo(null);

      // Expand parent if adding child
      if (addingChildTo !== null) {
        setExpandedNodes((prev) => new Set(prev).add(addingChildTo));
      }
    } catch (error) {
      console.error('Failed to add category:', error);
    }
  };

  const handleEditCategory = async (category: Category, newName: string) => {
    if (!newName.trim() || newName === category.name) return;

    try {
      await onUpdateCategory(category.id, { name: newName.trim() });
      setEditingCategory(null);
    } catch (error) {
      console.error('Failed to update category:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: number) => {
    if (
      confirm(
        'Are you sure you want to delete this category? This action cannot be undone.',
      )
    ) {
      try {
        await onDeleteCategory(categoryId);
      } catch (error) {
        console.error('Failed to delete category:', error);
      }
    }
  };

  const renderTreeNodes = (nodes: Category[], level = 0): React.ReactNode => {
    return nodes.map((category) => (
      <CategoryNode
        key={category.id}
        category={category}
        level={level}
        isExpanded={expandedNodes.has(category.id)}
        isSelected={selectedCategoryId === category.id}
        showUsageStats={showUsageStats}
        enableInlineEditing={enableInlineEditing}
        maxDepth={maxDepth}
        onToggleExpand={handleToggleExpand}
        onSelect={onSelectCategory || (() => {})}
        onAddChild={setAddingChildTo}
        onEdit={setEditingCategory}
        onDelete={(id) => void handleDeleteCategory(id)}
        onMove={(id, newParentId) =>
          void (onMoveCategory && onMoveCategory(id, newParentId))
        }
      />
    ));
  };

  return (
    <div className="space-y-4">
      {/* Header with add category button */}
      <div className="flex flex-wrap items-center justify-between">
        <h3 className="text-lg font-semibold">Categories</h3>
        <Button
          onClick={() => setAddingChildTo(null)}
          size="sm"
          className="gap-2 max-w-full"
        >
          <Plus className="h-4 w-4" />
          Add Category
        </Button>
      </div>

      {/* Category tree */}
      <div className="border rounded-lg p-4 min-h-[200px]">
        {hierarchicalCategories.length > 0 ? (
          <div className="space-y-1">
            {renderTreeNodes(hierarchicalCategories)}
          </div>
        ) : (
          <div className="flex flex-wrap flex-col items-center justify-center py-8 text-muted-foreground">
            <FolderPlus className="h-12 w-12 mb-2" />
            <p className="text-sm">No categories yet</p>
            <p className="text-xs text-muted-foreground">
              Add your first category to get started
            </p>
          </div>
        )}
      </div>

      {/* Add Category Dialog */}
      <Dialog
        open={addingChildTo !== null}
        onOpenChange={(open) => !open && setAddingChildTo(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {addingChildTo === null ? 'Add New Category' : 'Add Subcategory'}
            </DialogTitle>
            <DialogDescription>
              {addingChildTo === null
                ? 'Create a new top-level category'
                : `Create a new subcategory under "${categories.find((c) => c.id === addingChildTo)?.name}"`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              placeholder="Category name"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e?.target?.value)}
              onKeyDown={(e) => e.key === 'Enter' && void handleAddCategory()}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setAddingChildTo(null)}
              className="max-w-full"
            >
              Cancel
            </Button>
            <Button
              onClick={() => void handleAddCategory()}
              disabled={!newCategoryName.trim()}
              className="max-w-full"
            >
              Add Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog
        open={editingCategory !== null}
        onOpenChange={(open) => !open && setEditingCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update the category name and properties
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              placeholder="Category name"
              defaultValue={editingCategory?.name}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && editingCategory) {
                  void handleEditCategory(
                    editingCategory,
                    (e.target as HTMLInputElement).value,
                  );
                }
              }}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditingCategory(null)}
              className="max-w-full"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (editingCategory) {
                  const input = document.querySelector<HTMLInputElement>(
                    'input[placeholder="Category name"]',
                  );
                  if (input) {
                    void handleEditCategory(editingCategory, input.value);
                  }
                }
              }}
              className="max-w-full"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CategoryTreeView;
