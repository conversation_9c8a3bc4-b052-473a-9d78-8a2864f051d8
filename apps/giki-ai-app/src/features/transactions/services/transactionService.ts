// apps/giki-ai-app/src/services/transactionService.ts
import { apiClient } from '@/shared/services/api/apiClient';
import {
  CategorizationStatus,
  type Transaction,
} from '@/shared/types/categorization';
import { logger } from '@/shared/utils/errorHandling';

// These types are moved from lib/api.ts. Consider centralizing them in @/shared/types/api.ts later.
export interface FetchTransactionsParams {
  uploadId?: string;
  withAiSuggestions?: boolean;
  startDate?: string;
  endDate?: string;
  status?: string; // e.g., 'needs_review', 'categorized', 'all'
  categoryId?: string;
  searchTerm?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  minAmount?: number;
  maxAmount?: number;
  useFastPagination?: boolean; // Flag to use cursor-based pagination
  cursor?: string; // Cursor for fast pagination
}

export interface PaginatedResponse<T> {
  items: T[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface FastPaginatedResponse<T> {
  items: T[];
  total_count: number; // Will be -1 for fast pagination
  page: number;
  page_size: number;
  total_pages: number; // Will be -1 for fast pagination
  next_cursor?: string; // Cursor for next page
  has_more?: boolean; // Whether there are more pages
}

export type FetchTransactionsResponse = PaginatedResponse<Transaction>;

export type FastFetchTransactionsResponse = FastPaginatedResponse<Transaction>;

export const fetchTransactions = async (
  params: FetchTransactionsParams = {},
): Promise<
  Transaction[] | FetchTransactionsResponse | FastFetchTransactionsResponse
> => {
  console.log(
    '[transactionService] fetchTransactions called with params:',
    params,
  );
  try {
    // Build query parameters
    const queryParams: Record<string, string> = {};

    // Determine which endpoint to use
    const useFastEndpoint =
      params.useFastPagination ||
      (!params.startDate &&
        !params.endDate &&
        !params.status &&
        !params.categoryId &&
        !params.searchTerm &&
        !params.minAmount &&
        !params.maxAmount);

    if (useFastEndpoint) {
      // Use fast endpoint with cursor-based pagination (no filters supported)
      if (params.pageSize) {
        queryParams.limit = params.pageSize.toString();
      }
      if (params.cursor) {
        queryParams.cursor = params.cursor;
      }
    } else {
      // Use regular endpoint with full filtering but slower performance
      if (params.uploadId) queryParams.upload_id = params.uploadId;
      if (params.withAiSuggestions) queryParams.with_ai_suggestions = 'true';
      if (params.startDate) queryParams.start_date = params.startDate;
      if (params.endDate) queryParams.end_date = params.endDate;
      if (params.status && params.status !== 'all')
        queryParams.status = params.status;
      if (params.categoryId && params.categoryId !== 'all_categories')
        queryParams.category_id = params.categoryId;
      if (params.searchTerm) queryParams.search_term = params.searchTerm;
      if (params.page) queryParams.page = params.page.toString();
      if (params.pageSize) {
        // Backend expects 'limit' parameter, not 'page_size'
        // Use the requested page size without arbitrary limits
        queryParams.limit = params.pageSize.toString();
      }
      if (params.sortBy) queryParams.sort_by = params.sortBy;
      if (params.sortDirection)
        queryParams.sort_direction = params.sortDirection;
      if (params.minAmount !== undefined)
        queryParams.min_amount = params.minAmount.toString();
      if (params.maxAmount !== undefined)
        queryParams.max_amount = params.maxAmount.toString();

      // Skip count for faster response when possible
      queryParams.skip_count = 'true';
    }

    // Choose endpoint based on performance needs
    const endpoint = useFastEndpoint
      ? '/api/v1/transactions/fast'
      : '/api/v1/transactions/';

    // Make the API request
    const response = await apiClient.get<
      Transaction[] | FetchTransactionsResponse | FastFetchTransactionsResponse
    >(endpoint, {
      params: queryParams,
    });

    const data = response.data;
    console.log('[transactionService] API response:', data);
    logger.debug('Transaction API response', 'transactionService', data);

    // Process the response
    if (data && 'items' in data && Array.isArray(data.items)) {
      const processedItems = data.items.map((item) => {
        if (!item.status) {
          if (item.is_user_modified) {
            item.status = CategorizationStatus.USER_MODIFIED;
          } else if (
            item.ai_suggested_category_path ||
            item.ai_suggested_category_id
          ) {
            item.status = CategorizationStatus.AI_SUGGESTED;
          } else {
            item.status = CategorizationStatus.UNCATEGORIZED;
          }
        }
        return item;
      });
      return { ...data, items: processedItems } as FetchTransactionsResponse;
    }

    if (Array.isArray(data)) {
      const processedItems = data.map((item) => {
        if (!item.status) {
          if (item.is_user_modified) {
            item.status = CategorizationStatus.USER_MODIFIED;
          } else if (
            item.ai_suggested_category_path ||
            item.ai_suggested_category_id
          ) {
            item.status = CategorizationStatus.AI_SUGGESTED;
          } else {
            item.status = CategorizationStatus.UNCATEGORIZED;
          }
        }
        return item;
      });
      return {
        items: processedItems,
        total_count: processedItems.length,
        page: params.page || 1,
        page_size: params.pageSize || processedItems.length,
        total_pages: 1,
      } as FetchTransactionsResponse;
    }

    logger.error('Unexpected API response format', 'transactionService', data);
    throw new Error('Unexpected response format from transactions API');
  } catch (error) {
    logger.error(
      'Error in fetchTransactions',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export const fetchTransaction = async (
  transactionId: string,
): Promise<Transaction> => {
  try {
    const response = await apiClient.get<Transaction>(
      `/api/v1/transactions/${transactionId}`,
    );

    const data = response.data;
    logger.debug(
      'Transaction details API response',
      'transactionService',
      data,
    );

    // Ensure the response has the required status field
    if (!data.status) {
      if (data.is_user_modified) {
        data.status = CategorizationStatus.USER_MODIFIED;
      } else if (
        data.ai_suggested_category_path ||
        data.ai_suggested_category_id
      ) {
        data.status = CategorizationStatus.AI_SUGGESTED;
      } else {
        data.status = CategorizationStatus.UNCATEGORIZED;
      }
    }

    return data;
  } catch (error) {
    logger.error(
      'Error in fetchTransaction',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export const updateTransactionCategory = async (
  transactionId: string,
  categoryId: number,
): Promise<Transaction> => {
  try {
    logger.info('Updating transaction with category', 'transactionService', {
      transactionId,
      categoryId,
    });

    const response = await apiClient.put<Transaction>(
      `/api/v1/transactions/${transactionId}/category`,
      { category_id: categoryId },
    );

    const data = response.data;
    logger.debug(
      'Update transaction category response',
      'transactionService',
      data,
    );

    // Ensure the response has the required status field
    if (!data.status) {
      data.status = CategorizationStatus.USER_MODIFIED;
    }

    return data;
  } catch (error) {
    logger.error(
      'Error in updateTransactionCategory',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};

export interface BatchCategoryUpdateParams {
  transaction_ids: string[];
  category_id: number | null;
  use_ai?: boolean;
  rag_corpus_id?: string | null;
  confidence_threshold?: number;
}

export const updateBatchCategories = async (
  params: BatchCategoryUpdateParams,
): Promise<{ success: boolean; message: string }> => {
  try {
    logger.info('Batch updating transactions', 'transactionService', {
      count: params.transaction_ids.length,
      use_ai: params.use_ai,
      category_id: params.category_id,
    });

    const response = await apiClient.put<{ success: boolean; message: string }>(
      '/api/v1/transactions/batch/category',
      params,
    );

    logger.debug('Batch update response', 'transactionService', response.data);
    return response.data;
  } catch (error) {
    logger.error(
      'Error in updateBatchCategories',
      'transactionService',
      error as Error,
    );
    // apiClient handles error creation and throws AppError instances.
    // Re-throwing allows the caller to handle it.
    throw error;
  }
};
