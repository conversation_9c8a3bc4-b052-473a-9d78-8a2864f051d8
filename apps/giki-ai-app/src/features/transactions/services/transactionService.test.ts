/**
 * Transaction Service Tests
 * Critical service layer tests for production transaction management
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  fetchTransactions,
  fetchTransaction,
  updateTransactionCategory,
  updateBatchCategories,
  type FetchTransactionsParams,
  type BatchCategoryUpdateParams,
} from './transactionService';
import {
  CategorizationStatus,
  type Transaction,
} from '@/shared/types/categorization';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    get: vi.fn(),
    put: vi.fn(),
  },
}));

// Mock logger
vi.mock('@/shared/utils/errorHandling', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
  },
}));

import { apiClient } from '@/shared/services/api/apiClient';
import { logger } from '@/shared/utils/errorHandling';

describe('TransactionService - Transaction Management API Layer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fetchTransactions', () => {
    const mockTransaction: Transaction = {
      id: '1',
      amount: 100.5,
      description: 'Test Transaction',
      date: '2024-01-15',
      status: CategorizationStatus.UNCATEGORIZED,
      is_user_modified: false,
      ai_suggested_category_path: null,
      ai_suggested_category_id: null,
    };

    describe('Fast Endpoint Usage', () => {
      it('uses fast endpoint when no filters are provided', async () => {
        const mockResponse = {
          items: [mockTransaction],
          total_count: -1,
          page: 1,
          page_size: 10,
          total_pages: -1,
          has_more: false,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        const result = await fetchTransactions();

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/fast', {
          params: {},
        });
        expect(result).toEqual({
          ...mockResponse,
          items: [
            expect.objectContaining({
              status: CategorizationStatus.UNCATEGORIZED,
            }),
          ],
        });
      });

      it('uses fast endpoint when useFastPagination is true', async () => {
        const params: FetchTransactionsParams = {
          useFastPagination: true,
          pageSize: 20,
          cursor: 'cursor123',
        };

        const mockResponse = {
          items: [mockTransaction],
          total_count: -1,
          page: 1,
          page_size: 20,
          total_pages: -1,
          next_cursor: 'next_cursor123',
          has_more: true,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/fast', {
          params: {
            limit: '20',
            cursor: 'cursor123',
          },
        });
      });

      it('includes cursor and limit in fast endpoint params', async () => {
        const params: FetchTransactionsParams = {
          useFastPagination: true,
          pageSize: 50,
          cursor: 'test_cursor',
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: {
            items: [],
            total_count: -1,
            page: 1,
            page_size: 50,
            total_pages: -1,
          },
        });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/fast', {
          params: {
            limit: '50',
            cursor: 'test_cursor',
          },
        });
      });
    });

    describe('Regular Endpoint Usage', () => {
      it('uses regular endpoint when filters are provided', async () => {
        const params: FetchTransactionsParams = {
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          status: 'needs_review',
          categoryId: '5',
          searchTerm: 'coffee',
          minAmount: 10,
          maxAmount: 100,
        };

        const mockResponse = {
          items: [mockTransaction],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/', {
          params: {
            start_date: '2024-01-01',
            end_date: '2024-01-31',
            status: 'needs_review',
            category_id: '5',
            search_term: 'coffee',
            min_amount: '10',
            max_amount: '100',
            skip_count: 'true',
          },
        });
      });

      it('excludes all status and all_categories from query params', async () => {
        const params: FetchTransactionsParams = {
          status: 'all',
          categoryId: 'all_categories',
          uploadId: 'upload123',
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: {
            items: [],
            total_count: 0,
            page: 1,
            page_size: 10,
            total_pages: 0,
          },
        });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/', {
          params: {
            upload_id: 'upload123',
            skip_count: 'true',
          },
        });
      });

      it('includes AI suggestions and pagination parameters', async () => {
        const params: FetchTransactionsParams = {
          withAiSuggestions: true,
          page: 2,
          pageSize: 25,
          sortBy: 'date',
          sortDirection: 'desc',
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: {
            items: [],
            total_count: 0,
            page: 2,
            page_size: 25,
            total_pages: 0,
          },
        });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/', {
          params: {
            with_ai_suggestions: 'true',
            page: '2',
            limit: '25',
            sort_by: 'date',
            sort_direction: 'desc',
            skip_count: 'true',
          },
        });
      });

      it('handles zero amount filters correctly', async () => {
        const params: FetchTransactionsParams = {
          minAmount: 0,
          maxAmount: 0,
        };

        (apiClient.get as unknown).mockResolvedValue({
          data: {
            items: [],
            total_count: 0,
            page: 1,
            page_size: 10,
            total_pages: 0,
          },
        });

        await fetchTransactions(params);

        expect(apiClient.get).toHaveBeenCalledWith('/transactions/', {
          params: {
            min_amount: '0',
            max_amount: '0',
            skip_count: 'true',
          },
        });
      });
    });

    describe('Response Processing', () => {
      it('processes paginated response with status assignment', async () => {
        const mockTransactionWithoutStatus = {
          id: '1',
          amount: 100.5,
          description: 'Test Transaction',
          date: '2024-01-15',
          is_user_modified: false,
          ai_suggested_category_path: 'Food & Dining',
          ai_suggested_category_id: 5,
        };

        const mockResponse = {
          items: [mockTransactionWithoutStatus],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        const result = await fetchTransactions();

        expect(result).toEqual({
          ...mockResponse,
          items: [
            expect.objectContaining({
              status: CategorizationStatus.AI_SUGGESTED,
            }),
          ],
        });
      });

      it('assigns USER_MODIFIED status when is_user_modified is true', async () => {
        const mockTransaction = {
          id: '1',
          amount: 100.5,
          description: 'Test Transaction',
          date: '2024-01-15',
          is_user_modified: true,
        };

        const mockResponse = {
          items: [mockTransaction],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        const result = await fetchTransactions();

        expect(result).toEqual({
          ...mockResponse,
          items: [
            expect.objectContaining({
              status: CategorizationStatus.USER_MODIFIED,
            }),
          ],
        });
      });

      it('assigns UNCATEGORIZED status when no AI suggestions or user modifications', async () => {
        const mockTransaction = {
          id: '1',
          amount: 100.5,
          description: 'Test Transaction',
          date: '2024-01-15',
          is_user_modified: false,
          ai_suggested_category_path: null,
          ai_suggested_category_id: null,
        };

        const mockResponse = {
          items: [mockTransaction],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        const result = await fetchTransactions();

        expect(result).toEqual({
          ...mockResponse,
          items: [
            expect.objectContaining({
              status: CategorizationStatus.UNCATEGORIZED,
            }),
          ],
        });
      });

      it('processes array response format', async () => {
        const mockTransactions = [
          {
            id: '1',
            amount: 100.5,
            description: 'Transaction 1',
            date: '2024-01-15',
            is_user_modified: false,
          },
          {
            id: '2',
            amount: 50.25,
            description: 'Transaction 2',
            date: '2024-01-16',
            is_user_modified: true,
          },
        ];

        (apiClient.get as unknown).mockResolvedValue({
          data: mockTransactions,
        });

        const result = await fetchTransactions({ pageSize: 20 });

        expect(result).toEqual({
          items: [
            expect.objectContaining({
              id: '1',
              status: CategorizationStatus.UNCATEGORIZED,
            }),
            expect.objectContaining({
              id: '2',
              status: CategorizationStatus.USER_MODIFIED,
            }),
          ],
          total_count: 2,
          page: 1,
          page_size: 20,
          total_pages: 1,
        });
      });

      it('preserves existing status when already present', async () => {
        const mockTransaction = {
          id: '1',
          amount: 100.5,
          description: 'Test Transaction',
          date: '2024-01-15',
          status: CategorizationStatus.USER_MODIFIED,
          is_user_modified: false,
        };

        const mockResponse = {
          items: [mockTransaction],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        const result = await fetchTransactions();

        expect(result).toEqual({
          ...mockResponse,
          items: [
            expect.objectContaining({
              status: CategorizationStatus.USER_MODIFIED,
            }),
          ],
        });
      });

      it('throws error for unexpected response format', async () => {
        const unexpectedResponse = { unexpected: 'format' };

        (apiClient.get as unknown).mockResolvedValue({
          data: unexpectedResponse,
        });

        await expect(fetchTransactions()).rejects.toThrow(
          'Unexpected response format from transactions API',
        );

        expect(logger.error).toHaveBeenCalledWith(
          'Unexpected API response format',
          'transactionService',
          unexpectedResponse,
        );
      });
    });

    describe('Error Handling', () => {
      it('logs and re-throws API errors', async () => {
        const apiError = new Error('API connection failed');
        (apiClient.get as unknown).mockRejectedValue(apiError);

        await expect(fetchTransactions()).rejects.toThrow(
          'API connection failed',
        );

        expect(logger.error).toHaveBeenCalledWith(
          'Error in fetchTransactions',
          'transactionService',
          apiError,
        );
      });

      it('handles network timeout errors', async () => {
        const timeoutError = new Error('Request timeout');
        (apiClient.get as unknown).mockRejectedValue(timeoutError);

        await expect(fetchTransactions()).rejects.toThrow('Request timeout');
      });
    });

    describe('Logging', () => {
      it('logs debug information on successful response', async () => {
        const mockResponse = {
          items: [mockTransaction],
          total_count: 1,
          page: 1,
          page_size: 10,
          total_pages: 1,
        };

        (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

        await fetchTransactions();

        expect(logger.debug).toHaveBeenCalledWith(
          'Transaction API response',
          'transactionService',
          mockResponse,
        );
      });
    });
  });

  describe('fetchTransaction', () => {
    const mockTransaction: Transaction = {
      id: '123',
      amount: 250.75,
      description: 'Single Transaction',
      date: '2024-01-20',
      status: CategorizationStatus.UNCATEGORIZED,
      is_user_modified: false,
      ai_suggested_category_path: null,
      ai_suggested_category_id: null,
    };

    it('fetches single transaction by ID', async () => {
      (apiClient.get as unknown).mockResolvedValue({ data: mockTransaction });

      const result = await fetchTransaction('123');

      expect(apiClient.get).toHaveBeenCalledWith('/transactions/123');
      expect(result).toEqual(mockTransaction);
    });

    it('assigns status when missing from response', async () => {
      const transactionWithoutStatus = {
        id: '123',
        amount: 250.75,
        description: 'Single Transaction',
        date: '2024-01-20',
        is_user_modified: false,
        ai_suggested_category_path: 'Travel',
        ai_suggested_category_id: 10,
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: transactionWithoutStatus,
      });

      const result = await fetchTransaction('123');

      expect(result.status).toBe(CategorizationStatus.AI_SUGGESTED);
    });

    it('assigns USER_MODIFIED status when is_user_modified is true', async () => {
      const userModifiedTransaction = {
        ...mockTransaction,
        is_user_modified: true,
        status: undefined,
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: userModifiedTransaction,
      });

      const result = await fetchTransaction('123');

      expect(result.status).toBe(CategorizationStatus.USER_MODIFIED);
    });

    it('assigns UNCATEGORIZED status when no AI suggestions or modifications', async () => {
      const uncategorizedTransaction = {
        ...mockTransaction,
        is_user_modified: false,
        ai_suggested_category_path: null,
        ai_suggested_category_id: null,
        status: undefined,
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: uncategorizedTransaction,
      });

      const result = await fetchTransaction('123');

      expect(result.status).toBe(CategorizationStatus.UNCATEGORIZED);
    });

    it('logs debug information', async () => {
      (apiClient.get as unknown).mockResolvedValue({ data: mockTransaction });

      await fetchTransaction('123');

      expect(logger.debug).toHaveBeenCalledWith(
        'Transaction details API response',
        'transactionService',
        mockTransaction,
      );
    });

    it('logs and re-throws errors', async () => {
      const error = new Error('Transaction not found');
      (apiClient.get as unknown).mockRejectedValue(error);

      await expect(fetchTransaction('999')).rejects.toThrow(
        'Transaction not found',
      );

      expect(logger.error).toHaveBeenCalledWith(
        'Error in fetchTransaction',
        'transactionService',
        error,
      );
    });
  });

  describe('updateTransactionCategory', () => {
    const mockUpdatedTransaction: Transaction = {
      id: '123',
      amount: 100.5,
      description: 'Updated Transaction',
      date: '2024-01-15',
      status: CategorizationStatus.USER_MODIFIED,
      is_user_modified: true,
      category_id: 5,
      category_path: 'Food & Dining',
    };

    it('updates transaction category successfully', async () => {
      (apiClient.put as unknown).mockResolvedValue({
        data: mockUpdatedTransaction,
      });

      const result = await updateTransactionCategory('123', 5);

      expect(apiClient.put).toHaveBeenCalledWith('/transactions/123/category', {
        category_id: 5,
      });
      expect(result).toEqual(mockUpdatedTransaction);
    });

    it('assigns USER_MODIFIED status when missing from response', async () => {
      const transactionWithoutStatus = {
        ...mockUpdatedTransaction,
        status: undefined,
      };

      (apiClient.put as unknown).mockResolvedValue({
        data: transactionWithoutStatus,
      });

      const result = await updateTransactionCategory('123', 5);

      expect(result.status).toBe(CategorizationStatus.USER_MODIFIED);
    });

    it('logs update information', async () => {
      (apiClient.put as unknown).mockResolvedValue({
        data: mockUpdatedTransaction,
      });

      await updateTransactionCategory('123', 5);

      expect(logger.info).toHaveBeenCalledWith(
        'Updating transaction with category',
        'transactionService',
        {
          transactionId: '123',
          categoryId: 5,
        },
      );

      expect(logger.debug).toHaveBeenCalledWith(
        'Update transaction category response',
        'transactionService',
        mockUpdatedTransaction,
      );
    });

    it('handles update errors', async () => {
      const error = new Error('Category assignment failed');
      (apiClient.put as unknown).mockRejectedValue(error);

      await expect(updateTransactionCategory('123', 5)).rejects.toThrow(
        'Category assignment failed',
      );

      expect(logger.error).toHaveBeenCalledWith(
        'Error in updateTransactionCategory',
        'transactionService',
        error,
      );
    });

    it('handles invalid category ID', async () => {
      const error = new Error('Invalid category ID');
      (apiClient.put as unknown).mockRejectedValue(error);

      await expect(updateTransactionCategory('123', -1)).rejects.toThrow(
        'Invalid category ID',
      );
    });
  });

  describe('updateBatchCategories', () => {
    const mockBatchResponse = {
      success: true,
      message: 'Successfully updated 3 transactions',
    };

    it('updates multiple transactions with category', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2', '3'],
        category_id: 5,
      };

      (apiClient.put as unknown).mockResolvedValue({ data: mockBatchResponse });

      const result = await updateBatchCategories(params);

      expect(apiClient.put).toHaveBeenCalledWith(
        '/transactions/batch/category',
        params,
      );
      expect(result).toEqual(mockBatchResponse);
    });

    it('handles AI-based batch categorization', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2', '3'],
        category_id: null,
        use_ai: true,
        rag_corpus_id: 'corpus123',
        confidence_threshold: 0.8,
      };

      (apiClient.put as unknown).mockResolvedValue({ data: mockBatchResponse });

      const result = await updateBatchCategories(params);

      expect(apiClient.put).toHaveBeenCalledWith(
        '/transactions/batch/category',
        params,
      );
      expect(result).toEqual(mockBatchResponse);
    });

    it('logs batch update information', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2', '3'],
        category_id: 5,
        use_ai: false,
      };

      (apiClient.put as unknown).mockResolvedValue({ data: mockBatchResponse });

      await updateBatchCategories(params);

      expect(logger.info).toHaveBeenCalledWith(
        'Batch updating transactions',
        'transactionService',
        {
          count: 3,
          use_ai: false,
          category_id: 5,
        },
      );

      expect(logger.debug).toHaveBeenCalledWith(
        'Batch update response',
        'transactionService',
        mockBatchResponse,
      );
    });

    it('handles empty transaction list', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: [],
        category_id: 5,
      };

      const emptyResponse = {
        success: true,
        message: 'No transactions to update',
      };

      (apiClient.put as unknown).mockResolvedValue({ data: emptyResponse });

      const result = await updateBatchCategories(params);

      expect(result).toEqual(emptyResponse);
    });

    it('handles batch update errors', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2', '3'],
        category_id: 5,
      };

      const error = new Error('Batch update failed');
      (apiClient.put as unknown).mockRejectedValue(error);

      await expect(updateBatchCategories(params)).rejects.toThrow(
        'Batch update failed',
      );

      expect(logger.error).toHaveBeenCalledWith(
        'Error in updateBatchCategories',
        'transactionService',
        error,
      );
    });

    it('handles partial batch failures', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2', '3'],
        category_id: 5,
      };

      const partialFailureResponse = {
        success: false,
        message: 'Updated 2 of 3 transactions. 1 transaction failed.',
      };

      (apiClient.put as unknown).mockResolvedValue({
        data: partialFailureResponse,
      });

      const result = await updateBatchCategories(params);

      expect(result).toEqual(partialFailureResponse);
    });

    it('validates AI parameters when use_ai is true', async () => {
      const params: BatchCategoryUpdateParams = {
        transaction_ids: ['1', '2'],
        category_id: null,
        use_ai: true,
        confidence_threshold: 0.9,
      };

      (apiClient.put as unknown).mockResolvedValue({ data: mockBatchResponse });

      await updateBatchCategories(params);

      expect(logger.info).toHaveBeenCalledWith(
        'Batch updating transactions',
        'transactionService',
        expect.objectContaining({
          use_ai: true,
          category_id: null,
        }),
      );
    });
  });

  describe('Edge Cases and Performance', () => {
    it('handles large transaction lists efficiently', async () => {
      const largeTransactionList = Array.from({ length: 1000 }, (_, i) => ({
        id: `${i + 1}`,
        amount: Math.random() * 1000,
        description: `Transaction ${i + 1}`,
        date: '2024-01-15',
        is_user_modified: false,
      }));

      const mockResponse = {
        items: largeTransactionList,
        total_count: 1000,
        page: 1,
        page_size: 1000,
        total_pages: 1,
      };

      (apiClient.get as unknown).mockResolvedValue({ data: mockResponse });

      const startTime = performance.now();
      const result = await fetchTransactions();
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(200);
      expect(result).toHaveProperty('items');
      expect((result as unknown).items).toHaveLength(1000);
    });

    it('handles concurrent API calls', async () => {
      const mockTransaction: Transaction = {
        id: '1',
        amount: 100,
        description: 'Test',
        date: '2024-01-15',
        status: CategorizationStatus.UNCATEGORIZED,
      };

      (apiClient.get as unknown).mockResolvedValue({ data: mockTransaction });

      const promises = Array.from({ length: 10 }, (_, i) =>
        fetchTransaction(`${i + 1}`),
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result) => {
        expect(result).toEqual(expect.objectContaining({ amount: 100 }));
      });
    });

    it('handles malformed transaction data gracefully', async () => {
      const malformedTransaction = {
        id: '1',
        // Missing required fields
        amount: null,
        description: undefined,
      };

      (apiClient.get as unknown).mockResolvedValue({
        data: malformedTransaction,
      });

      const result = await fetchTransaction('1');

      expect(result).toEqual(
        expect.objectContaining({
          id: '1',
          status: CategorizationStatus.UNCATEGORIZED,
        }),
      );
    });
  });
});
