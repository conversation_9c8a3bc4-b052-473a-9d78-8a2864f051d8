/**
 * Transactions Hook
 *
 * Custom hook for managing transaction operations.
 */

import { useState, useCallback } from 'react';
import {
  TransactionFilter,
  PaginatedTransactions,
  TransactionSummary,
} from '../types/transaction';
import { Transaction } from '@/shared/types/categorization';
import { FetchTransactionsParams } from '../services/transactionService';

// Helper function to calculate transaction summary
const calculateSummary = (transactions: Transaction[]): TransactionSummary => {
  const totalIncome = transactions
    .filter((t) => t.amount > 0)
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter((t) => t.amount < 0)
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const netIncome = totalIncome - totalExpenses;
  const transactionCount = transactions.length;
  const averageTransaction =
    transactionCount > 0 ? (totalIncome + totalExpenses) / transactionCount : 0;

  return {
    totalIncome,
    totalExpenses,
    netIncome,
    transactionCount,
    averageTransaction,
  };
};

export interface UseTransactionsReturn {
  transactions: Transaction[];
  paginatedData: PaginatedTransactions | null;
  summary: TransactionSummary | null;
  isLoading: boolean;
  error: string | null;
  fetchTransactions: (
    filter?: TransactionFilter,
    page?: number,
  ) => Promise<void>;
  updateTransaction: (
    id: string,
    updates: Partial<Transaction>,
  ) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  categorizeTransaction: (id: string, categoryId: string) => Promise<void>;
}

export const useTransactions = (): UseTransactionsReturn => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paginatedData, setPaginatedData] =
    useState<PaginatedTransactions | null>(null);
  const [summary, setSummary] = useState<TransactionSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = useCallback(
    async (filter?: TransactionFilter, page = 1): Promise<void> => {
      setIsLoading(true);
      setError(null);

      try {
        // Use the real transaction service
        const { fetchTransactions: apiGetTransactions } = await import(
          '../services/transactionService'
        );

        // Map filter to API params
        const extendedFilter = filter as {
          uploadId?: string;
          status?: string;
          pageSize?: number;
          sortBy?: string;
          sortDirection?: string;
        };
        const params: FetchTransactionsParams = {
          uploadId: extendedFilter?.uploadId,
          startDate: filter?.dateRange?.start.toISOString().split('T')[0],
          endDate: filter?.dateRange?.end.toISOString().split('T')[0],
          status: extendedFilter?.status,
          categoryId: filter?.categories?.[0], // Use first category if multiple
          searchTerm: filter?.searchTerm,
          page,
          pageSize: extendedFilter?.pageSize || 50,
          sortBy: extendedFilter?.sortBy,
          sortDirection: extendedFilter?.sortDirection,
          minAmount: filter?.amountRange?.min,
          maxAmount: filter?.amountRange?.max,
        };

        const response = await apiGetTransactions(params);

        // Handle the response format
        if ('items' in response) {
          // It's a paginated response
          const paginatedResponse = response;
          setTransactions(paginatedResponse.items);
          setPaginatedData({
            items: paginatedResponse.items,
            total: paginatedResponse.total_count,
            page: paginatedResponse.page,
            limit: paginatedResponse.page_size,
            hasNext: paginatedResponse.page < paginatedResponse.total_pages,
            hasPrev: paginatedResponse.page > 1,
          });

          // Calculate summary from transactions
          const summary = calculateSummary(paginatedResponse.items);
          setSummary(summary);
        } else {
          // Legacy array response
          const transactionArray = response;
          setTransactions(transactionArray);
          setPaginatedData({
            items: transactionArray,
            total: transactionArray.length,
            page: 1,
            limit: transactionArray.length,
            hasNext: false,
            hasPrev: false,
          });

          const summary = calculateSummary(transactionArray);
          setSummary(summary);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to fetch transactions';
        setError(errorMessage);
        console.error('Error fetching transactions:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const updateTransaction = useCallback(
    (id: string, updates: Partial<Transaction>): Promise<void> => {
      try {
        // For now, just update locally - the actual API update would happen through categorizeTransaction
        setTransactions((prev) =>
          prev.map((transaction) =>
            transaction.id === id
              ? { ...transaction, ...updates }
              : transaction,
          ),
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to update transaction';
        setError(errorMessage);
        throw err;
      }
    },
    [],
  );

  const deleteTransaction = useCallback((id: string): Promise<void> => {
    try {
      // Note: No delete endpoint in the backend currently, just remove from local state
      setTransactions((prev) =>
        prev.filter((transaction) => transaction.id !== id),
      );
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete transaction';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const categorizeTransaction = useCallback(
    async (id: string, categoryId: string): Promise<void> => {
      try {
        const { updateTransactionCategory } = await import(
          '../services/transactionService'
        );

        // Call the real API
        const updatedTransaction = await updateTransactionCategory(
          id,
          parseInt(categoryId),
        );

        // Update local state with the response
        setTransactions((prev) =>
          prev.map((transaction) =>
            transaction.id === id ? updatedTransaction : transaction,
          ),
        );

        // Update paginated data if it exists
        if (paginatedData) {
          setPaginatedData({
            ...paginatedData,
            items: paginatedData?.items?.map((transaction) =>
              transaction.id === id ? updatedTransaction : transaction,
            ),
          });
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to categorize transaction';
        setError(errorMessage);
        throw err;
      }
    },
    [paginatedData],
  );

  return {
    transactions,
    paginatedData,
    summary,
    isLoading,
    error,
    fetchTransactions,
    updateTransaction,
    deleteTransaction,
    categorizeTransaction,
  };
};
