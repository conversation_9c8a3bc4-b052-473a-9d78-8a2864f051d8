import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Badge } from '@/shared/components/ui/badge';
import { Save } from 'lucide-react';
import {
  GikiTable,
  type GikiTableColumn,
} from '@/shared/components/ui/giki-table';
import { cn, formatFinancialDate } from '@/shared/utils/utils';
import {
  formatCurrencyForTable,
  getAmountColorClass,
  FINANCIAL_TABLE_CLASSES,
} from '@/shared/utils/formatCurrency';

import { Transaction } from '@/shared/types/categorization';
import {
  fetchCategories,
  type Category,
} from '@/features/categories/services/categoryService';

interface TransactionTableProps {
  transactions: Transaction[];
  onUpdateTransaction: (
    id: string,
    field: keyof Transaction,
    value: string | number | boolean | null,
  ) => void;
  onSaveChanges: () => void;
  hasUnsavedChanges: boolean;
  isLoading?: boolean;
  selectedRows?: Set<string>;
  onSelectionChange?: (selectedIds: Set<string>) => void;
}

// Editable cell component
const EditableCell = ({
  value: initialValue,
  onUpdate,
  type = 'text',
  options = [],
}: {
  value: string | number | boolean | null;
  onUpdate: (value: string | number | boolean | null) => void;
  type?: 'text' | 'number' | 'select';
  options?: string[];
}) => {
  const [value, setValue] = useState(initialValue);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = useCallback(() => {
    if (value !== initialValue) {
      onUpdate(value);
    }
    setIsEditing(false);
  }, [value, initialValue, onUpdate]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setValue(initialValue);
      setIsEditing(false);
    }
  };

  if (type === 'select') {
    return (
      <Select value={String(value)} onValueChange={onUpdate}>
        <SelectTrigger className="h-8 border-none shadow-none">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  if (isEditing) {
    return (
      <Input
        value={String(value)}
        onChange={(e) =>
          setValue(
            type === 'number'
              ? parseFloat(e?.target?.value) || 0
              : e?.target?.value,
          )
        }
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        className="h-8 border-none shadow-none"
        type={type}
        autoFocus
      />
    );
  }

  return (
    <div
      className={`h-8 px-2 py-1 cursor-pointer hover:bg-muted/50 rounded ${
        type === 'number' ||
        (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}$/))
          ? 'text-financial'
          : ''
      }`}
      onClick={() => setIsEditing(true)}
    >
      {type === 'number' ? `$${Number(value).toFixed(2)}` : String(value)}
    </div>
  );
};

export const TransactionTable: React.FC<TransactionTableProps> = ({
  transactions,
  onUpdateTransaction,
  onSaveChanges,
  hasUnsavedChanges,
  isLoading = false,
  selectedRows = new Set(),
  onSelectionChange,
}) => {
  console.log(
    '[TransactionTable] Rendering with transactions:',
    transactions.length,
  );
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  // Fetch categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      setCategoriesLoading(true);
      try {
        const result = await fetchCategories();
        if (Array.isArray(result)) {
          setCategories(result);
        } else {
          // Failed to load categories - API returned non-array result
          // Set fallback categories on error
          setCategories([
            {
              id: 0,
              name: 'Uncategorized',
              parent_id: null,
              path: 'Uncategorized',
            },
          ]);
        }
      } catch {
        // Error loading categories - network or API failure
        // Set fallback categories on error
        setCategories([
          {
            id: 0,
            name: 'Uncategorized',
            parent_id: null,
            path: 'Uncategorized',
          },
        ]);
      } finally {
        setCategoriesLoading(false);
      }
    };

    void loadCategories();
  }, []);

  // Convert categories to options for the select dropdown
  const categoryOptions = useMemo(() => {
    return categories.map((cat) => cat.path || cat.name);
  }, [categories]);

  // Define columns for GikiTable
  const columns = useMemo<GikiTableColumn<Transaction>[]>(
    () => [
      {
        key: 'date',
        title: 'Date',
        dataIndex: 'date',
        sortable: true,
        type: 'date',
        width: 120,
        render: (value) => (
          <span className="truncatelabel">{formatFinancialDate(value)}</span>
        ),
      },
      {
        key: 'description',
        title: 'Description',
        dataIndex: 'description',
        sortable: true,
        filterable: true,
        render: (value, record) => (
          <EditableCell
            value={value}
            onUpdate={(newValue) =>
              onUpdateTransaction(record.id, 'description', newValue)
            }
          />
        ),
      },
      {
        key: 'amount',
        title: 'Amount',
        dataIndex: 'amount',
        sortable: true,
        type: 'currency',
        width: 140,
        align: 'right',
        render: (value, record) => {
          const formattedAmount = formatCurrencyForTable(value);
          const colorClass = getAmountColorClass(
            value,
            record.transaction_type === 'credit' ? 'income' : 'expense',
          );

          return (
            <span
              className={cn(
                FINANCIAL_TABLE_CLASSES.currency,
                'text-sm font-semibold',
                colorClass,
              )}
            >
              {formattedAmount}
            </span>
          );
        },
      },
      {
        key: 'category_path',
        title: 'Category',
        dataIndex: 'category_path',
        sortable: true,
        filterable: true,
        width: 200,
        render: (value, record) => (
          <EditableCell
            value={value || ''}
            onUpdate={(newValue) =>
              onUpdateTransaction(record.id, 'category_path', newValue)
            }
            type="select"
            options={categoriesLoading ? ['Loading...'] : categoryOptions}
          />
        ),
      },
      {
        key: 'account',
        title: 'Account',
        dataIndex: 'account',
        sortable: true,
        filterable: true,
        width: 150,
        render: (value) => (
          <Badge
            variant="outline"
            className="font-medium max-w-[150px] truncate"
          >
            {value}
          </Badge>
        ),
      },
      {
        key: 'transaction_type',
        title: 'Type',
        dataIndex: 'transaction_type',
        sortable: true,
        width: 100,
        render: (value) => (
          <Badge
            variant={value === 'credit' ? 'default' : 'secondary'}
            className="max-w-[150px] truncate"
          >
            {value || 'N/A'}
          </Badge>
        ),
      },
      {
        key: 'status',
        title: 'Status',
        dataIndex: 'status',
        sortable: true,
        width: 120,
        render: (value) => (
          <Badge
            variant="outline"
            className="font-medium max-w-[150px] truncate"
          >
            {value}
          </Badge>
        ),
      },
    ],
    [onUpdateTransaction, categoryOptions, categoriesLoading],
  );

  // Handle selection changes for GikiTable
  const handleSelectionChange = useCallback(
    (selectedRows: Transaction[], selectedRowKeys: string[]) => {
      if (onSelectionChange) {
        onSelectionChange(new Set(selectedRowKeys));
      }
    },
    [onSelectionChange],
  );

  // Convert Set to array for GikiTable (unused but kept for future use)
  const _selectedRowKeysArray = Array.from(selectedRows);

  // Get row key for GikiTable
  const getRowKey = (record: Transaction) => record.id;

  // Header actions
  const headerActions = (
    <div className="flex flex-wrap items-center gap-2">
      {hasUnsavedChanges && (
        <Button
          className="max-w-full gap-2 shadow-lg"
          onClick={() => void onSaveChanges()}
          size="sm"
          variant="gradient"
        >
          <Save className="h-4 w-4" />
          Save Changes
        </Button>
      )}
    </div>
  );

  return (
    <GikiTable<Transaction>
      data={transactions}
      columns={columns}
      loading={isLoading}
      variant="financial"
      size="default"
      selectable={!!onSelectionChange}
      onSelectionChange={handleSelectionChange}
      rowKey={getRowKey}
      className="w-full"
      title="Transaction Review"
      subtitle={`${transactions.length} transactions loaded ${hasUnsavedChanges ? '• Unsaved changes' : ''}`}
      actions={headerActions}
      exportable={true}
      onExport={() => {
        // TODO: Implement export functionality
        console.log('Export transactions');
      }}
    />
  );
};

export default TransactionTable;
