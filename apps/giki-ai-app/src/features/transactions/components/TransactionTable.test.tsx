/**
 * TransactionTable Component Tests
 * Critical financial workflow component tests for transaction management
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  render,
  mockTransaction,
  formatCurrency,
  expectToBeFinanciallyValid,
} from '../../../test-utils';
import TransactionTable from './TransactionTable';

// Mock the required hooks
const mockUseTransactionStore = vi.fn();
const mockUseCategoryStore = vi.fn();

vi.mock('@/features/transactions/hooks/useTransactions', () => ({
  useTransactionStore: mockUseTransactionStore,
}));

vi.mock('@/features/categories/hooks/useCategories', () => ({
  useCategoryStore: mockUseCategoryStore,
}));

const mockTransactions = [
  {
    ...mockTransaction,
    id: '1',
    description: 'STARBUCKS COFFEE #12345',
    amount: -4.95,
    category_path: ['Dining', 'Coffee'],
    ai_category_confidence: 0.89,
  },
  {
    ...mockTransaction,
    id: '2',
    description: 'UBER RIDE',
    amount: -15.5,
    category_path: ['Transportation'],
    ai_category_confidence: 0.95,
  },
  {
    ...mockTransaction,
    id: '3',
    description: 'SALARY DEPOSIT',
    amount: 5000.0,
    category_path: ['Income', 'Salary'],
    ai_category_confidence: 0.99,
  },
];

describe('TransactionTable - Financial Workflow Component', () => {
  const mockFetchTransactions = vi.fn();
  const mockUpdateTransaction = vi.fn();
  const mockCategorizeTransaction = vi.fn();
  const mockFetchCategories = vi.fn();

  const mockTransactionStore = {
    transactions: mockTransactions,
    fetchTransactions: mockFetchTransactions,
    updateTransaction: mockUpdateTransaction,
    categorizeTransaction: mockCategorizeTransaction,
    isLoading: false,
    error: null,
    totalCount: mockTransactions.length,
  };

  const mockCategoryStore = {
    categories: [],
    fetchCategories: mockFetchCategories,
    isLoading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseTransactionStore.mockReturnValue(mockTransactionStore);
    mockUseCategoryStore.mockReturnValue(mockCategoryStore);
  });

  it('renders transaction table with financial data', () => {
    render(<TransactionTable />);

    // Check table headers
    expect(screen.getByText(/date/i)).toBeInTheDocument();
    expect(screen.getByText(/description/i)).toBeInTheDocument();
    expect(screen.getByText(/amount/i)).toBeInTheDocument();
    expect(screen.getByText(/category/i)).toBeInTheDocument();
    expect(screen.getByText(/confidence/i)).toBeInTheDocument();

    // Check transaction data
    expect(screen.getByText('STARBUCKS COFFEE #12345')).toBeInTheDocument();
    expect(screen.getByText('UBER RIDE')).toBeInTheDocument();
    expect(screen.getByText('SALARY DEPOSIT')).toBeInTheDocument();
  });

  it('displays amounts with proper financial formatting', () => {
    render(<TransactionTable />);

    // Check negative amounts (expenses)
    expect(screen.getByText('-$4.95')).toBeInTheDocument();
    expect(screen.getByText('-$15.50')).toBeInTheDocument();

    // Check positive amounts (income)
    expect(screen.getByText('$5,000.00')).toBeInTheDocument();

    // Validate financial formatting
    mockTransactions.forEach((transaction) => {
      expectToBeFinanciallyValid(transaction.amount);
    });
  });

  it('shows AI confidence scores with visual indicators', () => {
    render(<TransactionTable />);

    // Check confidence percentages
    expect(screen.getByText('89%')).toBeInTheDocument();
    expect(screen.getByText('95%')).toBeInTheDocument();
    expect(screen.getByText('99%')).toBeInTheDocument();

    // Check confidence indicators (high confidence should be green)
    const highConfidenceElements = screen.getAllByText(/9[59]%/);
    highConfidenceElements.forEach((element) => {
      expect(element.closest('div')).toHaveClass(/green|success/);
    });
  });

  it('displays category hierarchy correctly', () => {
    render(<TransactionTable />);

    // Check hierarchical category display
    expect(screen.getByText('Dining > Coffee')).toBeInTheDocument();
    expect(screen.getByText('Transportation')).toBeInTheDocument();
    expect(screen.getByText('Income > Salary')).toBeInTheDocument();
  });

  it('enables category editing for transactions', async () => {
    const user = userEvent.setup();
    render(<TransactionTable />);

    // Click on category to edit
    const categoryCell = screen.getByText('Dining > Coffee');
    await user.click(categoryCell);

    // Should show category selector
    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
  });

  it('handles bulk category operations', async () => {
    const user = userEvent.setup();
    render(<TransactionTable />);

    // Select multiple transactions
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]); // First transaction
    await user.click(checkboxes[2]); // Second transaction

    // Bulk action button should appear
    await waitFor(() => {
      expect(screen.getByText(/2 selected/i)).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /bulk categorize/i }),
      ).toBeInTheDocument();
    });
  });

  it('filters transactions by date range', async () => {
    const user = userEvent.setup();
    render(<TransactionTable />);

    // Open date filter
    const dateFilter = screen.getByRole('button', { name: /date filter/i });
    await user.click(dateFilter);

    // Should show date picker
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  it('sorts transactions by different columns', async () => {
    const user = userEvent.setup();
    render(<TransactionTable />);

    // Click amount header to sort
    const amountHeader = screen.getByText(/amount/i);
    await user.click(amountHeader);

    // Should sort by amount (highest first for income)
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('SALARY DEPOSIT');
  });

  it('shows transaction status indicators', () => {
    const transactionsWithStatus = mockTransactions.map((t) => ({
      ...t,
      is_categorized: t.id !== '2', // Second transaction uncategorized
      user_corrected: t.id === '1', // First transaction user-corrected
    }));

    mockUseTransactionStore.mockReturnValue({
      ...mockTransactionStore,
      transactions: transactionsWithStatus,
    });

    render(<TransactionTable />);

    // Check status indicators
    expect(screen.getByText(/user corrected/i)).toBeInTheDocument();
    expect(screen.getByText(/needs review/i)).toBeInTheDocument();
  });

  it('handles pagination for large datasets', () => {
    const manyTransactions = Array.from({ length: 150 }, (_, i) => ({
      ...mockTransaction,
      id: `${i + 1}`,
      description: `Transaction ${i + 1}`,
      amount: Math.random() * 100 - 50,
    }));

    mockUseTransactionStore.mockReturnValue({
      ...mockTransactionStore,
      transactions: manyTransactions.slice(0, 20), // First page
      totalCount: manyTransactions.length,
    });

    render(<TransactionTable />);

    // Should show pagination controls
    expect(screen.getByText(/page/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /previous/i }),
    ).toBeInTheDocument();
  });

  it('exports transaction data', async () => {
    const user = userEvent.setup();
    render(<TransactionTable />);

    // Click export button
    const exportButton = screen.getByRole('button', { name: /export/i });
    await user.click(exportButton);

    // Should show export options
    await waitFor(() => {
      expect(screen.getByText(/csv/i)).toBeInTheDocument();
      expect(screen.getByText(/excel/i)).toBeInTheDocument();
    });
  });

  it('maintains financial data precision', () => {
    render(<TransactionTable />);

    // Verify no rounding errors in display
    expect(screen.getByText('-$4.95')).toBeInTheDocument();
    expect(screen.getByText('-$15.50')).toBeInTheDocument();
    expect(screen.getByText('$5,000.00')).toBeInTheDocument();

    // Verify precision in data
    mockTransactions.forEach((transaction) => {
      const displayedAmount = formatCurrency(transaction.amount);
      expect(displayedAmount).toMatch(/^\$[\d,]+\.\d{2}$|^-\$[\d,]+\.\d{2}$/);
    });
  });

  it('provides accessibility features', () => {
    render(<TransactionTable />);

    // Check table accessibility
    const table = screen.getByRole('table');
    expect(table).toHaveAttribute('aria-label');

    // Check sortable headers
    const headers = screen.getAllByRole('columnheader');
    headers.forEach((header) => {
      if (
        header.textContent?.includes('Amount') ||
        header.textContent?.includes('Date')
      ) {
        expect(header).toHaveAttribute('aria-sort');
      }
    });

    // Check row selection
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);
    checkboxes.forEach((checkbox) => {
      expect(checkbox).toHaveAttribute('aria-label');
    });
  });

  it('handles real-time updates', async () => {
    const { rerender } = render(<TransactionTable />);

    // Update transaction data
    const updatedTransactions = [...mockTransactions];
    updatedTransactions[0] = {
      ...updatedTransactions[0],
      category_path: ['Dining', 'Restaurant'],
      user_corrected: true,
    };

    mockUseTransactionStore.mockReturnValue({
      ...mockTransactionStore,
      transactions: updatedTransactions,
    });

    rerender(<TransactionTable />);

    // Should reflect changes
    expect(screen.getByText('Dining > Restaurant')).toBeInTheDocument();
    expect(screen.getByText(/user corrected/i)).toBeInTheDocument();
  });

  it('shows empty state when no transactions', () => {
    mockUseTransactionStore.mockReturnValue({
      ...mockTransactionStore,
      transactions: [],
      totalCount: 0,
    });

    render(<TransactionTable />);

    expect(screen.getByText(/no transactions found/i)).toBeInTheDocument();
    expect(screen.getByText(/upload financial data/i)).toBeInTheDocument();
  });

  it('handles loading state gracefully', () => {
    mockUseTransactionStore.mockReturnValue({
      ...mockTransactionStore,
      transactions: [],
      isLoading: true,
    });

    render(<TransactionTable />);

    expect(screen.getByText(/loading transactions/i)).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});
