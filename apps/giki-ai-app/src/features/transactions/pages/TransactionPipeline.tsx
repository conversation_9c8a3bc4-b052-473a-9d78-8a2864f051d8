import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Play,
  Pause,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
} from 'lucide-react';
import { Progress } from '@/shared/components/ui/progress';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { toast } from '@/shared/components/ui/use-toast';

// Types for transaction pipeline
interface PipelineStage {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  description: string;
  duration?: number;
  error?: string;
}

interface PipelineStatus {
  isRunning: boolean;
  currentStage: string | null;
  completedStages: number;
  totalStages: number;
  startTime: Date | null;
  estimatedTimeRemaining: number;
}

const TransactionPipeline: React.FC = () => {
  const [pipelineStatus, setPipelineStatus] = useState<PipelineStatus>({
    isRunning: false,
    currentStage: null,
    completedStages: 0,
    totalStages: 5,
    startTime: null,
    estimatedTimeRemaining: 0,
  });

  const [stages, setStages] = useState<PipelineStage[]>([
    {
      id: 'validation',
      name: 'Data Validation',
      status: 'pending',
      progress: 0,
      description: 'Validating uploaded transaction data',
    },
    {
      id: 'parsing',
      name: 'Data Parsing',
      status: 'pending',
      progress: 0,
      description: 'Parsing and structuring transaction records',
    },
    {
      id: 'categorization',
      name: 'AI Categorization',
      status: 'pending',
      progress: 0,
      description: 'Applying category classification',
    },
    {
      id: 'enrichment',
      name: 'Data Enrichment',
      status: 'pending',
      progress: 0,
      description: 'Enriching transactions with additional metadata',
    },
    {
      id: 'finalization',
      name: 'Finalization',
      status: 'pending',
      progress: 0,
      description: 'Finalizing and storing processed transactions',
    },
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch pipeline status from API
  const fetchStatus = useCallback(async () => {
    try {
      setIsLoading(true);

      // Simulate API call - replace with actual service
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Mock response - replace with actual API call
      const mockStatus: PipelineStatus = {
        isRunning: false,
        currentStage: null,
        completedStages: 0,
        totalStages: 5,
        startTime: null,
        estimatedTimeRemaining: 0,
      };

      setPipelineStatus(mockStatus);
      setError(null);
    } catch {
      setError('Failed to fetch pipeline status');
      toast({
        title: 'Error',
        description: 'Failed to fetch pipeline status',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Process stages sequentially
  const processStages = useCallback(async () => {
    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];

      // Update current stage
      setPipelineStatus((prev) => ({
        ...prev,
        currentStage: stage.id,
      }));

      // Update stage status to running
      setStages((prev) =>
        prev.map((s) =>
          s.id === stage.id ? { ...s, status: 'running' as const } : s,
        ),
      );

      try {
        // Simulate stage processing with progress updates
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise((resolve) => setTimeout(resolve, 100));

          setStages((prev) =>
            prev.map((s) => (s.id === stage.id ? { ...s, progress } : s)),
          );
        }

        // Mark stage as completed
        setStages((prev) =>
          prev.map((s) =>
            s.id === stage.id
              ? { ...s, status: 'completed' as const, progress: 100 }
              : s,
          ),
        );

        // Update completed stages count
        setPipelineStatus((prev) => ({
          ...prev,
          completedStages: i + 1,
        }));
      } catch {
        // Mark stage as error
        setStages((prev) =>
          prev.map((s) =>
            s.id === stage.id
              ? {
                  ...s,
                  status: 'error' as const,
                  error: `Failed to process ${stage.name.toLowerCase()}`,
                }
              : s,
          ),
        );

        // Stop pipeline on error
        setPipelineStatus((prev) => ({
          ...prev,
          isRunning: false,
          currentStage: null,
        }));

        setError(`Pipeline failed at stage: ${stage.name}`);
        return;
      }
    }

    // All stages completed successfully
    setPipelineStatus((prev) => ({
      ...prev,
      isRunning: false,
      currentStage: null,
    }));

    toast({
      title: 'Pipeline Completed',
      description: 'All transaction data has been processed successfully',
    });
  }, [stages]);

  // Start pipeline processing
  const startPipeline = useCallback(async () => {
    try {
      setError(null);

      // Update status to running
      setPipelineStatus((prev) => ({
        ...prev,
        isRunning: true,
        startTime: new Date(),
        currentStage: stages[0].id,
      }));

      // Reset all stages to pending
      setStages((prev) =>
        prev.map((stage) => ({
          ...stage,
          status: 'pending',
          progress: 0,
          error: undefined,
        })),
      );

      // Simulate API call to start pipeline
      await new Promise((resolve) => setTimeout(resolve, 200));

      toast({
        title: 'Pipeline Started',
        description: 'Transaction processing pipeline has been initiated',
      });

      // Start stage processing simulation
      void processStages();
    } catch {
      setError('Failed to start pipeline');
      setPipelineStatus((prev) => ({ ...prev, isRunning: false }));
      toast({
        title: 'Error',
        description: 'Failed to start transaction pipeline',
        variant: 'destructive',
      });
    }
  }, [processStages, stages]);

  // Stop pipeline processing
  const stopPipeline = useCallback(async () => {
    try {
      // Simulate API call to stop pipeline
      await new Promise((resolve) => setTimeout(resolve, 200));

      setPipelineStatus((prev) => ({
        ...prev,
        isRunning: false,
        currentStage: null,
      }));

      toast({
        title: 'Pipeline Stopped',
        description: 'Transaction processing pipeline has been stopped',
      });
    } catch {
      setError('Failed to stop pipeline');
      toast({
        title: 'Error',
        description: 'Failed to stop transaction pipeline',
        variant: 'destructive',
      });
    }
  }, []);

  // Load initial data
  useEffect(() => {
    void fetchStatus();
  }, [fetchStatus]);

  // Get status icon for stage
  const getStageIcon = (stage: PipelineStage) => {
    switch (stage.status) {
      case 'running':
        return <Clock className="h-4 w-4 truncateblue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 truncategreen-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 truncatered-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-border" />;
    }
  };

  // Get status badge for stage
  const getStatusBadge = (status: PipelineStage['status']) => {
    switch (status) {
      case 'running':
        return (
          <Badge variant="secondary" className="max-w-[150px] truncate">
            Running
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-success/10 text-success-foreground max-w-[150px] truncate">
            Completed
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="destructive" className="max-w-[150px] truncate">
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="max-w-[150px] truncate">
            Pending
          </Badge>
        );
    }
  };

  // Calculate overall progress
  const overallProgress = Math.round(
    (pipelineStatus.completedStages / pipelineStatus.totalStages) * 100,
  );

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex flex-wrap flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-6">
        <div className="space-y-2">
          <div className="flex flex-wrap items-center gap-3">
            <div className="w-10 h-10 bg-gradient-logo-subtle rounded-xl flex flex-wrap items-center justify-center shadow-lg">
              <FileText className="w-6 h-6 truncatewhite" />
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight text-gradient">
                Transaction Pipeline
              </h1>
              <p className="text-muted-foreground text-lg">
                Process and analyze transaction data through our AI-powered
                pipeline
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <Button
            className="max-w-full"
            variant="outline"
            size="icon"
            onClick={() => void fetchStatus()}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
          </Button>

          {pipelineStatus.isRunning ? (
            <Button
              className="max-w-full"
              variant="destructive"
              onClick={() => void stopPipeline()}
            >
              <Pause className="mr-2 h-4 w-4" />
              Stop Pipeline
            </Button>
          ) : (
            <Button
              className="max-w-full"
              onClick={() => void startPipeline()}
              disabled={isLoading}
            >
              <Play className="mr-2 h-4 w-4" />
              Start Pipeline
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overall Progress */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Overall Progress</CardTitle>
          <CardDescription>
            {pipelineStatus.isRunning
              ? `Processing stage: ${pipelineStatus.currentStage || 'Unknown'}`
              : `${pipelineStatus.completedStages} of ${pipelineStatus.totalStages} stages completed`}
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="space-y-4">
            <Progress value={overallProgress} className="h-3" />
            <div className="flex flex-wrap justify-between truncatebody-small">
              <span>{overallProgress}% Complete</span>
              <span>
                {pipelineStatus.completedStages}/{pipelineStatus.totalStages}{' '}
                Stages
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pipeline Stages */}
      <div className="grid gap-4">
        {stages.map((stage, index) => (
          <Card
            key={stage.id}
            className={`
            transition-all duration-200
            ${stage.status === 'running' ? 'ring-2 ring-blue-500 shadow-lg' : ''}
            ${stage.status === 'completed' ? 'bg-green-50 border-green-200' : ''}
            ${stage.status === 'error' ? 'bg-red-50 border-red-200' : ''}
          `}
          >
            <CardHeader className="pb-3">
              <div className="flex flex-wrap items-center justify-between">
                <div className="flex flex-wrap items-center gap-3">
                  <div className="flex flex-wrap items-center justify-center w-8 h-8 rounded-full bg-muted">
                    <span className="truncatelabel">{index + 1}</span>
                  </div>
                  {getStageIcon(stage)}
                  <div>
                    <CardTitle className="text-lg">{stage.name}</CardTitle>
                    <CardDescription>{stage.description}</CardDescription>
                  </div>
                </div>
                {getStatusBadge(stage.status)}
              </div>
            </CardHeader>

            {(stage.status === 'running' || stage.progress > 0) && (
              <CardContent className="pt-0 overflow-hidden">
                <div className="space-y-2">
                  <Progress value={stage.progress} className="h-2" />
                  <div className="flex flex-wrap justify-between truncate text-caption text-muted-foreground">
                    <span>{stage.progress}% Complete</span>
                    {stage.duration && <span>Duration: {stage.duration}s</span>}
                  </div>
                </div>
              </CardContent>
            )}

            {stage.error && (
              <CardContent className="pt-0 overflow-hidden">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{stage.error}</AlertDescription>
                </Alert>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TransactionPipeline;
