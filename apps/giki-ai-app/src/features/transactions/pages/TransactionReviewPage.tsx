import React, { useState, useEffect, useCallback } from 'react';
import { TransactionTable } from '../components/TransactionTable';
import { Transaction } from '@/shared/types/categorization';
import { Button } from '@/shared/components/ui/button';
import CategoryBatchTool from '@/features/categories/components/CategoryBatchTool';
import {
  fetchCategories,
  type Category,
} from '@/features/categories/services/categoryService';
import {
  useRealtimeSync,
  useRealtimeEmit,
} from '@/shared/services/realtime/realtimeSync';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import {
  Download,
  RefreshCw,
  AlertCircle,
  TrendingUp,
  DollarSign,
  Calendar,
  FileText,
  FileSpreadsheet,
  FileDown,
} from 'lucide-react';
import { toast } from '@/shared/components/ui/use-toast';
import { fetchTransactions } from '../services/transactionService';
import {
  exportTransactions,
  type ExportFormat,
} from '../services/exportService';

const TransactionReviewPage: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTransactionIds, setSelectedTransactionIds] = useState<
    Set<string>
  >(new Set());
  const [showBatchCategorization, setShowBatchCategorization] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);

  // Real-time sync
  const { emit } = useRealtimeEmit();

  // Listen for transaction updates from agent
  useRealtimeSync(
    'transaction.categorized',
    (data: unknown) => {
      const updateData = data as {
        source?: string;
        transactionId?: string;
        field?: string;
        value?: unknown;
      };
      if (
        updateData.source === 'agent' &&
        updateData.transactionId &&
        updateData.field
      ) {
        // Update transaction if it was categorized by the agent
        setTransactions((prev) =>
          prev.map((transaction) =>
            transaction.id === updateData.transactionId
              ? {
                  ...transaction,
                  [updateData.field]: updateData.value,
                  ai_categorized: true,
                }
              : transaction,
          ),
        );
        toast({
          title: 'AI Update',
          description: `Transaction categorized by AI assistant`,
        });
      }
    },
    [],
  );

  // Statistics
  const stats = React.useMemo(() => {
    const totalTransactions = transactions.length;
    const totalAmount = transactions.reduce(
      (sum, t) =>
        sum + (t.transaction_type === 'credit' ? t.amount : -t.amount),
      0,
    );
    const pendingCount = transactions.filter((t) => !t.is_categorized).length;
    const categorizedCount = transactions.filter(
      (t) => t.category_path && t.category_path !== 'Other',
    ).length;

    return {
      totalTransactions,
      totalAmount,
      pendingCount,
      categorizedCount,
      categorizationRate:
        totalTransactions > 0
          ? (categorizedCount / totalTransactions) * 100
          : 0,
    };
  }, [transactions]);

  // Load real transactions on component mount
  useEffect(() => {
    const loadTransactions = async () => {
      setIsLoading(true);
      try {
        const response = await fetchTransactions({
          page: 1,
          pageSize: 100,
        });
        setTransactions(
          Array.isArray(response) ? response : response.items || [],
        );
      } catch (_error) {
        console.error('Error loading transactions:', _error);
        toast({
          title: 'Error loading transactions',
          description: 'Failed to load transaction data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadTransactions();
  }, []);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const result = await fetchCategories();
        if (Array.isArray(result)) {
          setCategories(result);
        }
      } catch (_error) {
        console.error('Error loading categories:', _error);
      }
    };
    void loadCategories();
  }, []);

  const handleUpdateTransaction = useCallback(
    (id: string, field: keyof Transaction, value: unknown) => {
      setTransactions((prev) =>
        prev.map((transaction) =>
          transaction.id === id
            ? { ...transaction, [field]: value }
            : transaction,
        ),
      );
      setHasUnsavedChanges(true);

      // Emit real-time event when category is updated
      if (field === 'category_path' || field === 'category_id') {
        emit('transaction.categorized', {
          transactionId: id,
          field,
          value,
          timestamp: Date.now(),
        });
      }
    },
    [emit],
  );

  const handleSaveChanges = useCallback(async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Here you would make actual API calls to save the changes
      // await updateTransactions(transactions);

      setHasUnsavedChanges(false);
      toast({
        title: 'Changes saved',
        description: 'All transaction updates have been saved successfully.',
      });
    } catch (error) {
      console.error('Error saving changes:', error);
      toast({
        title: 'Error saving changes',
        description: 'Failed to save transaction updates. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call to refresh data
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Here you would fetch fresh data from the API
      // const freshData = await fetchTransactions();
      // setTransactions(freshData);

      toast({
        title: 'Data refreshed',
        description: 'Transaction data has been updated.',
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: 'Error refreshing data',
        description: 'Failed to refresh transaction data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleExport = useCallback(
    async (format: ExportFormat) => {
      try {
        await exportTransactions(transactions, {
          format,
          includeSummary: true,
          includeMetadata: true,
          categories,
        });

        toast({
          title: 'Export complete',
          description: `Transaction data has been exported to ${format.toUpperCase()}.`,
        });
      } catch (exportError) {
        console.error('Export failed:', exportError);
        toast({
          title: 'Export failed',
          description:
            exportError instanceof Error
              ? exportError.message
              : 'Failed to export transactions',
          variant: 'destructive',
        });
      }
    },
    [transactions, categories],
  );

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header with Logo-Based Gradient */}
      <div className="flex flex-wrap items-center justify-between">
        <div>
          <h1 className="truncate text-heading-1 tracking-tight text-gradient">
            Transaction Review
          </h1>
          <p className="text-muted-foreground">
            Review and edit your transaction data with real-time updates
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {selectedTransactionIds.size > 0 && (
            <Button
              onClick={() => setShowBatchCategorization(true)}
              className="max-w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              Batch Categorize ({selectedTransactionIds.size})
            </Button>
          )}
          <Button
            className="max-w-full"
            variant="outline"
            onClick={() => void handleRefresh()}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="max-w-full" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => void handleExport('csv')}>
                <FileDown className="h-4 w-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => void handleExport('excel')}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => void handleExport('pdf')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="truncatelabel">Total Transactions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">{stats.totalTransactions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="truncatelabel">Net Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div
              className={`text-2xl font-bold ${stats.totalAmount >= 0 ? 'text-success' : 'text-destructive'}`}
            >
              ${Math.abs(stats.totalAmount).toFixed(2)}
            </div>
            <p className="truncate text-caption text-muted-foreground">
              {stats.totalAmount >= 0 ? 'Positive' : 'Negative'} balance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="truncatelabel">Pending Review</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">{stats.pendingCount}</div>
            <p className="truncate text-caption text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="truncatelabel">Categorization Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="text-sm-large">
              {stats?.categorizationRate?.toFixed(1)}%
            </div>
            <p className="truncate text-caption text-muted-foreground">
              {stats.categorizedCount} of {stats.totalTransactions} categorized
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Unsaved Changes Alert */}
      {hasUnsavedChanges && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Don&apos;t forget to save your edits.
            <Button
              onClick={() => void handleSaveChanges()}
              disabled={isSaving}
              size="sm"
              className="max-w-full ml-2"
            >
              {isSaving ? 'Saving...' : 'Save Now'}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Batch Categorization Panel */}
      {showBatchCategorization && selectedTransactionIds.size > 0 && (
        <CategoryBatchTool
          selectedTransactions={transactions.filter((t) =>
            selectedTransactionIds.has(t.id),
          )}
          categories={categories}
          onCategorized={() => {
            setShowBatchCategorization(false);
            setSelectedTransactionIds(new Set());
            void handleRefresh();
          }}
          onCancel={() => setShowBatchCategorization(false)}
        />
      )}

      {/* Transaction Table */}
      <TransactionTable
        transactions={transactions}
        onUpdateTransaction={handleUpdateTransaction}
        onSaveChanges={() => void handleSaveChanges()}
        hasUnsavedChanges={hasUnsavedChanges}
        isLoading={isLoading}
        selectedRows={selectedTransactionIds}
        onSelectionChange={setSelectedTransactionIds}
      />
    </div>
  );
};

export default TransactionReviewPage;
