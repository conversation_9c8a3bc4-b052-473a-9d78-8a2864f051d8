import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Tabs,
  TabsContent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  BarChart3,
  Calendar,
  Download,
  FileText,
  Filter,
  LineChart,
  PieChart,
  RefreshCw,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';
import { DateRangePicker } from '@/shared/components/ui/date-range-picker';

import { Progress } from '@/shared/components/ui/progress';
import { toast } from '@/shared/components/ui/use-toast';
import { Loading } from '@/shared/components/ui/loading'; // Import Loading component

// Types for transaction analysis
interface CategoryBreakdown {
  name: string;
  amount: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
  transactionCount: number;
}

interface MonthlyTrend {
  month: string;
  year: number;
  income: number;
  expense: number;
  balance: number;
}

interface TransactionPattern {
  description: string;
  frequency: string;
  amount: number;
  category: string;
  lastOccurrence: string;
}

const TransactionAnalysisPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [_error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [timeFrame, setTimeFrame] = useState<string>('last_6_months');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<{
    startDate: string | null;
    endDate: string | null;
  }>({
    startDate: null,
    endDate: null,
  });

  // Mock data - would be fetched from API in real implementation
  const [categoryBreakdown, setCategoryBreakdown] = useState<
    CategoryBreakdown[]
  >([]);
  const [monthlyTrend, setMonthlyTrend] = useState<MonthlyTrend[]>([]);
  const [transactionPatterns, setTransactionPatterns] = useState<
    TransactionPattern[]
  >([]);
  const [summaryStats, setSummaryStats] = useState({
    totalIncome: 0,
    totalExpense: 0,
    netBalance: 0,
    transactionCount: 0,
    incomeVsExpenseRatio: 0,
    largestExpenseCategory: '',
    largestIncomeSource: '',
  });

  // Initialize with real transaction data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Import transaction service
        const { fetchTransactions } = await import(
          '../services/transactionService'
        );

        // Get real transaction data based on selected filters
        const startDate = dateRange.startDate || undefined;
        const endDate = dateRange.endDate || undefined;

        const response = await fetchTransactions({
          startDate,
          endDate,
          pageSize: 1000, // Get more data for analysis
        });

        const transactions = 'items' in response ? response.items : response;

        // Calculate category breakdown from real data
        const categoryMap = new Map<
          string,
          { amount: number; count: number }
        >();
        let totalExpenses = 0;

        transactions.forEach((tx) => {
          if (tx.amount < 0) {
            // Only expenses for breakdown
            const category = tx.category_path || 'Uncategorized';
            const existing = categoryMap.get(category) || {
              amount: 0,
              count: 0,
            };
            existing.amount += Math.abs(tx.amount);
            existing.count += 1;
            categoryMap.set(category, existing);
            totalExpenses += Math.abs(tx.amount);
          }
        });

        // Convert to CategoryBreakdown array
        const categoryBreakdown: CategoryBreakdown[] = Array.from(
          categoryMap.entries(),
        )
          .map(([name, data]) => ({
            name,
            amount: data.amount,
            percentage:
              totalExpenses > 0 ? (data.amount / totalExpenses) * 100 : 0,
            trend: 'stable' as const, // Would need historical data to calculate
            transactionCount: data.count,
          }))
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 8); // Top 8 categories

        // Calculate monthly trend from real data
        const monthlyData = new Map<
          string,
          { income: number; expense: number }
        >();
        const monthNames = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ];

        // Group transactions by month
        transactions.forEach((tx) => {
          const date = new Date(tx.date);
          const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
          const existing = monthlyData.get(monthKey) || {
            income: 0,
            expense: 0,
          };

          if (tx.amount > 0) {
            existing.income += tx.amount;
          } else {
            existing.expense += Math.abs(tx.amount);
          }

          monthlyData.set(monthKey, existing);
        });

        // Convert to MonthlyTrend array
        const monthlyTrend: MonthlyTrend[] = [];
        const sortedMonths = Array.from(monthlyData.keys()).sort().slice(-6); // Last 6 months

        sortedMonths.forEach((monthKey) => {
          const [year, month] = monthKey.split('-').map(Number);
          const data = monthlyData.get(monthKey);

          monthlyTrend.push({
            month: monthNames[month],
            year: year,
            income: Math.round(data.income * 100) / 100,
            expense: Math.round(data.expense * 100) / 100,
            balance: Math.round((data.income - data.expense) * 100) / 100,
          });
        });

        // Detect recurring transaction patterns
        const transactionPatterns: TransactionPattern[] = [];
        const descriptionGroups = new Map<string, typeof transactions>();

        // Group transactions by similar descriptions
        transactions.forEach((tx) => {
          const key = tx?.description?.toLowerCase().trim();
          const group = descriptionGroups.get(key) || [];
          group.push(tx);
          descriptionGroups.set(key, group);
        });

        // Analyze groups for patterns
        descriptionGroups.forEach((group, _description) => {
          if (group.length >= 2) {
            // Need at least 2 occurrences to detect pattern
            // Sort by date
            const sorted = [...group].sort(
              (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
            );

            // Calculate average interval between transactions
            const intervals: number[] = [];
            for (let i = 1; i < sorted.length; i++) {
              const days = Math.floor(
                (new Date(sorted[i].date).getTime() -
                  new Date(sorted[i - 1].date).getTime()) /
                  (1000 * 60 * 60 * 24),
              );
              intervals.push(days);
            }

            const avgInterval =
              intervals.reduce((a, b) => a + b, 0) / intervals.length;

            // Determine frequency
            let frequency: string;
            if (avgInterval <= 7) frequency = 'Weekly';
            else if (avgInterval <= 14) frequency = 'Bi-weekly';
            else if (avgInterval <= 31) frequency = 'Monthly';
            else if (avgInterval <= 93) frequency = 'Quarterly';
            else frequency = 'Irregular';

            // Calculate average amount
            const avgAmount =
              group.reduce((sum, tx) => sum + Math.abs(tx.amount), 0) /
              group.length;

            transactionPatterns.push({
              description: sorted[0].description,
              frequency,
              amount: Math.round(avgAmount * 100) / 100,
              category: sorted[0].category_path || 'Uncategorized',
              lastOccurrence: sorted[sorted.length - 1].date,
            });
          }
        });

        // Sort by frequency and amount
        transactionPatterns.sort((a, b) => b.amount - a.amount);

        // Calculate summary stats
        const totalIncome = monthlyTrend.reduce(
          (sum, month) => sum + month.income,
          0,
        );
        const totalExpense = monthlyTrend.reduce(
          (sum, month) => sum + month.expense,
          0,
        );
        const netBalance = totalIncome - totalExpense;
        const incomeVsExpenseRatio = totalIncome / totalExpense;
        const transactionCount = categoryBreakdown.reduce(
          (sum, cat) => sum + cat.transactionCount,
          0,
        );

        // Find largest categories
        const largestExpenseCategory =
          categoryBreakdown.length > 0 ? categoryBreakdown[0].name : 'None';

        // Find largest income source
        const incomeByCategory = new Map<string, number>();
        transactions.forEach((tx) => {
          if (tx.amount > 0) {
            const category = tx.category_path || 'Uncategorized';
            incomeByCategory.set(
              category,
              (incomeByCategory.get(category) || 0) + tx.amount,
            );
          }
        });
        const largestIncomeSource =
          incomeByCategory.size > 0
            ? Array.from(incomeByCategory.entries()).sort(
                (a, b) => b[1] - a[1],
              )[0][0]
            : 'None';

        // Update state with real data
        setCategoryBreakdown(categoryBreakdown);
        setMonthlyTrend(monthlyTrend);
        setTransactionPatterns(transactionPatterns.slice(0, 10)); // Top 10 patterns
        setSummaryStats({
          totalIncome,
          totalExpense,
          netBalance,
          transactionCount,
          incomeVsExpenseRatio,
          largestExpenseCategory,
          largestIncomeSource,
        });
      } catch {
        setError('Failed to load transaction analysis data. Please try again.');
        toast({
          title: 'Error',
          description: 'Failed to load transaction analysis data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    };

    void loadData();
  }, [timeFrame, dateRange]); // Re-fetch when timeframe changes

  const handleTimeFrameChange = (value: string) => {
    setTimeFrame(value);
  };

  const handleExport = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      const headers = ['Category', 'Amount', 'Percentage', 'Transaction Count'];
      const csvRows = [
        headers.join(','),
        ...categoryBreakdown.map((cat) =>
          [
            cat.name,
            cat?.amount?.toFixed(2),
            cat?.percentage?.toFixed(1) + '%',
            cat.transactionCount,
          ].join(','),
        ),
      ];

      // Create and download CSV
      const csvContent = csvRows.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window?.URL?.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transaction-analysis-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window?.URL?.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: 'Transaction analysis report has been exported',
      });
    } catch {
      toast({
        title: 'Export Failed',
        description: 'Failed to export transaction analysis',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // This would re-fetch data from the API in a real implementation
    // For now, we'll just wait a bit and set isRefreshing to false
    setTimeout(() => {
      setIsRefreshing(false);
      toast({
        title: 'Data Refreshed',
        description: 'Transaction analysis data has been updated',
      });
    }, 1500);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Loading state
  if (isLoading && !isRefreshing) {
    return (
      <div className="container mx-auto py-6 px-4 flex flex-wrap flex-col items-center justify-center h-64">
        <Loading text="Loading transaction analysis..." />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex flex-wrap flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-6">
        <div className="space-y-2">
          <div className="flex flex-wrap items-center gap-3">
            <div className="w-10 h-10 bg-gradient-logo-subtle rounded-xl flex flex-wrap items-center justify-center shadow-lg">
              <BarChart3 className="w-6 h-6 truncatewhite" />
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight text-gradient">
                Transaction Analysis
              </h1>
              <p className="text-muted-foreground text-lg">
                Analyze your spending patterns and transaction history with
                insights
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap flex-wrap gap-3 w-full md:w-auto">
          <Select value={timeFrame} onValueChange={handleTimeFrameChange}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Time Frame" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_month">Last Month</SelectItem>
              <SelectItem value="last_3_months">Last 3 Months</SelectItem>
              <SelectItem value="last_6_months">Last 6 Months</SelectItem>
              <SelectItem value="year_to_date">Year to Date</SelectItem>
              <SelectItem value="last_year">Last Year</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          {timeFrame === 'custom' && (
            <DateRangePicker
              onChange={(range) => {
                setDateRange({
                  startDate: range?.from
                    ? range?.from?.toISOString().split('T')[0]
                    : null,
                  endDate: range?.to
                    ? range?.to?.toISOString().split('T')[0]
                    : null,
                });
              }}
            />
          )}

          <div className="flex flex-wrap space-x-2">
            <Button
              className="max-w-full"
              variant="outline"
              size="icon"
              onClick={() => void handleRefresh()}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <Loading size="sm" className="py-0" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>

            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => void handleExport()}
              disabled={isExporting}
            >
              {isExporting ? (
                <Loading size="sm" text="Exporting..." className="py-0" />
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export Report
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div className="absolute inset-0 bg-gradient-to-br from-success to-success/80 opacity-5 group-hover:opacity-10 transition-opacity" />
          <CardContent className="p-6 overflow-hidden">
            <div className="flex flex-wrap justify-between items-start mb-3">
              <div className="space-y-2">
                <p className="truncatelabel text-muted-foreground">
                  Total Income
                </p>
                <p className="truncate text-heading-1 text-success">
                  {formatCurrency(summaryStats.totalIncome)}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-success to-success/80 shadow-lg">
                <TrendingUp className="w-6 h-6 truncatewhite" />
              </div>
            </div>
            <p className="truncate text-caption text-muted-foreground">
              Largest source: {summaryStats.largestIncomeSource}
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div className="absolute inset-0 bg-gradient-to-br from-destructive to-destructive/80 opacity-5 group-hover:opacity-10 transition-opacity" />
          <CardContent className="p-6 overflow-hidden">
            <div className="flex flex-wrap justify-between items-start mb-3">
              <div className="space-y-2">
                <p className="truncatelabel text-muted-foreground">
                  Total Expenses
                </p>
                <p className="truncate text-heading-1 text-destructive">
                  {formatCurrency(summaryStats.totalExpense)}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-destructive to-destructive/80 shadow-lg">
                <TrendingDown className="w-6 h-6 truncatewhite" />
              </div>
            </div>
            <p className="truncate text-caption text-muted-foreground">
              Largest category: {summaryStats.largestExpenseCategory}
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div
            className={`absolute inset-0 bg-gradient-to-br ${summaryStats.netBalance >= 0 ? 'from-success to-success/80' : 'from-destructive to-destructive/80'} opacity-5 group-hover:opacity-10 transition-opacity`}
          />
          <CardContent className="p-6 overflow-hidden">
            <div className="flex flex-wrap justify-between items-start mb-3">
              <div className="space-y-2">
                <p className="truncatelabel text-muted-foreground">
                  Net Balance
                </p>
                <p
                  className={`text-3xl font-bold ${summaryStats.netBalance >= 0 ? 'text-success' : 'text-destructive'}`}
                >
                  {formatCurrency(summaryStats.netBalance)}
                </p>
              </div>
              <div
                className={`p-3 rounded-xl bg-gradient-to-br ${summaryStats.netBalance >= 0 ? 'from-success to-success/80' : 'from-destructive to-destructive/80'} shadow-lg`}
              >
                {summaryStats.netBalance >= 0 ? (
                  <TrendingUp className="w-6 h-6 truncatewhite" />
                ) : (
                  <TrendingDown className="w-6 h-6 truncatewhite" />
                )}
              </div>
            </div>
            <p className="truncate text-caption text-muted-foreground">
              Income/Expense Ratio:{' '}
              {summaryStats?.incomeVsExpenseRatio?.toFixed(2)}
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <div className="absolute inset-0 bg-gradient-to-br from-primary to-primary/80 opacity-5 group-hover:opacity-10 transition-opacity" />
          <CardContent className="p-6 overflow-hidden">
            <div className="flex flex-wrap justify-between items-start mb-3">
              <div className="space-y-2">
                <p className="truncatelabel text-muted-foreground">
                  Total Transactions
                </p>
                <p className="truncate text-heading-1 text-foreground">
                  {summaryStats.transactionCount}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg">
                <FileText className="w-6 h-6 truncatewhite" />
              </div>
            </div>
            <p className="truncate text-caption text-muted-foreground">
              Time Period: {timeFrame.replace(/_/g, ' ')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="category-breakdown">
            Category Breakdown
          </TabsTrigger>
          <TabsTrigger value="monthly-trends">Monthly Trends</TabsTrigger>
          <TabsTrigger value="recurring-transactions">
            Recurring Transactions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Monthly Income & Expenses</CardTitle>
                <CardDescription>
                  Six-month view of financial activity
                </CardDescription>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <div className="h-80 flex flex-wrap items-center justify-center">
                  {/* This would be a real chart in a complete implementation */}
                  <div className="text-center text-muted-foreground">
                    <LineChart className="h-12 w-12 mx-auto mb-2 text-primary/60" />
                    <p>Monthly trend chart would render here</p>
                    <p className="truncatebody-small mt-1">
                      Using real chart libraries like Recharts or Chart.js
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t bg-muted/10 flex flex-wrap justify-between">
                <div className="truncatebody-small">
                  Data from {monthlyTrend[0]?.month} {monthlyTrend[0]?.year} to{' '}
                  {monthlyTrend[monthlyTrend.length - 1]?.month}{' '}
                  {monthlyTrend[monthlyTrend.length - 1]?.year}
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Expense Categories</CardTitle>
                <CardDescription>Where your money is going</CardDescription>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <div className="h-48 flex flex-wrap items-center justify-center mb-4">
                  {/* This would be a real chart in a complete implementation */}
                  <div className="text-center text-muted-foreground">
                    <PieChart className="h-12 w-12 mx-auto mb-2 text-primary/60" />
                    <p>Pie chart would render here</p>
                  </div>
                </div>

                <div className="space-y-2">
                  {categoryBreakdown
                    .sort((a, b) => b.amount - a.amount)
                    .slice(0, 5)
                    .map((category, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex flex-wrap justify-between items-center text-sm">
                          <span>{category.name}</span>
                          <span className="font-medium">
                            {formatCurrency(category.amount)}
                          </span>
                        </div>
                        <div className="flex flex-wrap items-center">
                          <Progress
                            value={category.percentage}
                            className="h-2 flex flex-wrap-1"
                          />
                          <span className="ml-2 truncate text-caption text-muted-foreground w-12 text-right">
                            {category.percentage}%
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
              <CardFooter className="border-t bg-muted/10">
                <Button
                  className="max-w-full p-0 h-auto text-sm"
                  variant="ghost"
                >
                  View All Categories
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>
                  Your most recent financial activity
                </CardDescription>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Recent transactions - just showing recurring ones for the sample */}
                    {transactionPatterns
                      .slice(0, 5)
                      .map((transaction, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {formatDate(transaction.lastOccurrence)}
                          </TableCell>
                          <TableCell className="font-medium">
                            {transaction.description}
                          </TableCell>
                          <TableCell>{transaction.category}</TableCell>
                          <TableCell
                            className={`text-right ${
                              transaction.category === 'Income'
                                ? 'text-success'
                                : 'text-destructive'
                            }`}
                          >
                            {transaction.category === 'Income' ? '+' : '-'}
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t bg-muted/10 flex flex-wrap justify-between">
                <div className="truncatebody-small">
                  Showing 5 of {transactionPatterns.length} transactions
                </div>
                <Button
                  className="max-w-full p-0 h-auto text-sm"
                  variant="ghost"
                >
                  View All Transactions
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="category-breakdown">
          <Card>
            <CardHeader>
              <CardTitle>Expense Categories</CardTitle>
              <CardDescription>
                Detailed breakdown of your spending by category
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="h-48 flex flex-wrap items-center justify-center mb-6">
                {/* This would be a real chart in a complete implementation */}
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2 text-primary/60" />
                  <p>Category breakdown chart would render here</p>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-right">Percentage</TableHead>
                    <TableHead className="text-right">Transactions</TableHead>
                    <TableHead className="text-right">Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categoryBreakdown
                    .sort((a, b) => b.amount - a.amount)
                    .map((category, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {category.name}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(category.amount)}
                        </TableCell>
                        <TableCell className="text-right">
                          {category.percentage}%
                        </TableCell>
                        <TableCell className="text-right">
                          {category.transactionCount}
                        </TableCell>
                        <TableCell className="text-right">
                          {category.trend === 'up' ? (
                            <Badge className="bg-green-50 text-success border-green-200 max-w-[150px] truncate">
                              <TrendingUp className="mr-1 h-3 w-3" />
                              Increasing
                            </Badge>
                          ) : category.trend === 'down' ? (
                            <Badge className="bg-red-50 text-destructive border-red-200 max-w-[150px] truncate">
                              <TrendingDown className="mr-1 h-3 w-3" />
                              Decreasing
                            </Badge>
                          ) : (
                            <Badge className="bg-info/5 text-info border-blue-200 max-w-[150px] truncate">
                              Stable
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monthly-trends">
          <Card>
            <CardHeader>
              <div className="flex flex-wrap justify-between items-end">
                <div>
                  <CardTitle>Monthly Financial Trends</CardTitle>
                  <CardDescription>
                    Track your income and expenses over time
                  </CardDescription>
                </div>

                <div className="truncatebody-small">
                  <div className="flex flex-wrap items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Months: {monthlyTrend.length}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="h-80 flex flex-wrap items-center justify-center mb-6">
                {/* This would be a real chart in a complete implementation */}
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-12 w-12 mx-auto mb-2 text-primary/60" />
                  <p>Monthly trend chart would render here</p>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead className="text-right">Income</TableHead>
                    <TableHead className="text-right">Expenses</TableHead>
                    <TableHead className="text-right">Balance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {monthlyTrend.map((month, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {month.month} {month.year}
                      </TableCell>
                      <TableCell className="text-right text-success">
                        {formatCurrency(month.income)}
                      </TableCell>
                      <TableCell className="text-right text-destructive">
                        {formatCurrency(month.expense)}
                      </TableCell>
                      <TableCell
                        className={`text-right font-medium ${
                          month.balance >= 0
                            ? 'text-success'
                            : 'text-destructive'
                        }`}
                      >
                        {formatCurrency(month.balance)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recurring-transactions">
          <Card>
            <CardHeader>
              <CardTitle>Recurring Transactions</CardTitle>
              <CardDescription>
                Regular income and expenses that occur on a schedule
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="flex flex-wrap items-center justify-between mb-4">
                <div className="truncatebody-small">
                  Showing {transactionPatterns.length} recurring transactions
                </div>
                <Button className="max-w-full" variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead>Last Occurrence</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactionPatterns.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {transaction.description}
                      </TableCell>
                      <TableCell>{transaction.frequency}</TableCell>
                      <TableCell>{transaction.category}</TableCell>
                      <TableCell
                        className={`text-right ${
                          transaction.category === 'Income'
                            ? 'text-success'
                            : 'text-destructive'
                        }`}
                      >
                        {transaction.category === 'Income' ? '+' : '-'}
                        {formatCurrency(transaction.amount)}
                      </TableCell>
                      <TableCell>
                        {formatDate(transaction.lastOccurrence)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="mt-6">
                <h3 className="text-base font-medium mb-2">
                  Upcoming Payments
                </h3>
                <div className="border rounded-md divide-y">
                  {transactionPatterns.slice(0, 3).map((transaction, index) => {
                    // Calculate next occurrence date
                    const lastDate = new Date(transaction.lastOccurrence);
                    const nextDate = new Date(lastDate);

                    if (transaction.frequency === 'Monthly') {
                      nextDate.setMonth(lastDate.getMonth() + 1);
                    } else if (transaction.frequency === 'Weekly') {
                      nextDate.setDate(lastDate.getDate() + 7);
                    } else if (transaction.frequency === 'Bi-weekly') {
                      nextDate.setDate(lastDate.getDate() + 14);
                    }

                    return (
                      <div key={index} className="p-4">
                        <div className="flex flex-wrap justify-between items-center">
                          <div>
                            <p className="font-medium">
                              {transaction.description}
                            </p>
                            <p className="truncatebody-small">
                              {transaction.category}
                            </p>
                          </div>
                          <div className="text-right">
                            <p
                              className={`font-medium ${
                                transaction.category === 'Income'
                                  ? 'text-success'
                                  : 'text-destructive'
                              }`}
                            >
                              {transaction.category === 'Income' ? '+' : '-'}
                              {formatCurrency(transaction.amount)}
                            </p>
                            <p className="truncatebody-small">
                              Due {formatDate(nextDate.toISOString())}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TransactionAnalysisPage;
