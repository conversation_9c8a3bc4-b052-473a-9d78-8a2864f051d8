import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { RefreshCw, AlertCircle } from 'lucide-react';
import { useDashboard } from '../hooks/useDashboard';
import { DashboardMetricsGrid } from '../components/DashboardMetricsGrid';
import { DashboardMetricsSkeleton } from '../components/DashboardMetricsSkeleton';
import { RecentTransactionsList } from '../components/RecentTransactionsList';
import { RecentTransactionsSkeleton } from '../components/RecentTransactionsSkeleton';
import { CategoryBreakdownChart } from '../components/CategoryBreakdownChart';
import { DateRangePicker } from '@/shared/components/ui/date-range-picker';
import { LoadingPage } from '@/shared/components/ui/giki-loading';

interface OnboardingStatus {
  isComplete: boolean;
  currentStep?: number;
  totalSteps?: number;
  accuracy?: number;
  hasHistoricalData?: boolean;
}

export const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const [dateRange, setDateRange] = useState<
    { from: string; to: string } | undefined
  >();
  const [onboardingStatus, setOnboardingStatus] =
    useState<OnboardingStatus | null>(null);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);

  // Check onboarding status on page load
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        // Import apiClient here to avoid circular imports
        const { apiClient } = await import('@/shared/services/api/apiClient');
        const response = await apiClient.get('/onboarding/status');

        setOnboardingStatus({
          isComplete: response.data?.is_complete || false,
          currentStep: response.data?.current_step || 0,
          totalSteps: response.data?.total_steps || 3,
          accuracy: response.data?.accuracy || 0,
          hasHistoricalData: response.data?.has_historical_data || false,
        });
      } catch (error) {
        // If onboarding endpoint fails, assume user needs onboarding
        setOnboardingStatus({
          isComplete: false,
          currentStep: 0,
          totalSteps: 3,
        });
      } finally {
        setIsCheckingOnboarding(false);
      }
    };

    void checkOnboardingStatus();
  }, []);

  // Only load dashboard if onboarding is complete
  const {
    data,
    loading,
    error,
    refresh,
    isRefreshing,
    lastUpdated,
    setDateRange: updateDateRange,
  } = useDashboard({
    dateRange,
    autoRefresh: onboardingStatus?.isComplete || false,
    refreshInterval: 5 * 60 * 1000, // 5 minutes
    enabled: onboardingStatus?.isComplete || false, // Only fetch if onboarded
  });

  const handleDateRangeChange = (
    newRange: { from?: Date; to?: Date } | undefined,
  ) => {
    if (newRange?.from && newRange?.to) {
      const range = {
        from: newRange?.from?.toISOString().split('T')[0],
        to: newRange?.to?.toISOString().split('T')[0],
      };
      setDateRange(range);
      updateDateRange(range);
    } else {
      setDateRange(undefined);
      updateDateRange(undefined);
    }
  };

  const formatLastUpdated = (timestamp: string | null) => {
    if (!timestamp) return 'Never';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  // Show loading while checking onboarding status
  if (isCheckingOnboarding) {
    return (
      <LoadingPage
        message="Loading Dashboard"
        description="Checking your setup status..."
      />
    );
  }

  // Simple onboarding redirect
  if (onboardingStatus && !onboardingStatus.isComplete) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-6">
          <h1 className="text-2xl font-bold">Welcome to Giki AI</h1>
          <p className="text-muted-foreground">
            Complete your setup to get started.
          </p>
          <Button onClick={() => navigate('/onboarding')}>
            Continue Setup
          </Button>
        </div>
      </div>
    );
  }

  // User has completed onboarding - show full dashboard
  if (loading && !data) {
    return (
      <LoadingPage
        message="Loading Dashboard"
        description="Gathering your financial overview..."
      />
    );
  }

  if (error && !data) {
    return (
      <div className="space-y-6">
        <div className="flex flex-wrap items-center justify-between">
          <h1 className="text-3xl font-bold">Dashboard</h1>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex flex-wrap items-center justify-between">
            <span>{error.message}</span>
            <Button
              className="max-w-full"
              variant="outline"
              size="sm"
              onClick={() => void refresh()}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Simple header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="flex items-center gap-3">
          <DateRangePicker onChange={handleDateRangeChange} />
          <Button
            variant="outline"
            size="sm"
            onClick={() => void refresh()}
            disabled={isRefreshing}
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error handling */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}

      {data && (
        <>
          {/* Key metrics - clean and simple */}
          {loading || isRefreshing ? (
            <DashboardMetricsSkeleton />
          ) : (
            <DashboardMetricsGrid metrics={data.metrics} />
          )}

          {/* Main content - single row layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent transactions */}
            <div>
              {loading || isRefreshing ? (
                <RecentTransactionsSkeleton />
              ) : (
                <RecentTransactionsList
                  transactions={data.recentTransactions}
                  onViewAll={() => (window.location.href = '/transactions')}
                />
              )}
            </div>

            {/* Category breakdown */}
            <div>
              <CategoryBreakdownChart
                data={data.categoryBreakdown}
                onViewAll={() => (window.location.href = '/reports')}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardPage;
