/**
 * DashboardPage Component Tests
 * Critical financial dashboard component tests for main user interface
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import DashboardPage from './DashboardPage';

// Mock the dashboard hook
vi.mock('../hooks/useDashboard', () => ({
  useDashboard: vi.fn(),
}));

// Mock navigation (window?.location?.href)
const mockLocationHref = vi.fn();
Object.defineProperty(window, 'location', {
  value: { href: mockLocationHref },
  writable: true,
});

// Mock dashboard components
vi.mock('../components/DashboardMetricsGrid', () => ({
  DashboardMetricsGrid: ({ metrics }: any) => (
    <div data-testid="metrics-grid">
      <div>Total: ₹{metrics?.totalSpent?.toLocaleString('en-IN')}</div>
      <div>Income: ₹{metrics?.totalIncome?.toLocaleString('en-IN')}</div>
    </div>
  ),
}));

vi.mock('../components/RecentTransactionsList', () => ({
  RecentTransactionsList: ({ transactions, onViewAll }: any) => (
    <div data-testid="recent-transactions">
      <div>Transactions: {transactions.length}</div>
      <button onClick={() => void onViewAll()}>View All</button>
    </div>
  ),
}));

vi.mock('../components/SpendingTrendChart', () => ({
  SpendingTrendChart: ({ data, onViewDetails }: any) => (
    <div data-testid="spending-chart">
      <div>Trend Data Points: {data.length}</div>
      <button onClick={() => void onViewDetails()}>View Reports</button>
    </div>
  ),
}));

import { useDashboard } from '../hooks/useDashboard';

describe('DashboardPage - Financial Dashboard Component', () => {
  const mockRefresh = vi.fn();
  const mockSetDateRange = vi.fn();

  const mockDashboardData = {
    metrics: {
      totalSpent: 45000,
      totalIncome: 85000,
      netIncome: 40000,
      totalTransactions: 156,
      categorizedTransactions: 142,
      avgTransactionAmount: 2890,
      topCategory: 'Food & Dining',
      topCategoryAmount: 12500,
    },
    recentTransactions: [
      { id: '1', description: 'SWIGGY ORDER', amount: -450 },
      { id: '2', description: 'SALARY CREDIT', amount: 75000 },
      { id: '3', description: 'UBER RIDE', amount: -180 },
    ],
    categoryBreakdown: [
      { category: 'Food & Dining', amount: 12500, percentage: 28 },
      { category: 'Transportation', amount: 8500, percentage: 19 },
    ],
    monthlyTrends: [
      { month: 'Jan', spent: 42000, income: 80000 },
      { month: 'Feb', spent: 45000, income: 85000 },
    ],
    quickActions: [
      { id: 'upload', title: 'Upload Data', action: '/upload' },
      { id: 'categorize', title: 'Review Categories', action: '/categories' },
    ],
    processingStatus: {
      total: 156,
      processed: 142,
      pending: 14,
      status: 'processing',
    },
  };

  const mockHookResponse = {
    data: mockDashboardData,
    loading: false,
    error: null,
    refresh: mockRefresh,
    isRefreshing: false,
    lastUpdated: '2024-01-15T10:30:00Z',
    setDateRange: mockSetDateRange,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useDashboard).mockReturnValue(mockHookResponse);
  });

  it('renders dashboard with financial data', () => {
    render(<DashboardPage />);

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(
      screen.getByText('Financial overview and insights'),
    ).toBeInTheDocument();

    // Check financial metrics display
    expect(screen.getByText('Total: ₹45,000')).toBeInTheDocument();
    expect(screen.getByText('Income: ₹85,000')).toBeInTheDocument();

    // Check categorization progress
    expect(screen.getByText('91%')).toBeInTheDocument(); // 142/156 * 100
    expect(screen.getByText('142 of 156 transactions')).toBeInTheDocument();
  });

  it('handles loading state correctly', () => {
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      loading: true,
      data: null,
    });

    render(<DashboardPage />);

    expect(screen.getByText('Loading Dashboard')).toBeInTheDocument();
    expect(
      screen.getByText('Gathering your financial overview...'),
    ).toBeInTheDocument();
  });

  it('handles error state with retry functionality', async () => {
    const user = userEvent.setup();
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      data: null,
      error: {
        message: 'Failed to load dashboard data',
        code: 'NETWORK_ERROR',
      },
    });

    render(<DashboardPage />);

    expect(
      screen.getByText('Failed to load dashboard data'),
    ).toBeInTheDocument();

    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });

  it('refreshes dashboard data when refresh button clicked', async () => {
    const user = userEvent.setup();
    render(<DashboardPage />);

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    await user.click(refreshButton);

    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });

  it('shows refreshing state correctly', () => {
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      isRefreshing: true,
    });

    render(<DashboardPage />);

    expect(screen.getByText('Updating...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /refresh/i })).toBeDisabled();
  });

  it('displays last updated time correctly', () => {
    render(<DashboardPage />);

    // Should show relative time for recent updates
    expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
  });

  it('handles date range filtering', async () => {
    const user = userEvent.setup();
    render(<DashboardPage />);

    // Find date range picker (mocked as input for simplicity)
    const dateRangePicker = screen
      .getByRole('button', { name: /refresh/i })
      .parentElement?.querySelector('[role="button"]');

    if (dateRangePicker) {
      await user.click(dateRangePicker);
    }

    // The actual date selection would happen in DateRangePicker component
    // We can test the handler is called by triggering it directly

    // This would be triggered by DateRangePicker onChange
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      data: mockDashboardData,
    });

    expect(mockSetDateRange).toBeDefined();
  });

  it('calculates financial metrics correctly', () => {
    render(<DashboardPage />);

    // Check categorization percentage calculation
    const categorizedPercentage = Math.round((142 / 156) * 100);
    expect(screen.getByText(`${categorizedPercentage}%`)).toBeInTheDocument();

    // Check average transaction amount formatting
    expect(screen.getByText('₹2,890')).toBeInTheDocument();

    // Check top category display
    expect(screen.getByText('Food & Dining')).toBeInTheDocument();
    expect(screen.getByText('₹12,500 spent')).toBeInTheDocument();
  });

  it('handles navigation to other sections', async () => {
    const user = userEvent.setup();
    render(<DashboardPage />);

    // Test navigation to transactions
    const viewAllTransactions = screen.getByText('View All');
    await user.click(viewAllTransactions);

    expect(mockLocationHref).toHaveBeenCalledWith('/transactions');
  });

  it('handles navigation to reports', async () => {
    const user = userEvent.setup();
    render(<DashboardPage />);

    // Test navigation to reports
    const viewReports = screen.getByText('View Reports');
    await user.click(viewReports);

    expect(mockLocationHref).toHaveBeenCalledWith('/reports');
  });

  it('shows error alert with cached data', () => {
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      error: { message: 'Network timeout', code: 'TIMEOUT' },
      data: mockDashboardData, // Has cached data
    });

    render(<DashboardPage />);

    expect(
      screen.getByText('Network timeout - Showing cached data'),
    ).toBeInTheDocument();
    // Should still show the data
    expect(screen.getByText('Total: ₹45,000')).toBeInTheDocument();
  });

  it('handles zero transaction scenario', () => {
    const emptyData = {
      ...mockDashboardData,
      metrics: {
        ...mockDashboardData.metrics,
        totalTransactions: 0,
        categorizedTransactions: 0,
      },
    };

    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      data: emptyData,
    });

    render(<DashboardPage />);

    // Should show 0% for empty data
    expect(screen.getByText('0%')).toBeInTheDocument();
    expect(screen.getByText('0 of 0 transactions')).toBeInTheDocument();
  });

  it('formats Indian currency correctly', () => {
    render(<DashboardPage />);

    // Check Indian number formatting
    expect(screen.getByText('₹45,000')).toBeInTheDocument();
    expect(screen.getByText('₹85,000')).toBeInTheDocument();
    expect(screen.getByText('₹2,890')).toBeInTheDocument();
  });

  it('shows trending indicators correctly', () => {
    render(<DashboardPage />);

    // With positive net income, should show trending up
    const topCategoryCard = screen.getByText('Top Category').closest('div');
    expect(topCategoryCard).toBeInTheDocument();
  });

  it('handles skeleton loading for partial updates', () => {
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      isRefreshing: true,
    });

    render(<DashboardPage />);

    // Should show loading skeletons while refreshing
    expect(screen.getByText('Updating...')).toBeInTheDocument();
  });

  it('displays date range status correctly', () => {
    render(<DashboardPage />);

    // Default should show "Last 30 days"
    expect(screen.getByText('Last 30 days')).toBeInTheDocument();
  });

  it('handles category drill-down navigation', () => {
    render(<DashboardPage />);

    // This would be triggered by InteractiveCategoryChart onCategoryDrillDown
    // The component should navigate to filtered transactions page
    expect(mockLocationHref).toBeDefined();
  });

  it('meets accessibility standards', () => {
    render(<DashboardPage />);

    // Check main heading
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toHaveTextContent('Dashboard');

    // Check interactive elements
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    expect(refreshButton).toBeEnabled();

    // Check alert has proper role
    vi.mocked(useDashboard).mockReturnValue({
      ...mockHookResponse,
      error: { message: 'Test error', code: 'TEST' },
    });

    const { rerender } = render(<DashboardPage />);
    rerender(<DashboardPage />);

    const alert = screen.queryByRole('alert');
    if (alert) {
      expect(alert).toBeInTheDocument();
    }
  });
});
