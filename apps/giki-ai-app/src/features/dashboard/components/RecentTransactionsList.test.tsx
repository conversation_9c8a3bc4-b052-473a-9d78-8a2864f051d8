/**
 * RecentTransactionsList Component Tests
 * Critical financial transaction display component tests for production
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import RecentTransactionsList from './RecentTransactionsList';
import type { RecentTransaction } from '../types/dashboard';

describe('RecentTransactionsList - Financial Transaction Display', () => {
  const mockOnViewAll = vi.fn();

  const baseMockTransactions: RecentTransaction[] = [
    {
      id: '1',
      description: 'Office Supplies Purchase',
      amount: 5000,
      date: '2024-06-14T10:00:00Z',
      category: 'Office Supplies',
      status: 'categorized',
      transaction_type: 'expense',
    },
    {
      id: '2',
      description: 'Client Payment Received',
      amount: 50000,
      date: '2024-06-13T15:30:00Z',
      category: 'Revenue',
      status: 'categorized',
      transaction_type: 'income',
    },
    {
      id: '3',
      description: 'Software Subscription',
      amount: 2500,
      date: '2024-06-12T09:15:00Z',
      category: 'Software',
      status: 'ai_suggested',
      transaction_type: 'expense',
    },
    {
      id: '4',
      description: 'Unknown Transaction',
      amount: 1200,
      date: '2024-06-11T14:45:00Z',
      status: 'needs_review',
      transaction_type: 'expense',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current date for consistent relative date testing
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-06-14T12:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Empty State Handling', () => {
    it('displays empty state when no transactions exist', () => {
      render(
        <RecentTransactionsList transactions={[]} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('No Recent Transactions')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Upload some financial data to see your recent transactions here.',
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: 'Upload Data' }),
      ).toBeInTheDocument();
    });

    it('navigates to upload page when upload button clicked', async () => {
      const user = userEvent.setup();

      // Mock window?.location?.href assignment
      const mockLocation = { href: '' };
      Object.defineProperty(window, 'location', {
        value: mockLocation,
        writable: true,
        configurable: true,
      });

      render(
        <RecentTransactionsList transactions={[]} onViewAll={mockOnViewAll} />,
      );

      const uploadButton = screen.getByRole('button', { name: 'Upload Data' });
      await user.click(uploadButton);

      expect(mockLocation.href).toBe('/upload');
    });
  });

  describe('Transaction List Display', () => {
    it('renders all provided transactions', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Office Supplies Purchase')).toBeInTheDocument();
      expect(screen.getByText('Client Payment Received')).toBeInTheDocument();
      expect(screen.getByText('Software Subscription')).toBeInTheDocument();
      expect(screen.getByText('Unknown Transaction')).toBeInTheDocument();
    });

    it('displays transaction count correctly', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(
        screen.getByText('Showing 4 recent transactions'),
      ).toBeInTheDocument();
    });

    it('shows view all button in header and footer', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const viewAllButtons = screen.getAllByText(/View All/);
      expect(viewAllButtons).toHaveLength(2);
    });
  });

  describe('Transaction Amount Formatting', () => {
    it('formats income amounts with positive indicator', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('+₹50,000')).toBeInTheDocument();
    });

    it('formats expense amounts with negative indicator', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('-₹5,000')).toBeInTheDocument();
      expect(screen.getByText('-₹2,500')).toBeInTheDocument();
      expect(screen.getByText('-₹1,200')).toBeInTheDocument();
    });

    it('formats large amounts correctly', () => {
      const largeAmountTransaction: RecentTransaction = {
        id: 'large',
        description: 'Major Contract Payment',
        amount: 1500000,
        date: '2024-06-14T10:00:00Z',
        category: 'Contracts',
        status: 'categorized',
        transaction_type: 'income',
      };

      render(
        <RecentTransactionsList
          transactions={[largeAmountTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('+₹15,00,000')).toBeInTheDocument(); // Indian number format
    });
  });

  describe('Date Formatting and Relative Dates', () => {
    it('shows "Today" for transactions from today', () => {
      const todayTransaction: RecentTransaction = {
        id: 'today',
        description: 'Today Transaction',
        amount: 1000,
        date: '2024-06-14T10:00:00Z',
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[todayTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Today')).toBeInTheDocument();
    });

    it('shows "Yesterday" for transactions from yesterday', () => {
      const yesterdayTransaction: RecentTransaction = {
        id: 'yesterday',
        description: 'Yesterday Transaction',
        amount: 1000,
        date: '2024-06-13T10:00:00Z',
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[yesterdayTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Yesterday')).toBeInTheDocument();
    });

    it('shows days ago for recent transactions', () => {
      const recentTransaction: RecentTransaction = {
        id: 'recent',
        description: 'Recent Transaction',
        amount: 1000,
        date: '2024-06-11T10:00:00Z', // 3 days ago
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[recentTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('3 days ago')).toBeInTheDocument();
    });

    it('shows formatted date for older transactions', () => {
      const oldTransaction: RecentTransaction = {
        id: 'old',
        description: 'Old Transaction',
        amount: 1000,
        date: '2024-06-01T10:00:00Z', // More than a week ago
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[oldTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Jun 1')).toBeInTheDocument();
    });
  });

  describe('Status Badge Display', () => {
    it('displays categorized status with correct styling', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const categorizedBadges = screen.getAllByText('Categorized');
      expect(categorizedBadges).toHaveLength(2); // Two categorized transactions
    });

    it('displays AI suggested status with correct styling', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('AI Suggested')).toBeInTheDocument();
    });

    it('displays needs review status with correct styling', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Needs Review')).toBeInTheDocument();
    });
  });

  describe('Category Display', () => {
    it('shows category when available', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Revenue')).toBeInTheDocument();
      expect(screen.getByText('Software')).toBeInTheDocument();
    });

    it('hides category section when not available', () => {
      const transactionWithoutCategory: RecentTransaction = {
        id: 'no-category',
        description: 'Uncategorized Transaction',
        amount: 1000,
        date: '2024-06-14T10:00:00Z',
        status: 'needs_review',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[transactionWithoutCategory]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Uncategorized Transaction')).toBeInTheDocument();
      // Should not show category separator
      expect(screen.queryByText('•')).not.toBeInTheDocument();
    });
  });

  describe('Transaction Type Indicators', () => {
    it('uses green styling for income transactions', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const incomeAmount = screen.getByText('+₹50,000');
      expect(incomeAmount).toHaveClass('text-green-600');
    });

    it('uses red styling for expense transactions', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const expenseAmount = screen.getByText('-₹5,000');
      expect(expenseAmount).toHaveClass('text-destructive');
    });
  });

  describe('User Interactions', () => {
    it('calls onViewAll when header view all button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const headerViewAllButton = screen.getAllByText('View All')[0];
      await user.click(headerViewAllButton);

      expect(mockOnViewAll).toHaveBeenCalledTimes(1);
    });

    it('calls onViewAll when footer view all button is clicked', async () => {
      const user = userEvent.setup();
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const footerViewAllButton = screen.getByText('View All Transactions');
      await user.click(footerViewAllButton);

      expect(mockOnViewAll).toHaveBeenCalledTimes(1);
    });

    it('shows hover effects on transaction items', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const transactionItems = screen.getAllByText(
        /Purchase|Payment|Subscription|Unknown/,
      );
      transactionItems.forEach((item) => {
        const transactionContainer = item.closest('.hover\\:bg-muted\\/50');
        expect(transactionContainer).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles transactions with very long descriptions', () => {
      const longDescriptionTransaction: RecentTransaction = {
        id: 'long',
        description:
          'This is a very long transaction description that should be truncated properly to avoid layout issues in the UI component when displayed to users',
        amount: 1000,
        date: '2024-06-14T10:00:00Z',
        category: 'Test Category',
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[longDescriptionTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(
        screen.getByText(longDescriptionTransaction.description),
      ).toBeInTheDocument();
    });

    it('handles zero amount transactions', () => {
      const zeroAmountTransaction: RecentTransaction = {
        id: 'zero',
        description: 'Zero Amount Transaction',
        amount: 0,
        date: '2024-06-14T10:00:00Z',
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[zeroAmountTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('-₹0')).toBeInTheDocument();
    });

    it('handles invalid date gracefully', () => {
      const invalidDateTransaction: RecentTransaction = {
        id: 'invalid-date',
        description: 'Invalid Date Transaction',
        amount: 1000,
        date: 'invalid-date',
        status: 'categorized',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[invalidDateTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Invalid Date Transaction')).toBeInTheDocument();
      // Should gracefully handle invalid date without crashing
    });

    it('handles missing category field gracefully', () => {
      const noCategoryTransaction: RecentTransaction = {
        id: 'no-cat',
        description: 'No Category Transaction',
        amount: 1000,
        date: '2024-06-14T10:00:00Z',
        status: 'needs_review',
        transaction_type: 'expense',
      };

      render(
        <RecentTransactionsList
          transactions={[noCategoryTransaction]}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('No Category Transaction')).toBeInTheDocument();
      expect(screen.getByText('Needs Review')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Semantic Structure', () => {
    it('uses proper semantic HTML structure', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(
        screen.getByRole('button', { name: /View All/ }),
      ).toBeInTheDocument();
      expect(screen.getByText('Recent Transactions')).toBeInTheDocument();
    });

    it('includes proper text contrast for amounts', () => {
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      // Income should be green, expenses should be red for accessibility
      expect(screen.getByText('+₹50,000')).toHaveClass('text-green-600');
      expect(screen.getByText('-₹5,000')).toHaveClass('text-destructive');
    });

    it('maintains keyboard navigation for interactive elements', async () => {
      const user = userEvent.setup();
      render(
        <RecentTransactionsList
          transactions={baseMockTransactions}
          onViewAll={mockOnViewAll}
        />,
      );

      const viewAllButton = screen.getAllByRole('button')[0];

      // Test keyboard navigation
      await user.tab();
      expect(viewAllButton).toHaveFocus();

      await user.keyboard('{Enter}');
      expect(mockOnViewAll).toHaveBeenCalled();
    });
  });

  describe('Performance and Large Datasets', () => {
    it('renders efficiently with many transactions', () => {
      const manyTransactions: RecentTransaction[] = Array.from(
        { length: 100 },
        (_, i): RecentTransaction => ({
          id: `trans-${i}`,
          description: `Transaction ${i}`,
          amount: 1000 + i,
          date: '2024-06-14T10:00:00Z',
          category: `Category ${i % 5}`,
          status:
            i % 3 === 0
              ? ('categorized' as const)
              : i % 3 === 1
                ? ('ai_suggested' as const)
                : ('needs_review' as const),
          transaction_type:
            i % 2 === 0 ? ('income' as const) : ('expense' as const),
        }),
      );

      const startTime = performance.now();
      render(
        <RecentTransactionsList
          transactions={manyTransactions}
          onViewAll={mockOnViewAll}
        />,
      );
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(200); // 200ms threshold
      expect(
        screen.getByText('Showing 100 recent transactions'),
      ).toBeInTheDocument();
    });
  });
});
