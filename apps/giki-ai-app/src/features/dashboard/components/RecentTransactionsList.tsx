import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  ArrowRight,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  Brain,
} from 'lucide-react';
import type { RecentTransaction } from '../types/dashboard';
import { cn } from '@/shared/utils/utils';

interface RecentTransactionsListProps {
  transactions: RecentTransaction[];
  onViewAll: () => void;
}

export const RecentTransactionsList: React.FC<RecentTransactionsListProps> = ({
  transactions,
  onViewAll,
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(Math.abs(amount));
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;

    return date.toLocaleDateString('en-IN', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: RecentTransaction['status']) => {
    switch (status) {
      case 'categorized':
        return (
          <Badge
            variant="default"
            className="bg-success/10 text-success border-success/20 max-w-[150px] truncate"
          >
            <CheckCircle className="w-3 h-3 mr-1" />
            Categorized
          </Badge>
        );
      case 'ai_suggested':
        return (
          <Badge
            variant="secondary"
            className="bg-info/10 text-info border-info/20 max-w-[150px] truncate"
          >
            <Brain className="w-3 h-3 mr-1" />
            AI Suggested
          </Badge>
        );
      case 'needs_review':
        return (
          <Badge
            variant="outline"
            className="bg-warning/10 text-warning border-warning/20 max-w-[150px] truncate"
          >
            <AlertCircle className="w-3 h-3 mr-1" />
            Needs Review
          </Badge>
        );
    }
  };

  const getTransactionIcon = (type: 'income' | 'expense') => {
    return type === 'income' ? (
      <div className="p-2 bg-success/10 rounded-lg">
        <TrendingUp className="h-4 w-4 text-success" />
      </div>
    ) : (
      <div className="p-2 bg-destructive/10 rounded-lg">
        <TrendingDown className="h-4 w-4 text-destructive" />
      </div>
    );
  };

  if (transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex flex-wrap items-center justify-between">
            <CardTitle className="text-lg font-semibold">
              Recent Transactions
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <Clock className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Recent Transactions</h3>
              <p className="text-sm max-w-md">
                Upload some financial data to see your recent transactions here.
              </p>
            </div>
            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => (window.location.href = '/upload')}
            >
              Upload Data
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Recent Transactions
          </CardTitle>
          <Button
            className="max-w-full"
            variant="ghost"
            size="sm"
            onClick={() => void onViewAll()}
          >
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        {transactions.map((transaction) => (
          <div
            key={transaction.id}
            className="flex flex-wrap items-center space-x-4 p-4 rounded-lg border border-border bg-card hover:bg-muted/50 transition-colors"
          >
            {/* Transaction Type Icon */}
            {getTransactionIcon(transaction.transaction_type)}

            {/* Transaction Details */}
            <div className="flex flex-wrap-1 min-w-0">
              <div className="flex flex-wrap items-start justify-between">
                <div className="flex flex-wrap-1 min-w-0">
                  <p className="font-medium text-foreground truncate">
                    {transaction.description}
                  </p>
                  <div className="flex flex-wrap items-center space-x-2 mt-1">
                    <span className="text-xs text-muted-foreground">
                      {formatDate(transaction.date)}
                    </span>
                    {transaction.category && (
                      <>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground truncate">
                          {transaction.category}
                        </span>
                      </>
                    )}
                  </div>
                </div>

                <div className="flex flex-wrap flex-col items-end space-y-2 ml-4">
                  <span
                    className={cn(
                      'text-sm font-semibold text-financial',
                      transaction.transaction_type === 'income'
                        ? 'text-success'
                        : 'text-destructive',
                    )}
                  >
                    {transaction.transaction_type === 'income' ? '+' : '-'}
                    {formatCurrency(transaction.amount)}
                  </span>
                  {getStatusBadge(transaction.status)}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Summary Footer */}
        <div className="pt-4 border-t border-border">
          <div className="flex flex-wrap items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Showing {transactions.length} recent transactions
            </span>
            <Button
              className="max-w-full"
              variant="ghost"
              size="sm"
              onClick={() => void onViewAll()}
            >
              View All Transactions
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentTransactionsList;
