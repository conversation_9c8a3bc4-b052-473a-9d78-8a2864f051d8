import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  LineChart as LineChartIcon,
  ArrowRight,
} from 'lucide-react';

interface MonthlyTrendData {
  month: string;
  income: number;
  expenses: number;
  net: number;
  transactionCount: number;
}

interface SpendingTrendChartProps {
  data: MonthlyTrendData[];
  onViewDetails?: () => void;
  variant?: 'line' | 'area';
}

export const SpendingTrendChart: React.FC<SpendingTrendChartProps> = ({
  data,
  onViewDetails,
  variant = 'area',
}) => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatMonth = (monthStr: string): string => {
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-IN', {
      month: 'short',
      year: '2-digit',
    });
  };

  const CustomTooltip = ({
    active,
    payload,
    label,
  }: {
    active?: boolean;
    payload?: Array<{
      payload: MonthlyTrendData;
      value: number;
      dataKey: string;
    }>;
    label?: string;
  }) => {
    if (active && payload && payload.length > 0) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-border rounded-lg shadow-lg">
          <p className="text-left mb-2">{formatMonth(label)}</p>
          <div className="space-y-1">
            <div className="flex flex-wrap items-center justify-between">
              <span className="text-success flex flex-wrap items-center">
                <div className="w-3 h-3 bg-success rounded-full mr-2" />
                Income:
              </span>
              <span className="font-semibold text-success text-financial">
                {formatCurrency(data.income)}
              </span>
            </div>
            <div className="flex flex-wrap items-center justify-between">
              <span className="text-destructive flex flex-wrap items-center">
                <div className="w-3 h-3 bg-destructive rounded-full mr-2" />
                Expenses:
              </span>
              <span className="font-semibold text-destructive text-financial">
                {formatCurrency(data.expenses)}
              </span>
            </div>
            <div className="flex flex-wrap items-center justify-between border-t pt-1">
              <span
                className={`flex items-center ${data.net >= 0 ? 'text-success' : 'text-destructive'}`}
              >
                <div
                  className={`w-3 h-3 rounded-full mr-2 ${data.net >= 0 ? 'bg-success' : 'bg-destructive'}`}
                />
                Net:
              </span>
              <span
                className={`font-semibold text-financial ${data.net >= 0 ? 'text-success' : 'text-destructive'}`}
              >
                {formatCurrency(Math.abs(data.net))}
              </span>
            </div>
            <div className="text-xs text-muted-foreground border-t pt-1">
              {data.transactionCount} transactions
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const getGrowthIndicator = () => {
    if (data.length < 2) return null;

    const lastMonth = data[data.length - 1];
    const prevMonth = data[data.length - 2];
    const growth = lastMonth.net - prevMonth.net;
    const growthPercent =
      prevMonth.net !== 0 ? (growth / Math.abs(prevMonth.net)) * 100 : 0;

    return (
      <div className="flex flex-wrap items-center space-x-2">
        {growth >= 0 ? (
          <TrendingUp className="w-4 h-4 text-green-600" />
        ) : (
          <TrendingDown className="w-4 h-4 text-red-600" />
        )}
        <Badge
          variant={growth >= 0 ? 'default' : 'destructive'}
          className="text-xs max-w-[150px] truncate"
        >
          {growth >= 0 ? '+' : ''}
          {growthPercent.toFixed(1)}%
        </Badge>
      </div>
    );
  };

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-card-foreground-title">
            Spending Trends
          </CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <LineChartIcon className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="truncate text-heading-5">No Trend Data</h3>
              <p className="text-xs text-muted-foreground max-w-md">
                Upload transactions to see monthly income and expense trends.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const chartData = data.map((item) => ({
    ...item,
    formattedMonth: formatMonth(item.month),
  }));

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-card-foreground-title">
              Spending Trends
            </CardTitle>
            <div className="flex flex-wrap items-center space-x-4">
              <span className="text-xs text-muted-foreground">
                {data.length} months
              </span>
              {getGrowthIndicator()}
            </div>
          </div>
          <div className="flex flex-wrap items-center space-x-2">
            <Button
              className="max-w-full"
              variant="ghost"
              size="sm"
              onClick={() => void onViewDetails()}
            >
              Details
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="overflow-hidden">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {variant === 'area' ? (
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient
                    id="incomeGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor="hsl(var(--giki-success))"
                      stopOpacity={0.3}
                    />
                    <stop
                      offset="95%"
                      stopColor="hsl(var(--giki-success))"
                      stopOpacity={0.05}
                    />
                  </linearGradient>
                  <linearGradient
                    id="expenseGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor="hsl(var(--giki-destructive))"
                      stopOpacity={0.3}
                    />
                    <stop
                      offset="95%"
                      stopColor="hsl(var(--giki-destructive))"
                      stopOpacity={0.05}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--giki-border-primary))"
                />
                <XAxis
                  dataKey="formattedMonth"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  tickFormatter={(value: number) => formatCurrency(value)}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="income"
                  stackId="1"
                  stroke="hsl(var(--giki-success))"
                  fill="url(#incomeGradient)"
                  strokeWidth={2}
                  name="Income"
                />
                <Area
                  type="monotone"
                  dataKey="expenses"
                  stackId="2"
                  stroke="hsl(var(--giki-destructive))"
                  fill="url(#expenseGradient)"
                  strokeWidth={2}
                  name="Expenses"
                />
              </AreaChart>
            ) : (
              <LineChart
                data={chartData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--giki-border-primary))"
                />
                <XAxis
                  dataKey="formattedMonth"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  tickFormatter={(value: number) => formatCurrency(value)}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="income"
                  stroke="hsl(var(--giki-success))"
                  strokeWidth={3}
                  dot={{
                    fill: 'hsl(var(--giki-success))',
                    strokeWidth: 2,
                    r: 4,
                  }}
                  activeDot={{
                    r: 6,
                    stroke: 'hsl(var(--giki-success))',
                    strokeWidth: 2,
                  }}
                  name="Income"
                />
                <Line
                  type="monotone"
                  dataKey="expenses"
                  stroke="hsl(var(--giki-destructive))"
                  strokeWidth={3}
                  dot={{
                    fill: 'hsl(var(--giki-destructive))',
                    strokeWidth: 2,
                    r: 4,
                  }}
                  activeDot={{
                    r: 6,
                    stroke: 'hsl(var(--giki-destructive))',
                    strokeWidth: 2,
                  }}
                  name="Expenses"
                />
                <Line
                  type="monotone"
                  dataKey="net"
                  stroke="hsl(var(--giki-brand-blue))"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{
                    fill: 'hsl(var(--giki-brand-blue))',
                    strokeWidth: 2,
                    r: 3,
                  }}
                  name="Net"
                />
              </LineChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 pt-6 border-t border-border">
          <div className="text-center space-y-2">
            <div className="text-xs text-muted-foreground">Avg Income</div>
            <div className="text-card-foreground-title text-financial text-success">
              {formatCurrency(
                data.reduce((sum, item) => sum + item.income, 0) / data.length,
              )}
            </div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-xs text-muted-foreground">Avg Expenses</div>
            <div className="text-card-foreground-title text-financial text-error">
              {formatCurrency(
                data.reduce((sum, item) => sum + item.expenses, 0) /
                  data.length,
              )}
            </div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-xs text-muted-foreground">Total Net</div>
            <div
              className={`text-lg font-semibold text-financial ${
                data.reduce((sum, item) => sum + item.net, 0) >= 0
                  ? 'text-success'
                  : 'text-error'
              }`}
            >
              {formatCurrency(data.reduce((sum, item) => sum + item.net, 0))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SpendingTrendChart;
