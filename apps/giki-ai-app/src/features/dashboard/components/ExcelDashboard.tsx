/**
 * Excel-Inspired Dashboard Demo
 *
 * Demonstrates the new Excel-inspired design components in a financial dashboard context.
 * Shows professional data presentation with Excel-like styling and functionality.
 */
import React, { useState } from 'react';
import ExcelLayout from '@/shared/components/ui/excel-layout';
import ExcelCard from '@/shared/components/ui/excel-card';
import ExcelTable from '@/shared/components/ui/excel-table';
import { Button } from '@/shared/components/ui/button';
// import { Badge } from '@/shared/components/ui/badge';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  PieChart,
  BarChart3,
} from 'lucide-react';

// Sample financial data
const sampleTransactions = [
  {
    id: '1',
    date: '2025-01-15',
    description: 'Office Supplies - Staples',
    amount: -156.78,
    category: 'Office Expenses',
    account: 'Business Checking',
    status: 'cleared',
    type: 'expense',
  },
  {
    id: '2',
    date: '2025-01-14',
    description: 'Client Payment - ABC Corp',
    amount: 2500.0,
    category: 'Revenue',
    account: 'Business Checking',
    status: 'cleared',
    type: 'income',
  },
  {
    id: '3',
    date: '2025-01-13',
    description: 'Software Subscription',
    amount: -99.99,
    category: 'Software',
    account: 'Business Credit Card',
    status: 'pending',
    type: 'expense',
  },
  {
    id: '4',
    date: '2025-01-12',
    description: 'Travel - Flight to NYC',
    amount: -487.5,
    category: 'Travel',
    account: 'Business Credit Card',
    status: 'cleared',
    type: 'expense',
  },
  {
    id: '5',
    date: '2025-01-11',
    description: 'Consulting Revenue',
    amount: 1800.0,
    category: 'Professional Services',
    account: 'Business Checking',
    status: 'cleared',
    type: 'income',
  },
];

const tableColumns = [
  {
    key: 'date',
    label: 'Date',
    type: 'date' as const,
    sortable: true,
    width: '100px',
  },
  {
    key: 'description',
    label: 'Description',
    type: 'text' as const,
    sortable: true,
    width: '300px',
  },
  {
    key: 'amount',
    label: 'Amount',
    type: 'currency' as const,
    sortable: true,
    aggregatable: true,
    width: '120px',
  },
  {
    key: 'category',
    label: 'Category',
    type: 'text' as const,
    sortable: true,
    width: '180px',
  },
  {
    key: 'account',
    label: 'Account',
    type: 'text' as const,
    sortable: true,
    width: '150px',
  },
  {
    key: 'status',
    label: 'Status',
    type: 'status' as const,
    sortable: true,
    width: '100px',
  },
];

const ExcelDashboard: React.FC = () => {
  const [selectedView, setSelectedView] = useState('overview');

  // Calculate summary metrics
  const totalIncome = sampleTransactions
    .filter((t) => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = sampleTransactions
    .filter((t) => t.type === 'expense')
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const netIncome = totalIncome - totalExpenses;

  return (
    <ExcelLayout
      title="Financial Dashboard"
      subtitle="Excel-inspired professional data presentation"
      showToolbar={true}
      showFormulaBar={false}
      actions={
        <div className="flex flex-wrap items-center gap-2">
          <Button
            className="max-w-full"
            variant={selectedView === 'overview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('overview')}
          >
            Overview
          </Button>
          <Button
            className="max-w-full"
            variant={selectedView === 'transactions' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('transactions')}
          >
            Transactions
          </Button>
          <Button
            className="max-w-full"
            variant={selectedView === 'analytics' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('analytics')}
          >
            Analytics
          </Button>
        </div>
      }
    >
      <div className="p-6 space-y-6">
        {selectedView === 'overview' && (
          <>
            {/* Financial Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <ExcelCard
                title="Total Income"
                value={totalIncome}
                subtitle="This month"
                trend={{
                  value: 12.5,
                  label: 'vs last month',
                  direction: 'up',
                }}
                icon={TrendingUp}
                variant="success"
              />

              <ExcelCard
                title="Total Expenses"
                value={totalExpenses}
                subtitle="This month"
                trend={{
                  value: -8.2,
                  label: 'vs last month',
                  direction: 'down',
                }}
                icon={TrendingDown}
                variant="warning"
              />

              <ExcelCard
                title="Net Income"
                value={netIncome}
                subtitle="This month"
                trend={{
                  value: 15.8,
                  label: 'vs last month',
                  direction: 'up',
                }}
                icon={DollarSign}
                variant={netIncome > 0 ? 'success' : 'error'}
              />

              <ExcelCard
                title="Transactions"
                value={sampleTransactions.length}
                subtitle="Total count"
                icon={CreditCard}
                variant="info"
              >
                <div className="flex flex-wrap items-center justify-between truncate text-caption">
                  <span>
                    Cleared:{' '}
                    {
                      sampleTransactions.filter((t) => t.status === 'cleared')
                        .length
                    }
                  </span>
                  <span>
                    Pending:{' '}
                    {
                      sampleTransactions.filter((t) => t.status === 'pending')
                        .length
                    }
                  </span>
                </div>
              </ExcelCard>
            </div>

            {/* Quick Summary Table */}
            <div className="bg-white rounded-lg border border-border p-4">
              <h3 className="text-card-foreground-title mb-4 flex flex-wrap items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Category Summary
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  'Office Expenses',
                  'Software',
                  'Travel',
                  'Revenue',
                  'Professional Services',
                ].map((category) => {
                  const categoryTransactions = sampleTransactions.filter(
                    (t) => t.category === category,
                  );
                  const categoryTotal = categoryTransactions.reduce(
                    (sum, t) => sum + t.amount,
                    0,
                  );

                  return (
                    <div
                      key={category}
                      className="flex flex-wrap items-center justify-between p-3 bg-muted/50 rounded border"
                    >
                      <span className="truncatelabel">{category}</span>
                      <div className="text-right">
                        <div className="text-sm text-financial">
                          {new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                          }).format(categoryTotal)}
                        </div>
                        <div className="truncate text-caption text-muted-foreground">
                          {categoryTransactions.length} transactions
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}

        {selectedView === 'transactions' && (
          <ExcelTable
            data={sampleTransactions}
            columns={tableColumns}
            title="Transaction Details"
            showAggregations={true}
            enableSelection={true}
            enableFiltering={true}
            enableExport={true}
          />
        )}

        {selectedView === 'analytics' && (
          <div className="bg-white rounded-lg border border-border p-6">
            <div className="flex flex-wrap items-center gap-2 mb-4">
              <PieChart className="h-5 w-5" />
              <h3 className="text-card-foreground-title">
                Financial Analytics
              </h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Income vs Expenses */}
              <div className="p-4 bg-muted/50 rounded border">
                <h4 className="font-medium mb-3">Income vs Expenses</h4>
                <div className="space-y-3">
                  <div className="flex flex-wrap items-center justify-between">
                    <span className="text-sm">Total Income</span>
                    <span className="text-sm text-success">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                      }).format(totalIncome)}
                    </span>
                  </div>
                  <div className="flex flex-wrap items-center justify-between">
                    <span className="text-sm">Total Expenses</span>
                    <span className="text-sm text-destructive">
                      -
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                      }).format(totalExpenses)}
                    </span>
                  </div>
                  <div className="pt-2 border-t border-border">
                    <div className="flex flex-wrap items-center justify-between font-semibold">
                      <span>Net Income</span>
                      <span
                        className={`font-mono ${netIncome >= 0 ? 'text-success' : 'text-destructive'}`}
                      >
                        {new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: 'USD',
                        }).format(netIncome)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Breakdown */}
              <div className="p-4 bg-muted/50 rounded border">
                <h4 className="font-medium mb-3">Account Breakdown</h4>
                <div className="space-y-2">
                  {['Business Checking', 'Business Credit Card'].map(
                    (account) => {
                      const accountTransactions = sampleTransactions.filter(
                        (t) => t.account === account,
                      );
                      const accountBalance = accountTransactions.reduce(
                        (sum, t) => sum + t.amount,
                        0,
                      );

                      return (
                        <div
                          key={account}
                          className="flex flex-wrap items-center justify-between"
                        >
                          <span className="text-sm">{account}</span>
                          <div className="text-right">
                            <div
                              className={`font-mono text-sm ${accountBalance >= 0 ? 'text-success' : 'text-destructive'}`}
                            >
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD',
                              }).format(accountBalance)}
                            </div>
                            <div className="truncate text-caption text-muted-foreground">
                              {accountTransactions.length} transactions
                            </div>
                          </div>
                        </div>
                      );
                    },
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ExcelLayout>
  );
};

export default ExcelDashboard;
