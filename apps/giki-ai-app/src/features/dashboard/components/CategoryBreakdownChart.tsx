import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { ArrowRight, PieChart, TrendingUp } from 'lucide-react';
import type { CategoryBreakdownItem } from '../types/dashboard';

interface CategoryBreakdownChartProps {
  data: CategoryBreakdownItem[];
  onViewAll: () => void;
}

export const CategoryBreakdownChart: React.FC<CategoryBreakdownChartProps> = ({
  data,
  onViewAll,
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number): string => {
    return `${percentage.toFixed(1)}%`;
  };

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex flex-wrap items-center justify-between">
            <CardTitle className="text-card-foreground-title">
              Category Breakdown
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <PieChart className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="truncate text-heading-5">No Category Data</h3>
              <p className="truncatebody-small max-w-md">
                Categorize your transactions to see spending breakdown by
                category.
              </p>
            </div>
            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => (window.location.href = '/transactions')}
            >
              Review Transactions
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between">
          <CardTitle className="text-card-foreground-title">
            Category Breakdown
          </CardTitle>
          <Button
            className="max-w-full"
            variant="ghost"
            size="sm"
            onClick={() => void onViewAll()}
          >
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        {/* Chart Visualization */}
        <div className="relative">
          {/* Simple Visual Chart - TODO: Replace with actual chart library */}
          <div className="space-y-3">
            {data.slice(0, 5).map((item) => (
              <div key={item.category} className="space-y-2">
                <div className="flex flex-wrap items-center justify-between text-sm">
                  <div className="flex flex-wrap items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor:
                          item.color || 'hsl(var(--giki-brand-blue))',
                      }}
                    />
                    <span className="font-medium truncate">
                      {item.category}
                    </span>
                  </div>
                  <span className="text-muted-foreground">
                    {formatPercentage(item.percentage)}
                  </span>
                </div>

                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-500"
                    style={{
                      backgroundColor:
                        item.color || 'hsl(var(--giki-brand-blue))',
                      width: `${item.percentage}%`,
                    }}
                  />
                </div>

                <div className="flex flex-wrap items-center justify-between truncate text-caption text-muted-foreground">
                  <span>{formatCurrency(item.amount)}</span>
                  {item.count > 0 && <span>{item.count} transactions</span>}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary */}
        <div className="pt-4 border-t border-border space-y-3">
          <div className="flex flex-wrap items-center justify-between">
            <span className="truncatelabel">Total Spending</span>
            <span className="truncatestatus-badge">
              {formatCurrency(totalAmount)}
            </span>
          </div>

          {data.length > 5 && (
            <div className="flex flex-wrap items-center justify-between truncatebody-small">
              <span>+{data.length - 5} more categories</span>
              <Button
                className="max-w-full"
                variant="ghost"
                size="sm"
                onClick={() => void onViewAll()}
              >
                View All
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </div>

        {/* Insights */}
        {data.length > 0 && (
          <div className="pt-4 border-t border-border">
            <div className="flex flex-wrap items-start space-x-3 p-3 bg-info/5 rounded-lg">
              <div className="p-1 bg-info/10 rounded">
                <TrendingUp className="w-4 h-4 text-info" />
              </div>
              <div className="flex flex-wrap-1">
                <p className="truncatelabel text-info-foreground">
                  Top Category: {data[0].category}
                </p>
                <p className="truncate text-caption text-info">
                  {formatPercentage(data[0].percentage)} of total spending •{' '}
                  {formatCurrency(data[0].amount)}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategoryBreakdownChart;
