/**
 * ActionsGrid Component Tests
 * Critical dashboard quick actions component tests for production
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import ActionsGrid from './ActionsGrid';
import type { Action } from '../types/dashboard';

// Mock React Router
vi.mock('react-router-dom', () => ({
  Link: ({
    children,
    to,
    ...props
  }: {
    children: React.ReactNode;
    to: string;
    [key: string]: unknown;
  }) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
}));

describe('ActionsGrid - Dashboard Quick Actions Component', () => {
  const baseMockActions: Action[] = [
    {
      id: 'upload-files',
      title: 'Upload Files',
      description: 'Upload financial data files for processing',
      icon: 'Upload',
      route: '/upload',
      color: 'blue',
      isEnabled: true,
      badge: 'New',
    },
    {
      id: 'view-transactions',
      title: 'View Transactions',
      description: 'Browse and manage your transactions',
      icon: 'FileCheck',
      route: '/transactions',
      color: 'green',
      isEnabled: true,
    },
    {
      id: 'generate-reports',
      title: 'Generate Reports',
      description: 'Create financial reports and analytics',
      icon: 'BarChart3',
      route: '/reports',
      color: 'purple',
      isEnabled: true,
      badge: 'Pro',
    },
    {
      id: 'manage-categories',
      title: 'Manage Categories',
      description: 'Organize transaction categories',
      icon: 'Folder',
      route: '/categories',
      color: 'orange',
      isEnabled: false,
      badge: 'Coming Soon',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Actions Grid Layout', () => {
    it('renders all provided actions', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      expect(screen.getByText('Upload Files')).toBeInTheDocument();
      expect(screen.getByText('View Transactions')).toBeInTheDocument();
      expect(screen.getByText('Generate Reports')).toBeInTheDocument();
      expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    });

    it('displays action descriptions correctly', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      expect(
        screen.getByText('Upload financial data files for processing'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Browse and manage your transactions'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Create financial reports and analytics'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('Organize transaction categories'),
      ).toBeInTheDocument();
    });

    it('renders with responsive grid layout', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const gridContainer = screen.getByText('Upload Files').closest('.grid');
      expect(gridContainer).toHaveClass(
        'grid-cols-1',
        'md:grid-cols-2',
        'lg:grid-cols-4',
      );
    });

    it('handles empty actions array', () => {
      render(<ActionsGrid actions={[]} />);

      const gridContainer = document.querySelector('.grid');
      expect(gridContainer).toBeInTheDocument();
      expect(gridContainer?.children).toHaveLength(0);
    });
  });

  describe('Action State Management', () => {
    it('displays enabled actions with proper styling', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const uploadAction = screen.getByText('Upload Files').closest('a');
      expect(uploadAction).not.toHaveClass('pointer-events-none');
      expect(uploadAction).toHaveAttribute('href', '/upload');
    });

    it('displays disabled actions with proper styling', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const disabledAction = screen.getByText('Manage Categories').closest('a');
      expect(disabledAction).toHaveClass('pointer-events-none');
      expect(disabledAction).toHaveAttribute('href', '/categories');
    });

    it('applies correct visual states for enabled vs disabled actions', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const enabledCard = screen.getByText('Upload Files').closest('.group');
      const disabledCard = screen
        .getByText('Manage Categories')
        .closest('.group');

      expect(enabledCard).toHaveClass('cursor-pointer');
      expect(disabledCard).toHaveClass('cursor-not-allowed');
    });

    it('shows hover effects only for enabled actions', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const enabledCard = screen.getByText('Upload Files').closest('.group');
      const disabledCard = screen
        .getByText('Manage Categories')
        .closest('.group');

      expect(enabledCard).toHaveClass('hover:shadow-sm');
      expect(disabledCard).not.toHaveClass('hover:shadow-sm');
    });
  });

  describe('Badge Display', () => {
    it('shows badges when provided', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.getByText('Pro')).toBeInTheDocument();
      expect(screen.getByText('Coming Soon')).toBeInTheDocument();
    });

    it('hides badge section when no badge provided', () => {
      const noBadgeActions: Action[] = [
        {
          id: 'simple-action',
          title: 'Simple Action',
          description: 'Action without badge',
          icon: 'Upload',
          route: '/simple',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={noBadgeActions} />);

      expect(screen.getByText('Simple Action')).toBeInTheDocument();
      expect(screen.queryByRole('badge')).not.toBeInTheDocument();
    });

    it('handles empty badge strings', () => {
      const emptyBadgeActions: Action[] = [
        {
          id: 'empty-badge',
          title: 'Empty Badge Action',
          description: 'Action with empty badge',
          icon: 'Upload',
          route: '/empty',
          color: 'blue',
          isEnabled: true,
          badge: '',
        },
      ];

      render(<ActionsGrid actions={emptyBadgeActions} />);

      expect(screen.getByText('Empty Badge Action')).toBeInTheDocument();
      // Empty badge should not render any badge text
      expect(screen.queryByText('New')).not.toBeInTheDocument();
      expect(screen.queryByText('Pro')).not.toBeInTheDocument();
    });
  });

  describe('Icon Rendering', () => {
    it('renders correct icons for each action', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      // Icons are rendered but we verify the component structure
      expect(screen.getByText('Upload Files')).toBeInTheDocument();
      expect(screen.getByText('View Transactions')).toBeInTheDocument();
      expect(screen.getByText('Generate Reports')).toBeInTheDocument();
      expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    });

    it('falls back to Upload icon for unknown icon names', () => {
      const unknownIconActions: Action[] = [
        {
          id: 'unknown-icon',
          title: 'Unknown Icon',
          description: 'Action with unknown icon',
          icon: 'NonExistentIcon',
          route: '/unknown',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={unknownIconActions} />);

      expect(screen.getByText('Unknown Icon')).toBeInTheDocument();
      // Should render without errors, falling back to Upload icon
    });

    it('handles missing icon property gracefully', () => {
      const noIconActions: Action[] = [
        {
          id: 'no-icon',
          title: 'No Icon',
          description: 'Action without icon',
          icon: '',
          route: '/no-icon',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={noIconActions} />);

      expect(screen.getByText('No Icon')).toBeInTheDocument();
      // Should fall back to Upload icon
    });
  });

  describe('Navigation and Routing', () => {
    it('creates proper links for enabled actions', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const uploadLink = screen.getByText('Upload Files').closest('a');
      const transactionsLink = screen
        .getByText('View Transactions')
        .closest('a');
      const reportsLink = screen.getByText('Generate Reports').closest('a');

      expect(uploadLink).toHaveAttribute('href', '/upload');
      expect(transactionsLink).toHaveAttribute('href', '/transactions');
      expect(reportsLink).toHaveAttribute('href', '/reports');
    });

    it('creates links for disabled actions but prevents interaction', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const disabledLink = screen.getByText('Manage Categories').closest('a');
      expect(disabledLink).toHaveAttribute('href', '/categories');
      expect(disabledLink).toHaveClass('pointer-events-none');
    });

    it('includes proper aria-labels for accessibility', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const uploadLink = screen.getByLabelText(
        'Upload Files: Upload financial data files for processing',
      );
      const transactionsLink = screen.getByLabelText(
        'View Transactions: Browse and manage your transactions',
      );

      expect(uploadLink).toBeInTheDocument();
      expect(transactionsLink).toBeInTheDocument();
    });

    it('handles special characters in route paths', () => {
      const specialRouteActions: Action[] = [
        {
          id: 'special-route',
          title: 'Special Route',
          description: 'Action with special route',
          icon: 'Upload',
          route: '/reports?filter=monthly&year=2024',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={specialRouteActions} />);

      const specialLink = screen.getByText('Special Route').closest('a');
      expect(specialLink).toHaveAttribute(
        'href',
        '/reports?filter=monthly&year=2024',
      );
    });
  });

  describe('User Interactions', () => {
    it('supports keyboard navigation for enabled actions', async () => {
      const user = userEvent.setup();
      render(<ActionsGrid actions={baseMockActions} />);

      const firstEnabledLink = screen.getByText('Upload Files').closest('a');

      if (firstEnabledLink) {
        await user.tab();
        expect(firstEnabledLink).toHaveFocus();
      }
    });

    it('skips disabled actions in keyboard navigation', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      // Disabled actions should not be focusable due to pointer-events-none
      const disabledLink = screen.getByText('Manage Categories').closest('a');
      expect(disabledLink).toHaveClass('pointer-events-none');
    });

    it('provides visual feedback on hover for enabled actions', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const enabledCard = screen.getByText('Upload Files').closest('.group');
      expect(enabledCard).toHaveClass('hover:shadow-sm');
    });

    it('supports focus management for accessibility', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const cards = screen.getAllByRole('link');
      cards.forEach((card) => {
        if (!card?.classList?.contains('pointer-events-none')) {
          const parentGroup = card.closest('.group');
          if (parentGroup) {
            expect(parentGroup).toHaveClass('focus-within:ring-2');
          }
        }
      });
    });
  });

  describe('Responsive Design', () => {
    it('maintains grid layout on different screen sizes', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const gridContainer = screen.getByText('Upload Files').closest('.grid');
      expect(gridContainer).toHaveClass(
        'grid-cols-1',
        'md:grid-cols-2',
        'lg:grid-cols-4',
      );
    });

    it('handles variable number of actions gracefully', () => {
      const manyActions: Action[] = Array.from({ length: 8 }, (_, i) => ({
        id: `action-${i}`,
        title: `Action ${i}`,
        description: `Description for action ${i}`,
        icon: 'Upload',
        route: `/action-${i}`,
        color: 'blue',
        isEnabled: true,
      }));

      render(<ActionsGrid actions={manyActions} />);

      manyActions.forEach((action) => {
        expect(screen.getByText(action.title)).toBeInTheDocument();
      });
    });

    it('adapts spacing and sizing appropriately', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const gridContainer = screen.getByText('Upload Files').closest('.grid');
      expect(gridContainer).toHaveClass('gap-6');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles actions with very long titles', () => {
      const longTitleActions: Action[] = [
        {
          id: 'long-title',
          title:
            'This is a very long action title that should handle text wrapping gracefully',
          description: 'Short description',
          icon: 'Upload',
          route: '/long-title',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={longTitleActions} />);

      expect(
        screen.getByText(
          'This is a very long action title that should handle text wrapping gracefully',
        ),
      ).toBeInTheDocument();
    });

    it('handles actions with very long descriptions', () => {
      const longDescActions: Action[] = [
        {
          id: 'long-desc',
          title: 'Short Title',
          description:
            'This is a very long description that explains in great detail what this action does and provides comprehensive information about its functionality and purpose',
          icon: 'Upload',
          route: '/long-desc',
          color: 'blue',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={longDescActions} />);

      expect(
        screen.getByText(
          'This is a very long description that explains in great detail what this action does and provides comprehensive information about its functionality and purpose',
        ),
      ).toBeInTheDocument();
    });

    it('handles missing required properties gracefully', () => {
      const incompleteActions: Action[] = [
        {
          id: '',
          title: '',
          description: '',
          icon: '',
          route: '',
          color: '',
          isEnabled: true,
        },
      ];

      render(<ActionsGrid actions={incompleteActions} />);

      // Should render without crashing
      const gridContainer = document.querySelector('.grid');
      expect(gridContainer).toBeInTheDocument();
    });

    it('handles null or undefined properties', () => {
      const nullPropActions: Action[] = [
        {
          id: 'null-props',
          title: 'Valid Title',
          description: 'Valid Description',
          icon: 'Upload',
          route: '/valid',
          color: 'blue',
          isEnabled: true,
          badge: undefined,
        },
      ];

      render(<ActionsGrid actions={nullPropActions} />);

      expect(screen.getByText('Valid Title')).toBeInTheDocument();
      expect(screen.getByText('Valid Description')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Semantic Structure', () => {
    it('uses proper semantic HTML structure', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const links = screen.getAllByRole('link');
      expect(links).toHaveLength(4);

      links.forEach((link) => {
        expect(link.tagName).toBe('A');
      });
    });

    it('provides descriptive aria-labels', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      expect(
        screen.getByLabelText(
          'Upload Files: Upload financial data files for processing',
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(
          'View Transactions: Browse and manage your transactions',
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(
          'Generate Reports: Create financial reports and analytics',
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(
          'Manage Categories: Organize transaction categories',
        ),
      ).toBeInTheDocument();
    });

    it('maintains proper heading hierarchy', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      // Action titles should be properly structured
      expect(screen.getByText('Upload Files')).toBeInTheDocument();
      expect(screen.getByText('View Transactions')).toBeInTheDocument();
      expect(screen.getByText('Generate Reports')).toBeInTheDocument();
      expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    });

    it('supports screen reader navigation', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const enabledLinks = screen
        .getAllByRole('link')
        .filter((link) => !link?.classList?.contains('pointer-events-none'));

      enabledLinks.forEach((link) => {
        expect(link).toHaveAttribute('aria-label');
      });
    });

    it('provides visual focus indicators', () => {
      render(<ActionsGrid actions={baseMockActions} />);

      const cards = screen.getAllByRole('link');
      cards.forEach((card) => {
        const parentGroup = card.closest('.group');
        if (parentGroup) {
          expect(parentGroup).toHaveClass('focus-within:ring-2');
          expect(parentGroup).toHaveClass('focus-within:ring-ring');
          expect(parentGroup).toHaveClass('focus-within:ring-offset-2');
        }
      });
    });
  });

  describe('Performance and Rendering', () => {
    it('renders efficiently with many actions', () => {
      const manyActions: Action[] = Array.from({ length: 20 }, (_, i) => ({
        id: `perf-action-${i}`,
        title: `Performance Action ${i}`,
        description: `Description ${i}`,
        icon: 'Upload',
        route: `/perf-${i}`,
        color: 'blue',
        isEnabled: i % 2 === 0,
      }));

      const startTime = performance.now();
      render(<ActionsGrid actions={manyActions} />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // 100ms threshold
      expect(screen.getByText('Performance Action 0')).toBeInTheDocument();
      expect(screen.getByText('Performance Action 19')).toBeInTheDocument();
    });

    it('handles rapid prop updates efficiently', () => {
      const { rerender } = render(<ActionsGrid actions={baseMockActions} />);

      const updatedActions = baseMockActions.map((action) => ({
        ...action,
        isEnabled: !action.isEnabled,
      }));

      rerender(<ActionsGrid actions={updatedActions} />);

      // Should update states correctly
      const previouslyDisabled = screen
        .getByText('Manage Categories')
        .closest('a');
      const previouslyEnabled = screen.getByText('Upload Files').closest('a');

      expect(previouslyDisabled).not.toHaveClass('pointer-events-none');
      expect(previouslyEnabled).toHaveClass('pointer-events-none');
    });
  });
});
