/**
 * CategoryBreakdownChart Component Tests
 * Critical financial category breakdown visualization tests for production
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import CategoryBreakdownChart from './CategoryBreakdownChart';
import type { CategoryBreakdownItem } from '../types/dashboard';

describe('CategoryBreakdownChart - Financial Category Visualization Component', () => {
  const mockOnViewAll = vi.fn();

  const baseMockData: CategoryBreakdownItem[] = [
    {
      category: 'Office Supplies',
      amount: 50000,
      percentage: 35.7,
      count: 25,
      color: '#3B82F6',
    },
    {
      category: 'Software Subscriptions',
      amount: 30000,
      percentage: 21.4,
      count: 12,
      color: '#10B981',
    },
    {
      category: 'Marketing',
      amount: 25000,
      percentage: 17.9,
      count: 8,
      color: '#F59E0B',
    },
    {
      category: 'Travel & Meals',
      amount: 20000,
      percentage: 14.3,
      count: 15,
      color: '#EF4444',
    },
    {
      category: 'Professional Services',
      amount: 15000,
      percentage: 10.7,
      count: 6,
      color: '#8B5CF6',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Empty State Handling', () => {
    it('displays empty state when no category data exists', () => {
      render(<CategoryBreakdownChart data={[]} onViewAll={mockOnViewAll} />);

      expect(screen.getByText('Category Breakdown')).toBeInTheDocument();
      expect(screen.getByText('No Category Data')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Categorize your transactions to see spending breakdown by category.',
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: 'Review Transactions' }),
      ).toBeInTheDocument();
    });

    it('navigates to transactions page when review button clicked', async () => {
      const user = userEvent.setup();

      // Mock window?.location?.href assignment
      delete (window as unknown).location;
      window.location = { href: '' } as unknown;

      render(<CategoryBreakdownChart data={[]} onViewAll={mockOnViewAll} />);

      const reviewButton = screen.getByRole('button', {
        name: 'Review Transactions',
      });
      await user.click(reviewButton);

      expect(window?.location?.href).toBe('/transactions');
    });

    it('does not show view all button in empty state', () => {
      render(<CategoryBreakdownChart data={[]} onViewAll={mockOnViewAll} />);

      expect(screen.queryByText('View All')).not.toBeInTheDocument();
    });
  });

  describe('Category Data Display', () => {
    it('renders all provided categories', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Software Subscriptions')).toBeInTheDocument();
      expect(screen.getByText('Marketing')).toBeInTheDocument();
      expect(screen.getByText('Travel & Meals')).toBeInTheDocument();
      expect(screen.getByText('Professional Services')).toBeInTheDocument();
    });

    it('displays category percentages correctly', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('35.7%')).toBeInTheDocument();
      expect(screen.getByText('21.4%')).toBeInTheDocument();
      expect(screen.getByText('17.9%')).toBeInTheDocument();
      expect(screen.getByText('14.3%')).toBeInTheDocument();
      expect(screen.getByText('10.7%')).toBeInTheDocument();
    });

    it('displays category amounts in correct currency format', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('₹50,000')).toBeInTheDocument();
      expect(screen.getByText('₹30,000')).toBeInTheDocument();
      expect(screen.getByText('₹25,000')).toBeInTheDocument();
      expect(screen.getByText('₹20,000')).toBeInTheDocument();
      expect(screen.getByText('₹15,000')).toBeInTheDocument();
    });

    it('displays transaction counts for each category', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('25 transactions')).toBeInTheDocument();
      expect(screen.getByText('12 transactions')).toBeInTheDocument();
      expect(screen.getByText('8 transactions')).toBeInTheDocument();
      expect(screen.getByText('15 transactions')).toBeInTheDocument();
      expect(screen.getByText('6 transactions')).toBeInTheDocument();
    });
  });

  describe('Visual Chart Rendering', () => {
    it('renders progress bars for each category', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      // Check for progress bar structure
      const progressBars = document.querySelectorAll('.h-2.rounded-full');
      expect(progressBars).toHaveLength(5); // One for each category
    });

    it('applies correct colors to category indicators', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      const colorIndicators = document.querySelectorAll(
        '.w-3.h-3.rounded-full',
      );
      expect(colorIndicators).toHaveLength(5);

      // Check if styles are applied (colors are inline styles)
      expect(colorIndicators[0]).toHaveStyle('background-color: #3B82F6');
      expect(colorIndicators[1]).toHaveStyle('background-color: #10B981');
      expect(colorIndicators[2]).toHaveStyle('background-color: #F59E0B');
    });

    it('sets progress bar widths based on percentages', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      const progressBars = document.querySelectorAll(
        '.transition-all.duration-500',
      );
      expect(progressBars[0]).toHaveStyle('width: 35.7%');
      expect(progressBars[1]).toHaveStyle('width: 21.4%');
      expect(progressBars[2]).toHaveStyle('width: 17.9%');
    });

    it('uses default color when no color provided', () => {
      const noColorData: CategoryBreakdownItem[] = [
        {
          category: 'No Color Category',
          amount: 10000,
          percentage: 100,
          count: 5,
        },
      ];

      render(
        <CategoryBreakdownChart data={noColorData} onViewAll={mockOnViewAll} />,
      );

      const colorIndicator = document.querySelector('.w-3.h-3.rounded-full');
      const progressBar = document.querySelector(
        '.transition-all.duration-500',
      );

      expect(colorIndicator).toHaveStyle('background-color: #3B82F6'); // Default blue
      expect(progressBar).toHaveStyle('background-color: #3B82F6');
    });
  });

  describe('Data Limiting and View All', () => {
    it('shows only top 5 categories in chart', () => {
      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 10 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000 - i * 1000,
          percentage: 10 - i,
          count: 5,
          color: '#3B82F6',
        }),
      );

      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );

      // Only first 5 should be visible in chart
      expect(screen.getByText('Category 1')).toBeInTheDocument();
      expect(screen.getByText('Category 2')).toBeInTheDocument();
      expect(screen.getByText('Category 3')).toBeInTheDocument();
      expect(screen.getByText('Category 4')).toBeInTheDocument();
      expect(screen.getByText('Category 5')).toBeInTheDocument();

      // 6th and beyond should not be in chart area
      expect(screen.queryByText('Category 6')).not.toBeInTheDocument();
    });

    it('shows additional categories count when more than 5 exist', () => {
      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 8 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000 - i * 1000,
          percentage: 10 - i,
          count: 5,
          color: '#3B82F6',
        }),
      );

      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('+3 more categories')).toBeInTheDocument();
    });

    it('shows view all button when more than 5 categories', () => {
      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 7 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000,
          percentage: 14.3,
          count: 5,
          color: '#3B82F6',
        }),
      );

      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );

      const viewAllButtons = screen.getAllByText('View All');
      expect(viewAllButtons).toHaveLength(2); // Header and footer
    });

    it('calls onViewAll when view all buttons are clicked', async () => {
      const user = userEvent.setup();

      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 7 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000,
          percentage: 14.3,
          count: 5,
          color: '#3B82F6',
        }),
      );

      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );

      const headerViewAllButton = screen.getAllByText('View All')[0];
      await user.click(headerViewAllButton);

      expect(mockOnViewAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('Total Spending Calculation', () => {
    it('calculates and displays total spending correctly', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      // Total: 50000 + 30000 + 25000 + 20000 + 15000 = 140000
      expect(screen.getByText('Total Spending')).toBeInTheDocument();
      expect(screen.getByText('₹1,40,000')).toBeInTheDocument();
    });

    it('handles zero amounts in total calculation', () => {
      const zeroAmountData: CategoryBreakdownItem[] = [
        {
          category: 'Zero Category',
          amount: 0,
          percentage: 0,
          count: 0,
          color: '#3B82F6',
        },
        {
          category: 'Normal Category',
          amount: 5000,
          percentage: 100,
          count: 5,
          color: '#10B981',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={zeroAmountData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('₹5,000')).toBeInTheDocument(); // Total should be 5000
    });

    it('handles negative amounts in calculation', () => {
      const negativeAmountData: CategoryBreakdownItem[] = [
        {
          category: 'Refund Category',
          amount: -2000,
          percentage: -40,
          count: 2,
          color: '#EF4444',
        },
        {
          category: 'Expense Category',
          amount: 3000,
          percentage: 60,
          count: 3,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={negativeAmountData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('₹1,000')).toBeInTheDocument(); // Total: -2000 + 3000 = 1000
    });
  });

  describe('Top Category Insights', () => {
    it('displays top category insights correctly', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(
        screen.getByText('Top Category: Office Supplies'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('35.7% of total spending • ₹50,000'),
      ).toBeInTheDocument();
    });

    it('shows insights for the first category when sorted', () => {
      const unsortedData: CategoryBreakdownItem[] = [
        {
          category: 'Small Category',
          amount: 5000,
          percentage: 10,
          count: 2,
          color: '#3B82F6',
        },
        {
          category: 'Large Category',
          amount: 45000,
          percentage: 90,
          count: 20,
          color: '#10B981',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={unsortedData}
          onViewAll={mockOnViewAll}
        />,
      );

      // Should show insights for first item in array (not necessarily largest)
      expect(
        screen.getByText('Top Category: Small Category'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('10% of total spending • ₹5,000'),
      ).toBeInTheDocument();
    });

    it('does not show insights section when no data', () => {
      render(<CategoryBreakdownChart data={[]} onViewAll={mockOnViewAll} />);

      expect(screen.queryByText('Top Category:')).not.toBeInTheDocument();
    });
  });

  describe('Percentage Formatting', () => {
    it('formats percentages to one decimal place', () => {
      const preciseData: CategoryBreakdownItem[] = [
        {
          category: 'Precise Category',
          amount: 33333,
          percentage: 33.333333,
          count: 10,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart data={preciseData} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('33.3%')).toBeInTheDocument();
    });

    it('handles whole number percentages', () => {
      const wholeData: CategoryBreakdownItem[] = [
        {
          category: 'Whole Category',
          amount: 10000,
          percentage: 50,
          count: 5,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart data={wholeData} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('50.0%')).toBeInTheDocument();
    });

    it('handles very small percentages', () => {
      const smallData: CategoryBreakdownItem[] = [
        {
          category: 'Tiny Category',
          amount: 50,
          percentage: 0.1,
          count: 1,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart data={smallData} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('0.1%')).toBeInTheDocument();
    });
  });

  describe('Transaction Count Display', () => {
    it('shows transaction count when count is greater than 0', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('25 transactions')).toBeInTheDocument();
      expect(screen.getByText('12 transactions')).toBeInTheDocument();
    });

    it('hides transaction count when count is 0', () => {
      const zeroCountData: CategoryBreakdownItem[] = [
        {
          category: 'Zero Count Category',
          amount: 5000,
          percentage: 100,
          count: 0,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={zeroCountData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.queryByText('transactions')).not.toBeInTheDocument();
    });

    it('handles singular vs plural transaction text', () => {
      const singleTransactionData: CategoryBreakdownItem[] = [
        {
          category: 'Single Transaction',
          amount: 5000,
          percentage: 100,
          count: 1,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={singleTransactionData}
          onViewAll={mockOnViewAll}
        />,
      );

      // Component shows "1 transactions" - this could be improved to handle singular/plural
      expect(screen.getByText('1 transactions')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles categories with very long names', () => {
      const longNameData: CategoryBreakdownItem[] = [
        {
          category:
            'This is a very long category name that should be truncated properly to avoid layout issues',
          amount: 10000,
          percentage: 100,
          count: 5,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={longNameData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(
        screen.getByText(
          'This is a very long category name that should be truncated properly to avoid layout issues',
        ),
      ).toBeInTheDocument();
    });

    it('handles missing color property gracefully', () => {
      const noColorData: CategoryBreakdownItem[] = [
        {
          category: 'No Color',
          amount: 5000,
          percentage: 100,
          count: 5,
        },
      ];

      render(
        <CategoryBreakdownChart data={noColorData} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('No Color')).toBeInTheDocument();
      // Should use default color without crashing
    });

    it('handles negative percentages', () => {
      const negativePercentData: CategoryBreakdownItem[] = [
        {
          category: 'Negative Percent',
          amount: -1000,
          percentage: -20,
          count: 2,
          color: '#EF4444',
        },
      ];

      render(
        <CategoryBreakdownChart
          data={negativePercentData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('-20.0%')).toBeInTheDocument();
      expect(screen.getByText('₹-1,000')).toBeInTheDocument();
    });

    it('handles very large amounts and percentages', () => {
      const largeData: CategoryBreakdownItem[] = [
        {
          category: 'Large Category',
          amount: 9999999999,
          percentage: 999.9,
          count: 999999,
          color: '#3B82F6',
        },
      ];

      render(
        <CategoryBreakdownChart data={largeData} onViewAll={mockOnViewAll} />,
      );

      expect(screen.getByText('999.9%')).toBeInTheDocument();
      expect(screen.getByText('₹9,99,99,99,999')).toBeInTheDocument(); // Indian number format
      expect(screen.getByText('999999 transactions')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Semantic Structure', () => {
    it('uses proper semantic HTML structure', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      expect(screen.getByText('Category Breakdown')).toBeInTheDocument();
      expect(screen.getByText('Total Spending')).toBeInTheDocument();
      expect(
        screen.getByText('Top Category: Office Supplies'),
      ).toBeInTheDocument();
    });

    it('provides accessible color indicators', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      const colorIndicators = document.querySelectorAll(
        '.w-3.h-3.rounded-full',
      );
      expect(colorIndicators).toHaveLength(5);

      // Each category should have a visible color indicator
      baseMockData.forEach((item) => {
        expect(screen.getByText(item.category)).toBeInTheDocument();
      });
    });

    it('maintains proper reading order', () => {
      render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      // Title should come first
      expect(screen.getByText('Category Breakdown')).toBeInTheDocument();

      // Categories should be present
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();

      // Summary should come after
      expect(screen.getByText('Total Spending')).toBeInTheDocument();

      // Insights should come last
      expect(
        screen.getByText('Top Category: Office Supplies'),
      ).toBeInTheDocument();
    });

    it('supports keyboard navigation for interactive elements', async () => {
      const user = userEvent.setup();

      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 7 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000,
          percentage: 14.3,
          count: 5,
          color: '#3B82F6',
        }),
      );

      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );

      const viewAllButton = screen.getAllByRole('button')[0];

      await user.tab();
      expect(viewAllButton).toHaveFocus();

      await user.keyboard('{Enter}');
      expect(mockOnViewAll).toHaveBeenCalled();
    });
  });

  describe('Performance and Rendering', () => {
    it('renders efficiently with many categories', () => {
      const manyCategories: CategoryBreakdownItem[] = Array.from(
        { length: 50 },
        (_, i) => ({
          category: `Category ${i + 1}`,
          amount: 10000 - i * 100,
          percentage: 20 - i * 0.4,
          count: 10 - Math.floor(i / 5),
          color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
        }),
      );

      const startTime = performance.now();
      render(
        <CategoryBreakdownChart
          data={manyCategories}
          onViewAll={mockOnViewAll}
        />,
      );
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // 100ms threshold

      // Should only render first 5 in chart
      expect(screen.getByText('Category 1')).toBeInTheDocument();
      expect(screen.getByText('Category 5')).toBeInTheDocument();
      expect(screen.queryByText('Category 6')).not.toBeInTheDocument();

      // Should show additional count
      expect(screen.getByText('+45 more categories')).toBeInTheDocument();
    });

    it('handles frequent data updates efficiently', () => {
      const { rerender } = render(
        <CategoryBreakdownChart
          data={baseMockData}
          onViewAll={mockOnViewAll}
        />,
      );

      const updatedData = baseMockData.map((item) => ({
        ...item,
        amount: item.amount + 1000,
        percentage: item.percentage + 1,
      }));

      rerender(
        <CategoryBreakdownChart data={updatedData} onViewAll={mockOnViewAll} />,
      );

      // Should update amounts correctly
      expect(screen.getByText('₹51,000')).toBeInTheDocument(); // 50000 + 1000
      expect(screen.getByText('₹31,000')).toBeInTheDocument(); // 30000 + 1000
    });
  });
});
