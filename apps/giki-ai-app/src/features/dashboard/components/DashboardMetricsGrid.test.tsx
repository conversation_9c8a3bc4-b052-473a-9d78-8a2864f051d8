/**
 * DashboardMetricsGrid Component Tests
 * Critical financial dashboard component tests for production reliability
 */

import { describe, it, expect } from 'vitest';
import { screen } from '@testing-library/react';
import { render } from '../../../test-utils';
import DashboardMetricsGrid from './DashboardMetricsGrid';
import type { DashboardMetrics } from '../types/dashboard';

describe('DashboardMetricsGrid - Financial Dashboard Component', () => {
  const baseMockMetrics: DashboardMetrics = {
    totalIncome: 150000,
    totalExpenses: 100000,
    netIncome: 50000,
    totalTransactions: 250,
    categorizedTransactions: 200,
    uncategorizedTransactions: 50,
    avgTransactionAmount: 800,
    topCategory: 'Office Supplies',
    topCategoryAmount: 25000,
  };

  describe('Financial Metrics Display', () => {
    it('displays total income correctly formatted', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('₹1,50,000')).toBeInTheDocument();
      expect(screen.getByText('Revenue generated')).toBeInTheDocument();
    });

    it('displays total expenses correctly formatted', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('₹1,00,000')).toBeInTheDocument();
      expect(screen.getByText('Amount spent')).toBeInTheDocument();
    });

    it('displays net income with profit status', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('₹50,000')).toBeInTheDocument();
      expect(screen.getByText('Profit')).toBeInTheDocument();
      expect(screen.getByText('Profit margin')).toBeInTheDocument();
    });

    it('displays transaction count with correct formatting', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      expect(screen.getByText('Transactions')).toBeInTheDocument();
      expect(screen.getByText('250')).toBeInTheDocument();
      expect(screen.getByText('200 categorized')).toBeInTheDocument();
      expect(screen.getByText('50 need review')).toBeInTheDocument();
    });
  });

  describe('Net Income Status Indicators', () => {
    it('shows profit status for positive net income', () => {
      const profitMetrics = { ...baseMockMetrics, netIncome: 25000 };
      render(<DashboardMetricsGrid metrics={profitMetrics} />);

      expect(screen.getByText('Profit')).toBeInTheDocument();
      expect(screen.getByText('Profit margin')).toBeInTheDocument();
      expect(screen.getByText('₹25,000')).toBeInTheDocument();
    });

    it('shows loss status for negative net income', () => {
      const lossMetrics = { ...baseMockMetrics, netIncome: -15000 };
      render(<DashboardMetricsGrid metrics={lossMetrics} />);

      expect(screen.getByText('Loss')).toBeInTheDocument();
      expect(screen.getByText('Loss amount')).toBeInTheDocument();
      expect(screen.getByText('₹15,000')).toBeInTheDocument(); // Absolute value
    });

    it('shows break even status for zero net income', () => {
      const breakEvenMetrics = { ...baseMockMetrics, netIncome: 0 };
      render(<DashboardMetricsGrid metrics={breakEvenMetrics} />);

      expect(screen.getByText('Break Even')).toBeInTheDocument();
      expect(screen.getByText('Profit margin')).toBeInTheDocument();
      expect(screen.getByText('₹0')).toBeInTheDocument();
    });
  });

  describe('Transaction Categorization Progress', () => {
    it('calculates and displays categorization percentage correctly', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      // 200/250 = 80%
      expect(screen.getByText('80.0% categorized')).toBeInTheDocument();
    });

    it('handles zero transactions gracefully', () => {
      const emptyMetrics = {
        ...baseMockMetrics,
        totalTransactions: 0,
        categorizedTransactions: 0,
        uncategorizedTransactions: 0,
      };
      render(<DashboardMetricsGrid metrics={emptyMetrics} />);

      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('0 categorized')).toBeInTheDocument();
      expect(screen.getByText('0.0% categorized')).toBeInTheDocument();
    });

    it('shows warning when uncategorized transactions exist', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      expect(screen.getByText('50 need review')).toBeInTheDocument();
    });

    it('hides uncategorized warning when all transactions are categorized', () => {
      const fullyCategorizedMetrics = {
        ...baseMockMetrics,
        categorizedTransactions: 250,
        uncategorizedTransactions: 0,
      };
      render(<DashboardMetricsGrid metrics={fullyCategorizedMetrics} />);

      expect(screen.queryByText('need review')).not.toBeInTheDocument();
      expect(screen.getByText('100.0% categorized')).toBeInTheDocument();
    });
  });

  describe('Large Number Formatting', () => {
    it('formats large income amounts correctly', () => {
      const largeMetrics = {
        ...baseMockMetrics,
        totalIncome: 1500000,
        totalExpenses: 1200000,
        netIncome: 300000,
      };
      render(<DashboardMetricsGrid metrics={largeMetrics} />);

      expect(screen.getByText('₹15,00,000')).toBeInTheDocument(); // Indian number format
      expect(screen.getByText('₹12,00,000')).toBeInTheDocument();
      expect(screen.getByText('₹3,00,000')).toBeInTheDocument();
    });

    it('handles decimal amounts by rounding', () => {
      const decimalMetrics = {
        ...baseMockMetrics,
        totalIncome: 150000.75,
        totalExpenses: 100000.25,
        netIncome: 50000.5,
      };
      render(<DashboardMetricsGrid metrics={decimalMetrics} />);

      expect(screen.getByText('₹1,50,001')).toBeInTheDocument(); // Rounded
      expect(screen.getByText('₹1,00,000')).toBeInTheDocument(); // Rounded
      expect(screen.getByText('₹50,001')).toBeInTheDocument(); // Rounded
    });

    it('formats transaction counts with thousands separator', () => {
      const highVolumeMetrics = {
        ...baseMockMetrics,
        totalTransactions: 5000,
        categorizedTransactions: 4500,
        uncategorizedTransactions: 500,
      };
      render(<DashboardMetricsGrid metrics={highVolumeMetrics} />);

      expect(screen.getByText('5,000')).toBeInTheDocument();
      expect(screen.getByText('4,500 categorized')).toBeInTheDocument();
      expect(screen.getByText('500 need review')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles negative expenses gracefully', () => {
      const negativeExpenseMetrics = {
        ...baseMockMetrics,
        totalExpenses: -50000, // Refund scenario
        netIncome: 200000, // Higher net income due to negative expense
      };
      render(<DashboardMetricsGrid metrics={negativeExpenseMetrics} />);

      expect(screen.getByText('₹-50,000')).toBeInTheDocument();
      expect(screen.getByText('₹2,00,000')).toBeInTheDocument();
    });

    it('handles zero values across all metrics', () => {
      const zeroMetrics = {
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        totalTransactions: 0,
        categorizedTransactions: 0,
        uncategorizedTransactions: 0,
        avgTransactionAmount: 0,
        topCategory: '',
        topCategoryAmount: 0,
      };
      render(<DashboardMetricsGrid metrics={zeroMetrics} />);

      expect(screen.getByText('₹0')).toBeInTheDocument();
      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('Break Even')).toBeInTheDocument();
    });

    it('handles very small amounts correctly', () => {
      const smallAmountMetrics = {
        ...baseMockMetrics,
        totalIncome: 1.5,
        totalExpenses: 0.75,
        netIncome: 0.75,
      };
      render(<DashboardMetricsGrid metrics={smallAmountMetrics} />);

      expect(screen.getByText('₹2')).toBeInTheDocument(); // Rounded up
      expect(screen.getByText('₹1')).toBeInTheDocument(); // Rounded up
    });
  });

  describe('Accessibility and UI Elements', () => {
    it('includes proper ARIA labels and semantic structure', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      // Check for card structure
      const cards = screen.getAllByRole('generic');
      expect(cards.length).toBeGreaterThan(0);

      // Check for headings
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('Transactions')).toBeInTheDocument();
    });

    it('displays appropriate icons for each metric', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      // Icons are rendered but we can't easily test their presence
      // Instead, verify the card structure is complete
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('Transactions')).toBeInTheDocument();
    });

    it('uses consistent styling and color coding', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      // Test that profit badge is present for positive income
      expect(screen.getByText('Profit')).toBeInTheDocument();

      // Test that categorization progress is visible
      expect(screen.getByText('80.0% categorized')).toBeInTheDocument();
    });
  });

  describe('Financial Calculation Accuracy', () => {
    it('correctly calculates net income display', () => {
      const testMetrics = {
        ...baseMockMetrics,
        totalIncome: 100000,
        totalExpenses: 75000,
        netIncome: 25000, // Should match income - expenses
      };
      render(<DashboardMetricsGrid metrics={testMetrics} />);

      expect(screen.getByText('₹25,000')).toBeInTheDocument();
      expect(screen.getByText('Profit')).toBeInTheDocument();
    });

    it('correctly calculates categorization percentage with precision', () => {
      const testMetrics = {
        ...baseMockMetrics,
        totalTransactions: 333,
        categorizedTransactions: 222,
        uncategorizedTransactions: 111,
      };
      render(<DashboardMetricsGrid metrics={testMetrics} />);

      // 222/333 = 66.67%
      expect(screen.getByText('66.7% categorized')).toBeInTheDocument();
    });
  });

  describe('Performance and Responsiveness', () => {
    it('renders efficiently with large datasets', () => {
      const largeDataMetrics = {
        ...baseMockMetrics,
        totalIncome: 999999999,
        totalExpenses: 888888888,
        netIncome: 111111111,
        totalTransactions: 99999,
        categorizedTransactions: 88888,
        uncategorizedTransactions: 11111,
      };

      const startTime = performance.now();
      render(<DashboardMetricsGrid metrics={largeDataMetrics} />);
      const endTime = performance.now();

      // Component should render quickly even with large numbers
      expect(endTime - startTime).toBeLessThan(100); // 100ms threshold

      expect(screen.getByText('₹99,99,99,999')).toBeInTheDocument();
      expect(screen.getByText('99,999')).toBeInTheDocument();
    });

    it('maintains grid layout structure', () => {
      render(<DashboardMetricsGrid metrics={baseMockMetrics} />);

      // All four main metrics should be present
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('Transactions')).toBeInTheDocument();
    });
  });

  describe('Real-world Financial Scenarios', () => {
    it('handles typical small business metrics', () => {
      const smallBusinessMetrics = {
        totalIncome: 500000, // 5 lakh
        totalExpenses: 350000, // 3.5 lakh
        netIncome: 150000, // 1.5 lakh profit
        totalTransactions: 120,
        categorizedTransactions: 100,
        uncategorizedTransactions: 20,
        avgTransactionAmount: 4167,
        topCategory: 'Office Supplies',
        topCategoryAmount: 50000,
      };
      render(<DashboardMetricsGrid metrics={smallBusinessMetrics} />);

      expect(screen.getByText('₹5,00,000')).toBeInTheDocument();
      expect(screen.getByText('₹3,50,000')).toBeInTheDocument();
      expect(screen.getByText('₹1,50,000')).toBeInTheDocument();
      expect(screen.getByText('120')).toBeInTheDocument();
      expect(screen.getByText('Profit')).toBeInTheDocument();
    });

    it('handles seasonal business with loss', () => {
      const lossMetrics = {
        totalIncome: 200000,
        totalExpenses: 300000,
        netIncome: -100000, // Loss scenario
        totalTransactions: 150,
        categorizedTransactions: 120,
        uncategorizedTransactions: 30,
        avgTransactionAmount: 2000,
        topCategory: 'Marketing',
        topCategoryAmount: 80000,
      };
      render(<DashboardMetricsGrid metrics={lossMetrics} />);

      expect(screen.getByText('₹2,00,000')).toBeInTheDocument();
      expect(screen.getByText('₹3,00,000')).toBeInTheDocument();
      expect(screen.getByText('₹1,00,000')).toBeInTheDocument(); // Absolute value
      expect(screen.getByText('Loss')).toBeInTheDocument();
      expect(screen.getByText('Loss amount')).toBeInTheDocument();
    });
  });
});
