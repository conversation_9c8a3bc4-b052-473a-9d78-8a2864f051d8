/**
 * ProcessingStatusCard Component Tests
 * Critical file processing status component tests for production
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen } from '@testing-library/react';
import { render } from '../../../test-utils';
import ProcessingStatusCard from './ProcessingStatusCard';
import type { ProcessingStatus } from '../types/dashboard';

describe('ProcessingStatusCard - File Processing Status Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current time for consistent relative time testing
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-06-14T12:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Empty State Handling', () => {
    it('returns null when no files and not processing', () => {
      const emptyStatus: ProcessingStatus = {
        totalFiles: 0,
        processedFiles: 0,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
      };

      const { container } = render(
        <ProcessingStatusCard status={emptyStatus} />,
      );
      expect(container.firstChild).toBeNull();
    });

    it('shows processing card when actively processing even with 0 files', () => {
      const processingStatus: ProcessingStatus = {
        totalFiles: 0,
        processedFiles: 0,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: true,
      };

      render(<ProcessingStatusCard status={processingStatus} />);
      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      expect(screen.getByText('Processing')).toBeInTheDocument();
    });

    it('shows upload guidance when no files uploaded', () => {
      const noFilesStatus: ProcessingStatus = {
        totalFiles: 0,
        processedFiles: 0,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: undefined,
      };

      const { container } = render(
        <ProcessingStatusCard status={noFilesStatus} />,
      );
      // Component returns null for empty state
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Processing State Display', () => {
    it('shows active processing state correctly', () => {
      const processingStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: 2,
        pendingFiles: 3,
        failedFiles: 0,
        isProcessing: true,
        lastProcessed: '2024-06-14T11:55:00Z',
      };

      render(<ProcessingStatusCard status={processingStatus} />);

      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      expect(screen.getByText('Processing')).toBeInTheDocument();
      expect(screen.getByText('Processing files...')).toBeInTheDocument();
      expect(screen.getByText('2/5 files')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Processed count
      expect(screen.getByText('3')).toBeInTheDocument(); // Pending count
      expect(screen.getByText('0')).toBeInTheDocument(); // Failed count
    });

    it('shows completed processing state', () => {
      const completedStatus: ProcessingStatus = {
        totalFiles: 10,
        processedFiles: 10,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-14T11:50:00Z',
      };

      render(<ProcessingStatusCard status={completedStatus} />);

      expect(
        screen.getByText('All files processed successfully'),
      ).toBeInTheDocument();
      expect(screen.getByText('Ready')).toBeInTheDocument();
      expect(screen.getByText('10/10 files')).toBeInTheDocument();
      expect(screen.getByText('10m ago')).toBeInTheDocument(); // Last processed time
    });

    it('shows failed processing state', () => {
      const failedStatus: ProcessingStatus = {
        totalFiles: 8,
        processedFiles: 6,
        pendingFiles: 0,
        failedFiles: 2,
        isProcessing: false,
        lastProcessed: '2024-06-14T11:45:00Z',
      };

      render(<ProcessingStatusCard status={failedStatus} />);

      expect(screen.getByText('2 files failed processing')).toBeInTheDocument();
      expect(screen.getByText('Issues')).toBeInTheDocument();
      expect(screen.getByText('6/8 files')).toBeInTheDocument();
      expect(screen.getByText('6')).toBeInTheDocument(); // Processed
      expect(screen.getByText('0')).toBeInTheDocument(); // Pending
      expect(screen.getByText('2')).toBeInTheDocument(); // Failed
    });

    it('shows pending processing state', () => {
      const pendingStatus: ProcessingStatus = {
        totalFiles: 12,
        processedFiles: 8,
        pendingFiles: 4,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-14T10:30:00Z',
      };

      render(<ProcessingStatusCard status={pendingStatus} />);

      expect(
        screen.getByText('4 files waiting to process'),
      ).toBeInTheDocument();
      expect(screen.getByText('Ready')).toBeInTheDocument();
      expect(screen.getByText('8/12 files')).toBeInTheDocument();
      expect(screen.getByText('1h ago')).toBeInTheDocument(); // Last processed time
    });
  });

  describe('Progress Calculation', () => {
    it('calculates progress percentage correctly', () => {
      const progressStatus: ProcessingStatus = {
        totalFiles: 20,
        processedFiles: 15,
        pendingFiles: 5,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={progressStatus} />);

      expect(screen.getByText('15/20 files')).toBeInTheDocument();
      // Progress bar should show 75% (15/20)
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '75');
    });

    it('handles zero total files without division error', () => {
      const zeroStatus: ProcessingStatus = {
        totalFiles: 0,
        processedFiles: 0,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: true,
      };

      render(<ProcessingStatusCard status={zeroStatus} />);

      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      // Should not show progress bar for zero files
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('shows 100% progress when all files processed', () => {
      const fullProgressStatus: ProcessingStatus = {
        totalFiles: 50,
        processedFiles: 50,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={fullProgressStatus} />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');
    });
  });

  describe('Time Formatting', () => {
    it('shows "Just now" for very recent processing', () => {
      const recentStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: 5,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-14T11:59:30Z', // 30 seconds ago
      };

      render(<ProcessingStatusCard status={recentStatus} />);

      expect(screen.getByText('Just now')).toBeInTheDocument();
    });

    it('shows minutes ago for recent processing', () => {
      const minutesAgoStatus: ProcessingStatus = {
        totalFiles: 3,
        processedFiles: 3,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-14T11:45:00Z', // 15 minutes ago
      };

      render(<ProcessingStatusCard status={minutesAgoStatus} />);

      expect(screen.getByText('15m ago')).toBeInTheDocument();
    });

    it('shows hours ago for processing within the day', () => {
      const hoursAgoStatus: ProcessingStatus = {
        totalFiles: 7,
        processedFiles: 7,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-14T09:00:00Z', // 3 hours ago
      };

      render(<ProcessingStatusCard status={hoursAgoStatus} />);

      expect(screen.getByText('3h ago')).toBeInTheDocument();
    });

    it('shows formatted date for older processing', () => {
      const oldStatus: ProcessingStatus = {
        totalFiles: 10,
        processedFiles: 10,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: '2024-06-12T10:00:00Z', // 2 days ago
      };

      render(<ProcessingStatusCard status={oldStatus} />);

      expect(screen.getByText('6/12/2024')).toBeInTheDocument();
    });

    it('shows "Never" when no lastProcessed timestamp', () => {
      const neverProcessedStatus: ProcessingStatus = {
        totalFiles: 2,
        processedFiles: 0,
        pendingFiles: 2,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: undefined,
      };

      render(<ProcessingStatusCard status={neverProcessedStatus} />);

      // Should not show last processed section when undefined
      expect(screen.queryByText('Last processed:')).not.toBeInTheDocument();
    });
  });

  describe('Status Icons and Colors', () => {
    it('shows spinning loader when processing', () => {
      const processingStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: 2,
        pendingFiles: 3,
        failedFiles: 0,
        isProcessing: true,
      };

      render(<ProcessingStatusCard status={processingStatus} />);

      // Check for spinning animation class
      const badge = screen.getByText('Processing');
      expect(badge).toHaveClass('animate-pulse');
    });

    it('shows success state for completed processing', () => {
      const successStatus: ProcessingStatus = {
        totalFiles: 8,
        processedFiles: 8,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={successStatus} />);

      expect(
        screen.getByText('All files processed successfully'),
      ).toBeInTheDocument();
      expect(screen.getByText('Ready')).toBeInTheDocument();
    });

    it('shows error state for failed processing', () => {
      const errorStatus: ProcessingStatus = {
        totalFiles: 6,
        processedFiles: 4,
        pendingFiles: 0,
        failedFiles: 2,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={errorStatus} />);

      expect(screen.getByText('2 files failed processing')).toBeInTheDocument();
      expect(screen.getByText('Issues')).toBeInTheDocument();
    });

    it('shows warning state for pending files', () => {
      const warningStatus: ProcessingStatus = {
        totalFiles: 10,
        processedFiles: 7,
        pendingFiles: 3,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={warningStatus} />);

      expect(
        screen.getByText('3 files waiting to process'),
      ).toBeInTheDocument();
    });
  });

  describe('Upload Navigation', () => {
    it('provides upload navigation when shown', () => {
      // This test would apply if the component shows upload guidance
      // Currently the component returns null for empty state
      const emptyStatus: ProcessingStatus = {
        totalFiles: 0,
        processedFiles: 0,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
      };

      const { container } = render(
        <ProcessingStatusCard status={emptyStatus} />,
      );
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles inconsistent file counts gracefully', () => {
      const inconsistentStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: 3,
        pendingFiles: 1,
        failedFiles: 2, // Total adds up to 6, not 5
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={inconsistentStatus} />);

      expect(screen.getByText('3/5 files')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument(); // Processed
      expect(screen.getByText('1')).toBeInTheDocument(); // Pending
      expect(screen.getByText('2')).toBeInTheDocument(); // Failed
    });

    it('handles negative file counts', () => {
      const negativeStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: -1, // Invalid negative count
        pendingFiles: 3,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={negativeStatus} />);

      expect(screen.getByText('-1/5 files')).toBeInTheDocument();
      expect(screen.getByText('-1')).toBeInTheDocument(); // Should display as is
    });

    it('handles very large file counts', () => {
      const largeStatus: ProcessingStatus = {
        totalFiles: 100000,
        processedFiles: 75000,
        pendingFiles: 20000,
        failedFiles: 5000,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={largeStatus} />);

      expect(screen.getByText('75000/100000 files')).toBeInTheDocument();
      expect(screen.getByText('75000')).toBeInTheDocument(); // Processed
      expect(screen.getByText('20000')).toBeInTheDocument(); // Pending
      expect(screen.getByText('5000')).toBeInTheDocument(); // Failed
    });

    it('handles invalid date strings gracefully', () => {
      const invalidDateStatus: ProcessingStatus = {
        totalFiles: 3,
        processedFiles: 3,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
        lastProcessed: 'invalid-date-string',
      };

      render(<ProcessingStatusCard status={invalidDateStatus} />);

      // Should handle invalid date without crashing
      expect(screen.getByText('Processing Status')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Semantic Structure', () => {
    it('uses proper semantic HTML structure', () => {
      const accessibleStatus: ProcessingStatus = {
        totalFiles: 10,
        processedFiles: 8,
        pendingFiles: 2,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={accessibleStatus} />);

      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('Processed')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
      expect(screen.getByText('Failed')).toBeInTheDocument();
    });

    it('provides appropriate ARIA labels', () => {
      const ariaStatus: ProcessingStatus = {
        totalFiles: 15,
        processedFiles: 12,
        pendingFiles: 3,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={ariaStatus} />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '80'); // 12/15 = 80%
    });

    it('maintains visual hierarchy with proper headings', () => {
      const hierarchyStatus: ProcessingStatus = {
        totalFiles: 5,
        processedFiles: 5,
        pendingFiles: 0,
        failedFiles: 0,
        isProcessing: false,
      };

      render(<ProcessingStatusCard status={hierarchyStatus} />);

      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      expect(screen.getByText('Processed')).toBeInTheDocument();
      expect(screen.getByText('Pending')).toBeInTheDocument();
      expect(screen.getByText('Failed')).toBeInTheDocument();
    });
  });

  describe('Performance and Rendering', () => {
    it('renders efficiently with frequent status updates', () => {
      const baseStatus: ProcessingStatus = {
        totalFiles: 100,
        processedFiles: 0,
        pendingFiles: 100,
        failedFiles: 0,
        isProcessing: true,
      };

      const startTime = performance.now();
      render(<ProcessingStatusCard status={baseStatus} />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // 50ms threshold
      expect(screen.getByText('Processing Status')).toBeInTheDocument();
    });

    it('handles rapid state transitions smoothly', () => {
      const { rerender } = render(
        <ProcessingStatusCard
          status={{
            totalFiles: 5,
            processedFiles: 0,
            pendingFiles: 5,
            failedFiles: 0,
            isProcessing: true,
          }}
        />,
      );

      expect(screen.getByText('Processing')).toBeInTheDocument();

      rerender(
        <ProcessingStatusCard
          status={{
            totalFiles: 5,
            processedFiles: 5,
            pendingFiles: 0,
            failedFiles: 0,
            isProcessing: false,
          }}
        />,
      );

      expect(screen.getByText('Ready')).toBeInTheDocument();
      expect(
        screen.getByText('All files processed successfully'),
      ).toBeInTheDocument();
    });
  });
});
