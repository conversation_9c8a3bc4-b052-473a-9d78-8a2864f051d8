import React from 'react';
import ExcelCard from '@/shared/components/ui/excel-card';
import {
  TrendingUp,
  TrendingDown,
  ArrowUpDown,
  FileText,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { formatCurrency, formatCompactNumber } from '@/shared/utils/utils';
import type { DashboardMetrics } from '../types/dashboard';

interface DashboardMetricsGridProps {
  metrics: DashboardMetrics;
}

export const DashboardMetricsGrid: React.FC<DashboardMetricsGridProps> = ({
  metrics,
}) => {
  const categorizationPercentage =
    metrics.totalTransactions > 0
      ? (metrics.categorizedTransactions / metrics.totalTransactions) * 100
      : 0;

  const getNetIncomeIcon = () => {
    if (metrics.netIncome > 0) return TrendingUp;
    if (metrics.netIncome < 0) return TrendingDown;
    return ArrowUpDown;
  };

  const getNetIncomeLabel = () => {
    if (metrics.netIncome > 0) return 'Profit';
    if (metrics.netIncome < 0) return 'Loss';
    return 'Break Even';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Income */}
      <ExcelCard
        title="Total Income"
        value={formatCurrency(metrics.totalIncome)}
        subtitle="Revenue generated"
        icon={TrendingUp}
        variant="success"
        className="text-sm"
      />

      {/* Total Expenses */}
      <ExcelCard
        title="Total Expenses"
        value={formatCurrency(metrics.totalExpenses)}
        subtitle="Amount spent"
        icon={TrendingDown}
        variant="warning"
        className="text-sm"
      />

      {/* Net Income */}
      <ExcelCard
        title="Net Income"
        value={formatCurrency(Math.abs(metrics.netIncome))}
        subtitle={metrics.netIncome >= 0 ? 'Profit margin' : 'Loss amount'}
        icon={getNetIncomeIcon()}
        variant={
          metrics.netIncome > 0
            ? 'success'
            : metrics.netIncome < 0
              ? 'error'
              : 'default'
        }
        trend={{
          value: formatCurrency(Math.abs(metrics.netIncome)),
          label: getNetIncomeLabel(),
          direction:
            metrics.netIncome > 0
              ? 'up'
              : metrics.netIncome < 0
                ? 'down'
                : 'neutral',
        }}
        className="text-sm"
      />

      {/* Transaction Summary */}
      <ExcelCard
        title="Transactions"
        value={formatCompactNumber(metrics.totalTransactions)}
        subtitle={`${categorizationPercentage.toFixed(1)}% categorized`}
        icon={FileText}
        variant="info"
      >
        <div className="space-y-2">
          <div className="flex flex-wrap items-center space-x-1 truncate text-caption">
            <CheckCircle className="h-3 w-3 truncateemerald-600" />
            <span className="text-muted-foreground text-data-table">
              {formatCompactNumber(metrics.categorizedTransactions)} categorized
            </span>
          </div>

          {metrics.uncategorizedTransactions > 0 && (
            <div className="flex flex-wrap items-center space-x-1 truncate text-caption">
              <AlertCircle className="h-3 w-3 truncateamber-600" />
              <span className="text-muted-foreground text-data-table">
                {formatCompactNumber(metrics.uncategorizedTransactions)} need
                review
              </span>
            </div>
          )}

          {/* Progress bar */}
          <div className="w-full bg-muted rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${categorizationPercentage}%` }}
            />
          </div>
        </div>
      </ExcelCard>
    </div>
  );
};

export default DashboardMetricsGrid;
