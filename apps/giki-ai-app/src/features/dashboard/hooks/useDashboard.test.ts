/**
 * useDashboard Hook Tests
 * Critical dashboard data management hook tests for production
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useDashboard } from './useDashboard';
import type { DashboardData } from '../types/dashboard';

// Mock the dashboard service
vi.mock('../services/dashboardService', () => ({
  getDashboardData: vi.fn(),
  refreshDashboardData: vi.fn(),
}));

import {
  getDashboardData,
  refreshDashboardData,
} from '../services/dashboardService';

describe('useDashboard - Dashboard Data Management Hook', () => {
  const mockDashboardData: DashboardData = {
    metrics: {
      totalIncome: 150000,
      totalExpenses: 100000,
      netIncome: 50000,
      totalTransactions: 250,
      categorizedTransactions: 200,
      uncategorizedTransactions: 50,
      avgTransactionAmount: 800,
      topCategory: 'Office Supplies',
      topCategoryAmount: 25000,
    },
    recentTransactions: [
      {
        id: '1',
        description: 'Office Supplies Purchase',
        amount: 5000,
        date: '2024-06-14T10:00:00Z',
        category: 'Office Supplies',
        status: 'categorized',
        transaction_type: 'expense',
      },
    ],
    categoryBreakdown: [
      {
        category: 'Office Supplies',
        amount: 50000,
        percentage: 35.7,
        count: 25,
        color: '#3B82F6',
      },
    ],
    monthlyTrends: [
      {
        month: '2024-06',
        income: 150000,
        expenses: 100000,
        net: 50000,
        transactionCount: 250,
      },
    ],
    processingStatus: {
      totalFiles: 5,
      processedFiles: 5,
      pendingFiles: 0,
      failedFiles: 0,
      isProcessing: false,
      lastProcessed: '2024-06-14T11:50:00Z',
    },
    quickActions: [
      {
        id: 'upload-files',
        title: 'Upload Files',
        description: 'Upload financial data files',
        icon: 'Upload',
        route: '/upload',
        color: 'blue',
        isEnabled: true,
      },
    ],
    dateRange: {
      from: '2024-05-15',
      to: '2024-06-14',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Mock successful data fetch by default
    (getDashboardData as unknown).mockResolvedValue(mockDashboardData);
    (refreshDashboardData as unknown).mockResolvedValue(mockDashboardData);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('Initial Loading', () => {
    it('starts with loading state', () => {
      const { result } = renderHook(() => useDashboard());

      expect(result?.current?.loading).toBe(true);
      expect(result?.current?.data).toBeNull();
      expect(result?.current?.error).toBeNull();
      expect(result?.current?.lastUpdated).toBeNull();
    });

    it('loads dashboard data on mount', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1);
      expect(result?.current?.data).toEqual(mockDashboardData);
      expect(result?.current?.error).toBeNull();
      expect(result?.current?.lastUpdated).toBeDefined();
    });

    it('passes date range to service when provided', async () => {
      const dateRange = { from: '2024-01-01', to: '2024-01-31' };

      const { result } = renderHook(() => useDashboard({ dateRange }));

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(getDashboardData).toHaveBeenCalledWith(dateRange);
    });

    it('handles loading errors correctly', async () => {
      const mockError = new Error('Dashboard service unavailable');
      (getDashboardData as unknown).mockRejectedValue(mockError);

      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result?.current?.data).toBeNull();
      expect(result?.current?.error).toEqual({
        code: 'DASHBOARD_LOAD_ERROR',
        message: 'Dashboard service unavailable',
        retryable: true,
      });
      expect(result?.current?.lastUpdated).toBeNull();
    });

    it('handles non-Error exceptions', async () => {
      (getDashboardData as unknown).mockRejectedValue('String error');

      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result?.current?.error).toEqual({
        code: 'DASHBOARD_LOAD_ERROR',
        message: 'Failed to load dashboard data',
        retryable: true,
      });
    });
  });

  describe('Manual Refresh', () => {
    it('provides refresh function', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(typeof result?.current?.refresh).toBe('function');
      expect(result?.current?.isRefreshing).toBe(false);
    });

    it('handles manual refresh correctly', async () => {
      const { result } = renderHook(() => useDashboard());

      // Wait for initial load
      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Trigger refresh
      act(() => {
        void result?.current?.refresh();
      });

      expect(result?.current?.isRefreshing).toBe(true);

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      expect(refreshDashboardData).toHaveBeenCalledTimes(1);
      expect(result?.current?.data).toEqual(mockDashboardData);
    });

    it('handles refresh errors without losing existing data', async () => {
      const { result } = renderHook(() => useDashboard());

      // Wait for initial load
      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      const initialData = result?.current?.data;

      // Mock refresh error
      const mockError = new Error('Refresh failed');
      (refreshDashboardData as unknown).mockRejectedValue(mockError);

      // Trigger refresh
      act(() => {
        void result?.current?.refresh();
      });

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      expect(result?.current?.data).toEqual(initialData); // Data should remain
      expect(result?.current?.error).toEqual({
        code: 'DASHBOARD_REFRESH_ERROR',
        message: 'Refresh failed',
        retryable: true,
      });
    });

    it('passes current date range to refresh', async () => {
      const dateRange = { from: '2024-02-01', to: '2024-02-29' };
      const { result } = renderHook(() => useDashboard({ dateRange }));

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      act(() => {
        void result?.current?.refresh();
      });

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      expect(refreshDashboardData).toHaveBeenCalledWith(dateRange);
    });
  });

  describe('Date Range Management', () => {
    it('provides setDateRange function', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(typeof result?.current?.setDateRange).toBe('function');
    });

    it('updates date range and reloads data', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      const newDateRange = { from: '2024-03-01', to: '2024-03-31' };

      act(() => {
        result?.current?.setDateRange(newDateRange);
      });

      // Should trigger a new load with the new date range
      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledWith(newDateRange);
      });
    });

    it('handles date range updates correctly', async () => {
      const initialDateRange = { from: '2024-01-01', to: '2024-01-31' };
      const { result } = renderHook(() =>
        useDashboard({ dateRange: initialDateRange }),
      );

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      const newDateRange = { from: '2024-02-01', to: '2024-02-29' };

      act(() => {
        result?.current?.setDateRange(newDateRange);
      });

      // Should call getDashboardData with new date range
      await waitFor(() => {
        expect(getDashboardData).toHaveBeenLastCalledWith(newDateRange);
      });
    });
  });

  describe('Auto Refresh', () => {
    it('does not auto refresh by default', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Advance time and check no additional calls
      act(() => {
        vi.advanceTimersByTime(10 * 60 * 1000); // 10 minutes
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1); // Only initial load
    });

    it('auto refreshes when enabled', async () => {
      const { result } = renderHook(() =>
        useDashboard({
          autoRefresh: true,
          refreshInterval: 5000, // 5 seconds for test
        }),
      );

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1);

      // Advance time to trigger auto refresh
      act(() => {
        vi.advanceTimersByTime(5000);
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('uses custom refresh interval', async () => {
      const { result } = renderHook(() =>
        useDashboard({
          autoRefresh: true,
          refreshInterval: 3000, // 3 seconds
        }),
      );

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Advance time less than interval
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1);

      // Advance past interval
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('cleans up auto refresh interval on unmount', async () => {
      const { result, unmount } = renderHook(() =>
        useDashboard({ autoRefresh: true, refreshInterval: 5000 }),
      );

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      unmount();

      // Advance time after unmount
      act(() => {
        vi.advanceTimersByTime(10000);
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1); // Only initial load
    });

    it('auto refresh does not show loading state', async () => {
      const { result } = renderHook(() =>
        useDashboard({ autoRefresh: true, refreshInterval: 3000 }),
      );

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result?.current?.loading).toBe(false);

      // Trigger auto refresh
      act(() => {
        vi.advanceTimersByTime(3000);
      });

      // Loading should remain false for silent refresh
      expect(result?.current?.loading).toBe(false);

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Event Listeners', () => {
    it('listens for data update events', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Trigger custom event
      act(() => {
        window.dispatchEvent(new Event('dataUpdate'));
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('listens for upload complete events with delay', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Trigger upload complete event
      act(() => {
        window.dispatchEvent(new Event('uploadComplete'));
      });

      // Should not call immediately
      expect(getDashboardData).toHaveBeenCalledTimes(1);

      // Advance time to trigger delayed refresh
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('listens for transaction updated events', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      act(() => {
        window.dispatchEvent(new Event('transactionUpdated'));
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('listens for category updated events', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      act(() => {
        window.dispatchEvent(new Event('categoryUpdated'));
      });

      await waitFor(() => {
        expect(getDashboardData).toHaveBeenCalledTimes(2);
      });
    });

    it('cleans up event listeners on unmount', async () => {
      const { result, unmount } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      unmount();

      // Trigger events after unmount
      act(() => {
        window.dispatchEvent(new Event('dataUpdate'));
        window.dispatchEvent(new Event('transactionUpdated'));
        window.dispatchEvent(new Event('categoryUpdated'));
      });

      expect(getDashboardData).toHaveBeenCalledTimes(1); // Only initial load
    });
  });

  describe('Hook State Management', () => {
    it('returns all required state properties', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result.current).toHaveProperty('data');
      expect(result.current).toHaveProperty('loading');
      expect(result.current).toHaveProperty('error');
      expect(result.current).toHaveProperty('lastUpdated');
      expect(result.current).toHaveProperty('refresh');
      expect(result.current).toHaveProperty('setDateRange');
      expect(result.current).toHaveProperty('isRefreshing');
    });

    it('maintains stable function references', async () => {
      const { result, rerender } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      const initialRefresh = result?.current?.refresh;
      const initialSetDateRange = result?.current?.setDateRange;

      rerender();

      expect(result?.current?.refresh).toBe(initialRefresh);
      expect(result?.current?.setDateRange).toBe(initialSetDateRange);
    });

    it('updates lastUpdated timestamp on successful load', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      const firstTimestamp = result?.current?.lastUpdated;
      expect(firstTimestamp).toBeDefined();

      // Wait a bit and trigger refresh
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      act(() => {
        void result?.current?.refresh();
      });

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      expect(result?.current?.lastUpdated).not.toBe(firstTimestamp);
    });
  });

  describe('Error Recovery', () => {
    it('allows retry after error', async () => {
      // First call fails
      (getDashboardData as unknown).mockRejectedValueOnce(
        new Error('Service down'),
      );

      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result?.current?.error).toBeDefined();
      expect(result?.current?.data).toBeNull();

      // Mock service recovery
      (refreshDashboardData as unknown).mockResolvedValue(mockDashboardData);

      // Retry with refresh
      act(() => {
        void result?.current?.refresh();
      });

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      expect(result?.current?.error).toBeNull();
      expect(result?.current?.data).toEqual(mockDashboardData);
    });

    it('maintains error state during loading after date range change', async () => {
      // Initial successful load
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Mock subsequent error
      (getDashboardData as unknown).mockRejectedValue(
        new Error('Network error'),
      );

      act(() => {
        result?.current?.setDateRange({ from: '2024-04-01', to: '2024-04-30' });
      });

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      expect(result?.current?.error).toBeDefined();
      expect(result?.current?.data).toBeNull();
    });
  });

  describe('Performance and Memory', () => {
    it('handles rapid date range changes efficiently', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Rapid date range changes
      act(() => {
        result?.current?.setDateRange({ from: '2024-01-01', to: '2024-01-31' });
        result?.current?.setDateRange({ from: '2024-02-01', to: '2024-02-29' });
        result?.current?.setDateRange({ from: '2024-03-01', to: '2024-03-31' });
      });

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Should handle without memory leaks or excessive calls
      expect(getDashboardData).toHaveBeenCalledWith({
        from: '2024-03-01',
        to: '2024-03-31',
      });
    });

    it('handles multiple simultaneous refresh calls', async () => {
      const { result } = renderHook(() => useDashboard());

      await waitFor(() => {
        expect(result?.current?.loading).toBe(false);
      });

      // Multiple refresh calls
      act(() => {
        void result?.current?.refresh();
        void result?.current?.refresh();
        void result?.current?.refresh();
      });

      await waitFor(() => {
        expect(result?.current?.isRefreshing).toBe(false);
      });

      // Should handle gracefully without duplicate requests
      expect(refreshDashboardData).toHaveBeenCalled();
    });
  });
});
