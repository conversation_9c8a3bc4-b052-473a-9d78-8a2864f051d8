import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';
import {
  getIncomeVsExpenseReport,
  getSpendingByCategoryReport,
} from '@/features/reports/services/reportService';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import type { Transaction } from '@/shared/types/transaction';
import type {
  DashboardData,
  DashboardMetrics,
  RecentTransaction,
  CategoryBreakdownItem,
  MonthlyTrendData,
  ProcessingStatus,
  Action,
} from '../types/dashboard';

// API Response interfaces
interface MonthlyTrendApiItem {
  month: string;
  total_income: number;
  total_expenses: number;
  net_amount: number;
  transaction_count: number;
}

interface ActionsApiResponse {
  actions: Action[];
}

interface MonthlyTrendsApiResponse {
  items: MonthlyTrendApiItem[];
}

/**
 * Dashboard Service
 * Aggregates data from various APIs to provide comprehensive dashboard view
 */

export class DashboardService {
  private static instance: DashboardService;
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  private isCacheValid(key: string): boolean {
    const cached = this?.cache?.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < this.CACHE_TTL;
  }

  private getCached<T>(key: string): T | null {
    if (this.isCacheValid(key)) {
      return (this?.cache?.get(key)?.data as T) || null;
    }
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this?.cache?.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(dateRange?: {
    from: string;
    to: string;
  }): Promise<DashboardData> {
    const cacheKey = `dashboard-${JSON.stringify(dateRange)}`;
    const cached = this.getCached<DashboardData>(cacheKey);
    if (cached) return cached;

    try {
      // Fetch category data once and reuse (fixes API duplication)
      let categoryData: { name: string; spending: number }[] = [];
      try {
        categoryData = await getSpendingByCategoryReport();
      } catch (error) {
        console.warn('Category data fetch failed, using fallback:', error);
      }

      // Fetch all other data in parallel for better performance
      const [
        metrics,
        recentTransactions,
        categoryBreakdown,
        monthlyTrends,
        processingStatus,
        quickActions,
      ] = await Promise.all([
        this.getMetrics(dateRange, categoryData),
        this.getRecentTransactions(),
        this.getCategoryBreakdown(dateRange, categoryData),
        this.getMonthlyTrends(dateRange),
        this.getProcessingStatus(),
        this.getActions(),
      ]);

      const dashboardData: DashboardData = {
        metrics,
        recentTransactions,
        categoryBreakdown,
        monthlyTrends,
        processingStatus,
        quickActions,
        dateRange: dateRange || this.getDefaultDateRange(),
      };

      this.setCache(cacheKey, dashboardData);
      return dashboardData;
    } catch (error) {
      throw handleApiError(error, {
        context: 'getDashboardData',
        defaultMessage: 'Failed to load dashboard data',
      });
    }
  }

  /**
   * Get financial metrics
   */
  private async getMetrics(
    dateRange?: {
      from: string;
      to: string;
    },
    categoryData?: { name: string; spending: number }[],
  ): Promise<DashboardMetrics> {
    try {
      // Get income vs expense data with fallback
      let incomeExpenseData: {
        totalIncome: number;
        totalExpense: number;
        netAmount: number;
      };
      try {
        incomeExpenseData = await getIncomeVsExpenseReport(
          dateRange
            ? { from: new Date(dateRange.from), to: new Date(dateRange.to) }
            : undefined,
        );
      } catch (error) {
        console.warn('Income/expense report failed, using fallback:', error);
        incomeExpenseData = { totalIncome: 0, totalExpense: 0, netAmount: 0 };
      }

      // Get transaction counts with fallback
      let transactions: Transaction[] = [];
      try {
        const transactionsResponse = await fetchTransactions({
          startDate: dateRange?.from,
          endDate: dateRange?.to,
          pageSize: 1000, // Get enough for accurate counts
        });

        transactions = Array.isArray(transactionsResponse)
          ? transactionsResponse
          : transactionsResponse.items;
      } catch (error) {
        console.warn('Transactions fetch failed, using empty array:', error);
        transactions = [];
      }

      const categorizedCount = transactions.filter(
        (t) => t.category_path || t.ai_suggested_category_path,
      ).length;

      const uncategorizedCount = transactions.length - categorizedCount;

      const avgTransactionAmount =
        transactions.length > 0
          ? transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0) /
            transactions.length
          : 0;

      // Use pre-fetched category data (prevents duplicate API call)
      const topCategory =
        categoryData && categoryData.length > 0 ? categoryData[0] : null;

      return {
        totalIncome: incomeExpenseData.totalIncome,
        totalExpenses: incomeExpenseData.totalExpense,
        netIncome: incomeExpenseData.netAmount,
        totalTransactions: transactions.length,
        categorizedTransactions: categorizedCount,
        uncategorizedTransactions: uncategorizedCount,
        avgTransactionAmount,
        topCategory: topCategory?.name || 'No data available',
        topCategoryAmount: topCategory?.spending || 0,
      };
    } catch (error) {
      // Final fallback to prevent dashboard from breaking
      console.warn(
        'Complete metrics fetch failed, using fallback data:',
        error,
      );
      return {
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        totalTransactions: 0,
        categorizedTransactions: 0,
        uncategorizedTransactions: 0,
        avgTransactionAmount: 0,
        topCategory: 'Dashboard loading...',
        topCategoryAmount: 0,
      };
    }
  }

  /**
   * Get recent transactions
   */
  private async getRecentTransactions(): Promise<RecentTransaction[]> {
    try {
      const response = await fetchTransactions({
        pageSize: 10,
        sortBy: 'date',
        sortDirection: 'desc',
      });

      const transactions = Array.isArray(response) ? response : response.items;

      return transactions.slice(0, 5).map((t) => ({
        id: t.id,
        description: t.description,
        amount: t.amount,
        date: t.date,
        category: t.category_path || t.ai_suggested_category_path || undefined,
        status: this.getTransactionStatus(t),
        transaction_type: t.transaction_type as 'income' | 'expense',
      }));
    } catch (error) {
      console.warn('Failed to fetch recent transactions:', error);
      return [];
    }
  }

  /**
   * Get category breakdown
   */
  private async getCategoryBreakdown(
    _dateRange?: {
      from: string;
      to: string;
    },
    categoryData?: { name: string; spending: number }[],
  ): Promise<CategoryBreakdownItem[]> {
    try {
      // Use pre-fetched category data (prevents duplicate API call)
      if (!categoryData || categoryData.length === 0) {
        return [];
      }

      const totalAmount = categoryData.reduce(
        (sum, item) => sum + item.spending,
        0,
      );

      return categoryData.slice(0, 5).map((item, index) => ({
        category: item.name,
        amount: item.spending,
        percentage: totalAmount > 0 ? (item.spending / totalAmount) * 100 : 0,
        count: 0, // TODO: Add transaction count to backend response
        color: this.getCategoryColor(index),
      }));
    } catch (error) {
      console.warn('Failed to process category breakdown:', error);
      return [];
    }
  }

  /**
   * Get monthly trends data
   */
  private async getMonthlyTrends(dateRange?: {
    from: string;
    to: string;
  }): Promise<MonthlyTrendData[]> {
    try {
      const params = new URLSearchParams();
      if (dateRange?.from) params.append('start_date', dateRange.from);
      if (dateRange?.to) params.append('end_date', dateRange.to);

      const response = await apiClient.get<MonthlyTrendsApiResponse>(
        `/api/v1/reports/monthly-trends?${params.toString()}`,
      );

      return response?.data?.items.map((item: MonthlyTrendApiItem) => ({
        month: item.month,
        income: item.total_income,
        expenses: item.total_expenses,
        net: item.net_amount,
        transactionCount: item.transaction_count,
      }));
    } catch (error) {
      console.warn('Failed to fetch monthly trends:', error);
      return [];
    }
  }

  /**
   * Get processing status
   */
  private async getProcessingStatus(): Promise<ProcessingStatus> {
    try {
      const response = await apiClient.get<ProcessingStatus>(
        '/api/v1/uploads/processing-status',
      );
      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'getProcessingStatus',
        defaultMessage: 'Failed to fetch file processing status',
      });
    }
  }

  /**
   * Get quick actions
   */
  private async getActions(): Promise<Action[]> {
    try {
      const response = await apiClient.get<ActionsApiResponse>(
        '/api/v1/auth/quick-actions',
      );
      return response?.data?.actions || [];
    } catch (error) {
      throw handleApiError(error, {
        context: 'getActions',
        defaultMessage: 'Failed to fetch user quick actions',
      });
    }
  }

  /**
   * Refresh dashboard data
   */
  async refreshDashboardData(dateRange?: {
    from: string;
    to: string;
  }): Promise<DashboardData> {
    const cacheKey = `dashboard-${JSON.stringify(dateRange)}`;
    this?.cache?.delete(cacheKey);
    return this.getDashboardData(dateRange);
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this?.cache?.clear();
  }

  // Helper methods
  private getTransactionStatus(
    transaction: Transaction,
  ): 'categorized' | 'needs_review' | 'ai_suggested' {
    if (transaction.is_user_modified || transaction.category_path) {
      return 'categorized';
    }
    if (transaction.ai_suggested_category_path) {
      return 'ai_suggested';
    }
    return 'needs_review';
  }

  private getCategoryColor(index: number): string {
    const colors = [
      'hsl(var(--giki-brand-blue))', // blue
      'hsl(var(--giki-success))', // emerald/green
      'hsl(var(--giki-brand-purple))', // violet
      'hsl(var(--giki-warning))', // amber
      'hsl(var(--giki-destructive))', // red
      'hsl(var(--giki-info))', // cyan
      'hsl(var(--giki-brand-green))', // lime/green
      'hsl(var(--giki-brand-pink))', // pink
    ];
    return colors[index % colors.length];
  }

  private getDefaultDateRange(): { from: string; to: string } {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      from: thirtyDaysAgo.toISOString().split('T')[0],
      to: today.toISOString().split('T')[0],
    };
  }
}

// Export singleton instance
export const dashboardService = DashboardService.getInstance();

// Export convenience functions
export const getDashboardData = (dateRange?: { from: string; to: string }) =>
  dashboardService.getDashboardData(dateRange);

export const refreshDashboardData = (dateRange?: {
  from: string;
  to: string;
}) => dashboardService.refreshDashboardData(dateRange);

export const clearDashboardCache = () => dashboardService.clearCache();
