export interface DashboardMetrics {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  totalTransactions: number;
  categorizedTransactions: number;
  uncategorizedTransactions: number;
  avgTransactionAmount: number;
  topCategory: string;
  topCategoryAmount: number;
}

export interface RecentTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  category?: string;
  status: 'categorized' | 'needs_review' | 'ai_suggested';
  transaction_type: 'income' | 'expense';
}

export interface CategoryBreakdownItem {
  category: string;
  amount: number;
  percentage: number;
  count: number;
  color?: string;
}

export interface ProcessingStatus {
  totalFiles: number;
  processedFiles: number;
  pendingFiles: number;
  failedFiles: number;
  lastProcessed?: string;
  isProcessing: boolean;
}

export interface Action {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
  isEnabled: boolean;
  badge?: string;
}

export interface MonthlyTrendData {
  month: string;
  income: number;
  expenses: number;
  net: number;
  transactionCount: number;
}

export interface DashboardData {
  metrics: DashboardMetrics;
  recentTransactions: RecentTransaction[];
  categoryBreakdown: CategoryBreakdownItem[];
  monthlyTrends: MonthlyTrendData[];
  processingStatus: ProcessingStatus;
  quickActions: Action[];
  dateRange: {
    from: string;
    to: string;
  };
}

export interface DashboardError {
  code: string;
  message: string;
  retryable: boolean;
}

export interface DashboardState {
  data: DashboardData | null;
  loading: boolean;
  error: DashboardError | null;
  lastUpdated: string | null;
}
