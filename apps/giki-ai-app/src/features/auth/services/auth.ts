/**
 * Authentication Token Management
 *
 * Manages JWT tokens in localStorage for authentication
 */

const ACCESS_TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';

/**
 * Get the access token from localStorage
 */
export function getAccessToken(): string | null {
  try {
    return localStorage.getItem(ACCESS_TOKEN_KEY);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
    return null;
  }
}

/**
 * Set the access token in localStorage
 */
export function setAccessToken(token: string): void {
  try {
    localStorage.setItem(ACCESS_TOKEN_KEY, token);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Get the refresh token from localStorage
 */
export function getRefreshToken(): string | null {
  try {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
    return null;
  }
}

/**
 * Set the refresh token in localStorage
 */
export function setRefreshToken(token: string): void {
  try {
    localStorage.setItem(REFRESH_TOKEN_KEY, token);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Clear all tokens from localStorage
 */
export function clearTokens(): void {
  try {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  } catch {
    // Gracefully handle localStorage errors in private browsing mode
  }
}

/**
 * Check if user is authenticated (has valid access token)
 */
export function isAuthenticated(): boolean {
  const token = getAccessToken();
  if (!token) return false;

  try {
    // Parse JWT to check expiration
    const payload = JSON.parse(atob(token.split('.')[1])) as { exp: number };
    const now = Math.floor(Date.now() / 1000);
    return payload.exp > now;
  } catch {
    // Token is invalid or malformed, consider user unauthenticated
    return false;
  }
}
