/**
 * Auth Service Tests
 * Tests for authentication operations
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { login, logout, refreshToken, getCurrentUser } from './authService';
import { apiClient } from '@/shared/services/api/apiClient';
import * as authStorage from './auth';

// Mock the API client
vi.mock('@/shared/services/api/apiClient', () => ({
  apiClient: {
    post: vi.fn(),
    postFormUrlEncoded: vi.fn(),
    get: vi.fn(),
  },
}));

// Mock the environment variable
vi.stubEnv('VITE_API_BASE_URL', 'http://localhost:8000');

// Mock the auth storage
vi.mock('./auth', () => ({
  setAccessToken: vi.fn(),
  setRefreshToken: vi.fn(),
  clearTokens: vi.fn(),
  getAccessToken: vi.fn(),
  getRefreshToken: vi.fn(),
}));

describe('Auth Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('login', () => {
    it('successfully logs in a user with valid credentials', async () => {
      const mockResponse = {
        data: {
          access_token: 'access-token-123',
          refresh_token: 'refresh-token-456',
          token_type: 'bearer',
          user: {
            id: 1,
            email: '<EMAIL>',
            full_name: 'Test User',
            tenant_id: 1,
            is_active: true,
            is_admin: false,
          },
        },
        status: 200,
        headers: new Headers(),
      };

      vi.mocked(apiClient.postFormUrlEncoded).mockResolvedValueOnce(
        mockResponse,
      );

      const result = await login({
        username: '<EMAIL>',
        password: 'password123',
      });

      expect(apiClient.postFormUrlEncoded).toHaveBeenCalledWith(
        '/auth/token',
        {
          username: '<EMAIL>',
          password: 'password123',
          grant_type: 'password',
        },
        expect.objectContaining({ requiresAuth: false }),
      );

      expect(result).toEqual({
        accessToken: 'access-token-123',
        refreshToken: 'refresh-token-456',
        tokenType: 'bearer',
      });
    });

    it('falls back to legacy endpoint when primary fails', async () => {
      const mockError = new Error('Primary auth failed');
      const mockFallbackResponse = {
        data: {
          access_token: 'fallback-token',
          refresh_token: 'fallback-refresh',
          token_type: 'bearer',
        },
        status: 200,
        headers: new Headers(),
      };

      vi.mocked(apiClient.postFormUrlEncoded)
        .mockRejectedValueOnce(mockError) // Primary fails
        .mockResolvedValueOnce(mockFallbackResponse); // Fallback succeeds

      const result = await login({
        username: '<EMAIL>',
        password: 'password123',
      });

      expect(apiClient.postFormUrlEncoded).toHaveBeenCalledTimes(2);
      expect(result.accessToken).toBe('fallback-token');
    });

    it('handles authentication failure on both endpoints', async () => {
      const mockError = new Error('Auth failed');

      vi.mocked(apiClient.postFormUrlEncoded)
        .mockRejectedValueOnce(mockError) // Primary fails
        .mockRejectedValueOnce(mockError); // Fallback fails

      await expect(
        login({ username: '<EMAIL>', password: 'wrong' }),
      ).rejects.toThrow();
    });
  });

  describe('logout', () => {
    it('clears all auth data', async () => {
      await logout();

      // Note: logout in authService doesn't call clearTokens directly
      // It only makes the API call. Token clearing is handled by the auth store
      expect(authStorage.clearTokens).not.toHaveBeenCalled();
    });
  });

  describe('refreshToken', () => {
    it('successfully refreshes tokens', async () => {
      const mockFetchResponse = {
        ok: true,
        json: async () => ({
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
          token_type: 'bearer',
        }),
      };

      // Mock global fetch for refreshToken which uses direct fetch
      global.fetch = vi.fn().mockResolvedValueOnce(mockFetchResponse);

      const result = await refreshToken('old-refresh-token');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/refresh'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            Authorization: 'Bearer old-refresh-token',
          }),
          body: JSON.stringify({ refresh_token: 'old-refresh-token' }),
        }),
      );

      expect(result).toEqual({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        tokenType: 'bearer',
      });
    });

    it('throws error for invalid refresh token', async () => {
      const mockFetchResponse = {
        ok: false,
        status: 401,
        json: async () => ({ detail: 'Invalid refresh token' }),
      };

      global.fetch = vi.fn().mockResolvedValueOnce(mockFetchResponse);

      await expect(refreshToken('invalid-token')).rejects.toThrow(
        'Refresh token expired or invalid',
      );
    });

    it('handles network errors', async () => {
      global.fetch = vi.fn().mockRejectedValueOnce(new Error('Network error'));

      await expect(refreshToken('token')).rejects.toThrow('Network error');
    });
  });

  describe('getCurrentUser', () => {
    it('fetches current user when authenticated', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        full_name: 'Test User',
        tenant_id: 1,
        is_active: true,
        is_admin: false,
      };

      const mockResponse = {
        data: mockUser,
        status: 200,
        headers: new Headers(),
      };

      vi.mocked(authStorage.getAccessToken).mockReturnValueOnce('valid-token');
      vi.mocked(apiClient.get).mockResolvedValueOnce(mockResponse);

      const result = await getCurrentUser();

      expect(apiClient.get).toHaveBeenCalledWith('/auth/users/me');
      expect(result).toEqual(mockUser);
    });

    it('returns null when API call fails', async () => {
      vi.mocked(authStorage.getAccessToken).mockReturnValueOnce(null);
      vi.mocked(apiClient.get).mockRejectedValueOnce(new Error('Unauthorized'));

      await expect(getCurrentUser()).rejects.toThrow('Unauthorized');
    });

    it('handles API errors by throwing them', async () => {
      vi.mocked(authStorage.getAccessToken).mockReturnValueOnce('valid-token');
      vi.mocked(apiClient.get).mockRejectedValueOnce(
        new Error('Network error'),
      );

      await expect(getCurrentUser()).rejects.toThrow('Network error');
      // getCurrentUser doesn't clear tokens on error in the actual implementation
      expect(authStorage.clearTokens).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('handles network errors during login', async () => {
      vi.mocked(apiClient.postFormUrlEncoded)
        .mockRejectedValueOnce(new TypeError('Failed to fetch'))
        .mockRejectedValueOnce(new TypeError('Failed to fetch'));

      await expect(
        login({ username: '<EMAIL>', password: 'password' }),
      ).rejects.toThrow('Failed to fetch');
    });

    it('handles malformed response during login', async () => {
      const mockResponse = {
        data: {
          // Missing required fields
          token_type: 'bearer',
        },
        status: 200,
        headers: new Headers(),
      };

      vi.mocked(apiClient.postFormUrlEncoded).mockResolvedValueOnce(
        mockResponse,
      );

      const result = await login({
        username: '<EMAIL>',
        password: 'password',
      });
      // Login still succeeds but with undefined tokens
      expect(result).toEqual({
        accessToken: undefined,
        refreshToken: undefined,
        tokenType: 'bearer',
      });
    });

    it('handles server errors gracefully', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' },
        },
      };

      vi.mocked(apiClient.postFormUrlEncoded)
        .mockRejectedValueOnce(mockError) // Primary fails
        .mockRejectedValueOnce(mockError); // Fallback fails too

      await expect(
        login({ username: '<EMAIL>', password: 'password' }),
      ).rejects.toThrow();
    });
  });

  describe('Integration Scenarios', () => {
    it('handles complete auth flow with token refresh', async () => {
      // Initial login
      const loginResponse = {
        data: {
          access_token: 'initial-access',
          refresh_token: 'initial-refresh',
          token_type: 'bearer',
          user: {
            id: 1,
            email: '<EMAIL>',
            full_name: 'Test User',
            tenant_id: 1,
            is_active: true,
            is_admin: false,
          },
        },
        status: 200,
        headers: new Headers(),
      };

      vi.mocked(apiClient.postFormUrlEncoded).mockResolvedValueOnce(
        loginResponse,
      );

      const loginResult = await login({
        username: '<EMAIL>',
        password: 'password',
      });
      expect(loginResult.accessToken).toBe('initial-access');

      // Token refresh
      const mockFetchResponse = {
        ok: true,
        json: async () => ({
          access_token: 'refreshed-access',
          refresh_token: 'refreshed-refresh',
          token_type: 'bearer',
        }),
      };

      global.fetch = vi.fn().mockResolvedValueOnce(mockFetchResponse);

      const refreshResult = await refreshToken('initial-refresh');
      expect(refreshResult.accessToken).toBe('refreshed-access');

      // Logout - it takes optional refresh token
      await logout('initial-refresh');

      expect(apiClient.post).toHaveBeenCalledWith(
        '/auth/logout',
        {},
        expect.objectContaining({
          requiresAuth: false,
          headers: {
            Authorization: 'Bearer initial-refresh',
          },
        }),
      );
    });
  });
});
