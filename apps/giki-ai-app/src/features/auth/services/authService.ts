import { apiClient } from '@/shared/services/api/apiClient';
import {
  ErrorType,
  createAuthenticationError,
  AppError,
} from '@/shared/types/errors';
import { logger } from '@/shared/utils/errorHandling';
import { User } from '../types/auth';

// API endpoint configuration
const AUTH_BASE_PATH = '/api/v1/auth';

export interface LoginCredentials {
  username: string; // email
  password: string;
}

export interface RegistrationCredentials {
  email: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  tokenType?: string;
}

// Interface for the raw token response from the backend
interface RawTokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type?: string;
}

/**
 * Login function
 */
export const login = async (
  credentials: LoginCredentials,
): Promise<AuthTokens> => {
  logger.info('authService.login called', 'authService', {
    username: credentials.username,
  });

  try {
    const response = await apiClient.postFormUrlEncoded<RawTokenResponse>(
      `${AUTH_BASE_PATH}/token`,
      {
        username: credentials.username,
        password: credentials.password,
        grant_type: 'password',
      },
      {
        requiresAuth: false,
        signal: AbortSignal.timeout(30000),
      },
    );

    const { data } = response;
    const tokens: AuthTokens = {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      tokenType: data.token_type || 'bearer',
    };
    return tokens;
  } catch (error) {
    logger.error('Authentication failed', 'authService', error as Error);

    const authError = error as AppError;
    if (authError.statusCode === 401) {
      throw createAuthenticationError(
        'Invalid email or password',
        authError.statusCode,
      );
    } else if (authError.statusCode === 403) {
      throw createAuthenticationError(
        'Account is inactive or locked',
        authError.statusCode,
      );
    } else if (
      authError.statusCode === 408 ||
      (error instanceof Error && error.name === 'TimeoutError') ||
      (error instanceof Error && error.name === 'AbortError')
    ) {
      throw createAuthenticationError(
        'Authentication is taking longer than expected. Please try again.',
        408,
      );
    }
    throw error;
  }
};

/**
 * Refreshes an access token using a refresh token.
 * This function is called by apiClient's internal refresh mechanism.
 * It MUST use direct fetch to avoid circular dependencies with apiClient.
 */
export const refreshToken = async (
  currentRefreshToken: string,
): Promise<AuthTokens> => {
  logger.info('authService.refreshToken called', 'authService');
  // Determine the full base URL for direct fetch
  // Assuming VITE_API_BASE_URL provides the absolute base (e.g., http://localhost:8000)
  // and apiClient's default relative path is /api/v1
  const apiBaseUrl = (import.meta.env.VITE_API_BASE_URL as string) || '';
  const fullRefreshUrl = `${apiBaseUrl}${AUTH_BASE_PATH}/refresh`;

  try {
    const response = await fetch(fullRefreshUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${currentRefreshToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify({ refresh_token: currentRefreshToken }),
      credentials: 'include',
    });

    if (!response.ok) {
      let errorMessage = 'Token refresh failed';
      try {
        const errorData = (await response.json()) as { detail?: string };
        errorMessage = errorData.detail || errorMessage;
      } catch (e) {
        logger.warn(
          'Could not parse error response during refresh',
          'authService',
          e as Error,
        );
      }
      if (response.status === 401) {
        throw createAuthenticationError(
          'Refresh token expired or invalid',
          response.status,
        );
      }
      throw createAuthenticationError(errorMessage, response.status);
    }

    const data = (await response.json()) as RawTokenResponse;
    const tokens: AuthTokens = {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      tokenType: data.token_type || 'bearer',
    };
    return tokens;
  } catch (error) {
    logger.error(
      'Token refresh error in authService',
      'authService',
      error as Error,
    );
    // Ensure a proper AppError is thrown for apiClient to catch
    // Check if it's already an AppError (duck typing by checking for 'type' property)
    if (error && typeof error === 'object' && 'type' in error) {
      throw error; // It's already an AppError, re-throw
    }
    // Otherwise, wrap it
    const message =
      error instanceof Error
        ? error.message
        : 'Token refresh failed due to an unexpected error';
    throw createAuthenticationError(message);
  }
};

/**
 * Registers a new user.
 */
export const register = async (
  credentials: RegistrationCredentials,
): Promise<{ message: string }> => {
  logger.info('authService.register called', 'authService', {
    email: credentials.email,
  });
  try {
    const response = await apiClient.post<{ message: string }>(
      `${AUTH_BASE_PATH}/register`,
      {
        email: credentials.email,
        password: credentials.password,
      },
      { requiresAuth: false }, // Registration does not require prior authentication
    );
    return { message: response?.data?.message || 'Registration successful' };
  } catch (error) {
    logger.error(
      'Registration error in authService',
      'authService',
      error as Error,
    );
    if ((error as AppError).statusCode === 409) {
      throw createAuthenticationError(
        'An account with this email already exists.',
        (error as AppError).statusCode,
      );
    } else if (
      (error as AppError).statusCode === 400 ||
      (error as AppError).type === ErrorType.VALIDATION
    ) {
      throw createAuthenticationError(
        'Invalid registration data. Please check your inputs.',
        (error as AppError).statusCode,
      );
    }
    throw error;
  }
};

/**
 * Logs out a user.
 * Server-side logout to invalidate refresh token.
 */
export const logout = async (currentRefreshToken?: string): Promise<void> => {
  logger.info('authService.logout called', 'authService');
  if (currentRefreshToken) {
    try {
      // Logout doesn't need the access token, but needs to send the refresh token.
      // apiClient's default post will try to use access token if requiresAuth is true.
      // So, set requiresAuth to false and manually add Authorization header.
      await apiClient.post<void>(
        `${AUTH_BASE_PATH}/logout`,
        {}, // No body needed for logout typically, or backend specific
        {
          requiresAuth: false, // Don't use access token
          headers: {
            Authorization: `Bearer ${currentRefreshToken}`,
          },
        },
      );
    } catch (error) {
      logger.warn(
        'Failed to call server-side logout, proceeding with client-side only',
        'authService',
        error as Error,
      );
      // apiClient will throw an AppError, which is fine.
      // We don't want to re-throw here as client-side token clearing should still happen.
    }
  }
  // Client-side token clearing is handled by the auth store.
  return Promise.resolve();
};

/**
 * Gets the current user's profile.
 */
export const getCurrentUser = async (): Promise<User> => {
  logger.info('authService.getCurrentUser called', 'authService');
  try {
    const response = await apiClient.get<User>(`${AUTH_BASE_PATH}/users/me`);
    return response.data;
  } catch (error) {
    logger.error(
      'Error getting user profile in authService',
      'authService',
      error as Error,
    );
    throw error;
  }
};
