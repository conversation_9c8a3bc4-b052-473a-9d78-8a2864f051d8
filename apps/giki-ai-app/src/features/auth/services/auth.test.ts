/**
 * Auth Storage Service Tests
 * Tests for token storage and management
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
  clearTokens,
  isAuthenticated,
} from './auth';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0,
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

describe('Auth Storage Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock?.getItem?.mockClear();
    localStorageMock?.setItem?.mockClear();
    localStorageMock?.removeItem?.mockClear();
    localStorageMock?.clear?.mockClear();
  });

  describe('Access Token Management', () => {
    it('sets access token in localStorage', () => {
      const token = 'test-access-token';
      setAccessToken(token);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'accessToken',
        token,
      );
    });

    it('gets access token from localStorage', () => {
      const token = 'test-access-token';
      localStorageMock?.getItem?.mockReturnValueOnce(token);

      const result = getAccessToken();

      expect(localStorageMock.getItem).toHaveBeenCalledWith('accessToken');
      expect(result).toBe(token);
    });

    it('returns null when no access token exists', () => {
      localStorageMock?.getItem?.mockReturnValueOnce(null);

      const result = getAccessToken();

      expect(result).toBeNull();
    });
  });

  describe('Refresh Token Management', () => {
    it('sets refresh token in localStorage', () => {
      const token = 'test-refresh-token';
      setRefreshToken(token);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'refreshToken',
        token,
      );
    });

    it('gets refresh token from localStorage', () => {
      const token = 'test-refresh-token';
      localStorageMock?.getItem?.mockReturnValueOnce(token);

      const result = getRefreshToken();

      expect(localStorageMock.getItem).toHaveBeenCalledWith('refreshToken');
      expect(result).toBe(token);
    });

    it('returns null when no refresh token exists', () => {
      localStorageMock?.getItem?.mockReturnValueOnce(null);

      const result = getRefreshToken();

      expect(result).toBeNull();
    });
  });

  describe('Token Clearing', () => {
    it('clears both tokens from localStorage', () => {
      clearTokens();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('accessToken');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('refreshToken');
      expect(localStorageMock.removeItem).toHaveBeenCalledTimes(2);
    });
  });

  describe('Authentication Check', () => {
    it('correctly identifies authenticated state with valid token', () => {
      // Create a valid JWT token (exp = 1 hour from now)
      const exp = Math.floor(Date.now() / 1000) + 3600;
      const payload = { exp, sub: 'user123' };
      const encodedPayload = btoa(JSON.stringify(payload));
      const token = `header.${encodedPayload}.signature`;

      localStorageMock?.getItem?.mockReturnValueOnce(token);

      const result = isAuthenticated();

      expect(result).toBe(true);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('accessToken');
    });

    it('correctly identifies unauthenticated state with expired token', () => {
      // Create an expired JWT token (exp = 1 hour ago)
      const exp = Math.floor(Date.now() / 1000) - 3600;
      const payload = { exp, sub: 'user123' };
      const encodedPayload = btoa(JSON.stringify(payload));
      const token = `header.${encodedPayload}.signature`;

      localStorageMock?.getItem?.mockReturnValueOnce(token);

      const result = isAuthenticated();

      expect(result).toBe(false);
    });

    it('returns false when no token exists', () => {
      localStorageMock?.getItem?.mockReturnValueOnce(null);

      const result = isAuthenticated();

      expect(result).toBe(false);
    });

    it('returns false for malformed token', () => {
      localStorageMock?.getItem?.mockReturnValueOnce('malformed-token');

      const result = isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('handles localStorage errors gracefully for getters', () => {
      localStorageMock?.getItem?.mockImplementationOnce(() => {
        throw new Error('Storage error');
      });

      expect(() => getAccessToken()).not.toThrow();
      expect(getAccessToken()).toBeUndefined();
    });

    it('handles localStorage errors gracefully for setters', () => {
      localStorageMock?.setItem?.mockImplementationOnce(() => {
        throw new Error('Storage error');
      });

      expect(() => setAccessToken('token')).not.toThrow();
    });

    it('handles localStorage errors gracefully for clear operations', () => {
      localStorageMock?.removeItem?.mockImplementationOnce(() => {
        throw new Error('Storage error');
      });

      expect(() => clearTokens()).not.toThrow();
    });
  });

  describe('Integration Scenarios', () => {
    it('handles complete auth flow', () => {
      const accessToken = 'access-123';
      const refreshToken = 'refresh-456';

      // Login
      setAccessToken(accessToken);
      setRefreshToken(refreshToken);

      expect(localStorageMock.setItem).toHaveBeenCalledTimes(2);

      // Verify stored data
      localStorageMock?.getItem?.mockImplementation((key) => {
        switch (key) {
          case 'accessToken':
            return accessToken;
          case 'refreshToken':
            return refreshToken;
          default:
            return null;
        }
      });

      expect(getAccessToken()).toBe(accessToken);
      expect(getRefreshToken()).toBe(refreshToken);

      // Logout
      clearTokens();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('accessToken');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('refreshToken');
    });
  });
});
