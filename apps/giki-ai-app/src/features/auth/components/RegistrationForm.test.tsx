/**
 * RegistrationForm Component Tests
 * Critical authentication component tests for user registration workflow
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import RegistrationForm from './RegistrationForm';
import { ErrorType } from '@/shared/types/errors';

// Mock the auth store
vi.mock('@/shared/services/auth/authStore', () => ({
  default: vi.fn(() => ({
    error: null,
  })),
}));

// Mock useApiPost hook
const mockExecuteRegister = vi.fn();
const mockResetRegisterApiState = vi.fn();
vi.mock('@/shared/hooks/useApi', () => ({
  useApiPost: vi.fn(() => [
    {
      data: null,
      isLoading: false,
      error: null,
    },
    {
      execute: mockExecuteRegister,
      reset: mockResetRegisterApiState,
    },
  ]),
}));

// Mock useToast
const mockToast = vi.fn();
vi.mock('@/shared/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}));

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock password validation
vi.mock('@/shared/types/errors', async () => {
  const actual = await vi.importActual('@/shared/types/errors');
  return {
    ...actual,
    validatePassword: vi.fn((password: string) => {
      if (password.length < 8) {
        return { message: 'Password must be at least 8 characters long' };
      }
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(password)) {
        return {
          message:
            'Password must include uppercase, lowercase, number, and special character',
        };
      }
      return null;
    }),
    PASSWORD_ERROR_MESSAGES: {
      PASSWORDS_DONT_MATCH: 'Passwords do not match',
    },
  };
});

import { useApiPost } from '@/shared/hooks/useApi';

describe('RegistrationForm - Authentication Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockExecuteRegister.mockClear();
    mockResetRegisterApiState.mockClear();
    mockToast.mockClear();
    mockNavigate.mockClear();
  });

  it('renders registration form with all required fields', () => {
    render(<RegistrationForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /register/i }),
    ).toBeInTheDocument();
    expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
  });

  it('shows password requirements', () => {
    render(<RegistrationForm />);

    expect(
      screen.getByText(/password must be at least 8 characters/i),
    ).toBeInTheDocument();
  });

  it('validates email requirement', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Please enter your email.',
    });
  });

  it('validates password requirement', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, '<EMAIL>');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Please enter a password.',
    });
  });

  it('validates password strength requirements', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'weak');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Password must be at least 8 characters long',
    });
  });

  it('validates password confirmation matching', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'StrongPass123!');
    await user.type(confirmPasswordInput, 'DifferentPass123!');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Passwords do not match',
    });
  });

  it('submits valid registration form successfully', async () => {
    const user = userEvent.setup();
    mockExecuteRegister.mockResolvedValue({
      data: { message: 'Registration successful! Please check your email.' },
    });

    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'StrongPass123!');
    await user.type(confirmPasswordInput, 'StrongPass123!');

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockExecuteRegister).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'StrongPass123!',
    });

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Registration Successful',
        description: 'Registration successful! Please check your email.',
      });
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  it('handles successful registration without message', async () => {
    const user = userEvent.setup();
    mockExecuteRegister.mockResolvedValue({
      data: { id: 'user123' }, // No message field
    });

    render(<RegistrationForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'StrongPass123!');
    await user.type(
      screen.getByLabelText(/confirm password/i),
      'StrongPass123!',
    );
    await user.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Registration Successful',
        description: 'You have been registered. Please log in.',
      });
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  it('handles duplicate email error', async () => {
    const user = userEvent.setup();
    const duplicateError = {
      message: 'User already exists',
      statusCode: 409,
      details: { message: 'User with this email already exists' },
    };
    mockExecuteRegister.mockRejectedValue(duplicateError);

    render(<RegistrationForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'StrongPass123!');
    await user.type(
      screen.getByLabelText(/confirm password/i),
      'StrongPass123!',
    );
    await user.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'destructive',
        title: 'Registration Failed',
        description:
          'An account with this email already exists. Please use a different email or try logging in.',
      });
    });
  });

  it('handles network error', async () => {
    const user = userEvent.setup();
    const networkError = {
      message: 'Network error occurred',
      type: 'NETWORK',
    };
    mockExecuteRegister.mockRejectedValue(networkError);

    render(<RegistrationForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'StrongPass123!');
    await user.type(
      screen.getByLabelText(/confirm password/i),
      'StrongPass123!',
    );
    await user.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'destructive',
        title: 'Registration Failed',
        description:
          'Cannot connect to the server. Please check your network or contact support.',
      });
    });
  });

  it('handles server-side password validation error', async () => {
    const user = userEvent.setup();
    const passwordError = {
      message: 'Password validation failed',
      statusCode: 400,
      details: { detail: 'Password must contain at least 12 characters' },
    };
    mockExecuteRegister.mockRejectedValue(passwordError);

    render(<RegistrationForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'StrongPass123!');
    await user.type(
      screen.getByLabelText(/confirm password/i),
      'StrongPass123!',
    );
    await user.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'destructive',
        title: 'Registration Failed',
        description: 'Password must contain at least 12 characters',
      });
    });
  });

  it('shows loading state during submission', async () => {
    vi.mocked(useApiPost).mockReturnValue([
      {
        data: null,
        isLoading: true,
        error: null,
      },
      {
        execute: mockExecuteRegister,
        reset: mockResetRegisterApiState,
      },
    ]);

    render(<RegistrationForm />);

    expect(screen.getByText('Registering...')).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeDisabled();
    expect(screen.getByLabelText(/^password$/i)).toBeDisabled();
    expect(screen.getByLabelText(/confirm password/i)).toBeDisabled();
    expect(screen.getByRole('button', { name: /registering/i })).toBeDisabled();
  });

  it('displays API error message', () => {
    vi.mocked(useApiPost).mockReturnValue([
      {
        data: null,
        isLoading: false,
        error: {
          type: ErrorType.SERVER,
          message: 'Server is temporarily unavailable',
        },
      },
      {
        execute: mockExecuteRegister,
        reset: mockResetRegisterApiState,
      },
    ]);

    render(<RegistrationForm />);

    expect(screen.getByText('Registration Error')).toBeInTheDocument();
    expect(
      screen.getByText('Server is temporarily unavailable'),
    ).toBeInTheDocument();
  });

  it('clears API error when inputs change', async () => {
    const user = userEvent.setup();
    vi.mocked(useApiPost).mockReturnValue([
      {
        data: null,
        isLoading: false,
        error: { type: ErrorType.SERVER, message: 'Previous error' },
      },
      {
        execute: mockExecuteRegister,
        reset: mockResetRegisterApiState,
      },
    ]);

    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, '<EMAIL>');

    expect(mockResetRegisterApiState).toHaveBeenCalled();
  });

  it('has proper autocomplete attributes for security', () => {
    render(<RegistrationForm />);

    expect(screen.getByLabelText(/email/i)).toHaveAttribute(
      'autocomplete',
      'email',
    );
    expect(screen.getByLabelText(/^password$/i)).toHaveAttribute(
      'autocomplete',
      'new-password',
    );
    expect(screen.getByLabelText(/confirm password/i)).toHaveAttribute(
      'autocomplete',
      'new-password',
    );
  });

  it('includes login link for existing users', () => {
    render(<RegistrationForm />);

    const loginLink = screen.getByText('Log in');
    expect(loginLink).toBeInTheDocument();
    expect(loginLink.closest('a')).toHaveAttribute('href', '/login');
  });

  it('validates email format (HTML5 validation)', () => {
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    expect(emailInput).toHaveAttribute('type', 'email');
  });

  it('handles unexpected registration response', async () => {
    const user = userEvent.setup();
    mockExecuteRegister.mockResolvedValue(null); // No response data

    render(<RegistrationForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'StrongPass123!');
    await user.type(
      screen.getByLabelText(/confirm password/i),
      'StrongPass123!',
    );
    await user.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: 'default',
        title: 'Registration Processed',
        description:
          'Registration processed, but please verify or try logging in.',
      });
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  it('meets accessibility standards', () => {
    render(<RegistrationForm />);

    // Check form has proper labeling
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password$/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

    expect(emailInput).toHaveAttribute('id', 'email');
    expect(passwordInput).toHaveAttribute('id', 'password');
    expect(confirmPasswordInput).toHaveAttribute('id', 'confirmPassword');

    // Check form can be navigated via keyboard
    expect(emailInput).toHaveAttribute('autofocus');

    // Check submit button is properly formed
    const submitButton = screen.getByRole('button', { name: /register/i });
    expect(submitButton).toHaveAttribute('type', 'submit');
  });

  it('prevents form submission with Enter key when invalid', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    emailInput.focus();
    await user.keyboard('{Enter}');

    // Should trigger validation error
    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Please enter your email.',
    });
  });

  it('trims email input before validation', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);

    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, '   '); // Only spaces

    const submitButton = screen.getByRole('button', { name: /register/i });
    await user.click(submitButton);

    expect(mockToast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Validation Error',
      description: 'Please enter your email.',
    });
  });
});
