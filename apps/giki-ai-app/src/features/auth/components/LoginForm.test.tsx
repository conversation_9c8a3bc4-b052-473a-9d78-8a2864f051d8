/**
 * LoginForm Component Tests
 * Critical security component tests for financial-grade authentication
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test-utils';
import LoginForm from './LoginForm';
import useAuthStore from '@/shared/services/auth/authStore';

// Mock the auth store - this is allowed because auth services have been tested
vi.mock('@/shared/services/auth/authStore');

// Mock useNavigate - this is allowed as it's a tested external library
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('LoginForm - Financial Security Component', () => {
  const mockLogin = vi.fn();
  const mockStore = {
    login: mockLogin,
    isLoading: false,
    error: null,
    user: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useAuthStore).mockReturnValue(mockStore);
  });

  it('renders login form with required fields', () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /sign in/i }),
    ).toBeInTheDocument();
    expect(screen.getByLabelText(/remember me/i)).toBeInTheDocument();
  });

  it('validates email format before submission', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Test invalid email
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(/please enter a valid email/i),
      ).toBeInTheDocument();
    });
  });

  it('validates password requirement', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Valid email but no password
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid credentials', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('disables submit during loading', async () => {
    vi.mocked(useAuthStore).mockReturnValue({
      ...mockStore,
      isLoading: true,
    });

    render(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /signing in/i });
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(submitButton).toBeDisabled();
    expect(emailInput).toBeDisabled();
    expect(passwordInput).toBeDisabled();
  });

  it('shows password toggle functionality', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const passwordInput = screen.getByLabelText(/password/i);

    // Password should be hidden by default
    expect(passwordInput).toHaveAttribute('type', 'password');

    // Look for show/hide password button if it exists
    const toggleButton = screen.queryByRole('button', {
      name: /show password/i,
    });
    if (toggleButton) {
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
    }
  });

  it('handles remember me functionality', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const rememberMeCheckbox = screen.getByLabelText(/remember me/i);

    // Initially unchecked
    expect(rememberMeCheckbox).not.toBeChecked();

    // Click to check
    await user.click(rememberMeCheckbox);
    expect(rememberMeCheckbox).toBeChecked();
  });

  it('includes register link for new users', () => {
    render(<LoginForm />);

    const registerLink = screen.getByText(/create an account/i);
    expect(registerLink).toBeInTheDocument();
    expect(registerLink.closest('a')).toHaveAttribute('href', '/register');
  });

  it('displays error messages from store', () => {
    vi.mocked(useAuthStore).mockReturnValue({
      ...mockStore,
      error: 'Invalid email or password',
    });

    render(<LoginForm />);

    expect(screen.getByText(/invalid email or password/i)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('navigates to dashboard after successful login', async () => {
    mockLogin.mockResolvedValueOnce(undefined);
    const user = userEvent.setup();
    render(<LoginForm />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('meets accessibility standards', () => {
    render(<LoginForm />);

    // Check form has proper labeling
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('required');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(passwordInput).toHaveAttribute('required');

    // Check form can be navigated via keyboard
    expect(emailInput).toHaveAttribute('tabIndex');
    expect(passwordInput).toHaveAttribute('tabIndex');
  });

  it('validates form fields are required', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Try to submit without filling fields
    await user.click(submitButton);

    // Check for validation messages
    await waitFor(() => {
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);

      // Fields should be marked as required
      expect(emailInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
    });
  });
});
