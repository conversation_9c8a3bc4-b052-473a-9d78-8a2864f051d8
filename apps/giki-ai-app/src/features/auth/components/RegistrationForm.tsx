import React, { useState, useEffect } from 'react';
import useAuthStore from '@/shared/services/auth/authStore'; // Keep for authError and clearing it
import { useNavigate } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading';
import {
  validatePassword,
  PASSWORD_ERROR_MESSAGES,
} from '@/shared/types/errors';
import { handleApiError, ErrorType } from '@/shared/utils/errorHandling';
import { Input } from '@/shared/components/ui/input';
import { Button } from '@/shared/components/ui/button';
import { Label } from '@/shared/components/ui/label';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { useApiPost } from '@/shared/hooks/useApi'; // Import useApiPost
import { useToast } from '@/shared/components/ui/use-toast';

// Define interfaces for registration data and response
interface RegistrationRequestData {
  email: string;
  password: string;
  // tenant_name?: string; // Example: if your API needs this
  // Add other fields as required by your backend registration endpoint
}

interface RegistrationResponseData {
  // Adjust based on your actual API response
  id?: string;
  email?: string;
  message?: string; // e.g., "User registered successfully. Please check your email for verification."
  // If tokens are returned directly (less common for registration), define them here.
}

const RegistrationForm: React.FC = () => {
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  // const [formError, setFormError] = useState<string | null>(null); // Replaced by apiError from hook
  // const [isSubmitting, setIsSubmitting] = useState(false); // Replaced by apiIsLoading from hook
  const { error: authStoreErrorHook } = useAuthStore(); // Only need error from store
  const navigate = useNavigate();

  const [
    { isLoading: apiIsLoading, error: apiError },
    { execute: executeRegister, reset: resetRegisterApiState },
  ] = useApiPost<RegistrationResponseData, RegistrationRequestData>(
    '/auth/register',
    { requiresAuth: false },
  );

  // Clear API error when inputs change
  useEffect(() => {
    if (apiError) {
      resetRegisterApiState();
    }
  }, [email, password, confirmPassword, apiError, resetRegisterApiState]);

  // Clear auth store error when component mounts or it changes
  useEffect(() => {
    if (authStoreErrorHook) {
      useAuthStore.setState({ error: null, errorDetails: null });
    }
  }, [authStoreErrorHook]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Form validation
    if (!email.trim()) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: 'Please enter your email.',
      });
      return;
    }

    if (!password) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: 'Please enter a password.',
      });
      return;
    }

    const passwordValidationError = validatePassword(password);
    if (passwordValidationError) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: passwordValidationError.message,
      });
      return;
    }

    if (password !== confirmPassword) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: PASSWORD_ERROR_MESSAGES.PASSWORDS_DONT_MATCH,
      });
      return;
    }

    // Clear previous auth store error if any
    useAuthStore.setState({ error: null, errorDetails: null });
    resetRegisterApiState(); // Clear previous API state

    try {
      const response = await executeRegister({
        email: email,
        password: password,
        // tenant_name: "Default Tenant" // Example: if your API needs this
      });

      if (response && response.data) {
        toast({
          title: 'Registration Successful',
          description:
            response?.data?.message ||
            'You have been registered. Please log in.',
        });
        navigate('/login'); // Navigate to login page on successful registration
      } else {
        // This case might occur if API returns 2xx but no data, or unexpected structure
        // Registration API returned success but no data or unexpected structure
        toast({
          variant: 'default', // Or "warning" if you have one
          title: 'Registration Processed',
          description:
            'Registration processed, but please verify or try logging in.',
        });
        navigate('/login');
      }
    } catch (error) {
      // Use proper error handling utilities
      const apiError = handleApiError(error, {
        showToast: false, // Handle manually for custom messaging
        context: 'RegistrationForm.handleSubmit',
      });

      let specificError = apiError.message;

      // Customize message based on error details
      if (
        apiError.statusCode === 409 ||
        (apiError.details &&
          Object.values(apiError.details).some((messages) =>
            messages.some((msg) => msg.includes('already exists')),
          ))
      ) {
        specificError =
          'An account with this email already exists. Please use a different email or try logging in.';
      } else if (apiError.type === ErrorType.NETWORK) {
        specificError =
          'Cannot connect to the server. Please check your network or contact support.';
      } else if (
        apiError.statusCode === 400 &&
        apiError.details &&
        Object.values(apiError.details).some((messages) =>
          messages.some((msg) => msg.toLowerCase().includes('password')),
        )
      ) {
        specificError = 'Password validation failed on the server.';
      }

      toast({
        variant: 'destructive',
        title: 'Registration Failed',
        description: specificError,
      });
    }
  };

  return (
    <form
      onSubmit={(e) => void handleSubmit(e)}
      className="mt-4 flex flex-wrap flex-col gap-4"
    >
      {/* Display API error from the hook */}
      {apiError && (
        <Alert variant="destructive" className="animate-fadeIn">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Registration Error</AlertTitle>
          <AlertDescription>
            {apiError.message || 'An error occurred.'}
          </AlertDescription>
        </Alert>
      )}

      {/* Display auth store error (e.g., if token refresh fails elsewhere) */}
      {authStoreErrorHook &&
        !apiError && ( // Show authStore error only if no specific API error for this form
          <Alert variant="destructive" className="animate-fadeIn">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Issue</AlertTitle>
            <AlertDescription>{authStoreErrorHook}</AlertDescription>
          </Alert>
        )}

      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium text-primary">
          Email
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setEmail(e?.target?.value)
          }
          placeholder="<EMAIL>"
          disabled={apiIsLoading}
          autoComplete="email"
          autoFocus
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-sm font-medium text-primary">
          Password
        </Label>
        <div className="text-sm text-[hsl(var(--giki-text-muted))] mb-1">
          Password must be at least 8 characters and include uppercase,
          lowercase, number, and special character.
        </div>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setPassword(e?.target?.value)
          }
          placeholder="••••••••"
          disabled={apiIsLoading}
          autoComplete="new-password"
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="confirmPassword"
          className="text-sm font-medium text-primary"
        >
          Confirm Password
        </Label>
        <Input
          id="confirmPassword"
          type="password"
          value={confirmPassword}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setConfirmPassword(e?.target?.value)
          }
          placeholder="••••••••"
          disabled={apiIsLoading}
          autoComplete="new-password"
          className="w-full"
        />
      </div>

      <div className="pt-4">
        <Button type="submit" disabled={apiIsLoading} className="w-full">
          {apiIsLoading ? (
            <Loading size="sm" text="Registering..." className="py-0" />
          ) : (
            'Register'
          )}
        </Button>
      </div>

      <div className="mt-4 text-center text-sm text-[hsl(var(--giki-text-muted))]">
        <p>
          Already have an account?{' '}
          <a href="/login" className="text-primary hover:underline">
            Log in
          </a>
        </p>
      </div>
    </form>
  );
};

export default RegistrationForm;
