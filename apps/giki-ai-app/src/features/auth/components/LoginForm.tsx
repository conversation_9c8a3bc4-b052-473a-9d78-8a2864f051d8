import React, { useState, useEffect } from 'react';
import useAuthStore from '@/shared/services/auth/authStore';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle, Eye, EyeOff, Shield, Lock } from 'lucide-react';
import { Input } from '@/shared/components/ui/input';
import { Button } from '@/shared/components/ui/button';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Label } from '@/shared/components/ui/label';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { useToast } from '@/shared/components/ui/use-toast';
import { Loading } from '@/shared/components/ui/loading';
import { handleApiError, ErrorType } from '@/shared/utils/errorHandling';
import { login } from '../services/authService';

// Interface for the raw token response from the backend (used below)

// Password strength checker
const getPasswordStrength = (
  password: string,
): 'weak' | 'medium' | 'strong' => {
  if (password.length < 6) return 'weak';

  let score = 0;
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;

  if (score >= 4) return 'strong';
  if (score >= 2) return 'medium';
  return 'weak';
};

const LoginForm: React.FC = () => {
  const { toast } = useToast();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Get location to redirect back after login
  const location = useLocation();
  const from =
    (location.state as { from?: { pathname?: string } })?.from?.pathname ||
    '/dashboard';

  // authStore actions/state needed for updating store after successful API call
  const {
    login: storeLoginMethod,
    error: authStoreErrorHook,
    isLoading: authStoreIsLoading,
  } = useAuthStore();
  const navigate = useNavigate();

  // Note: Using optimized authService.login directly instead of useApi hook
  // This provides better error handling and automatic fallback to standard endpoint

  // Clear authentication state when inputs change
  useEffect(() => {
    if (isAuthenticating) {
      setIsAuthenticating(false);
    }
  }, [username, password, isAuthenticating]);

  // Clear auth store error when component mounts or it changes
  useEffect(() => {
    if (authStoreErrorHook) {
      useAuthStore.setState({ error: null, errorDetails: null });
    }
  }, [authStoreErrorHook]);

  // Check if there's a saved username in localStorage
  useEffect(() => {
    const savedUsername = localStorage.getItem('giki_username');
    if (savedUsername) {
      setUsername(savedUsername);
      setRememberMe(true);
    }
  }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Form validation (client-side)
    if (!username.trim()) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: 'Please enter your username or email.',
      });
      return;
    }

    if (!password) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: 'Please enter your password.',
      });
      return;
    }

    // Clear previous auth store error if any
    useAuthStore.setState({ error: null, errorDetails: null });
    setIsAuthenticating(true);

    try {
      // Use optimized authentication service
      const result = await login({ username, password });

      // Update authStore state.
      // The login method in authStore will handle setting tokens in localStorage and parsing the JWT.
      await storeLoginMethod({
        accessToken: result.accessToken,
        refreshToken: result.refreshToken || null,
      });

      if (rememberMe) {
        localStorage.setItem('giki_username', username);
      } else {
        localStorage.removeItem('giki_username');
      }
      navigate(from, { replace: true });
    } catch (error) {
      setIsAuthenticating(false);

      // Use proper error handling utilities
      const apiError = handleApiError(error, {
        showToast: false, // Handle toast manually for custom messaging
        context: 'LoginForm.handleSubmit',
      });

      let specificError = apiError.message;

      // Customize message based on error details
      if (apiError.statusCode === 401) {
        specificError = 'Invalid email or password. Please try again.';
      } else if (apiError.type === ErrorType.NETWORK) {
        specificError =
          'Cannot connect to the server. Please check your network or contact support.';
      } else if (apiError.statusCode === 403) {
        specificError =
          'Your account is inactive or locked. Please contact your administrator.';
      }

      toast({
        variant: 'destructive',
        title: 'Login Failed',
        description: specificError,
      });
    }
  };

  const isLoading = isAuthenticating || authStoreIsLoading;

  return (
    <div className="space-y-6">
      {/* Security Trust Indicator */}
      <div className="flex flex-wrap items-center justify-center gap-2 p-3 bg-success/10 border border-success/20 rounded-lg">
        <Shield className="h-4 w-4 text-success" />
        <span className="text-sm text-success">
          Secure connection • Data encrypted
        </span>
        <Lock className="h-4 w-4 text-success" />
      </div>

      <form
        onSubmit={(e) => void handleSubmit(e)}
        className="flex flex-wrap flex-col gap-4"
      >
        {/* Display auth store error */}
        {authStoreErrorHook && (
          <Alert variant="destructive" className="animate-fadeIn">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Issue</AlertTitle>
            <AlertDescription>{authStoreErrorHook}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-3">
          <Label
            htmlFor="username"
            className="text-sm font-medium text-foreground"
          >
            Email
          </Label>
          <Input
            id="username"
            type="email"
            value={username}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setUsername(e?.target?.value)
            }
            placeholder="<EMAIL>"
            disabled={isLoading}
            autoComplete="email"
            autoFocus={!username}
            className="w-full h-12 px-4 py-3 text-sm rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 hover:border-border/80"
          />
        </div>

        <div className="space-y-3">
          <div className="flex flex-wrap items-center justify-between">
            <Label
              htmlFor="password"
              className="text-sm font-medium text-foreground"
            >
              Password
            </Label>
            <a
              href="#"
              className="text-sm text-muted-foreground hover:text-foreground hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 rounded"
            >
              Forgot password?
            </a>
          </div>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setPassword(e?.target?.value)
              }
              placeholder="••••••••"
              disabled={isLoading}
              autoComplete="current-password"
              className="w-full h-12 px-4 py-3 text-sm rounded-lg border border-border bg-background pr-12 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 hover:border-border/80"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-10 w-10 p-0 hover:bg-accent rounded-md transition-colors"
              onClick={() => setShowPassword(!showPassword)}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>

          {/* Password strength indicator */}
          {password && (
            <div className="mt-2 space-y-1">
              <div className="flex flex-wrap items-center justify-between">
                <span className="text-xs text-muted-foreground">
                  Password strength
                </span>
                <span
                  className={`text-xs font-medium ${
                    getPasswordStrength(password) === 'strong'
                      ? 'text-success'
                      : getPasswordStrength(password) === 'medium'
                        ? 'text-warning'
                        : 'text-destructive'
                  }`}
                >
                  {getPasswordStrength(password) === 'strong'
                    ? 'Strong'
                    : getPasswordStrength(password) === 'medium'
                      ? 'Medium'
                      : 'Weak'}
                </span>
              </div>
              <div className="h-1 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className={`h-full transition-all duration-300 ${
                    getPasswordStrength(password) === 'strong'
                      ? 'w-full bg-success dark:bg-success'
                      : getPasswordStrength(password) === 'medium'
                        ? 'w-2/3 bg-warning dark:bg-warning'
                        : 'w-1/3 bg-destructive'
                  }`}
                />
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-wrap items-center gap-3 mt-4">
          <Checkbox
            id="remember-me"
            checked={rememberMe}
            onCheckedChange={(checked) => setRememberMe(checked === true)}
            disabled={isLoading}
            className="h-5 w-5 border-2 border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary focus:ring-2 focus:ring-primary/20"
          />
          <Label
            htmlFor="remember-me"
            className="text-sm text-muted-foreground cursor-pointer select-none"
          >
            Remember me
          </Label>
        </div>

        <div className="pt-8">
          <Button
            type="submit"
            disabled={isLoading}
            variant="default"
            className="w-full h-12 text-base font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
          >
            {isLoading ? (
              <Loading size="sm" text="Authenticating..." className="py-0" />
            ) : (
              'Sign In'
            )}
          </Button>
        </div>

        <div className="mt-6 text-center text-sm">
          <p>
            Having trouble signing in?{' '}
            <a
              href="#"
              className="text-muted-foreground hover:text-foreground hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 rounded"
            >
              Contact support
            </a>
          </p>

          {/* Legal compliance links */}
          <div className="mt-4 flex flex-wrap justify-center gap-4 text-xs text-muted-foreground">
            <a
              href="#"
              className="hover:text-muted-foreground hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 rounded"
            >
              Terms of Service
            </a>
            <span>•</span>
            <a
              href="#"
              className="hover:text-muted-foreground hover:underline transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 rounded"
            >
              Privacy Policy
            </a>
          </div>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
