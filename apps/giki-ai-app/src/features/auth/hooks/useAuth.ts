/**
 * Auth Hook
 *
 * Custom hook for managing authentication state and operations.
 */

import { useCallback } from 'react';
import useAuthStore from '@/shared/services/auth/authStore';
import { User, LoginCredentials, RegisterCredentials } from '../types/auth';

export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => void;
  resetPassword: (email: string) => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const {
    isAuthenticated,
    isLoading,
    error,
    userId,
    tenantId,
    token,
    logout: storeLogout,
    checkAuth: storeCheckAuth,
  } = useAuthStore();

  // Create a mock user object from the store data
  const user: User | null = userId
    ? {
        id: userId,
        email: '', // We don't store email in the auth store currently
        name: 'User',
        tenantId: tenantId || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    : null;

  const login = useCallback((_credentials: LoginCredentials): Promise<void> => {
    // This is handled by the LoginForm component directly
    // The useAuth hook doesn't need to implement login since it's done in the component
    return Promise.reject(
      new Error('Login should be handled by LoginForm component'),
    );
  }, []);

  const register = useCallback(
    (_credentials: RegisterCredentials): Promise<void> => {
      // TODO: Implement when needed
      return Promise.reject(new Error('Register not implemented yet'));
    },
    [],
  );

  const logout = useCallback((): Promise<void> => {
    storeLogout();
    return Promise.resolve();
  }, [storeLogout]);

  const checkAuth = useCallback((): void => {
    storeCheckAuth();
  }, [storeCheckAuth]);

  const resetPassword = useCallback((email: string): Promise<void> => {
    // TODO: Implement password reset
    console.log('Password reset requested for:', email);
    return Promise.resolve();
  }, []);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    token,
    login,
    register,
    logout,
    checkAuth,
    resetPassword,
  };
};
