import React, { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Badge } from '@/shared/components/ui/badge';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  Download,
  RotateCcw,
  Settings,
  Table as TableIcon,
  X,
  Calculator,
  BarChart3,
} from 'lucide-react';

// Simplified interfaces without drag and drop
interface PivotField {
  id: string;
  name: string;
  type: 'string' | 'number' | 'date';
  category?: 'dimension' | 'metric';
}

interface AggregatedField extends PivotField {
  aggregation: 'sum' | 'avg' | 'count' | 'min' | 'max';
}

interface PivotConfiguration {
  id: string;
  name: string;
  rows: string[];
  columns: string[];
  values: AggregatedField[];
  filters: Record<string, unknown>;
}

interface PivotTableProps {
  data: Record<string, unknown>[];
  availableFields: PivotField[];
  initialConfiguration?: PivotConfiguration;
  onConfigurationChange?: (config: PivotConfiguration) => void;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  savedConfigurations?: PivotConfiguration[];
}

// Simplified Field Component without drag and drop
const SimpleField: React.FC<{
  field: PivotField | AggregatedField;
  onRemove?: () => void;
  showAggregation?: boolean;
  onAggregationChange?: (agg: string) => void;
}> = ({ field, onRemove, showAggregation, onAggregationChange }) => {
  const aggregatedField = field as AggregatedField;

  return (
    <div className="flex flex-wrap items-center gap-2 px-3 py-2 bg-card border rounded-lg">
      <div className="flex flex-wrap-1">
        <span className="truncatelabel">{field.name}</span>
        {showAggregation && aggregatedField.aggregation && (
          <Select
            value={aggregatedField.aggregation}
            onValueChange={onAggregationChange}
          >
            <SelectTrigger className="h-6 truncate text-caption ml-2 w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sum">Sum</SelectItem>
              <SelectItem value="avg">Avg</SelectItem>
              <SelectItem value="count">Count</SelectItem>
              <SelectItem value="min">Min</SelectItem>
              <SelectItem value="max">Max</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>
      {onRemove && (
        <Button
          className="max-w-full h-6 w-6 p-0"
          variant="ghost"
          size="sm"
          onClick={() => void onRemove()}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

// Simplified Drop Zone Component
const SimpleDropZone: React.FC<{
  title: string;
  fields: Record<string, unknown>[];
  acceptsType?: string;
  onFieldAdd: (field: PivotField) => void;
  onFieldRemove: (index: number) => void;
  showAggregation?: boolean;
  onAggregationChange?: (index: number, agg: string) => void;
  icon?: React.ReactNode;
}> = ({
  title,
  fields,
  onFieldAdd: _onFieldAdd,
  onFieldRemove,
  showAggregation,
  onAggregationChange,
  icon,
}) => {
  return (
    <Card className="h-40">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex flex-wrap items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 overflow-hidden">
        <div className="min-h-[80px] border-2 border-dashed rounded-lg p-2 space-y-2">
          {fields.length === 0 ? (
            <div className="flex flex-wrap items-center justify-center h-16 truncate text-muted text-caption">
              Drop fields here
            </div>
          ) : (
            fields.map((field, index) => (
              <SimpleField
                key={field.id || index}
                field={field}
                onRemove={() => onFieldRemove(index)}
                showAggregation={showAggregation}
                onAggregationChange={(agg) => onAggregationChange?.(index, agg)}
              />
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const PivotTable: React.FC<PivotTableProps> = ({
  data,
  availableFields,
  initialConfiguration,
  onConfigurationChange,
  onExport,
  savedConfigurations: _savedConfigurations = [],
}) => {
  const [configuration, setConfiguration] = useState<PivotConfiguration>(
    initialConfiguration || {
      id: 'default',
      name: 'New Pivot Table',
      rows: [],
      columns: [],
      values: [],
      filters: {},
    },
  );

  const [showSettings, setShowSettings] = useState(false);

  // Simplified handlers
  const handleFieldAdd = (
    field: PivotField,
    area: 'rows' | 'columns' | 'values',
  ) => {
    const newConfig = { ...configuration };
    if (area === 'values') {
      const aggregatedField: AggregatedField = {
        ...field,
        aggregation: field.type === 'number' ? 'sum' : 'count',
      };
      newConfig.values = [...newConfig.values, aggregatedField];
    } else {
      newConfig[area] = [...newConfig[area], field.id];
    }
    setConfiguration(newConfig);
    onConfigurationChange?.(newConfig);
  };

  const handleFieldRemove = (
    area: 'rows' | 'columns' | 'values',
    index: number,
  ) => {
    const newConfig = { ...configuration };
    if (area === 'values') {
      newConfig.values = newConfig?.values?.filter((_, i) => i !== index);
    } else {
      newConfig[area] = newConfig[area].filter((_, i) => i !== index);
    }
    setConfiguration(newConfig);
    onConfigurationChange?.(newConfig);
  };

  // Simplified pivot calculation
  const pivotData = useMemo(() => {
    if (!data.length || !configuration?.values?.length) {
      return [];
    }

    // Basic pivot table calculation - simplified version
    return data.slice(0, 10); // Simplified to just show first 10 rows
  }, [data, configuration]);

  return (
    <div className="space-y-6">
      {/* Configuration Panel */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap items-center justify-between">
            <div>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <TableIcon className="h-5 w-5" />
                Pivot Table Builder
              </CardTitle>
              <CardDescription>
                Configure your pivot table by selecting fields (simplified
                version)
              </CardDescription>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                className="max-w-full"
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                className="max-w-full"
                variant="outline"
                size="sm"
                onClick={() =>
                  setConfiguration({
                    id: 'default',
                    name: 'New Pivot Table',
                    rows: [],
                    columns: [],
                    values: [],
                    filters: {},
                  })
                }
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Available Fields */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="truncatelabel">
                  Available Fields
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 overflow-hidden">
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {availableFields.map((field) => (
                      <div
                        key={field.id}
                        className="flex flex-wrap items-center justify-between p-2 bg-muted rounded-lg cursor-pointer hover:bg-muted/80"
                        onClick={() => {
                          // Simplified: add to values by default
                          handleFieldAdd(field, 'values');
                        }}
                      >
                        <span className="text-sm">{field.name}</span>
                        <Badge
                          variant="outline"
                          className="truncate text-caption max-w-[150px] truncate"
                        >
                          {field.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Configuration Areas */}
            <SimpleDropZone
              title="Rows"
              fields={configuration.rows
                .map((id) => availableFields.find((f) => f.id === id))
                .filter(Boolean)}
              onFieldAdd={(field) => handleFieldAdd(field, 'rows')}
              onFieldRemove={(index) => handleFieldRemove('rows', index)}
              icon={<BarChart3 className="h-4 w-4" />}
            />

            <SimpleDropZone
              title="Columns"
              fields={configuration.columns
                .map((id) => availableFields.find((f) => f.id === id))
                .filter(Boolean)}
              onFieldAdd={(field) => handleFieldAdd(field, 'columns')}
              onFieldRemove={(index) => handleFieldRemove('columns', index)}
              icon={<BarChart3 className="h-4 w-4 rotate-90" />}
            />

            <SimpleDropZone
              title="Values"
              fields={configuration.values}
              onFieldAdd={(field) => handleFieldAdd(field, 'values')}
              onFieldRemove={(index) => handleFieldRemove('values', index)}
              showAggregation={true}
              onAggregationChange={(index, agg) => {
                const newValues = [...configuration.values];
                newValues[index].aggregation = agg as
                  | 'sum'
                  | 'avg'
                  | 'count'
                  | 'min'
                  | 'max';
                setConfiguration({ ...configuration, values: newValues });
              }}
              icon={<Calculator className="h-4 w-4" />}
            />
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap items-center justify-between">
            <CardTitle>Pivot Table Results</CardTitle>
            <div className="flex flex-wrap gap-2">
              {onExport && (
                <Button
                  className="max-w-full"
                  variant="outline"
                  size="sm"
                  onClick={() => onExport('csv')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  {configuration?.rows?.map((rowId) => (
                    <TableHead key={rowId}>
                      {availableFields.find((f) => f.id === rowId)?.name ||
                        rowId}
                    </TableHead>
                  ))}
                  {configuration?.values?.map((value, index) => (
                    <TableHead key={index}>
                      {value.name} ({value.aggregation})
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {pivotData.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={
                        configuration?.rows?.length +
                        configuration?.values?.length
                      }
                      className="text-center text-[hsl(var(--giki-text-muted))]"
                    >
                      Configure fields to generate pivot table
                    </TableCell>
                  </TableRow>
                ) : (
                  pivotData.map((row, index) => (
                    <TableRow key={index}>
                      {configuration?.rows?.map((rowId) => (
                        <TableCell key={rowId}>{row[rowId] || '-'}</TableCell>
                      ))}
                      {configuration?.values?.map((value, valueIndex) => (
                        <TableCell key={valueIndex}>
                          {row[value.id] || 0}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PivotTable;
