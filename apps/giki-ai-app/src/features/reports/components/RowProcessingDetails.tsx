import React, { useState } from 'react';
import {
  <PERSON><PERSON>ronDown,
  ChevronRight,
  CheckCircle,
  XCircle,
  <PERSON>ert<PERSON><PERSON>gle,
  <PERSON><PERSON>,
  <PERSON>,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Alert,
  AlertDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  ScrollArea,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/ui';
import ExcelTable from '@/shared/components/ui/excel-table';

export interface RowDetail {
  row_number: number;
  original_data: Record<string, unknown>;
  processed_data: Record<string, unknown>;
  processing_status: 'success' | 'failed' | 'skipped';
  error_message?: string | null;
  validation_errors?: Record<string, unknown> | null;
  created_at?: string;
}

interface RowProcessingDetailsProps {
  rows: RowDetail[];
  title?: string;
  description?: string;
  variant?: 'table' | 'cards' | 'expandable';
  showOriginalData?: boolean;
  showProcessedData?: boolean;
  className?: string;
  onPageChange?: (page: number) => void;
  currentPage?: number;
  totalPages?: number;
  totalRows?: number;
}

export function RowProcessingDetails({
  rows,
  title = 'Row Processing Details',
  description = 'Detailed information about each processed row',
  variant = 'table',
  showOriginalData = true,
  showProcessedData = true,
  className = '',
  onPageChange,
  currentPage = 1,
  totalPages = 1,
  totalRows = rows.length,
}: RowProcessingDetailsProps) {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 truncategreen-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 truncatered-500" />;
      case 'skipped':
        return <AlertTriangle className="h-4 w-4 truncateyellow-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
      success: 'default',
      failed: 'destructive',
      skipped: 'secondary',
    };

    return (
      <Badge
        variant={variants[status] || 'secondary'}
        className="max-w-[150px] truncate"
      >
        <span className="flex flex-wrap items-center gap-1">
          {getStatusIcon(status)}
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </Badge>
    );
  };

  const copyToClipboard = (data: unknown) => {
    void navigator.clipboard.writeText(JSON.stringify(data, null, 2));
  };

  const toggleRowExpansion = (rowNumber: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowNumber)) {
      newExpanded.delete(rowNumber);
    } else {
      newExpanded.add(rowNumber);
    }
    setExpandedRows(newExpanded);
  };

  // Table variant
  if (variant === 'table') {
    // Prepare data for ExcelTable
    const tableData = rows.map((row) => {
      const baseData: Record<string, unknown> = {
        'Row #': row.row_number,
        Status: row.processing_status,
        Error: row.error_message || 'None',
      };

      // Add processed data columns if available
      if (showProcessedData && row.processed_data) {
        Object.entries(row.processed_data).forEach(([key, value]) => {
          baseData[key] = value;
        });
      }

      return baseData;
    });

    // Build columns dynamically
    const columns: Array<{ key: string; label: string; type: string }> = [
      {
        key: 'Row #',
        label: 'Row #',
        type: 'number',
      },
      {
        key: 'Status',
        label: 'Status',
        type: 'status',
        renderCell: (value: string) => getStatusBadge(value),
      },
      {
        key: 'Error',
        label: 'Error',
        type: 'text',
        renderCell: (value: string) => (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="truncate text-caption truncate max-w-[200px] block">
                  {value}
                </span>
              </TooltipTrigger>
              {value !== 'None' && (
                <TooltipContent>
                  <p className="max-w-xs">{value}</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        ),
      },
    ];

    // Add dynamic columns from processed data
    if (showProcessedData && rows.length > 0 && rows[0].processed_data) {
      Object.keys(rows[0].processed_data).forEach((key) => {
        columns.push({
          key,
          label: key,
          type: 'text',
        });
      });
    }

    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <ExcelTable
            data={tableData}
            columns={columns}
            enableSorting
            enableFiltering
            enableColumnResize
          />

          {onPageChange && totalPages > 1 && (
            <div className="flex flex-wrap items-center justify-between mt-4">
              <div className="truncatebody-small">
                Showing {rows.length} of {totalRows} rows
              </div>
              <div className="flex flex-wrap gap-2">
                <Button
                  className="max-w-full"
                  size="sm"
                  variant="outline"
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex flex-wrap items-center px-3 text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  className="max-w-full"
                  size="sm"
                  variant="outline"
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Expandable variant
  if (variant === 'expandable') {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="space-y-2">
            {rows.map((row) => (
              <Collapsible
                key={row.row_number}
                open={expandedRows.has(row.row_number)}
                onOpenChange={() => toggleRowExpansion(row.row_number)}
              >
                <div className="border rounded-lg">
                  <CollapsibleTrigger asChild>
                    <Button
                      className="max-w-full w-full justify-between p-4 hover:bg-muted/50"
                      variant="ghost"
                    >
                      <div className="flex flex-wrap items-center gap-4">
                        {expandedRows.has(row.row_number) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        <span className="font-medium">
                          Row #{row.row_number}
                        </span>
                        {getStatusBadge(row.processing_status)}
                      </div>
                      {row.error_message && (
                        <span className="truncate text-caption text-destructive truncate max-w-[300px]">
                          {row.error_message}
                        </span>
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="p-4 pt-0">
                      <Tabs defaultValue="processed" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="processed">
                            Processed Data
                          </TabsTrigger>
                          <TabsTrigger value="original">
                            Original Data
                          </TabsTrigger>
                          <TabsTrigger value="errors">
                            Errors & Validation
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="processed" className="space-y-2">
                          <div className="flex flex-wrap justify-between items-center mb-2">
                            <h4 className="truncatelabel">Processed Data</h4>
                            <Button
                              className="max-w-full"
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                copyToClipboard(row.processed_data)
                              }
                            >
                              <Copy className="h-3 w-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <ScrollArea className="h-[200px] w-full rounded border p-3">
                            <pre className="truncate text-caption">
                              {JSON.stringify(row.processed_data, null, 2)}
                            </pre>
                          </ScrollArea>
                        </TabsContent>

                        <TabsContent value="original" className="space-y-2">
                          <div className="flex flex-wrap justify-between items-center mb-2">
                            <h4 className="truncatelabel">Original Data</h4>
                            <Button
                              className="max-w-full"
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(row.original_data)}
                            >
                              <Copy className="h-3 w-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <ScrollArea className="h-[200px] w-full rounded border p-3">
                            <pre className="truncate text-caption">
                              {JSON.stringify(row.original_data, null, 2)}
                            </pre>
                          </ScrollArea>
                        </TabsContent>

                        <TabsContent value="errors" className="space-y-2">
                          {row.error_message ? (
                            <Alert variant="destructive">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertDescription>
                                <strong>Processing Error:</strong>{' '}
                                {row.error_message}
                              </AlertDescription>
                            </Alert>
                          ) : (
                            <Alert>
                              <CheckCircle className="h-4 w-4" />
                              <AlertDescription>
                                No processing errors for this row.
                              </AlertDescription>
                            </Alert>
                          )}

                          {row.validation_errors &&
                            Object.keys(row.validation_errors).length > 0 && (
                              <div className="mt-4">
                                <h4 className="truncatelabel mb-2">
                                  Validation Errors
                                </h4>
                                <ScrollArea className="h-[150px] w-full rounded border p-3">
                                  <pre className="truncate text-caption">
                                    {JSON.stringify(
                                      row.validation_errors,
                                      null,
                                      2,
                                    )}
                                  </pre>
                                </ScrollArea>
                              </div>
                            )}
                        </TabsContent>
                      </Tabs>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ))}
          </div>

          {onPageChange && totalPages > 1 && (
            <div className="flex flex-wrap items-center justify-between mt-4">
              <div className="truncatebody-small">
                Showing {rows.length} of {totalRows} rows
              </div>
              <div className="flex flex-wrap gap-2">
                <Button
                  className="max-w-full"
                  size="sm"
                  variant="outline"
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="flex flex-wrap items-center px-3 text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  className="max-w-full"
                  size="sm"
                  variant="outline"
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Cards variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {rows.map((row) => (
            <Card key={row.row_number} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex flex-wrap items-center justify-between">
                  <CardTitle className="truncatelabel">
                    Row #{row.row_number}
                  </CardTitle>
                  {getStatusBadge(row.processing_status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-3 overflow-hidden">
                {row.error_message && (
                  <Alert variant="destructive" className="py-2">
                    <AlertDescription className="truncate text-caption">
                      {row.error_message}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  {showProcessedData && (
                    <div>
                      <div className="flex flex-wrap items-center justify-between mb-1">
                        <span className="truncate text-caption font-medium">
                          Processed Data
                        </span>
                        <Button
                          className="max-w-full h-6 px-2"
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(row.processed_data)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="bg-muted rounded p-2 truncate text-caption text-financial overflow-x-auto">
                        {Object.entries(row.processed_data || {})
                          .slice(0, 3)
                          .map(([key, value]) => (
                            <div key={key}>
                              {key}: {JSON.stringify(value)}
                            </div>
                          ))}
                        {Object.keys(row.processed_data || {}).length > 3 && (
                          <div className="text-muted-foreground">
                            ...and {Object.keys(row.processed_data).length - 3}{' '}
                            more fields
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {showOriginalData && (
                    <div>
                      <div className="flex flex-wrap items-center justify-between mb-1">
                        <span className="truncate text-caption font-medium">
                          Original Data
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                className="max-w-full h-6 px-2"
                                size="sm"
                                variant="ghost"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <ScrollArea className="h-[200px] w-[300px]">
                                <pre className="truncate text-caption">
                                  {JSON.stringify(row.original_data, null, 2)}
                                </pre>
                              </ScrollArea>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="bg-muted/50 rounded p-2 truncate text-caption text-financial overflow-x-auto">
                        {Object.entries(row.original_data || {})
                          .slice(0, 2)
                          .map(([key, value]) => (
                            <div key={key} className="truncate">
                              {key}: {JSON.stringify(value)}
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {onPageChange && totalPages > 1 && (
          <div className="flex flex-wrap items-center justify-between mt-4">
            <div className="truncatebody-small">
              Showing {rows.length} of {totalRows} rows
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                className="max-w-full"
                size="sm"
                variant="outline"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="flex flex-wrap items-center px-3 text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="max-w-full"
                size="sm"
                variant="outline"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default RowProcessingDetails;
