'use client';

import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from '@/shared/components/ui/table'; // Assuming Shadcn components are in @/shared/components/ui
import { Badge } from '@/shared/components/ui/badge';

// Define a type for our transaction data
interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: number;
  status: 'Pending' | 'Completed' | 'Failed';
}

// Sample transaction data
const transactions: Transaction[] = [
  {
    id: 'txn_1',
    date: '2024-05-15',
    description: 'Office Supplies Purchase',
    category: 'Office Expenses',
    amount: 125.5,
    status: 'Completed',
  },
  {
    id: 'txn_2',
    date: '2024-05-14',
    description: 'Software Subscription Renewal',
    category: 'Software',
    amount: 49.99,
    status: 'Completed',
  },
  {
    id: 'txn_3',
    date: '2024-05-13',
    description: 'Client Dinner',
    category: 'Meals & Entertainment',
    amount: 85.0,
    status: 'Pending',
  },
  {
    id: 'txn_4',
    date: '2024-05-12',
    description: 'Travel Expenses - Conference',
    category: 'Travel',
    amount: 350.75,
    status: 'Completed',
  },
  {
    id: 'txn_5',
    date: '2024-05-11',
    description: 'Online Advertisement Campaign',
    category: 'Marketing',
    amount: 200.0,
    status: 'Failed',
  },
];

const TransactionListTable: React.FC = () => {
  const totalAmount = transactions.reduce((sum, transaction) => {
    return transaction.status === 'Completed' ? sum + transaction.amount : sum;
  }, 0);

  return (
    <div data-testid="transaction-list-table-container">
      <Table>
        <TableCaption>A list of recent transactions.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[150px] text-primary-text font-semibold">
              Date
            </TableHead>
            <TableHead className="text-primary-text font-semibold">
              Description
            </TableHead>
            <TableHead className="text-primary-text font-semibold">
              Category
            </TableHead>
            <TableHead className="text-primary-text font-semibold">
              Status
            </TableHead>
            <TableHead className="text-right text-primary-text font-semibold">
              Amount
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow
              key={transaction.id}
              data-testid={`transaction-row-${transaction.id}`}
            >
              <TableCell className="font-medium text-primary-text">
                {transaction.date}
              </TableCell>
              <TableCell className="text-primary-text">
                {transaction.description}
              </TableCell>
              <TableCell className="text-primary-text">
                {transaction.category}
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    transaction.status === 'Completed'
                      ? 'default'
                      : transaction.status === 'Pending'
                        ? 'secondary'
                        : 'destructive'
                  }
                  className="max-w-[150px] truncate"
                >
                  {transaction.status}
                </Badge>
              </TableCell>
              <TableCell className="text-right text-primary-text">
                ${transaction?.amount?.toFixed(2)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={4} className="text-primary-text font-semibold">
              Total (Completed)
            </TableCell>
            <TableCell
              className="text-right text-primary-text font-semibold"
              data-testid="total-amount"
            >
              ${totalAmount.toFixed(2)}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default TransactionListTable;
