import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  ArrowRight,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Button,
  Progress,
} from '@/shared/components/ui';
import { formatDateTime } from '@/shared/utils/utils';

export interface ProcessingReportSummary {
  upload_id: string;
  file_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  total_rows: number;
  success_rate: number;
  data_quality_score: number;
  processing_duration_seconds: number | null;
  created_at: string;
}

interface FileProcessingReportCardProps {
  report: ProcessingReportSummary;
  className?: string;
}

export function FileProcessingReportCard({
  report,
  className = '',
}: FileProcessingReportCardProps) {
  const navigate = useNavigate();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 truncategreen-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 truncatered-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 truncateyellow-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      'default' | 'secondary' | 'destructive' | 'outline'
    > = {
      completed: 'default',
      failed: 'destructive',
      processing: 'secondary',
      pending: 'outline',
    };

    return (
      <Badge
        variant={variants[status] || 'outline'}
        className="max-w-[150px] truncate"
      >
        <span className="flex flex-wrap items-center gap-1">
          {getStatusIcon(status)}
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </Badge>
    );
  };

  const getQualityBadge = (score: number) => {
    if (score >= 0.8) {
      return (
        <Badge variant="default" className="max-w-[150px] truncate">
          Good Quality
        </Badge>
      );
    } else if (score >= 0.6) {
      return (
        <Badge variant="secondary" className="max-w-[150px] truncate">
          Fair Quality
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="max-w-[150px] truncate">
          Poor Quality
        </Badge>
      );
    }
  };

  const handleViewDetails = () => {
    navigate(`/reports/processing/${report.upload_id}`);
  };

  return (
    <Card
      className={`hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={handleViewDetails}
    >
      <CardHeader className="pb-4">
        <div className="flex flex-wrap items-start justify-between">
          <div className="flex flex-wrap items-center gap-3">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <div>
              <CardTitle className="text-base font-medium">
                {report.file_name}
              </CardTitle>
              <CardDescription className="truncate text-caption mt-1">
                Uploaded {formatDateTime(report.created_at)}
              </CardDescription>
            </div>
          </div>
          {getStatusBadge(report.status)}
        </div>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="truncate text-caption text-muted-foreground mb-1">
              Success Rate
            </div>
            <div className="space-y-1">
              <div className="flex flex-wrap items-center justify-between">
                <span className="text-card-foreground-title">
                  {report.success_rate.toFixed(1)}%
                </span>
                <span className="truncate text-caption text-muted-foreground">
                  {report.total_rows} rows
                </span>
              </div>
              <Progress value={report.success_rate} className="h-1.5" />
            </div>
          </div>

          <div>
            <div className="truncate text-caption text-muted-foreground mb-1">
              Data Quality
            </div>
            <div className="space-y-1">
              <div className="flex flex-wrap items-center justify-between">
                <span className="text-card-foreground-title">
                  {(report.data_quality_score * 100).toFixed(0)}%
                </span>
                {getQualityBadge(report.data_quality_score)}
              </div>
              <Progress
                value={report.data_quality_score * 100}
                className="h-1.5"
              />
            </div>
          </div>
        </div>

        {report.processing_duration_seconds !== null && (
          <div className="truncate text-caption text-muted-foreground">
            Processing time: {report.processing_duration_seconds.toFixed(2)}s
          </div>
        )}

        <Button
          className="max-w-full w-full justify-between"
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            handleViewDetails();
          }}
        >
          View Detailed Report
          <ArrowRight className="h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  );
}

export default FileProcessingReportCard;
