/**
 * Reports Hook
 *
 * Custom hook for managing report generation and data.
 */

import { useState, useCallback } from 'react';
import { ReportData, ReportConfig } from '../types/reports';
// import { ReportResponse } from '../services/reportService';

export interface UseReportsReturn {
  reports: ReportData[];
  isLoading: boolean;
  error: string | null;
  generateReport: (config: ReportConfig) => Promise<ReportData>;
  deleteReport: (reportId: string) => Promise<void>;
  refreshReports: () => Promise<void>;
}

export const useReports = (): UseReportsReturn => {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateReport = useCallback(
    async (config: ReportConfig): Promise<ReportData> => {
      setIsLoading(true);
      setError(null);

      try {
        // Use real report service
        const { generateReport: apiGenerateReport } = await import(
          '../services/reportService'
        );

        const reportRequest = {
          report_type: config.type as unknown,
          start_date:
            config.filters?.dateRange?.start.toISOString().split('T')[0] ||
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split('T')[0],
          end_date:
            config.filters?.dateRange?.end.toISOString().split('T')[0] ||
            new Date().toISOString().split('T')[0],
          format: 'json' as const,
          filters: config.filters,
        };

        const response = await apiGenerateReport(reportRequest);

        if ('type' in response && 'message' in response) {
          // It's an ApiError
          throw new Error(response.message || 'Failed to generate report');
        }

        // It's a ReportResponse
        const reportResponse = response;
        const report: ReportData = {
          id: reportResponse.report_id,
          title: config.title,
          type: config.type as ReportData['type'],
          data: reportResponse.data || {},
          generatedAt: new Date(reportResponse.created_at),
          filters: config.filters,
        };

        setReports((prev) => [...prev, report]);
        return report;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to generate report';
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const deleteReport = useCallback(async (reportId: string): Promise<void> => {
    try {
      const { deleteReport: apiDeleteReport } = await import(
        '../services/reportService'
      );
      await apiDeleteReport(reportId);
      setReports((prev) => prev.filter((report) => report.id !== reportId));
    } catch (err) {
      console.error('Failed to delete report:', err);
      // Still remove from local state for UX
      setReports((prev) => prev.filter((report) => report.id !== reportId));
    }
  }, []);

  const refreshReports = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Use real API to fetch report history
      const { getReportHistory } = await import('../services/reportService');
      const response = await getReportHistory(1, 50);

      // Check if response is an ApiError
      if ('type' in response && 'message' in response) {
        throw new Error(response.message || 'Failed to fetch reports');
      }

      // Convert API response to ReportData format
      const reportsData: ReportData[] = response?.reports?.map((apiReport) => ({
        id: apiReport.report_id,
        title: `${apiReport.report_type} Report`,
        type: apiReport.report_type as ReportData['type'],
        data: (apiReport.data || {}) as Record<string, unknown>,
        generatedAt: new Date(apiReport.created_at),
        filters: {},
      }));

      setReports(reportsData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to refresh reports';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    reports,
    isLoading,
    error,
    generateReport,
    deleteReport,
    refreshReports,
  };
};
