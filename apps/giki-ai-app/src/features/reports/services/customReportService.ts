/**
 * Custom Reports Service
 *
 * Handles custom report creation, execution, and management.
 * Integrates with existing backend report endpoints and provides
 * foundation for future custom report API endpoints.
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface CustomReportMetric {
  id: string;
  name: string;
  type: 'metric' | 'dimension';
  dataType: 'number' | 'string' | 'date';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
}

export interface CustomReportFilter {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'gt' | 'lt' | 'between' | 'in';
  value: string | number | Date | [Date, Date] | string[];
}

export interface CustomReportConfig {
  name: string;
  description: string;
  metrics: CustomReportMetric[];
  dimensions: CustomReportMetric[];
  filters: CustomReportFilter[];
  chartType: 'bar' | 'line' | 'pie' | 'table';
  dateRange?: {
    from: Date;
    to: Date;
  };
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export interface SavedCustomReport extends CustomReportConfig {
  id: string;
  savedAt: string;
  lastGenerated?: string;
}

export interface CustomReportData {
  config: CustomReportConfig;
  data: {
    chartData: unknown[];
    summary: {
      totalRecords: number;
      dateRange?: {
        from: Date;
        to: Date;
      };
      metrics: Array<{
        id: string;
        name: string;
        value: number;
      }>;
    };
  };
  generatedAt: string;
}

// ============================================================================
// AVAILABLE METRICS AND DIMENSIONS
// ============================================================================

export const AVAILABLE_METRICS: CustomReportMetric[] = [
  {
    id: 'amount',
    name: 'Transaction Amount',
    type: 'metric',
    dataType: 'number',
    aggregation: 'sum',
  },
  {
    id: 'count',
    name: 'Transaction Count',
    type: 'metric',
    dataType: 'number',
    aggregation: 'count',
  },
  {
    id: 'avg_amount',
    name: 'Average Amount',
    type: 'metric',
    dataType: 'number',
    aggregation: 'avg',
  },
  {
    id: 'min_amount',
    name: 'Minimum Amount',
    type: 'metric',
    dataType: 'number',
    aggregation: 'min',
  },
  {
    id: 'max_amount',
    name: 'Maximum Amount',
    type: 'metric',
    dataType: 'number',
    aggregation: 'max',
  },
];

export const AVAILABLE_DIMENSIONS: CustomReportMetric[] = [
  { id: 'category', name: 'Category', type: 'dimension', dataType: 'string' },
  {
    id: 'category_l1',
    name: 'Category L1',
    type: 'dimension',
    dataType: 'string',
  },
  {
    id: 'category_l2',
    name: 'Category L2',
    type: 'dimension',
    dataType: 'string',
  },
  {
    id: 'category_l3',
    name: 'Category L3',
    type: 'dimension',
    dataType: 'string',
  },
  {
    id: 'transaction_type',
    name: 'Transaction Type',
    type: 'dimension',
    dataType: 'string',
  },
  {
    id: 'merchant',
    name: 'Merchant/Entity',
    type: 'dimension',
    dataType: 'string',
  },
  { id: 'date', name: 'Date', type: 'dimension', dataType: 'date' },
  { id: 'month', name: 'Month', type: 'dimension', dataType: 'string' },
  { id: 'year', name: 'Year', type: 'dimension', dataType: 'string' },
  { id: 'gl_code', name: 'GL Code', type: 'dimension', dataType: 'string' },
  { id: 'account', name: 'Account', type: 'dimension', dataType: 'string' },
];

// ============================================================================
// CUSTOM REPORT GENERATION
// ============================================================================

/**
 * Generate a custom report based on configuration
 *
 * Uses existing backend endpoints and intelligent data transformation
 * to create custom reports until dedicated custom report endpoints are available.
 */
export const generateCustomReport = async (
  config: CustomReportConfig,
): Promise<CustomReportData | ApiError> => {
  try {
    // Determine the best backend endpoint to use based on configuration
    const reportStrategy = determineReportStrategy(config);

    let apiData: unknown;

    switch (reportStrategy.type) {
      case 'spending_by_category':
        apiData = await fetchSpendingByCategoryData(config);
        break;
      case 'spending_by_entity':
        apiData = await fetchSpendingByEntityData(config);
        break;
      case 'income_expense':
        apiData = await fetchIncomeExpenseData(config);
        break;
      case 'custom_aggregation':
        // For complex custom reports, use dedicated custom report API
        apiData = await fetchCustomAggregationData(config);
        break;
      default:
        throw new Error(`Unsupported report strategy: ${reportStrategy.type}`);
    }

    // improve the API data to match the custom report format
    const transformedData = transformApiDataToCustomFormat(apiData, config);

    return {
      config,
      data: transformedData,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    return handleApiError(error, {
      context: 'generateCustomReport',
      defaultMessage: 'Failed to generate custom report.',
    });
  }
};

/**
 * Determine the best report strategy based on configuration
 */
const determineReportStrategy = (config: CustomReportConfig) => {
  const hasCategory = config?.dimensions?.some((d) =>
    d?.id?.includes('category'),
  );
  const hasEntity = config?.dimensions?.some((d) => d.id === 'merchant');
  const hasAmountMetric = config?.metrics?.some((m) => m.id === 'amount');

  if (hasCategory && hasAmountMetric) {
    return {
      type: 'spending_by_category',
      endpoint: '/api/v1/reports/spending-by-category',
    };
  }

  if (hasEntity && hasAmountMetric) {
    return {
      type: 'spending_by_entity',
      endpoint: '/api/v1/reports/spending-by-entity',
    };
  }

  // Check if it's a simple income vs expense report
  const hasIncomeExpenseMetrics = config?.metrics?.some((m) =>
    ['total_income', 'total_expense', 'net_amount'].includes(m.id),
  );

  if (hasIncomeExpenseMetrics) {
    return {
      type: 'income_expense',
      endpoint: '/api/v1/reports/income-expense-summary',
    };
  }

  // For complex custom reports, use dedicated custom aggregation API
  return {
    type: 'custom_aggregation',
    endpoint: '/api/v1/reports/custom-aggregation',
  };
};

/**
 * Fetch spending by category data with date filtering
 */
const fetchSpendingByCategoryData = async (config: CustomReportConfig) => {
  const params: unknown = {};

  if (config.dateRange) {
    params.start_date = config?.dateRange?.from.toISOString().split('T')[0];
    params.end_date = config?.dateRange?.to.toISOString().split('T')[0];
  }

  const response = await apiClient.get('/api/v1/reports/spending-by-category', {
    params,
  });
  return response.data;
};

/**
 * Fetch spending by entity data with date filtering
 */
const fetchSpendingByEntityData = async (config: CustomReportConfig) => {
  const params: unknown = {};

  if (config.dateRange) {
    params.start_date = config?.dateRange?.from.toISOString().split('T')[0];
    params.end_date = config?.dateRange?.to.toISOString().split('T')[0];
  }

  const response = await apiClient.get('/api/v1/reports/spending-by-entity', {
    params,
  });
  return response.data;
};

/**
 * Fetch income expense data with date filtering
 */
const fetchIncomeExpenseData = async (config: CustomReportConfig) => {
  const params: unknown = {};

  if (config.dateRange) {
    params.start_date = config?.dateRange?.from.toISOString().split('T')[0];
    params.end_date = config?.dateRange?.to.toISOString().split('T')[0];
  }

  const response = await apiClient.get(
    '/api/v1/reports/income-expense-summary',
    {
      params,
    },
  );
  return response.data;
};

/**
 * improve API data to custom report format
 */
const transformApiDataToCustomFormat = (
  apiData: unknown,
  config: CustomReportConfig,
) => {
  const _primaryMetric = config.metrics[0];
  const primaryDimension = config.dimensions[0];

  let chartData: unknown[] = [];
  let totalRecords = 0;
  let metricValues: Array<{ id: string; name: string; value: number }> = [];

  // Handle spending by category data
  if (apiData.items && Array.isArray(apiData.items)) {
    chartData = (
      apiData?.items as Array<{
        category_path?: string;
        entity_name?: string;
        total_amount?: number;
        transaction_count?: number;
      }>
    )?.map((item) => {
      const result: Record<string, unknown> = {};

      // Map dimension
      if (primaryDimension) {
        if (primaryDimension.id === 'category') {
          result[primaryDimension.id] =
            item.category_path || item.entity_name || 'Unknown';
        } else if (primaryDimension.id === 'merchant') {
          result[primaryDimension.id] =
            item.entity_name || item.category_path || 'Unknown';
        } else {
          result[primaryDimension.id] =
            item.category_path || item.entity_name || 'Unknown';
        }
      }

      // Map metrics
      config?.metrics?.forEach((metric) => {
        if (metric.id === 'amount') {
          result[metric.id] = item.total_amount || 0;
        } else if (metric.id === 'count') {
          result[metric.id] = item.transaction_count || 0;
        } else if (metric.id === 'avg_amount') {
          result[metric.id] =
            item.total_amount && item.transaction_count
              ? item.total_amount / item.transaction_count
              : 0;
        }
      });

      return result;
    });

    totalRecords =
      (apiData as { total_records?: number }).total_records ||
      (apiData?.items as unknown[])?.length ||
      0;

    // Calculate metric summaries
    metricValues = config?.metrics?.map((metric) => ({
      id: metric.id,
      name: metric.name,
      value: (chartData as Array<Record<string, unknown>>).reduce(
        (sum: number, item) => sum + (Number(item[metric.id]) || 0),
        0,
      ),
    }));
  }

  // Handle income expense summary data
  else if ((apiData as { total_income?: number }).total_income !== undefined) {
    const incomeData = apiData as {
      total_income?: number;
      total_expenses?: number;
      net_income_loss?: number;
    };
    chartData = [
      { name: 'Income', value: incomeData.total_income || 0 },
      { name: 'Expenses', value: incomeData.total_expenses || 0 },
    ];

    metricValues = [
      {
        id: 'total_income',
        name: 'Total Income',
        value: incomeData.total_income || 0,
      },
      {
        id: 'total_expenses',
        name: 'Total Expenses',
        value: incomeData.total_expenses || 0,
      },
      {
        id: 'net_amount',
        name: 'Net Amount',
        value: incomeData.net_income_loss || 0,
      },
    ];

    totalRecords = 2; // Income and expense categories
  }

  return {
    chartData,
    summary: {
      totalRecords,
      dateRange: config.dateRange,
      metrics: metricValues,
    },
  };
};

/**
 * Fetch custom aggregation data from dedicated API
 */
const fetchCustomAggregationData = async (config: CustomReportConfig) => {
  const params: unknown = {
    dimensions: config?.dimensions?.map((d) => d.id),
    metrics: config?.metrics?.map((m) => m.id),
  };

  if (config.dateRange) {
    params.start_date = config?.dateRange?.from.toISOString().split('T')[0];
    params.end_date = config?.dateRange?.to.toISOString().split('T')[0];
  }

  const response = await apiClient.post(
    '/api/v1/reports/custom-aggregation',
    params,
  );
  return response.data;
};

// ============================================================================
// SAVED REPORTS MANAGEMENT
// ============================================================================

const STORAGE_KEY = 'giki_saved_custom_reports';

/**
 * Save a custom report configuration
 */
export const saveCustomReport = (
  config: CustomReportConfig,
): Promise<SavedCustomReport> => {
  const savedReport: SavedCustomReport = {
    ...config,
    id: `custom_report_${Date.now()}`,
    savedAt: new Date().toISOString(),
  };

  // Get existing saved reports
  const existingReports = getSavedCustomReports();

  // Add new report
  const updatedReports = [...existingReports, savedReport];

  // Save to localStorage
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedReports));

  return savedReport;
};

/**
 * Get all saved custom reports
 */
export const getSavedCustomReports = (): SavedCustomReport[] => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? (JSON.parse(saved) as SavedCustomReport[]) : [];
  } catch (error) {
    console.error('Error loading saved custom reports:', error);
    return [];
  }
};

/**
 * Delete a saved custom report
 */
export const deleteSavedCustomReport = (reportId: string): Promise<void> => {
  const existingReports = getSavedCustomReports();
  const updatedReports = existingReports.filter(
    (report) => report.id !== reportId,
  );
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedReports));
};

/**
 * Update a saved custom report
 */
export const updateSavedCustomReport = (
  reportId: string,
  config: Partial<CustomReportConfig>,
): Promise<SavedCustomReport | null> => {
  const existingReports = getSavedCustomReports();
  const reportIndex = existingReports.findIndex(
    (report) => report.id === reportId,
  );

  if (reportIndex === -1) {
    return null;
  }

  const updatedReport = {
    ...existingReports[reportIndex],
    ...config,
    lastGenerated: new Date().toISOString(),
  };

  existingReports[reportIndex] = updatedReport;
  localStorage.setItem(STORAGE_KEY, JSON.stringify(existingReports));

  return updatedReport;
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate custom report configuration
 */
export const validateCustomReportConfig = (
  config: CustomReportConfig,
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!config?.name?.trim()) {
    errors.push('Report name is required');
  }

  if (config?.metrics?.length === 0) {
    errors.push('At least one metric is required');
  }

  if (config?.dimensions?.length === 0) {
    errors.push('At least one dimension is required');
  }

  if (config.dateRange) {
    if (config?.dateRange?.from > config?.dateRange?.to) {
      errors.push('Start date must be before end date');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Get available filter operators for a field type
 */
export const getFilterOperators = (dataType: string) => {
  switch (dataType) {
    case 'string':
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'contains', label: 'Contains' },
        { value: 'in', label: 'In List' },
      ];
    case 'number':
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'gt', label: 'Greater Than' },
        { value: 'lt', label: 'Less Than' },
        { value: 'between', label: 'Between' },
      ];
    case 'date':
      return [
        { value: 'equals', label: 'On Date' },
        { value: 'gt', label: 'After' },
        { value: 'lt', label: 'Before' },
        { value: 'between', label: 'Date Range' },
      ];
    default:
      return [{ value: 'equals', label: 'Equals' }];
  }
};

/**
 * Format custom report data for export
 */
export const formatCustomReportForExport = (
  reportData: CustomReportData,
  format: 'csv' | 'json' = 'csv',
): string => {
  if (format === 'json') {
    return JSON.stringify(reportData, null, 2);
  }

  // CSV format
  const { chartData } = reportData.data;
  if (chartData.length === 0) {
    return 'No data available\n';
  }

  const headers = Object.keys(
    (chartData as Array<Record<string, unknown>>)[0],
  ).join(',');
  const rows = (chartData as Array<Record<string, unknown>>).map((row) =>
    Object.values(row)
      .map((value) =>
        typeof value === 'string'
          ? `"${value}"`
          : typeof value === 'number' || typeof value === 'boolean'
            ? String(value)
            : '',
      )
      .join(','),
  );

  return [headers, ...rows].join('\n');
};
