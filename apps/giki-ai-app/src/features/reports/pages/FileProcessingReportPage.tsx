import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Download,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { apiClient } from '@/shared/services/api/apiClient';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Alert,
  AlertDescription,
  Progress,
} from '@/shared/components/ui';
import ExcelTable from '@/shared/components/ui/excel-table';
import { Loading } from '@/shared/components';
import { formatDateTime, formatNumber } from '@/shared/utils/utils';

interface FileProcessingReport {
  id: string;
  upload_id: string;
  file_name: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  total_rows: number;
  successful_rows: number;
  failed_rows: number;
  processing_started_at: string;
  processing_completed_at: string | null;
  error_messages: Record<string, unknown> | null;
  data_quality_score: number;
  processing_time_seconds: number | null;
  created_at: string;
  updated_at: string;
}

interface ColumnStatistic {
  id: string;
  report_id: string;
  column_name: string;
  original_name: string;
  data_type: string;
  null_count: number;
  null_percentage: number;
  unique_values: number;
  most_common_value: string | null;
  most_common_count: number | null;
  min_value: unknown;
  max_value: unknown;
  avg_value: number | null;
  detected_format: string | null;
  quality_score: number;
  sample_values: string[];
}

interface RowProcessingDetail {
  id: string;
  report_id: string;
  row_number: number;
  original_data: Record<string, unknown>;
  processed_data: Record<string, unknown>;
  processing_status: 'success' | 'failed' | 'skipped';
  error_message: string | null;
  validation_errors: Record<string, unknown> | null;
  created_at: string;
}

interface SchemaMappingReport {
  discovered_columns: string[];
  mapped_columns: Record<string, string>;
  unmapped_columns: string[];
  category_mappings: Array<{
    original_category: string;
    unified_category: string;
    confidence_score: number;
  }>;
}

export function FileProcessingReportPage() {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const client = apiClient;

  const [report, setReport] = useState<FileProcessingReport | null>(null);
  const [columnStats, setColumnStats] = useState<ColumnStatistic[]>([]);
  const [rowDetails, setRowDetails] = useState<RowProcessingDetail[]>([]);
  const [schemaMapping, setSchemaMapping] =
    useState<SchemaMappingReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [rowDetailsPage, setRowDetailsPage] = useState(1);
  const [totalRowDetails, setTotalRowDetails] = useState(0);
  const [_tenantId, setTenantId] = useState<number | null>(null);

  const fetchReportData = useCallback(async () => {
    if (!reportId) return;
    try {
      setLoading(true);

      // First fetch the report to get tenant info
      const reportRes = await client.get<FileProcessingReport>(
        `/api/v1/onboarding/file-processing-report/${reportId}`,
      );
      setReport(reportRes.data);

      // Get tenant ID from auth context or report
      const currentTenantId =
        (reportRes.data as { tenant_id?: number }).tenant_id || 1;
      setTenantId(currentTenantId);

      // Fetch all other data in parallel
      const [statsRes, rowsRes, schemaRes] = await Promise.all([
        client.get<ColumnStatistic[]>(
          `/api/v1/onboarding/column-statistics/${reportId}`,
        ),
        client.post<{ rows: RowProcessingDetail[]; total_rows: number }>(
          `/api/v1/onboarding/row-details/${reportId}`,
          {
            page: rowDetailsPage,
            page_size: 50,
          },
        ),
        client.get<SchemaMappingReport>(
          `/api/v1/onboarding/schema-mapping-report/${currentTenantId}`,
        ),
      ]);

      setColumnStats(statsRes.data || []);
      setRowDetails(rowsRes.data.rows || []);
      setTotalRowDetails(rowsRes.data.total_rows || 0);
      setSchemaMapping(schemaRes.data);
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  }, [reportId, client, rowDetailsPage]);

  useEffect(() => {
    void fetchReportData();
  }, [reportId, fetchReportData]);

  const fetchRowDetailsPage = async (page: number) => {
    try {
      const response = await client.post<{
        rows: RowProcessingDetail[];
        total_rows: number;
      }>(`/api/v1/onboarding/row-details/${reportId}`, {
        page,
        page_size: 50,
      });
      setRowDetails(response.data.rows || []);
      setRowDetailsPage(page);
    } catch (error) {
      console.error('Error fetching row details:', error);
    }
  };

  const downloadReport = async () => {
    try {
      const response = await client.get<Blob>(
        `/api/v1/onboarding/export-processing-report/${reportId}`,
        { responseType: 'blob' },
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `processing-report-${reportId}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading report:', error);
    }
  };

  if (loading) {
    return <Loading fullPage text="Loading processing report..." />;
  }

  if (!report) {
    return (
      <div className="flex flex-wrap items-center justify-center min-h-screen">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Report not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  const successRate =
    report.total_rows > 0
      ? (report.successful_rows / report.total_rows) * 100
      : 0;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      'default' | 'secondary' | 'destructive' | 'outline'
    > = {
      completed: 'default',
      failed: 'destructive',
      processing: 'secondary',
      pending: 'outline',
    };

    return (
      <Badge
        variant={variants[status] || 'outline'}
        className="max-w-[150px] truncate"
      >
        <span className="flex flex-wrap items-center gap-1">
          {getStatusIcon(status)}
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </Badge>
    );
  };

  // Prepare data for ExcelTable
  const columnStatsData = columnStats.map((stat) => ({
    'Column Name': stat.column_name,
    'Original Name': stat.original_name,
    'Data Type': stat.data_type,
    'Null %': `${stat.null_percentage.toFixed(2)}%`,
    'Unique Values': stat.unique_values,
    'Most Common': stat.most_common_value || 'N/A',
    'Quality Score': `${(stat.quality_score * 100).toFixed(0)}%`,
    Format: stat.detected_format || 'Unknown',
  }));

  const rowDetailsData = rowDetails.map((row) => ({
    'Row #': row.row_number,
    Status: row.processing_status,
    Error: row.error_message || 'None',
    ...row.processed_data,
  }));

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between">
        <div className="flex flex-wrap items-center gap-4">
          <Button
            className="max-w-full"
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">File Processing Report</h1>
        </div>
        <Button className="max-w-full" onClick={() => void downloadReport()}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">File Name</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="flex flex-wrap items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="truncate font-medium">{report.file_name}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              Processing Status
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            {getStatusBadge(report.processing_status)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="space-y-2">
              <div className="flex flex-wrap items-center justify-between">
                <span className="truncate text-2xl font-bold">
                  {successRate.toFixed(1)}%
                </span>
                <span className="text-xs text-muted-foreground">
                  {report.successful_rows}/{report.total_rows}
                </span>
              </div>
              <Progress value={successRate} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="truncate text-sm font-medium">
              Data Quality
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="space-y-2">
              <div className="flex flex-wrap items-center justify-between">
                <span className="truncate text-2xl font-bold">
                  {(report.data_quality_score * 100).toFixed(0)}%
                </span>
                <Badge
                  variant={
                    report.data_quality_score >= 0.8 ? 'default' : 'secondary'
                  }
                  className="max-w-[150px] truncate"
                >
                  {report.data_quality_score >= 0.8 ? 'Good' : 'Needs Review'}
                </Badge>
              </div>
              <Progress
                value={report.data_quality_score * 100}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="columns">Column Analysis</TabsTrigger>
          <TabsTrigger value="rows">Row Details</TabsTrigger>
          <TabsTrigger value="schema">Schema Mapping</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Processing Summary</CardTitle>
              <CardDescription>
                Detailed information about the file processing
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Total Rows
                  </dt>
                  <dd className="truncate text-heading-3">
                    {formatNumber(report.total_rows)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Successful Rows
                  </dt>
                  <dd className="truncate text-heading-3 text-success">
                    {formatNumber(report.successful_rows)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Failed Rows
                  </dt>
                  <dd className="truncate text-heading-3 text-destructive">
                    {formatNumber(report.failed_rows)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Processing Time
                  </dt>
                  <dd className="truncate text-heading-3">
                    {report.processing_time_seconds
                      ? `${report.processing_time_seconds.toFixed(2)}s`
                      : 'N/A'}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Started At
                  </dt>
                  <dd className="text-sm">
                    {formatDateTime(report.processing_started_at)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Completed At
                  </dt>
                  <dd className="text-sm">
                    {report.processing_completed_at
                      ? formatDateTime(report.processing_completed_at)
                      : 'In Progress'}
                  </dd>
                </div>
              </dl>

              {report.error_messages &&
                Object.keys(report.error_messages).length > 0 && (
                  <Alert className="mt-4" variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-semibold mb-2">
                        Processing Errors:
                      </div>
                      <pre className="text-xs overflow-auto">
                        {JSON.stringify(report.error_messages, null, 2)}
                      </pre>
                    </AlertDescription>
                  </Alert>
                )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="columns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Column Statistics</CardTitle>
              <CardDescription>
                Analysis of each column in the uploaded file
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              {columnStats.length > 0 ? (
                <ExcelTable
                  data={columnStatsData}
                  columns={[
                    { key: 'Column Name', label: 'Column Name', type: 'text' },
                    {
                      key: 'Original Name',
                      label: 'Original Name',
                      type: 'text',
                    },
                    { key: 'Data Type', label: 'Data Type', type: 'text' },
                    { key: 'Null %', label: 'Null %', type: 'percentage' },
                    {
                      key: 'Unique Values',
                      label: 'Unique Values',
                      type: 'number',
                    },
                    { key: 'Most Common', label: 'Most Common', type: 'text' },
                    {
                      key: 'Quality Score',
                      label: 'Quality Score',
                      type: 'percentage',
                    },
                    { key: 'Format', label: 'Format', type: 'text' },
                  ]}
                  enableSorting
                  enableFiltering
                  enableColumnResize
                />
              ) : (
                <Alert>
                  <AlertDescription>
                    No column statistics available
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Row Processing Details</CardTitle>
              <CardDescription>
                Detailed information about each processed row
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              {rowDetails.length > 0 ? (
                <>
                  <ExcelTable
                    data={rowDetailsData}
                    columns={[
                      { key: 'Row #', label: 'Row #', type: 'number' },
                      {
                        key: 'Status',
                        label: 'Status',
                        type: 'status',
                        renderCell: (value: string) => (
                          <Badge
                            variant={
                              value === 'success'
                                ? 'default'
                                : value === 'failed'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                            className="max-w-[150px] truncate"
                          >
                            {value}
                          </Badge>
                        ),
                      },
                      { key: 'Error', label: 'Error', type: 'text' },
                      ...Object.keys(rowDetails[0]?.processed_data || {}).map(
                        (key) => ({
                          key,
                          label: key,
                          type: 'text' as const,
                        }),
                      ),
                    ]}
                    enableSorting
                    enableFiltering
                    enableColumnResize
                  />

                  {/* Pagination */}
                  <div className="flex flex-wrap items-center justify-between mt-4">
                    <div className="text-xs text-muted-foreground">
                      Showing {rowDetails.length} of {totalRowDetails} rows
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        className="max-w-full"
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          void fetchRowDetailsPage(rowDetailsPage - 1)
                        }
                        disabled={rowDetailsPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        className="max-w-full"
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          void fetchRowDetailsPage(rowDetailsPage + 1)
                        }
                        disabled={rowDetails.length < 50}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <Alert>
                  <AlertDescription>No row details available</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schema" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Schema Mapping</CardTitle>
              <CardDescription>
                How columns were mapped and categories discovered
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              {schemaMapping ? (
                <div className="space-y-6">
                  {/* Column Mappings */}
                  <div>
                    <h4 className="font-semibold mb-3">Column Mappings</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="text-sm font-medium text-muted-foreground mb-2">
                          Discovered Columns
                        </h5>
                        <div className="space-y-1">
                          {schemaMapping.discovered_columns.map((col, idx) => (
                            <div
                              key={idx}
                              className="text-sm p-2 bg-muted rounded"
                            >
                              {col}
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h5 className="text-sm font-medium text-muted-foreground mb-2">
                          Mapped To
                        </h5>
                        <div className="space-y-1">
                          {Object.entries(schemaMapping.mapped_columns).map(
                            ([orig, mapped], idx) => (
                              <div
                                key={idx}
                                className="text-sm p-2 bg-muted rounded"
                              >
                                {orig} → {mapped}
                              </div>
                            ),
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Category Mappings */}
                  {schemaMapping.category_mappings &&
                    schemaMapping.category_mappings.length > 0 && (
                      <div>
                        <h4 className="font-semibold mb-3">
                          Category Mappings
                        </h4>
                        <ExcelTable
                          data={schemaMapping.category_mappings}
                          columns={[
                            {
                              key: 'original_category',
                              label: 'Original Category',
                              type: 'text',
                            },
                            {
                              key: 'unified_category',
                              label: 'Unified Category',
                              type: 'text',
                            },
                            {
                              key: 'confidence_score',
                              label: 'Confidence',
                              type: 'percentage',
                              renderCell: (value: number) =>
                                `${(value * 100).toFixed(0)}%`,
                            },
                          ]}
                          enableSorting
                          enableFiltering
                        />
                      </div>
                    )}
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    No schema mapping information available
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default FileProcessingReportPage;
