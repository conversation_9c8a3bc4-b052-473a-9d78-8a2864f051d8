import React, { useState, useEffect } from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  // AreaChart,
  // Area,
} from 'recharts';
import { Button } from '@/shared/components/ui/button';
import {
  Card,
  CardContent,
  // CardFooter,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';

import { UnifiedError } from '@/shared/components/ui/unified-error';
// import { Separator } from '@/shared/components/ui/separator';
// import { ScrollArea } from '@/shared/components/ui/scroll-area';

import { format } from 'date-fns';
import { useToast } from '@/shared/components/ui/use-toast';

import {
  PieChart as PieChartIcon,
  RefreshCw,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';
import { DashboardCardSkeleton } from '@/shared/components/ui/skeleton';
import {
  getSpendingByCategoryReport,
  getIncomeVsExpenseReport,
  getMonthlyTrends,
  exportReport,
} from '@/features/reports/services/reportService';
import type {
  SpendingByCategoryItem,
  ReportExportParams,
} from '@/shared/types/api';
import { useRealtimeSync } from '@/shared/services/realtime/realtimeSync';
import { useNavigate } from 'react-router-dom';
import type {
  CustomReportConfig,
  CustomReportData,
  CustomReportMetric,
  SavedCustomReport,
} from '../services/customReportService';

// Interface for the simplified transaction structure used in this component's table
interface ReportTransaction {
  id: string;
  date: string;
  description: string;
  category: string; // category_path or a simplified name
  amount: number;
  currency: string;
}

// Interface for monthly trend data
interface MonthlyTrendItem {
  month: string;
  income: number;
  expense: number;
  net: number;
}

// Interface for income vs expense data
interface IncomeExpenseItem {
  name: string;
  value: number;
}

// COLORS for charts using design tokens
const COLORS = [
  'hsl(var(--giki-brand-blue))',
  'hsl(var(--giki-brand-emerald))',
  'hsl(var(--giki-brand-peach))',
  'hsl(var(--giki-warning))',
  'hsl(var(--giki-brand-purple))',
  'hsl(var(--giki-success))',
  'hsl(var(--giki-info))',
  'hsl(var(--giki-primary))',
];
const INCOME_COLOR = 'hsl(var(--giki-success))';
const EXPENSE_COLOR = 'hsl(var(--giki-destructive))';
// const NET_COLOR = 'hsl(var(--giki-brand-blue))';

const ReportsPage: React.FC = () => {
  const navigate = useNavigate();

  // State for report data
  const [spendingByCategory, setSpendingByCategory] = useState<
    SpendingByCategoryItem[]
  >([]);
  const [transactions, setTransactions] = useState<ReportTransaction[]>([]);
  const [monthlyTrends, setMonthlyTrends] = useState<MonthlyTrendItem[]>([]);
  const [incomeVsExpense, setIncomeVsExpense] = useState<IncomeExpenseItem[]>(
    [],
  );
  const [summaryData, setSummaryData] = useState({
    totalIncome: 0,
    totalExpense: 0,
    netAmount: 0,
  });

  // State for UI controls
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof ReportTransaction>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { toast } = useToast();

  // Custom report state
  const [customReportData, setCustomReportData] =
    useState<CustomReportData | null>(null);
  const [savedReports, setSavedReports] = useState<SavedCustomReport[]>([]);

  // Listen for report generation requests from agent
  interface ReportGenerateData {
    source?: string;
    request?: {
      type?: string;
      naturalLanguageQuery?: string;
      parameters?: {
        metrics?: CustomReportMetric[];
        dimensions?: CustomReportMetric[];
        chartType?: 'bar' | 'line' | 'pie' | 'table';
        dateRange?: { from: Date; to: Date };
        groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
      };
    };
  }

  useRealtimeSync(
    'report.generate',
    (data: ReportGenerateData) => {
      const reportData = data;
      if (reportData.source === 'agent') {
        // Navigate to reports page if not already there
        if (window?.location?.pathname !== '/reports') {
          navigate('/reports');
        }

        // Handle different report types
        const request = reportData.request;
        if (!request) return;

        switch (request.type) {
          case 'spending_by_category':
          case 'income_vs_expense':
            setActiveTab('overview');
            setRefreshTrigger((prev) => prev + 1);
            break;
          case 'monthly_trends':
          case 'transaction_analysis':
            setActiveTab('trends');
            setRefreshTrigger((prev) => prev + 1);
            break;
          case 'pivot':
            // For pivot tables, we need to integrate with the PivotTable component
            toast({
              title: 'Pivot Table',
              description:
                'Navigate to the pivot table builder to create your custom analysis.',
            });
            break;
          case 'custom':
            // Generate custom report based on parameters
            setActiveTab('custom');
            handleCustomReportGenerate({
              name: 'Agent Generated Report',
              description: request.naturalLanguageQuery,
              metrics: request.parameters?.metrics || [
                {
                  id: 'amount',
                  name: 'Transaction Amount',
                  type: 'metric',
                  dataType: 'number',
                  aggregation: 'sum',
                },
              ],
              dimensions: request.parameters?.dimensions || [
                {
                  id: 'category',
                  name: 'Category',
                  type: 'dimension',
                  dataType: 'string',
                },
              ],
              filters: [],
              chartType: request.parameters?.chartType || 'bar',
              dateRange: request.parameters?.dateRange,
              groupBy: request.parameters?.groupBy,
            });
            break;
        }

        toast({
          title: 'Report Generated',
          description: `Your ${request?.type?.replace('_', ' ')} report is ready.`,
          variant: 'success',
        });
      }
    },
    [navigate],
  );

  // Format month from YYYY-MM to readable format
  const formatMonth = (monthStr: string) => {
    const [year, month] = monthStr.split('-');
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Format currency with proper error handling
  const formatCurrency = (
    amount: number | undefined | null,
    currency: string = 'INR',
  ) => {
    // Handle undefined, null, or NaN values
    if (amount === undefined || amount === null || isNaN(amount)) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: currency,
      }).format(0);
    }

    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  // Handle sorting
  const handleSort = (column: keyof ReportTransaction) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Get sorted and filtered transactions
  const getSortedAndFilteredTransactions = () => {
    return [...transactions]
      .filter(
        (tx) =>
          tx?.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tx?.category?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      .sort((a, b) => {
        const aValue = a[sortColumn];
        const bValue = b[sortColumn];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }

        return 0;
      });
  };

  // Render sort indicator
  const renderSortIndicator = (column: keyof ReportTransaction) => {
    if (sortColumn !== column) {
      return <ArrowUpDown className="ml-1 h-4 w-4 text-muted-foreground/50" />;
    }

    return sortDirection === 'asc' ? (
      <ArrowUp className="ml-1 h-4 w-4 text-primary" />
    ) : (
      <ArrowDown className="ml-1 h-4 w-4 text-primary" />
    );
  };

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch all report data in parallel including monthly trends
        const [spendingData, incomeExpenseData, monthlyData] =
          await Promise.all([
            getSpendingByCategoryReport(),
            getIncomeVsExpenseReport(),
            getMonthlyTrends(
              dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
              dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
            ),
          ]);

        setSpendingByCategory(spendingData);

        // Set income vs expense data from API
        setIncomeVsExpense([
          { name: 'Income', value: incomeExpenseData.totalIncome },
          { name: 'Expense', value: incomeExpenseData.totalExpense },
        ]);

        // Set summary values for the cards
        setSummaryData({
          totalIncome: incomeExpenseData.totalIncome,
          totalExpense: incomeExpenseData.totalExpense,
          netAmount: incomeExpenseData.netAmount,
        });

        // Process monthly trends data from API
        if (!Array.isArray(monthlyData)) {
          // Monthly trends data is not an array - falling back to empty array
          setMonthlyTrends([]);
        } else {
          const processedTrends = monthlyData.map((item) => ({
            month: formatMonth(item.month),
            income: item.income,
            expense: item.expenses,
            net: item.net,
          }));
          setMonthlyTrends(processedTrends);
        }

        // Set empty transactions array since we can't fetch them
        setTransactions([]);
      } catch (err) {
        // Failed to fetch report data - error displayed to user
        setError(
          err instanceof Error ? err.message : 'An unknown error occurred',
        );
      } finally {
        setIsLoading(false);
      }
    };
    void fetchData();
  }, [refreshTrigger, dateRange]);

  // Use summary data from API instead of calculating from transactions
  const { totalIncome, totalExpense, netAmount } = summaryData;

  // Handle custom report generation
  const handleCustomReportGenerate = (config: CustomReportConfig) => {
    // Generate custom report data based on configuration
    // In a real app, this would call an API with the config
    const mockData = {
      config,
      data: {
        chartData: generateMockCustomReportData(config),
        summary: {
          totalRecords: Math.floor(Math.random() * 1000) + 100,
          dateRange: config.dateRange,
          metrics: config.metrics.map((m: CustomReportMetric) => ({
            id: m.id,
            name: m.name,
            value: Math.floor(Math.random() * 10000) + 1000,
          })),
        },
      },
      generatedAt: new Date().toISOString(),
    };

    setCustomReportData(mockData);
    setActiveTab('custom'); // Keep on custom tab to show results
  };

  // Handle custom report save
  const _handleCustomReportSave = (config: CustomReportConfig) => {
    const newReport = {
      ...config,
      id: Date.now().toString(),
      savedAt: new Date().toISOString(),
    };

    setSavedReports((prev) => [...prev, newReport]);

    // Save to localStorage for persistence
    const allReports = [...savedReports, newReport];
    localStorage.setItem('savedCustomReports', JSON.stringify(allReports));

    toast({
      title: 'Report Saved',
      description: `"${config.name}" has been saved to your reports.`,
      variant: 'success',
    });
  };

  // Generate mock data for custom reports
  const generateMockCustomReportData = (
    config: CustomReportConfig,
  ): Record<string, string | number>[] => {
    const dataPoints = 10;
    const data: Record<string, string | number>[] = [];

    for (let i = 0; i < dataPoints; i++) {
      const point: Record<string, string | number> = {};

      // Add dimension values
      config.dimensions.forEach((dim: CustomReportMetric) => {
        if (dim.dataType === 'date') {
          point[dim.id] = `2024-${String(i + 1).padStart(2, '0')}`;
        } else {
          point[dim.id] = `${dim.name} ${i + 1}`;
        }
      });

      // Add metric values
      config.metrics.forEach((metric: CustomReportMetric) => {
        point[metric.id] = Math.floor(Math.random() * 5000) + 500;
      });

      data.push(point);
    }

    return data;
  };

  // Load saved reports from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('savedCustomReports');
    if (saved) {
      try {
        const parsedReports = JSON.parse(saved) as SavedCustomReport[];
        setSavedReports(parsedReports);
      } catch {
        // Error loading saved reports from localStorage - invalid JSON
      }
    }
  }, []);

  // Handle export
  const handleExport = async (type: 'csv' | 'pdf') => {
    setIsLoading(true);
    setError(null);
    try {
      const params: ReportExportParams = {
        type,
        reportName:
          activeTab === 'transactions'
            ? 'transaction_list'
            : `${activeTab}_report`,
        filters: {
          searchTerm,
          sortColumn,
          sortDirection,
          dateFrom: dateRange.from
            ? format(dateRange.from, 'yyyy-MM-dd')
            : undefined,
          dateTo: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
        },
      };
      const blob = await exportReport(params);
      const url = window?.URL?.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report_${activeTab}_${format(new Date(), 'yyyy-MM-dd')}.${type}`;
      document?.body?.appendChild(a);
      a.click();
      a.remove();
      window?.URL?.revokeObjectURL(url);
      toast({
        title: 'Export Successful',
        description: `Report ${activeTab}_${format(new Date(), 'yyyy-MM-dd')}.${type} downloaded.`,
        variant: 'success',
      });
    } catch (_err) {
      // Export failed - error displayed to user in toast
      setError(
        _err instanceof Error
          ? _err.message
          : `Failed to export ${type.toUpperCase()}`,
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Simple header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Reports</h1>
        <Button
          variant="outline"
          onClick={() => setRefreshTrigger((prev) => prev + 1)}
          disabled={isLoading}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>

      {/* Error handling */}
      {error && (
        <UnifiedError
          description={error}
          variant="error"
          showRetry
          onRetry={() => setRefreshTrigger((prev) => prev + 1)}
          layout="centered"
          className="my-6"
        />
      )}

      {/* Loading state */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <DashboardCardSkeleton />
          <DashboardCardSkeleton />
          <DashboardCardSkeleton />
        </div>
      )}

      {/* Simple reports */}
      {!isLoading && !error && (
        <div className="space-y-8">
          {/* Summary metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Total Income</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalIncome)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Total Expenses</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(totalExpense)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Net Amount</CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className={`text-2xl font-bold ${netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}
                >
                  {formatCurrency(netAmount)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          {spendingByCategory.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Spending by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div style={{ width: '100%', height: 300 }}>
                    <ResponsiveContainer>
                      <BarChart data={spendingByCategory}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip
                          formatter={(value) => formatCurrency(value as number)}
                        />
                        <Bar dataKey="spending" fill="hsl(var(--primary))" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Income vs Expense</CardTitle>
                </CardHeader>
                <CardContent>
                  <div style={{ width: '100%', height: 300 }}>
                    <ResponsiveContainer>
                      <BarChart data={incomeVsExpense}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip
                          formatter={(value) => formatCurrency(value as number)}
                        />
                        <Bar dataKey="value">
                          {incomeVsExpense.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={
                                entry.name === 'Income' ? '#22c55e' : '#ef4444'
                              }
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-16">
                <PieChartIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">
                  No Data Available
                </h3>
                <p className="text-muted-foreground mb-4">
                  Upload transaction data to see your financial reports.
                </p>
                <Button onClick={() => navigate('/transactions')}>
                  Upload Transactions
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default ReportsPage;
