'use client';

import React from 'react';
import TransactionListTable from '../components/TransactionListTable';

const ReportsPage: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="truncate text-heading-1 mb-8">Reports</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Cash Flow Report Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="truncate text-heading-4 mb-4">Cash Flow Report</h2>
          <div
            data-testid="cash-flow-placeholder"
            className="border-2 border-dashed border-border rounded-md p-8 text-center text-[hsl(var(--giki-text-muted))]"
          >
            Cash Flow Report Placeholder
          </div>
        </div>

        {/* MIS Report Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="truncate text-heading-4 mb-4">MIS Report</h2>
          <div
            data-testid="mis-placeholder"
            className="border-2 border-dashed border-border rounded-md p-8 text-center text-[hsl(var(--giki-text-muted))]"
          >
            MIS Report Placeholder
          </div>
        </div>

        {/* Expense Breakdown Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="truncate text-heading-4 mb-4">Expense Breakdown</h2>
          <div
            data-testid="expense-breakdown-placeholder"
            className="border-2 border-dashed border-border rounded-md p-8 text-center text-[hsl(var(--giki-text-muted))]"
          >
            Expense Breakdown Placeholder
          </div>
        </div>

        {/* Employee Cost Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="truncate text-heading-4 mb-4">Employee Cost</h2>
          <div
            data-testid="employee-cost-placeholder"
            className="border-2 border-dashed border-border rounded-md p-8 text-center text-[hsl(var(--giki-text-muted))]"
          >
            Employee Cost Analysis Placeholder
          </div>
        </div>

        {/* Transaction List Section - Full Width */}
        <div className="bg-white rounded-lg shadow p-6 md:col-span-2">
          <h2 className="truncate text-heading-4 mb-4">Transaction List</h2>
          {/* Replaced placeholder with TransactionListTable component */}
          <TransactionListTable />
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
