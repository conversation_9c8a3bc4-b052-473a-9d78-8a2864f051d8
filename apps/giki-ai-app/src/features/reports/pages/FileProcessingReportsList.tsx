import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, FileText, Search, Filter } from 'lucide-react';
import { apiClient } from '@/shared/services/api/apiClient';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui';
import { Loading } from '@/shared/components';
import {
  FileProcessingReportCard,
  ProcessingReportSummary,
} from '../components/FileProcessingReportCard';

export function FileProcessingReportsList() {
  const navigate = useNavigate();
  const client = apiClient;

  const [reports, setReports] = useState<ProcessingReportSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at_desc');

  const fetchReports = useCallback(async () => {
    try {
      setLoading(true);
      const response = await client.get<ProcessingReportSummary[]>(
        '/api/v1/onboarding/recent-processing-reports',
        {
          params: { limit: 50 },
        },
      );
      setReports(response.data || []);
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setLoading(false);
    }
  }, [client]);

  useEffect(() => {
    void fetchReports();
  }, [fetchReports]);

  const filteredReports = reports.filter((report) => {
    // Search filter
    if (
      searchTerm &&
      !report.file_name.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    // Status filter
    if (statusFilter !== 'all' && report.status !== statusFilter) {
      return false;
    }

    return true;
  });

  const sortedReports = [...filteredReports].sort((a, b) => {
    switch (sortBy) {
      case 'created_at_desc':
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case 'created_at_asc':
        return (
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      case 'success_rate_desc':
        return b.success_rate - a.success_rate;
      case 'success_rate_asc':
        return a.success_rate - b.success_rate;
      case 'quality_desc':
        return b.data_quality_score - a.data_quality_score;
      case 'quality_asc':
        return a.data_quality_score - b.data_quality_score;
      default:
        return 0;
    }
  });

  if (loading) {
    return <Loading fullPage text="Loading processing reports..." />;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">File Processing Reports</h1>
          <p className="text-muted-foreground mt-1">
            View detailed reports for all uploaded files
          </p>
        </div>
        <Button className="max-w-full" onClick={() => navigate('/upload')}>
          <Plus className="h-4 w-4 mr-2" />
          Upload New File
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Filters</CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by file name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at_desc">Newest First</SelectItem>
                <SelectItem value="created_at_asc">Oldest First</SelectItem>
                <SelectItem value="success_rate_desc">
                  Highest Success Rate
                </SelectItem>
                <SelectItem value="success_rate_asc">
                  Lowest Success Rate
                </SelectItem>
                <SelectItem value="quality_desc">Best Quality</SelectItem>
                <SelectItem value="quality_asc">Worst Quality</SelectItem>
              </SelectContent>
            </Select>

            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setSortBy('created_at_desc');
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Reports Grid */}
      {sortedReports.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12 overflow-hidden">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-card-foreground-title mb-2">
              No reports found
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your filters'
                : 'Upload files to see processing reports here'}
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Button
                className="max-w-full"
                onClick={() => navigate('/upload')}
              >
                <Plus className="h-4 w-4 mr-2" />
                Upload Your First File
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="truncatebody-small">
            Showing {sortedReports.length} of {reports.length} reports
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedReports.map((report) => (
              <FileProcessingReportCard
                key={report.upload_id}
                report={report}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
}

export default FileProcessingReportsList;
