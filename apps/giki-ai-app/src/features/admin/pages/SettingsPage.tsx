/**
 * System Settings & Administration Page
 *
 * Comprehensive system administration including:
 * - User management
 * - Tenant configuration
 * - System health monitoring
 * - Database operations
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Switch } from '@/shared/components/ui/switch';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import { Loading } from '@/shared/components/ui/loading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import {
  Settings,
  Users,
  Building,
  Database,
  Activity,
  Shield,
  AlertTriangle,
  Plus,
  RefreshCw,
  Monitor,
  Key,
  Palette,
  Tags,
} from 'lucide-react';
import { toast } from '@/shared/components/ui/use-toast';
import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

interface SystemHealth {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  services: {
    database: { status: string };
  };
  metrics: {
    total_tenants: number;
    active_tenants: number;
    total_users: number;
  };
}

interface TenantData {
  id: number;
  name: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  settings?: Record<string, unknown>;
}

interface UserData {
  id: number;
  email: string;
  tenant_id: number;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
}

interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  language: string;
  timezone: string;
  currency: string;
}

const SettingsPage: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [tenants, setTenants] = useState<TenantData[]>([]);
  const [users, setUsers] = useState<UserData[]>([]);
  const [userSettings, setUserSettings] = useState<UserSettings>({
    theme: 'system',
    notifications: true,
    language: 'en',
    timezone: 'UTC',
    currency: 'USD',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('preferences');
  const [newTenantDialog, setNewTenantDialog] = useState(false);
  const [newUserDialog, setNewUserDialog] = useState(false);

  // New tenant form state
  const [newTenant, setNewTenant] = useState({
    name: '',
    email: '',
    is_active: true,
  });

  // New user form state
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    tenant_id: 1,
    is_active: true,
    is_verified: false,
  });

  const loadSystemData = useCallback(async () => {
    setIsLoading(true);
    try {
      await Promise.all([loadSystemHealth(), loadTenants(), loadUsers()]);
    } catch (error) {
      console.error('Failed to load system data:', error);
      toast({
        title: 'Error loading data',
        description:
          'Failed to load system information. Some features may not be available.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadUserSettings = useCallback(() => {
    // Load from localStorage or API
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      setUserSettings(JSON.parse(savedSettings) as UserSettings);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    void loadSystemData();
    void loadUserSettings();
  }, [loadSystemData, loadUserSettings]);

  const loadSystemHealth = async () => {
    try {
      const response = await apiClient.get<SystemHealth>(
        '/admin/system-health',
      );
      setSystemHealth(response.data);
    } catch (error) {
      console.error('Failed to load system health:', error);
    }
  };

  const loadTenants = async () => {
    try {
      const response = await apiClient.get<{ tenants: TenantData[] }>(
        '/admin/tenants',
      );
      setTenants(response?.data?.tenants || []);
    } catch (error) {
      console.error('Failed to load tenants:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await apiClient.get<{ users: UserData[] }>(
        '/admin/users',
      );
      setUsers(response?.data?.users || []);
    } catch (error) {
      console.error('Failed to load users:', error);
    }
  };

  const saveUserSettings = (settings: UserSettings) => {
    setIsSaving(true);
    try {
      // Save to localStorage and optionally to API
      localStorage.setItem('userSettings', JSON.stringify(settings));

      // Apply theme
      if (settings.theme !== 'system') {
        document?.documentElement?.classList.toggle(
          'dark',
          settings.theme === 'dark',
        );
      } else {
        // Use system preference
        const isDark = window.matchMedia(
          '(prefers-color-scheme: dark)',
        ).matches;
        document?.documentElement?.classList.toggle('dark', isDark);
      }

      setUserSettings(settings);

      toast({
        title: 'Settings saved',
        description: 'Your preferences have been updated successfully.',
      });
    } catch (error) {
      console.error('Failed to save user settings:', error);
      toast({
        title: 'Error saving settings',
        description: 'Failed to save your preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const createTenant = async () => {
    if (!newTenant.name || !newTenant.email) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await apiClient.post<TenantData>(
        '/admin/tenants',
        newTenant,
      );
      setTenants((prev) => [...prev, response.data]);
      setNewTenant({ name: '', email: '', is_active: true });
      setNewTenantDialog(false);

      toast({
        title: 'Tenant created',
        description: `Tenant "${response?.data?.name}" has been created successfully.`,
      });
    } catch (error) {
      const apiError = handleApiError(error, {
        context: 'createTenant',
        defaultMessage: 'Failed to create tenant.',
      });

      toast({
        title: 'Error creating tenant',
        description: apiError.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const createUser = async () => {
    if (!newUser.email || !newUser.password) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await apiClient.post<UserData>('/admin/users', newUser);
      setUsers((prev) => [...prev, response.data]);
      setNewUser({
        email: '',
        password: '',
        tenant_id: 1,
        is_active: true,
        is_verified: false,
      });
      setNewUserDialog(false);

      toast({
        title: 'User created',
        description: `User "${response?.data?.email}" has been created successfully.`,
      });
    } catch (error) {
      const apiError = handleApiError(error, {
        context: 'createUser',
        defaultMessage: 'Failed to create user.',
      });

      toast({
        title: 'Error creating user',
        description: apiError.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const runDatabaseMigration = async () => {
    setIsSaving(true);
    try {
      await apiClient.post('/admin/run-migrations');

      toast({
        title: 'Migration started',
        description: 'Database migration has been started in the background.',
      });

      // Refresh system health after a short delay
      setTimeout(() => void loadSystemHealth(), 2000);
    } catch (error) {
      console.error('Database migration failed:', error);
      toast({
        title: 'Migration failed',
        description: 'Failed to run database migration. Please check logs.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getHealthStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return (
          <Badge
            variant="default"
            className="bg-success/10 text-success-foreground max-w-[150px] truncate"
          >
            Healthy
          </Badge>
        );
      case 'unhealthy':
        return (
          <Badge variant="destructive" className="max-w-[150px] truncate">
            Unhealthy
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary" className="max-w-[150px] truncate">
            Unknown
          </Badge>
        );
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Manage system configuration, users, and monitor health
          </p>
        </div>
        <Button
          className="max-w-full"
          onClick={() => void loadSystemData()}
          disabled={isLoading}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>

      {/* Settings Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger
            value="preferences"
            className="flex flex-wrap items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger
            value="management"
            className="flex flex-wrap items-center gap-2"
          >
            <Users className="h-4 w-4" />
            User Management
          </TabsTrigger>
          <TabsTrigger
            value="system"
            className="flex flex-wrap items-center gap-2"
          >
            <Monitor className="h-4 w-4" />
            System & Security
          </TabsTrigger>
        </TabsList>

        {/* Preferences */}
        <TabsContent value="preferences" className="space-y-6">
          {/* Categories Management Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <Tags className="h-5 w-5" />
                Categories & GL Codes
              </CardTitle>
              <CardDescription>
                Manage hierarchical categories and GL code mappings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Configure parent-child category relationships and map them to GL
                codes for accounting integration.
              </p>
              <Button
                className="max-w-full"
                onClick={() => (window.location.href = '/categories')}
              >
                <Tags className="h-4 w-4 mr-2" />
                Manage Categories
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <Palette className="h-5 w-5" />
                Appearance
              </CardTitle>
              <CardDescription>
                Customize the appearance and behavior of the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <select
                    id="theme"
                    value={userSettings.theme}
                    onChange={(e) =>
                      saveUserSettings({
                        ...userSettings,
                        theme: e?.target?.value as UserSettings['theme'],
                      })
                    }
                    className="w-full p-2 border rounded"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="system">System</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <select
                    id="language"
                    value={userSettings.language}
                    onChange={(e) =>
                      saveUserSettings({
                        ...userSettings,
                        language: e?.target?.value,
                      })
                    }
                    className="w-full p-2 border rounded"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Input
                    id="timezone"
                    value={userSettings.timezone}
                    onChange={(e) =>
                      saveUserSettings({
                        ...userSettings,
                        timezone: e?.target?.value,
                      })
                    }
                    placeholder="UTC"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <select
                    id="currency"
                    value={userSettings.currency}
                    onChange={(e) =>
                      saveUserSettings({
                        ...userSettings,
                        currency: e?.target?.value,
                      })
                    }
                    className="w-full p-2 border rounded"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="CAD">CAD</option>
                  </select>
                </div>
              </div>

              <div className="flex flex-wrap items-center space-x-2">
                <Switch
                  id="notifications"
                  checked={userSettings.notifications}
                  onCheckedChange={(checked) =>
                    saveUserSettings({
                      ...userSettings,
                      notifications: checked,
                    })
                  }
                />
                <Label htmlFor="notifications">Enable notifications</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Management - Combines Users and Tenants */}
        <TabsContent value="management" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-wrap items-center justify-between">
                <div>
                  <CardTitle className="flex flex-wrap items-center gap-2">
                    <Users className="h-5 w-5" />
                    User Management
                  </CardTitle>
                  <CardDescription>
                    Manage user accounts and permissions
                  </CardDescription>
                </div>
                <Dialog open={newUserDialog} onOpenChange={setNewUserDialog}>
                  <DialogTrigger asChild>
                    <Button className="max-w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New User</DialogTitle>
                      <DialogDescription>
                        Add a new user to the system
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="userEmail">Email</Label>
                        <Input
                          id="userEmail"
                          type="email"
                          value={newUser.email}
                          onChange={(e) =>
                            setNewUser({ ...newUser, email: e?.target?.value })
                          }
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="userPassword">Password</Label>
                        <Input
                          id="userPassword"
                          type="password"
                          value={newUser.password}
                          onChange={(e) =>
                            setNewUser({
                              ...newUser,
                              password: e?.target?.value,
                            })
                          }
                          placeholder="••••••••"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="userTenant">Tenant ID</Label>
                        <Input
                          id="userTenant"
                          type="number"
                          value={newUser.tenant_id}
                          onChange={(e) =>
                            setNewUser({
                              ...newUser,
                              tenant_id: parseInt(e?.target?.value),
                            })
                          }
                        />
                      </div>
                      <div className="flex flex-wrap items-center space-x-2">
                        <Switch
                          id="userActive"
                          checked={newUser.is_active}
                          onCheckedChange={(checked) =>
                            setNewUser({ ...newUser, is_active: checked })
                          }
                        />
                        <Label htmlFor="userActive">Active</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        onClick={() => setNewUserDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="max-w-full"
                        onClick={() => void createUser()}
                        disabled={isSaving}
                      >
                        {isSaving ? 'Creating...' : 'Create User'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent className="overflow-hidden">
              {isLoading ? (
                <Loading text="Loading users..." />
              ) : (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div
                      key={user.id}
                      className="flex flex-wrap items-center justify-between p-4 border rounded"
                    >
                      <div>
                        <p className="font-medium">{user.email}</p>
                        <p className="text-sm text-muted-foreground">
                          Tenant: {user.tenant_id} | Created:{' '}
                          {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex flex-wrap items-center gap-2">
                        {user.is_active ? (
                          <Badge
                            variant="default"
                            className="max-w-[150px] truncate"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="max-w-[150px] truncate"
                          >
                            Inactive
                          </Badge>
                        )}
                        {user.is_verified && (
                          <Badge
                            variant="outline"
                            className="max-w-[150px] truncate"
                          >
                            Verified
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {users.length === 0 && (
                    <p className="text-center text-muted-foreground py-8">
                      No users found. Create your first user to get started.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tenants Management */}
          <Card>
            <CardHeader>
              <div className="flex flex-wrap items-center justify-between">
                <div>
                  <CardTitle className="flex flex-wrap items-center gap-2">
                    <Building className="h-5 w-5" />
                    Tenant Management
                  </CardTitle>
                  <CardDescription>
                    Manage organizational tenants and their settings
                  </CardDescription>
                </div>
                <Dialog
                  open={newTenantDialog}
                  onOpenChange={setNewTenantDialog}
                >
                  <DialogTrigger asChild>
                    <Button className="max-w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Tenant
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New Tenant</DialogTitle>
                      <DialogDescription>
                        Add a new organizational tenant
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="tenantName">Name</Label>
                        <Input
                          id="tenantName"
                          value={newTenant.name}
                          onChange={(e) =>
                            setNewTenant({
                              ...newTenant,
                              name: e?.target?.value,
                            })
                          }
                          placeholder="Organization Name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="tenantEmail">Email</Label>
                        <Input
                          id="tenantEmail"
                          type="email"
                          value={newTenant.email}
                          onChange={(e) =>
                            setNewTenant({
                              ...newTenant,
                              email: e?.target?.value,
                            })
                          }
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="flex flex-wrap items-center space-x-2">
                        <Switch
                          id="tenantActive"
                          checked={newTenant.is_active}
                          onCheckedChange={(checked) =>
                            setNewTenant({ ...newTenant, is_active: checked })
                          }
                        />
                        <Label htmlFor="tenantActive">Active</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        onClick={() => setNewTenantDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="max-w-full"
                        onClick={() => void createTenant()}
                        disabled={isSaving}
                      >
                        {isSaving ? 'Creating...' : 'Create Tenant'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent className="overflow-hidden">
              {isLoading ? (
                <Loading text="Loading tenants..." />
              ) : (
                <div className="space-y-4">
                  {tenants.map((tenant) => (
                    <div
                      key={tenant.id}
                      className="flex flex-wrap items-center justify-between p-4 border rounded"
                    >
                      <div>
                        <p className="font-medium">{tenant.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {tenant.email} | Created:{' '}
                          {new Date(tenant.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex flex-wrap items-center gap-2">
                        {tenant.is_active ? (
                          <Badge
                            variant="default"
                            className="max-w-[150px] truncate"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="max-w-[150px] truncate"
                          >
                            Inactive
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {tenants.length === 0 && (
                    <p className="text-center text-muted-foreground py-8">
                      No tenants found. Create your first tenant to get started.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* System & Security */}
        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <Activity className="h-5 w-5" />
                System Health
              </CardTitle>
              <CardDescription>
                Monitor system status and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 overflow-hidden">
              {systemHealth ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Overall Status
                      </p>
                      {getHealthStatusBadge(systemHealth.status)}
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Database</p>
                      {getHealthStatusBadge(
                        systemHealth?.services?.database.status,
                      )}
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Total Tenants
                      </p>
                      <p className="text-2xl font-bold">
                        {systemHealth?.metrics?.total_tenants}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Total Users
                      </p>
                      <p className="text-2xl font-bold">
                        {systemHealth?.metrics?.total_users}
                      </p>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    Last updated:{' '}
                    {new Date(systemHealth.timestamp).toLocaleString()}
                  </div>
                </>
              ) : (
                <Loading text="Loading system health..." />
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <Database className="h-5 w-5" />
                Database Operations
              </CardTitle>
              <CardDescription>
                Manage database migrations and maintenance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 overflow-hidden">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Warning</AlertTitle>
                <AlertDescription>
                  Database operations can affect system availability. Please
                  ensure you have proper backups before proceeding.
                </AlertDescription>
              </Alert>

              <Button
                className="max-w-full"
                onClick={() => void runDatabaseMigration()}
                disabled={isSaving}
              >
                <Database className="h-4 w-4 mr-2" />
                {isSaving ? 'Running...' : 'Run Database Migration'}
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex flex-wrap items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Configuration
              </CardTitle>
              <CardDescription>
                Manage security settings and access controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 overflow-hidden">
              <Alert>
                <Key className="h-4 w-4" />
                <AlertTitle>API Keys</AlertTitle>
                <AlertDescription>
                  API key management and authentication settings are configured
                  via environment variables for security.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-muted-foreground">
                      Enable 2FA for enhanced security
                    </p>
                  </div>
                  <Switch disabled />
                </div>

                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Session Timeout</p>
                    <p className="text-sm text-muted-foreground">
                      Automatic logout after inactivity
                    </p>
                  </div>
                  <Switch disabled />
                </div>

                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Audit Logging</p>
                    <p className="text-sm text-muted-foreground">
                      Log administrative actions
                    </p>
                  </div>
                  <Switch disabled />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
