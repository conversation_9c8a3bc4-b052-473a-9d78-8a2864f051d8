// CRITICAL: DO NOT CHANGE THIS IMPORT STRUCTURE
// index.css contains modern Tailwind CSS v4 with @theme directive
// Changing this structure can break the login page and other components
import './index.css';
import './shared/styles/accessibility.css';
import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './core/app/App';

const root = document.getElementById('root');

if (root) {
  ReactDOM.createRoot(root).render(
    <React.StrictMode>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </React.StrictMode>,
  );
} else {
  console.error('main.tsx: Root element not found!');
}
