Building in development mode

> nx run giki-ai-app:lint

Linting "giki-ai-app"...
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/auth/services/authService.ts[24m[0m
[0m  [2m218:18[22m  [31merror[39m  Replace `'Token·refresh·error·in·authService',·'authService',·error·as·Error` with `⏎······'Token·refresh·error·in·authService',⏎······'authService',⏎······error·as·Error,⏎····`  [2mprettier/prettier[22m[0m
[0m  [2m253:18[22m  [31merror[39m  Replace `'Registration·error·in·authService',·'authService',·error·as·Error` with `⏎······'Registration·error·in·authService',⏎······'authService',⏎······error·as·Error,⏎····`    [2mprettier/prettier[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/files/components/ColumnMappingModal.tsx[24m[0m
[0m  [2m387:6[22m  [33mwarning[39m  React Hook useEffect has a missing dependency: 'apiCallInProgress'. Either include it or remove the dependency array  [2mreact-hooks/exhaustive-deps[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/files/pages/EnhancedOnboardingPage.tsx[24m[0m
[0m  [2m135:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m138:28[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `SetStateAction<string>`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m491:21[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected             [2m@typescript-eslint/no-misused-promises[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/intelligence/components/RagCorpusManagement.tsx[24m[0m
[0m   [2m90:1[22m   [31merror[39m  Delete `··`                       [2mprettier/prettier[22m[0m
[0m  [2m516:58[22m  [31merror[39m  Insert `?.categorization_result`  [2mprettier/prettier[22m[0m
[0m  [2m517:33[22m  [31merror[39m  Delete `?.categorization_result`  [2mprettier/prettier[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/intelligence/services/agentService.ts[24m[0m
[0m  [2m109:1[22m  [31merror[39m  Delete `⏎⏎`  [2mprettier/prettier[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/intelligence/services/chatService.ts[24m[0m
[0m  [2m285:19[22m  [31merror[39m  Replace `·type:·string;·action:·string;·data?:·Record<string,·unknown>` with `⏎····type:·string;⏎····action:·string;⏎····data?:·Record<string,·unknown>;⏎·`  [2mprettier/prettier[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/intelligence/tools/reportGenerationTool.ts[24m[0m
[0m   [2m60:12[22m  [33mwarning[39m  '_error' is defined but never used  [2munused-imports/no-unused-vars[22m[0m
[0m  [2m120:12[22m  [33mwarning[39m  '_error' is defined but never used  [2munused-imports/no-unused-vars[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/reports/components/SpendingAnalytics.tsx[24m[0m
[0m  [2m71:1[22m  [31merror[39m  Delete `··`  [2mprettier/prettier[22m[0m
[0m  [2m77:1[22m  [31merror[39m  Delete `··`  [2mprettier/prettier[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/reports/hooks/useReports.ts[24m[0m
[0m  [2m115:9[22m  [33mwarning[39m  Unsafe assignment of an `any` value  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/reports/pages/ReportsPage.tsx[24m[0m
[0m   [2m172:67[22m  [31merror[39m    Replace `string,·unknown` with `⏎····string,⏎····unknown⏎··`                               [2mprettier/prettier[22m[0m
[0m   [2m182:1[22m   [31merror[39m    Delete `··`                                                                                [2mprettier/prettier[22m[0m
[0m   [2m221:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m222:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m222:44[22m  [33mwarning[39m  Unsafe member access .metrics on an `error` typed value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m231:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m231:47[22m  [33mwarning[39m  Unsafe member access .dimensions on an `error` typed value                                 [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m240:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m240:46[22m  [33mwarning[39m  Unsafe member access .chartType on an `error` typed value                                  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m241:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m241:46[22m  [33mwarning[39m  Unsafe member access .dateRange on an `error` typed value                                  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m242:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m242:44[22m  [33mwarning[39m  Unsafe member access .groupBy on an `error` typed value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m418:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m419:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m419:20[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m   [2m419:37[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m419:58[22m  [33mwarning[39m  Unsafe return of a value of type error                                                     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m   [2m434:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m462:7[22m   [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m   [2m462:27[22m  [33mwarning[39m  Unsafe member access .forEach on an `error` typed value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m464:17[22m  [33mwarning[39m  Computed name [dim.id] resolves to an `error` typed value                                  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m466:17[22m  [33mwarning[39m  Computed name [dim.id] resolves to an `error` typed value                                  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m471:7[22m   [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m   [2m471:24[22m  [33mwarning[39m  Unsafe member access .forEach on an `error` typed value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m472:15[22m  [33mwarning[39m  Computed name [metric.id] resolves to an `error` typed value                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m478:5[22m   [33mwarning[39m  Unsafe return of a value of type `any[]`                                                   [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m   [2m486:25[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `SetStateAction<unknown[]>`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m   [2m487:16[22m  [33mwarning[39m  '_error' is defined but never used                                                         [2munused-imports/no-unused-vars[22m[0m
[0m   [2m529:5[22m   [31merror[39m    Insert `··`                                                                                [2mprettier/prettier[22m[0m
[0m   [2m530:5[22m   [31merror[39m    Insert `··`                                                                                [2mprettier/prettier[22m[0m
[0m   [2m531:1[22m   [31merror[39m    Insert `····`                                                                              [2mprettier/prettier[22m[0m
[0m   [2m532:1[22m   [31merror[39m    Replace `····` with `··········`                                                           [2mprettier/prettier[22m[0m
[0m   [2m533:1[22m   [31merror[39m    Insert `······`                                                                            [2mprettier/prettier[22m[0m
[0m   [2m534:1[22m   [31merror[39m    Insert `··`                                                                                [2mprettier/prettier[22m[0m
[0m  [2m1377:62[22m  [33mwarning[39m  Unsafe member access .totalRecords on an `error` typed value                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1381:24[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1381:56[22m  [33mwarning[39m  Unsafe member access .metrics on an `error` typed value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1391:49[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number`               [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m1403:43[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1407:33[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1407:70[22m  [33mwarning[39m  Unsafe member access [0] on an `error` typed value                                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1418:30[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1418:64[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1421:40[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1422:44[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1424:41[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1430:44[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1434:33[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1434:70[22m  [33mwarning[39m  Unsafe member access [0] on an `error` typed value                                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1445:30[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1445:64[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1448:40[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1450:44[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1452:41[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1460:37[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1462:33[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1462:67[22m  [33mwarning[39m  Unsafe member access [0] on an `error` typed value                                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1466:33[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1466:70[22m  [33mwarning[39m  Unsafe member access [0] on an `error` typed value                                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1475:32[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1475:66[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1496:36[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1496:73[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1498:55[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1503:36[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1503:70[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1506:46[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1516:34[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1516:68[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1519:40[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1519:77[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1521:59[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1522:50[22m  [33mwarning[39m  Computed name [dim.id] resolves to an `error` typed value                                  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1526:40[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                               [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m1526:74[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m1529:50[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m1532:61[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number`               [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m1532:65[22m  [33mwarning[39m  Computed name [metric.id] resolves to an `error` typed value                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/reports/services/customReportService.ts[24m[0m
[0m  [2m315:5[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m315:17[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                              [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m315:33[22m  [33mwarning[39m  Unsafe member access .map on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m321:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m324:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m327:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m335:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m337:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m349:5[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m349:61[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m365:25[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m366:27[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m373:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m378:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m383:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m429:31[22m  [33mwarning[39m  Async arrow function 'saveCustomReport' has no 'await' expression         [2m@typescript-eslint/require-await[22m[0m
[0m  [2m454:5[22m   [33mwarning[39m  Unsafe return of a value of type `any`                                    [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m466:18[22m  [33mwarning[39m  Async arrow function 'deleteSavedCustomReport' has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m  [2m480:38[22m  [33mwarning[39m  Async arrow function 'updateSavedCustomReport' has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m  [2m591:24[22m  [33mwarning[39m  Unsafe return of a value of type `any`                                    [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/reports/services/reportService.ts[24m[0m
[0m   [2m36:10[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m479:53[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/hooks/useTransactions.ts[24m[0m
[0m   [2m79:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value       [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m82:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value       [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m86:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value       [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m87:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value       [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m88:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value       [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m141:70[22m  [33mwarning[39m  Async arrow function has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m  [2m161:75[22m  [33mwarning[39m  Async arrow function has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/pages/ReviewPage.tsx[24m[0m
[0m  [2m144:6[22m   [33mwarning[39m  React Hook useEffect has missing dependencies: 'filters' and 'toast'. Either include them or remove the dependency array  [2mreact-hooks/exhaustive-deps[22m[0m
[0m  [2m250:25[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `string`                                                    [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m305:16[22m  [33mwarning[39m  '_error' is defined but never used                                                                                        [2munused-imports/no-unused-vars[22m[0m
[0m  [2m315:5[22m   [33mwarning[39m  React Hook useCallback has a missing dependency: 'toast'. Either include it or remove the dependency array                [2mreact-hooks/exhaustive-deps[22m[0m
[0m  [2m353:14[22m  [33mwarning[39m  '_error' is defined but never used                                                                                        [2munused-imports/no-unused-vars[22m[0m
[0m  [2m401:6[22m   [33mwarning[39m  React Hook useCallback has a missing dependency: 'toast'. Either include it or remove the dependency array                [2mreact-hooks/exhaustive-deps[22m[0m
[0m  [2m420:14[22m  [33mwarning[39m  '_error' is defined but never used                                                                                        [2munused-imports/no-unused-vars[22m[0m
[0m  [2m429:6[22m   [33mwarning[39m  React Hook useCallback has a missing dependency: 'toast'. Either include it or remove the dependency array                [2mreact-hooks/exhaustive-deps[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/pages/TransactionPipeline.tsx[24m[0m
[0m  [2m169:6[22m  [33mwarning[39m  React Hook useCallback has a missing dependency: 'processStages'. Either include it or remove the dependency array  [2mreact-hooks/exhaustive-deps[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/pages/TransactionReviewPage.tsx[24m[0m
[0m  [2m200:14[22m  [33mwarning[39m  '_error' is defined but never used                                                                                                                                                                                                                    [2munused-imports/no-unused-vars[22m[0m
[0m  [2m209:6[22m   [33mwarning[39m  React Hook useCallback has unnecessary dependencies: 'toast' and 'transactions'. Either exclude them or remove the dependency array. Outer scope values like 'toast' aren't valid dependencies because mutating them doesn't re-render the component  [2mreact-hooks/exhaustive-deps[22m[0m
[0m  [2m225:14[22m  [33mwarning[39m  '_error' is defined but never used                                                                                                                                                                                                                    [2munused-imports/no-unused-vars[22m[0m
[0m  [2m250:16[22m  [33mwarning[39m  '_error' is defined but never used                                                                                                                                                                                                                    [2munused-imports/no-unused-vars[22m[0m
[0m  [2m253:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                                                                                                                                                                                             [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m255:23[22m  [33mwarning[39m  Unsafe member access .message on an `error` typed value                                                                                                                                                                                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m303:41[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected                                                                                                                                                                     [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m307:41[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected                                                                                                                                                                     [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m311:41[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected                                                                                                                                                                     [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m420:23[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected                                                                                                                                                                     [2m@typescript-eslint/no-misused-promises[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/services/exportService.ts[24m[0m
[0m  [2m215:11[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                 [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m217:12[22m  [33mwarning[39m  Unsafe member access .z on an `any` value                           [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m373:7[22m   [33mwarning[39m  Unsafe assignment of an error typed value                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m373:50[22m  [33mwarning[39m  Unsafe member access .finalY on an `error` typed value              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m442:8[22m   [33mwarning[39m  Async function 'exportTransactions' has no 'await' expression       [2m@typescript-eslint/require-await[22m[0m
[0m  [2m468:8[22m   [33mwarning[39m  Async function 'exportTransactionReport' has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/features/transactions/services/transactionService.ts[24m[0m
[0m   [2m46:18[22m  [33mwarning[39m  An interface declaring no members is equivalent to its supertype       [2m@typescript-eslint/no-empty-object-type[22m[0m
[0m   [2m49:18[22m  [33mwarning[39m  An interface declaring no members is equivalent to its supertype       [2m@typescript-eslint/no-empty-object-type[22m[0m
[0m  [2m172:70[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `Error`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m210:69[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `Error`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m249:7[22m   [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `Error`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m283:74[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `Error`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/agent/ADKAgentClient.tsx[24m[0m
[0m  [2m198:14[22m  [33mwarning[39m  'error' is defined but never used                                                  [2munused-imports/no-unused-vars[22m[0m
[0m  [2m207:29[22m  [33mwarning[39m  Async arrow function has no 'await' expression                                     [2m@typescript-eslint/require-await[22m[0m
[0m  [2m259:12[22m  [33mwarning[39m  'agentId' is defined but never used. Allowed unused args must match /^_/u          [2munused-imports/no-unused-vars[22m[0m
[0m  [2m259:29[22m  [33mwarning[39m  Async arrow function has no 'await' expression                                     [2m@typescript-eslint/require-await[22m[0m
[0m  [2m394:27[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected  [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m503:27[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected  [2m@typescript-eslint/no-misused-promises[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/agent/AudioInput.tsx[24m[0m
[0m  [2m156:15[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m162:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m162:35[22m  [33mwarning[39m  Unsafe member access .transcription on an `any` value                              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m163:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m163:36[22m  [33mwarning[39m  Unsafe member access .agent_response on an `any` value                             [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m164:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m164:32[22m  [33mwarning[39m  Unsafe member access .confidence on an `any` value                                 [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m165:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m165:39[22m  [33mwarning[39m  Unsafe member access .detected_language on an `any` value                          [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m166:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m166:29[22m  [33mwarning[39m  Unsafe member access .actions on an `any` value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m167:13[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m167:40[22m  [33mwarning[39m  Unsafe member access .processing_time_ms on an `any` value                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m173:28[22m  [33mwarning[39m  Unsafe call of a(n) `any` typed value                                              [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m173:36[22m  [33mwarning[39m  Unsafe member access .transcription on an `any` value                              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m173:78[22m  [33mwarning[39m  Unsafe member access .transcription on an `any` value                              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m185:11[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                                [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m185:26[22m  [33mwarning[39m  Unsafe member access .message on an `any` value                                    [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m305:21[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected  [2m@typescript-eslint/no-misused-promises[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/agent/ChatInterface.tsx[24m[0m
[0m   [2m90:37[22m  [33mwarning[39m  Async arrow function 'discoverAgents' has no 'await' expression                    [2m@typescript-eslint/require-await[22m[0m
[0m  [2m318:54[22m  [33mwarning[39m  Unsafe member access .audio_result on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m324:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m325:26[22m  [33mwarning[39m  Unsafe member access .audio_result on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m330:23[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                             [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m331:23[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                       [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m331:37[22m  [33mwarning[39m  Unsafe member access .substring on an `error` typed value                          [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m369:9[22m   [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`       [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m379:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m379:40[22m  [33mwarning[39m  Unsafe member access .audio_result on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m388:13[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m389:13[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m390:13[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m391:13[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m391:46[22m  [33mwarning[39m  Unsafe member access .audio_result on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m392:13[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m392:44[22m  [33mwarning[39m  Unsafe member access .audio_result on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m508:26[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                          [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m515:27[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`       [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m536:28[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                       [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m536:28[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                       [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m536:49[22m  [33mwarning[39m  Unsafe member access .slice on an `error` typed value                              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m536:61[22m  [33mwarning[39m  Unsafe member access .join on an `error` typed value                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m537:49[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                             [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m538:55[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                             [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m585:37[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                             [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m706:21[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`       [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m725:21[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`       [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m748:32[22m  [33mwarning[39m  Promise-returning function provided to attribute where a void return was expected  [2m@typescript-eslint/no-misused-promises[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/agent/SimpleChatInterface.tsx[24m[0m
[0m  [2m108:10[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m110:12[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m132:64[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m135:7[22m   [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m167:48[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m173:9[22m   [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m174:19[22m  [33mwarning[39m  Unsafe member access .message on an `any` value                               [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m180:14[22m  [33mwarning[39m  'err' is defined but never used                                               [2munused-imports/no-unused-vars[22m[0m
[0m  [2m186:55[22m  [33mwarning[39m  Async arrow function 'handleFilterCommand' has no 'await' expression          [2m@typescript-eslint/require-await[22m[0m
[0m  [2m193:55[22m  [33mwarning[39m  Async arrow function 'handleExportCommand' has no 'await' expression          [2m@typescript-eslint/require-await[22m[0m
[0m  [2m200:63[22m  [33mwarning[39m  Async arrow function 'handleBulkCategorizeCommand' has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m  [2m207:55[22m  [33mwarning[39m  Async arrow function 'handleReportCommand' has no 'await' expression          [2m@typescript-eslint/require-await[22m[0m
[0m  [2m214:55[22m  [33mwarning[39m  Async arrow function 'handleSearchCommand' has no 'await' expression          [2m@typescript-eslint/require-await[22m[0m
[0m  [2m286:14[22m  [33mwarning[39m  'err' is defined but never used                                               [2munused-imports/no-unused-vars[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/layout/AppLayout.tsx[24m[0m
[0m  [2m38:5[22m  [33mwarning[39m  React Hook useCallback has missing dependencies: 'handleMouseMove' and 'handleMouseUp'. Either include them or remove the dependency array  [2mreact-hooks/exhaustive-deps[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/ui/error-state.test.tsx[24m[0m
[0m  [2m518:38[22m  [33mwarning[39m  'index' is defined but never used. Allowed unused args must match /^_/u  [2munused-imports/no-unused-vars[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/ui/excel-table.tsx[24m[0m
[0m   [2m99:21[22m  [33mwarning[39m  Unsafe return of a value of type error                                                 [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m100:42[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number`           [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m103:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                              [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m103:45[22m  [33mwarning[39m  Unsafe return of a value of type error                                                 [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m105:26[22m  [33mwarning[39m  Unsafe spread of an error array type                                                   [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m106:26[22m  [33mwarning[39m  Unsafe spread of an error array type                                                   [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m108:14[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                              [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m123:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                              [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m124:15[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                              [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m333:41[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number | bigint`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m334:69[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number | bigint`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m375:39[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number | bigint`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m376:67[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number | bigint`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/ui/pagination.tsx[24m[0m
[0m  [2m49:5[22m   [33mwarning[39m  Unsafe return of a value of type `any[]`                                [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m94:41[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `number`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/ui/textarea.tsx[24m[0m
[0m  [2m5:18[22m  [33mwarning[39m  An interface declaring no members is equivalent to its supertype  [2m@typescript-eslint/no-empty-object-type[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/components/ui/use-toast.ts[24m[0m
[0m  [2m17:7[22m  [33mwarning[39m  'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u  [2munused-imports/no-unused-vars[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/hooks/useAccuracyMetrics.ts[24m[0m
[0m  [2m180:58[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m182:17[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m186:39[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m187:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m188:23[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m206:38[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m208:39[22m  [33mwarning[39m  Unsafe return of a value of type error     [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m218:7[22m   [33mwarning[39m  Unsafe assignment of an error typed value  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/hooks/useApi.ts[24m[0m
[0m   [2m23:39[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m28:33[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m102:35[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m162:34[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m222:36[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m405:31[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/hooks/useProgressTracking.ts[24m[0m
[0m  [2m134:11[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m156:15[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m156:51[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `string`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/services/api/apiClient.ts[24m[0m
[0m   [2m27:11[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m28:5[22m   [33mwarning[39m  Unsafe return of a value of type `any`                                        [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m   [2m28:31[22m  [33mwarning[39m  Unsafe call of a(n) `any` typed value                                         [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m   [2m28:39[22m  [33mwarning[39m  Unsafe member access .endsWith on an `any` value                              [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m29:9[22m   [33mwarning[39m  Unsafe call of a(n) `any` typed value                                         [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m   [2m29:17[22m  [33mwarning[39m  Unsafe member access .slice on an `any` value                                 [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m   [2m57:18[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m199:11[22m  [33mwarning[39m  Unsafe argument of type `any` assigned to a parameter of type `Error`         [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m222:9[22m   [33mwarning[39m  Expected non-Promise value in a boolean conditional                           [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m233:47[22m  [33mwarning[39m  Promise returned in function argument where a void return was expected        [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m233:47[22m  [33mwarning[39m  Promise executor functions should not be async                                [2mno-async-promise-executor[22m[0m
[0m  [2m295:11[22m  [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m320:7[22m   [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m408:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m412:60[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m450:13[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `number`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m466:18[22m  [33mwarning[39m  'refreshError' is defined but never used                                      [2munused-imports/no-unused-vars[22m[0m
[0m  [2m529:12[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m543:12[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m557:12[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/services/api/apiOptimization.ts[24m[0m
[0m   [2m73:34[22m  [33mwarning[39m  Promise returned in function argument where a void return was expected        [2m@typescript-eslint/no-misused-promises[22m[0m
[0m  [2m144:13[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m144:35[22m  [33mwarning[39m  Unsafe member access .forEach on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m148:44[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/services/auth/authStore.ts[24m[0m
[0m  [2m37:5[22m  [33mwarning[39m  Unsafe return of a value of type `any`          [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m74:3[22m  [33mwarning[39m  Async method 'login' has no 'await' expression  [2m@typescript-eslint/require-await[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/services/realtime/realtimeSync.ts[24m[0m
[0m   [2m17:25[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m24:23[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m61:17[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m82:12[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m164:37[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m186:14[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m193:10[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/types/api.ts[24m[0m
[0m  [2m34:18[22m  [33mwarning[39m  Unexpected any. Specify a different type  [2m@typescript-eslint/no-explicit-any[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/shared/utils/errorHandling.ts[24m[0m
[0m   [2m22:10[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m47:12[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m55:7[22m   [33mwarning[39m  Unsafe assignment of an `any` value                                           [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m   [2m92:51[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m   [2m96:50[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m100:50[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m104:66[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m108:66[22m  [33mwarning[39m  Unexpected any. Specify a different type                                      [2m@typescript-eslint/no-explicit-any[22m[0m
[0m  [2m179:40[22m  [33mwarning[39m  Unsafe member access .status on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m182:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m183:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m183:33[22m  [33mwarning[39m  Unsafe member access .status on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m184:11[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m184:35[22m  [33mwarning[39m  Unsafe member access .data on an `error` typed value                          [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m190:24[22m  [33mwarning[39m  Unsafe member access .message on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m190:55[22m  [33mwarning[39m  Unsafe member access .message on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m191:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m191:32[22m  [33mwarning[39m  Unsafe member access .message on an `error` typed value                       [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m193:22[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m194:29[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m196:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m196:32[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m197:31[22m  [33mwarning[39m  Unsafe member access .error on an `error` typed value                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m197:60[22m  [33mwarning[39m  Unsafe member access .error on an `error` typed value                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m198:9[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m198:32[22m  [33mwarning[39m  Unsafe member access .error on an `error` typed value                         [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m206:20[22m  [33mwarning[39m  Unsafe member access .errors on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m207:27[22m  [33mwarning[39m  Unsafe member access .errors on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m209:7[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m209:30[22m  [33mwarning[39m  Unsafe member access .errors on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m213:20[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m214:34[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m215:7[22m   [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m215:21[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m216:28[22m  [33mwarning[39m  Unsafe return of a value of type error                                        [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m220:7[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m220:17[22m  [33mwarning[39m  Unsafe call of a(n) `error` type typed value                                  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m220:31[22m  [33mwarning[39m  Unsafe member access .detail on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m222:17[22m  [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m223:42[22m  [33mwarning[39m  Unsafe member access .length on an `error` typed value                        [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m224:29[22m  [33mwarning[39m  Unsafe member access [1] on an `error` typed value                            [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m226:20[22m  [33mwarning[39m  Computed name [field] resolves to an `error` typed value                      [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m227:17[22m  [33mwarning[39m  Computed name [field] resolves to an `error` typed value                      [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m229:15[22m  [33mwarning[39m  Computed name [field] resolves to an `error` typed value                      [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m229:27[22m  [33mwarning[39m  Unsafe argument of type error typed assigned to a parameter of type `string`  [2m@typescript-eslint/no-unsafe-argument[22m[0m
[0m  [2m318:7[22m   [33mwarning[39m  Unsafe assignment of an error typed value                                     [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m473:7[22m   [33mwarning[39m  Unsafe return of a value of type error                                        [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m  [2m476:7[22m   [33mwarning[39m  Unsafe return of a value of type error                                        [2m@typescript-eslint/no-unsafe-return[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src/test-setup.ts[24m[0m
[0m  [2m16:5[22m  [33mwarning[39m  Unsafe assignment of an `any` value  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m[0m
[0m[4m/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/tests/setup.ts[24m[0m
[0m  [2m8:5[22m  [33mwarning[39m  Unsafe assignment of an `any` value  [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m[0m
[0m[4m/Users/<USER>



 NX   Running target lint for project giki-ai-app failed

Failed tasks:

- giki-ai-app:lint

Hint: run the command with --verbose for more details.

