import baseConfig from '../../eslint.base.config.mjs';
import nx from '@nx/eslint-plugin';
import tseslint from 'typescript-eslint';
import unusedImports from 'eslint-plugin-unused-imports';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get current directory path in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export default tseslint.config(
  ...baseConfig,
  ...nx.configs['flat/react-typescript'],

  // CRITICAL: Add ignores to prevent memory overflow
  {
    ignores: [
      '**/node_modules/**',
      '**/.vite-cache/**',
      '**/dist/**',
      '**/coverage/**',
      '**/*.lock',
      '**/bun.lock',
      '**/pnpm-lock.yaml',
      '**/package-lock.json',
      '**/test-results/**',
      '**/playwright-report/**',
      '**/.DS_Store',
      '**/vitest-report.json',
      '**/*.d.ts',
      'index.d.ts',
    ],
  },

  // Project-specific override to point to its own tsconfig.eslint.json
  {
    files: ['**/*.ts', '**/*.tsx'], // Apply to all TS/TSX files in this project
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.eslint.json'], // Point to the project-specific ESLint tsconfig
        tsconfigRootDir: __dirname, // Set tsconfigRootDir relative to this eslint.config.mjs
      },
    },
    plugins: {
      'unused-imports': unusedImports,
    },
    rules: {
      // Disable the default unused-vars rule
      '@typescript-eslint/no-unused-vars': 'off',
      // Use the unused-imports plugin instead
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      // Make linting more practical for existing codebase
      '@typescript-eslint/no-explicit-any': 'warn', // Change to warning instead of error
      '@typescript-eslint/no-unsafe-assignment': 'warn',
      '@typescript-eslint/no-unsafe-member-access': 'warn',
      '@typescript-eslint/no-unsafe-call': 'warn',
      '@typescript-eslint/no-unsafe-return': 'warn',
      '@typescript-eslint/no-unsafe-argument': 'warn',
      '@typescript-eslint/only-throw-error': 'off', // Allow throwing strings for API errors
      '@typescript-eslint/prefer-promise-reject-errors': 'off', // Allow rejecting with strings
      '@typescript-eslint/require-await': 'warn', // Make this a warning
      '@typescript-eslint/no-misused-promises': 'warn',
      '@typescript-eslint/unbound-method': 'off', // Too strict for test mocking
      '@typescript-eslint/no-empty-object-type': 'warn',
      'no-useless-escape': 'warn',
      'no-async-promise-executor': 'warn',
    },
  },

  // Special rules for test files
  {
    files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off', // Allow any in tests
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/unbound-method': 'off', // Essential for mocking
      '@typescript-eslint/require-await': 'off', // Test helpers often don't need await
    },
  }
);
