/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
// import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'; // Removed
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig({
  cacheDir: '../../node_modules/.vite/apps/giki-ai-app',

  plugins: [react()], // Removed nxViteTsPaths()

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      // Paths to libs need to go up from apps/giki-ai-app to workspace root, then into libs.
      '@giki-ai/ui': path.resolve(__dirname, '../..', 'libs/ui/src/index.ts'),
      ui: path.resolve(__dirname, '../..', 'libs/ui/src/index.ts'),
      utils: path.resolve(__dirname, '../..', 'libs/utils/src/index.ts'),
    },
  },

  // Test configuration
  test: {
    testTimeout: 60000, // 60 seconds
    hookTimeout: 10000, // 10 seconds
    globals: true,
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{ts,tsx}'],
    setupFiles: ['src/test-setup.ts', 'src/test-utils.tsx'],
    // define for import.meta.env.VITE_API_BASE_URL moved to top-level define
    css: true,
    reporters: ['default', 'json', 'junit', 'html'],
    outputFile: {
      json: './test-results/vitest-results.json',
      junit: './test-results/vitest-results.xml',
      html: './test-results/vitest-report.html',
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json-summary', 'cobertura', 'lcov'],
      reportsDirectory: './test-results/coverage/vitest',
    },
    deps: {
      // Optimizer configuration for web environment
      optimizer: {
        web: {
          include: [
            'react-resizable-panels',
            '@react-router',
            'react-router-dom',
          ],
          // Force ESM modules to be processed as CommonJS
          esbuildOptions: {
            format: 'cjs',
            target: 'es2020',
          },
        },
      },
      // Explicitly handle ESM modules
      interopDefault: true,
    },
  },

  build: {
    outDir: path.resolve(__dirname, '../../dist/apps/giki-ai-app'),
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },

  define: {
    'process.env.NODE_ENV': JSON.stringify(
      process.env.NODE_ENV || 'development',
    ),
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify(
      'http://localhost:3000',
    ),
  },
});
