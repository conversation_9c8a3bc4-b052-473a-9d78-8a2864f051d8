{"extends": "./tsconfig.json", "compilerOptions": {"composite": true, "outDir": "../../dist/out-tsc", "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "utils": ["./src/lib/utils.ts"]}, "types": ["vitest/globals", "node", "@testing-library/jest-dom"], "target": "ES2022", "lib": ["es2022", "dom", "dom.iterable", "esnext"], "skipLibCheck": true}, "include": ["src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.d.ts"]}