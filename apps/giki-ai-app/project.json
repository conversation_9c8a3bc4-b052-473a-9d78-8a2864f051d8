{"name": "giki-ai-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/giki-ai-app", "projectType": "application", "tags": [], "implicitDependencies": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-app && vitest run 2>&1 | ../../scripts/nx/log-with-limit.sh ../../logs/frontend-test.log 1000", "cwd": "{workspaceRoot}"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/serve-frontend.sh", "cwd": "{workspaceRoot}"}, "dependsOn": [{"project": "giki-ai-api", "target": "db"}], "configurations": {"production": {"command": "./scripts/nx/serve-frontend.sh production", "cwd": "{workspaceRoot}"}}}, "check-logs": {"executor": "nx:run-commands", "configurations": {"development": {"command": "./scripts/nx/check-development-logs.sh frontend", "cwd": "{workspaceRoot}"}, "production": {"command": "./scripts/nx/check-production-logs.sh frontend", "cwd": "{workspaceRoot}"}}, "defaultConfiguration": "development"}, "deploy": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/deploy-all.sh --skip-api", "cwd": "{workspaceRoot}"}, "configurations": {"production": {"command": "./scripts/nx/deploy-all.sh --skip-api", "cwd": "{workspaceRoot}"}}}}}