# Cursor IDE Multi-Agent Workspace Ignore
# Prevents workspace indexing of large log files and temporary data

# Development Server Logs (prevent exponential growth)
**/logs/
**/*.log
**/*.log.*
**/server.log
**/api.log
**/frontend.log
**/dev-server.log
**/error.log
**/access.log

# Zellij Session Data
**/.zellij/
**/zellij-logs/

# Runtime Process Data
**/.nx/workspace-data/
**/.nx/cache/
**/node_modules/.pnpm/.cache/
**/node_modules/.vite/
**/.pytest_cache/
**/.ruff_cache/
**/.mypy_cache/

# Temporary Upload Files (prevent indexing but keep in git)
apps/giki-ai-api/uploads/
apps/giki-ai-api/temp_uploads/
uploads/

# Large Build Artifacts
**/dist/
**/build/
**/.vite-cache/
**/coverage_html_report/
**/htmlcov/

# Database Files (local development)
**/*.db
**/*.sqlite
**/*.sqlite3
**/.coverage

# AI Agent Temporary Files
**/.augment/
**/.cursor-tmp/
**/.claude-tmp/
**/.roo-tmp/

# Performance Test Results
**/test-results/
**/playwright-report/
**/screenshots/

# Service Account Keys (security)
**/service-account*.json
**/*-key.json
service-accounts/

# Environment Files (security)
**/.env
**/.env.local
**/.env.production
**/.env.development

# Large Data Files
**/*.xlsx
**/*.csv
**/*.parquet
**/data/exports/