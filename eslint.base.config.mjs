// eslint.base.config.mjs
import tseslint from 'typescript-eslint';
import eslintPluginReact from 'eslint-plugin-react';
import eslintPluginReactHooks from 'eslint-plugin-react-hooks';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import nxPlugin from '@nx/eslint-plugin';
import js from '@eslint/js';

export default tseslint.config(
  // Each object/spread here is a direct argument to tseslint.config()

  // 0. Global Ignores
  {
    ignores: [
      '**/node_modules/**', '**/.nx/**', '**/dist/**',
      '**/*.config.js', '**/*.config.mjs', '**/*.config.cjs',
      '**/coverage/**',
      '**/.vitepress/cache/**', '**/.vite-cache/**',
      '**/__mocks__/**/*.js',
      // CRITICAL: Additional ignores to prevent memory overflow
      '**/*.lock',
      '**/bun.lock',
      '**/pnpm-lock.yaml',
      '**/package-lock.json',
      '**/test-results/**',
      '**/playwright-report/**',
      '**/.DS_Store',
      '**/vitest-report.json',
      '**/deps/**',
      '**/deps_temp*/**',
      '**/.pytest_cache/**',
      '**/.ruff_cache/**',
      '**/.mypy_cache/**',
      '**/temp_uploads/**',
    ],
  }, // Comma after this object

  // 1. Foundational TypeScript Configuration: Parser, Plugin, and Type-Aware Settings
  {
    files: ['**/*.ts', '**/*.tsx'],
    plugins: {
      '@typescript-eslint': tseslint.plugin,
    },
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: true,
        tsconfigRootDir: process.cwd(),
        ecmaFeatures: { jsx: true },
      },
    },
  }, // Comma after this object

  // 2. ESLint Recommended (primarily for .js files, or as a very basic layer)
  js.configs.recommended, // Comma after this config object

  // 3. TypeScript ESLint Recommended Type-Checked Rules
  ...tseslint.configs.recommendedTypeChecked, // Comma after this spread operation's result

  // 4. Custom TypeScript Overrides (after recommended)
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
    },
  }, // Comma after this object

  // 5. React Specifics for .tsx files
  {
    files: ['**/*.tsx'],
    plugins: {
      'react': eslintPluginReact,
      'react-hooks': eslintPluginReactHooks,
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: { jsx: true },
      },
    },
    rules: {
      ...eslintPluginReact.configs.recommended.rules,
      ...eslintPluginReactHooks.configs.recommended.rules,
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    },
    settings: {
      react: { version: 'detect' },
    },
  }, // Comma after this object

  // 6. Nx Plugin Specifics (applies broadly)
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    plugins: { '@nx': nxPlugin },
  }, // Comma after this object

  // 7. Configuration for React in plain JS/JSX files (Non-TypeScript)
  {
    files: ['**/*.{js,jsx}'],
    ignores: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: { jsx: true },
      },
      globals: {
        browser: true,
        es2021: true,
      },
    }, // Corrected: Comma added here
    plugins: {
      'react': eslintPluginReact,
      'react-hooks': eslintPluginReactHooks,
    },
    rules: {
      ...eslintPluginReact.configs.recommended.rules,
      ...eslintPluginReactHooks.configs.recommended.rules,
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    },
    settings: { react: { version: 'detect' } },
  }, // Comma after this object

  // 8. Prettier - Must be absolutely last
  eslintPluginPrettierRecommended
);