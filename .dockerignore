# Node modules
node_modules
**/node_modules

# Build outputs
dist
build
.next

# Development files
.env*
*.log
logs

# Git
.git
.gitignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Test files
coverage
.nyc_output

# Cache
.cache
.parcel-cache
.npm

# Temporary files
*.tmp
*.temp

# Screenshots and uploads
screenshots
apps/giki-ai-api/uploads

# Documentation
docs
*.md
!apps/giki-ai-api/README.md

# Scripts and tools
scripts

# Archive
docs/archive

# Development database
*.db
*.sqlite

# Python cache
__pycache__
*.pyc
*.pyo

# Virtual environments
.venv
env
venv

# Docker
Dockerfile*
docker-compose*

# Terraform
terraform
.terraform

# Test data
test-*
**/test_*

# Large files and assets that shouldn't be in container
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico