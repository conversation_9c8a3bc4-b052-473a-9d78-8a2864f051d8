{"name": "giki-ai-workspace", "$schema": "./node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": ".", "targets": {"deploy": {"executor": "nx:run-commands", "configurations": {"development": {"commands": ["./infrastructure/scripts/deploy-infrastructure.sh development", "nx deploy giki-ai-api", "nx deploy giki-ai-app"], "cwd": "{workspaceRoot}", "dependsOn": ["build"]}, "production": {"commands": ["./infrastructure/scripts/deploy-infrastructure.sh production", "nx deploy giki-ai-api --configuration=production", "nx deploy giki-ai-app --configuration=production"], "cwd": "{workspaceRoot}", "dependsOn": ["build"]}}, "defaultConfiguration": "development"}, "setup": {"executor": "nx:run-commands", "configurations": {"development": {"commands": ["curl -LsSf https://astral.sh/uv/install.sh | sh", "uv venv", "uv add -e './apps/giki-ai-api[dev]'", "pnpm install"]}, "production": {"commands": ["gcloud --version", "terraform --version"]}}, "defaultConfiguration": "development"}, "test:e2e": {"executor": "nx:run-commands", "configurations": {"production": {"command": "PLAYWRIGHT_BASE_URL=https://giki-ai-frontend-273348121056.us-central1.run.app pnpm exec playwright test"}, "development": {"command": "pnpm exec playwright test"}}, "defaultConfiguration": "development"}, "commit-push": {"executor": "nx:run-commands", "options": {"command": "git add . && git commit -m \"$COMMIT_MSG\" && git push origin master"}}, "playwright:install": {"executor": "nx:run-commands", "options": {"command": "pnpm exec playwright install", "cwd": "."}}}}