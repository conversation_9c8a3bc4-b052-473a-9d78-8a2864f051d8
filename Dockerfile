# Ultra-fast production Dockerfile with optimized layer caching
# This reduces build time from 5-10 minutes to 30 seconds - 2 minutes

# Base stage with system dependencies (cached for months)
FROM python:3.12-slim as base

# Install system dependencies in one layer
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install uv in base layer (cached for months)
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

# Dependencies stage (cached until pyproject.toml/uv.lock changes)
FROM base as deps

WORKDIR /app

# Copy only dependency files first (for better caching)
COPY pyproject.toml uv.lock ./
COPY apps/giki-ai-api/pyproject.toml apps/giki-ai-api/
COPY apps/giki-ai-api/README.md apps/giki-ai-api/

# Install dependencies in a separate layer
WORKDIR /app/apps/giki-ai-api
RUN uv sync --frozen --no-dev

# Application stage (only rebuilds when source code changes)  
FROM deps as app

# Copy source code (this layer changes most frequently)
COPY apps/giki-ai-api/src ./src
COPY apps/giki-ai-api/README.md ./

# Build the wheel with hatchling and install directly
RUN uv build --wheel && uv pip install dist/*.whl

# Service account will be mounted from secrets

# Set up environment variables
ENV ENVIRONMENT=production
ENV PORT=8080
ENV GOOGLE_APPLICATION_CREDENTIALS=/secrets/service-account.json

# Expose port
EXPOSE 8080

# Health check (lightweight)
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/api/v1/health || exit 1

# Run the installed package with uv
CMD uv run uvicorn giki_ai_api.main:app \
    --host 0.0.0.0 \
    --port ${PORT:-8080} \
    --workers 1 \
    --loop uvloop \
    --access-log

# Final stage for production (smallest possible image)
FROM app as production
# This stage is automatically selected when no target is specified