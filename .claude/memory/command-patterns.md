# Command Patterns - Shared Implementation Templates

## Context Awareness Pattern
```python
def analyze_context():
    # Read recent conversation for user concerns
    recent_context = get_conversation_context()
    
    # Check for specific area of concern
    if area_of_concern:
        filter_by_area(area_of_concern)
    
    # Prioritize based on:
    # 1. User-mentioned issues (immediate pain)
    # 2. Performance violations (compliance)
    # 3. Requirements gaps (missing features)
    # 4. Existing active tasks
```

## Task Generation Pattern
```python
def generate_task(finding_type, details):
    task = {
        "id": f"TASK-{finding_type}-{timestamp}",
        "priority": calculate_rice_score(finding_type),
        "requirements": link_to_requirements(details),
        "evidence": capture_evidence(details),
        "acceptance_criteria": define_success(details)
    }
    append_to_required_tasks(task)
    update_workflow_state(task)
```

## Parallel Execution Pattern
```python
def execute_parallel_work():
    # Batch discovery
    all_files = glob("**/*.{py,ts}", "tests/**/*")
    read_batch(all_files)
    
    # Parallel validation
    tasks = [
        Task("Lint check", "pnpm lint"),
        Task("Test run", "pnpm test"),
        Task("Build", "pnpm build")
    ]
    execute_parallel(tasks)
    
    # Batch modifications
    multi_edit(file, all_changes)
```

## Testing Integration Pattern
```python
def continuous_validation():
    # Run tests while developing
    while implementing:
        test_affected_code()
        capture_screenshots()
        measure_performance()
        validate_requirements()
```

## Documentation Update Pattern
```python
def update_living_docs():
    # Update on every change
    update_functional_status(feature_completion)
    update_quality_metrics(test_coverage)
    update_performance_metrics(response_times)
    update_audit_history(findings)
```

## Regression Testing Pattern
```python
def test_previous_issues():
    # Check customer-identified issues
    issues = read_audit_history()
    for issue in issues:
        if issue.fixed:
            verify_still_fixed(issue)
        else:
            check_if_resolved(issue)
```

## Deployment Pattern
```python
def smart_deployment():
    # Pre-flight checks
    if not all_quality_gates_pass():
        block_deployment()
    
    # Parallel deployment tasks
    tasks = [
        update_documentation(),
        run_final_tests(),
        prepare_deployment()
    ]
    execute_parallel(tasks)
```

**Usage**: Reference these patterns from command files instead of duplicating code