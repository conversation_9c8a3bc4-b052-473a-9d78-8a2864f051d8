# Testing Strategy

## Test Hierarchy
1. **E2E Tests** (Playwright): Customer journey simulation
2. **Integration Tests**: API endpoints and data flow
3. **Unit Tests**: Components, services, models

## Test Execution Commands
```bash
# All tests
pnpm test           # All tests (nx run-many)

# Specific test suites
pnpm test:api       # Backend pytest tests
pnpm test:app       # Frontend Vitest tests (when implemented)
pnpm test:e2e       # Playwright comprehensive test

# Individual backend tests
pnpm nx test giki-ai-api
```

## E2E Test Strategy
- **Single Comprehensive File**: `tests/e2e/the-one-e2e-test.spec.ts`
- **Incremental Growth**: Add scenarios as features are implemented
- **Customer Simulation**: Complete user workflows from registration to reporting
- **Real Data**: Uses actual test files (Capital One.xlsx, Credit Card.xlsx)
- **Comments First**: Document intended tests, implement progressively

## Test Structure Pattern
```typescript
test.describe('Complete System E2E Tests', () => {
  // 1. Authentication & Registration
  test('New user registration flow', async ({ page }) => { /* implemented */ });
  
  // 2. Complete Onboarding Flow  
  test.todo('Full onboarding with historical data upload');
  
  // 3. Production Usage Flow
  test('Upload new transactions', async ({ page }) => { /* partially implemented */ });
});
```

## Testing Standards
- **Write Test First**: Test → Run → Fix → Commit only when passing
- **Real Data Testing**: Use actual Excel files for integration tests
- **Performance Testing**: API <200ms, Frontend <2s load times
- **Accessibility**: axe-core integration for a11y compliance

## Test Coverage Targets
- **Backend**: 80%+ (currently 11%)
- **Frontend**: 70%+ (currently 0%)
- **E2E**: 100% critical paths passing

## Quality Gates (Must Pass Before Commit)
- [ ] Zero linting errors
- [ ] All tests passing
- [ ] Coverage >80% for new code
- [ ] Type-safe (no 'any' types)
- [ ] Performance validated

## Test File Organization
```
tests/
├── e2e/
│   └── the-one-e2e-test.spec.ts    # Comprehensive customer test
├── integration/
│   ├── test_api_simple.py          # API integration tests
│   └── test_auth_integration.py    # Auth flow tests
└── unit/                           # Component/service unit tests
```

## Debugging Test Failures
1. **Stop & Analyze**: Don't immediately fix - investigate comprehensively
2. **System Context**: Check logs, performance metrics, database state
3. **Customer Impact**: Map failures to actual user journey disruptions
4. **Root Cause**: Identify underlying architectural issues
5. **Strategic Solution**: Fix causes not symptoms with measurable improvements