# Quick Troubleshooting

## Common Issues & Solutions

### Server Issues
**Servers won't start**:
```bash
pnpm serve:stop && pnpm serve:restart
```

**Process overload (>20 node/python processes)**:
```bash
pnpm serve:stop
./scripts/kill-all-processes.sh
pnpm serve
```

**Port conflicts**:
```bash
lsof -i :8000  # Check API port
lsof -i :4200  # Check frontend port
kill $(lsof -t -i:8000)  # Kill process on port
```

### Database Issues
**Connection failures**:
```bash
# Check database connection
lsof -i :5432

# Restart with clean state
pnpm serve:restart
```

**Performance issues (>10s responses)**:
```bash
# Check logs for slow queries
tail -f logs/api-server.log | grep "SLOW REQUEST"
```

### Authentication Errors
**JWT Token Expired**:
- Clear browser localStorage and re-login

**401 Unauthorized**:
- Verify Bearer token format
- Check CORS configuration
- Validate OAuth2 form encoding

### CORS Errors
```bash
# Verify CORS configuration
grep CORS_ALLOWED_ORIGINS apps/giki-ai-api/src/giki_ai_api/core/config.py
```

### Test Failures
**E2E tests failing**:
```bash
pnpm exec playwright test --debug  # Debug specific test
```

**Integration tests failing**:
```bash
pnpm test:api --verbose  # Detailed test output
```

### Build Issues
**Out of memory**:
```bash
pnpm build --parallel=1  # Reduce parallel builds
```

**TypeScript errors**:
```bash
rm -rf .nx/cache && pnpm build  # Clear cache and rebuild
```

### Lint Issues
**Frontend warnings**:
- Focus on critical errors first
- Use ESLint auto-fix: `pnpm lint:app --fix`

**Backend warnings**:
- Usually import sorting or unused variables
- Use ruff auto-fix in Python files

### Playwright MCP Issues
**Chrome profile locked**:
```bash
./scripts/playwright-stop.sh
rm -rf .playwright-mcp/user-data/Singleton*
./scripts/playwright-server.sh
```

**Server won't start (port 3100)**:
```bash
lsof -i :3100
kill $(lsof -t -i:3100)
```

## Emergency Recovery
```bash
# Full system reset
rm -rf node_modules .nx/cache
pnpm install
pnpm serve

# Database reset (if needed)
uv run scripts/database/reset-local.py
```

## Performance Debug
```bash
# API timing
time curl http://localhost:8000/api/v1/health

# Database query timing
tail -f logs/api-server.log | grep "SLOW REQUEST"
```

## Log Locations
- **API logs**: `logs/api-server.log`
- **Frontend logs**: `logs/frontend-server.log`
- **E2E test logs**: `test-results/`
- **Database logs**: Check Cloud Console for production

## Health Check URLs
- **Local API**: http://localhost:8000/health
- **Local Frontend**: http://localhost:4200/health  
- **Production API**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health
- **Production Frontend**: https://giki-ai-app-6uyufgxcxa-uc.a.run.app/health