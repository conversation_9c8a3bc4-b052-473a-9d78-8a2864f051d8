# Common Commands

**🚨 CRITICAL WORKSPACE RULE 🚨**
**ALWAYS work from workspace root: /Users/<USER>/giki-ai-workspace**
**NEVER cd into subdirectories (apps/giki-ai-api, apps/giki-ai-app, etc.)**
**This is an NX monorepo - ALL commands run from root!**

## Development
```bash
# Check current work
cat @docs/tasks/active-tasks.md    # ALWAYS check first

# Development servers (NX orchestrated)
pnpm serve          # Start both servers with dependency management
pnpm serve:status   # Check if servers are running  
pnpm serve:stop     # Stop all servers
pnpm serve:restart  # Restart servers

# Individual services (FROM WORKSPACE ROOT)
pnpm serve:api      # Backend only (:8000) via nx
pnpm serve:app      # Frontend only (:4200) via nx
# Alternative if script issues: pnpm -w run serve:api

# Testing & Quality
pnpm lint       # MUST pass before commit (nx run-many)
pnpm test       # Run all tests (nx run-many)
pnpm test:e2e   # Run Playwright E2E tests (customer simulation)

# Commit & Push (after every meaningful change)
git add .
git commit -m "description"
git push

# Deployment
pnpm deploy:dev  # Deploy to development environment
pnpm deploy:prod # Deploy to production environment
```

## Package Management (MONOREPO CRITICAL)
**ALWAYS add packages from workspace root!**

### Adding TypeScript/JavaScript Packages
```bash
# ALL packages go to workspace root - NO EXCEPTIONS!
pnpm add react -w                    # Production dependency
pnpm add -D @types/react -w         # Dev dependency

# NEVER do any of these:
# pnpm add --filter giki-ai-api       # WRONG! No app-specific packages
# cd apps/giki-ai-app && npm install  # WRONG! Never cd into apps
# pnpm add without -w flag            # WRONG! Always use -w flag
```

### Adding Python Packages
```bash
# From workspace root ONLY
uv add fastapi                       # Add to root pyproject.toml
uv add --dev pytest                  # Dev dependency

# NEVER do this:
# cd apps/giki-ai-api && pip install  # WRONG!
# cd apps/giki-ai-api && uv add      # WRONG!
```

## Package Execution (pnpm vs npx)
**ALWAYS use pnpm instead of npx**:
- `pnpm exec` - For packages already installed in the project (faster)
- `pnpm dlx` - For one-time execution of packages not in project

```bash
# Examples with installed packages (use pnpm exec)
pnpm exec playwright test     # Instead of: npx playwright test
pnpm exec nx build           # Instead of: npx nx build

# Examples for one-time use (use pnpm dlx)
pnpm dlx create-react-app    # Instead of: npx create-react-app
```

## Python Script Execution
**REQUIRED**: Use `uv run` for all Python scripts FROM WORKSPACE ROOT
```bash
uv run scripts/test-auth.py              # Correct
# NEVER: cd apps/giki-ai-api && uv run   # Wrong!
```

## Database Access (FASTEST - MCP Servers)
**Use MCP servers instead of Python scripts for instant database queries!**
```bash
# Development Database
mcp__postgres-dev__query               # Query local database
mcp__postgres-dev__list_tables         # List all tables
mcp__postgres-dev__describe_table      # Get table structure

# Production Database (READ ONLY)
mcp__postgres-prod__query              # Query production database
mcp__postgres-prod__list_tables        # List production tables
mcp__postgres-prod__describe_table     # Get production table structure

# Example: Check test users
# Use: mcp__postgres-dev__query with SQL: SELECT * FROM users WHERE email LIKE '%@test.local'
```


## Enhanced Slash Commands (giki.ai specific)
```bash
/deploy [prod]    # Deploy with health verification
/health [local]   # Comprehensive system health check
/test [e2e|api|coverage]  # Run test suites with options
/setup [verify]   # Environment setup and verification
```

## Reference Files
**Development Workflow**: @.claude/memory/development-workflow.md
**Testing Strategy**: @.claude/memory/testing-strategy.md
**Deployment Process**: @.claude/memory/deployment-process.md
**Quick Troubleshooting**: @.claude/memory/troubleshooting-quick.md
**Claude Code Tips**: @.claude/memory/claude-code-tips.md

## Prompt Management Commands
```bash
# Review and manage AI prompts
uv run scripts/review-prompts.py list                    # List all prompts
uv run scripts/review-prompts.py show <prompt_id>        # Show specific prompt
uv run scripts/review-prompts.py markdown               # Export for review
uv run scripts/review-prompts.py langfuse               # Prepare for Langfuse

# Examples
uv run scripts/review-prompts.py list --category schema_interpretation
uv run scripts/review-prompts.py show schema_interpretation_main
uv run scripts/review-prompts.py markdown --output docs/prompts-review.md
```

## Additional Commands
See @package.json for all available npm scripts
See @scripts/ directory for workflow and utility scripts