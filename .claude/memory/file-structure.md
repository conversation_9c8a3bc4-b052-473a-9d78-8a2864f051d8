# File Structure

**Modify-First Philosophy**: Always search for existing files to enhance before creating new ones. Remove redundant files when discovered. Consolidate fragmented information.

## Documentation Structure

See @docs/README.md for complete documentation structure rules

**CRITICAL**: Modify existing files first. The docs/ folder has exactly 18 files - modify these rather than create new ones

### Documentation Rules (18 Files Exactly)
1. **Use living documents** - Update existing files with current date metadata
2. **Update in place** - Modify existing documents rather than create summaries
3. **Make permanent** - Edit existing files to keep them current and complete
4. **Follow structure** - Use only the files defined in @docs/README.md
5. **Organize by purpose** - Keep docs/ root empty except README.md

## Code Structure

See @docs/specifications/system-design-spec.md for architecture details
See @docs/specifications/frontend-spec.md for frontend structure
See @docs/specifications/contracts-spec.md for API structure

### Required Files Per Domain
**Backend Domain** must have:
- `__init__.py` - Package marker
- `router.py` - API endpoints (one only)
- `service.py` - Business logic (one only)
- `schemas.py` - Request/response models
- `models.py` - Database models (if needed)

**Frontend Feature** must have:
- `index.ts` - Public exports
- `pages/` - Page components
- `components/` - Feature components
- `hooks/` - React hooks (if needed)
- `services/` - API calls (if needed)
- `types/` - TypeScript types

## Modify-First Patterns
1. **Evolve existing files**: Improve `customer_agent.py` directly (not create `customer_agent_v2.py`)
2. **Clean during editing**: Remove `*_raw.py`, `*.backup`, `*.bak` when found
3. **Organize tests properly**: Move any scripts/test_* files to tests/ directories
4. **Integrate examples**: Move demo code into main implementation or remove
5. **Enhance services**: Extend existing service.py files with new functionality
6. **Consolidate experiments**: Merge poc/ or experiments/ content into main code, then remove folders

## File Naming Conventions
- **Python**: `snake_case.py` (modules), `PascalCase` (classes)
- **TypeScript**: `PascalCase.tsx` (components), `camelCase.ts` (utils)
- **Tests**: `test_*.py` (Python), `*.test.ts` (TypeScript), `*.spec.ts` (E2E)
- **Services**: `*Service.ts` or `*_service.py`
- **Schemas**: `schemas.py` (backend), `types/index.ts` (frontend)