# Quality Standards

See @docs/tasks/quality-gates.md for task state management and definition of done
See @docs/audits/quality-metrics.md for current quality metrics and test coverage
See @docs/audits/performance-metrics.md for performance baselines and targets

## Language Standards
**FORBIDDEN**: 
- NO emojis, informal language, exclamation marks
- NO AI personality, filler words, unnecessary enthusiasm
- NO "Let me", "I'll help you", "Great!", etc.

**REQUIRED**: 
- Formal technical language
- Direct and factual communication
- Professional terminology only

## Process Management
**CRITICAL: Process Management**
- **ALWAYS** run `pnpm cleanup:processes` if you see >20 node/python processes
- **NX daemon disabled** to prevent process spawning
- **Build parallelism limited** to prevent resource exhaustion
- **MCP servers duplicated** across terminals - normal behavior

## Test Data Locations
- Essential: `data/input_files/` (Capital One.xlsx, Credit Card.xlsx, etc.)
- Fixtures: `tests/fixtures/`
- Logs: `logs/` (auto-rotated after 3 days)