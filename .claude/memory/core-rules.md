# Core Development Rules (Quick Reference)

## Essential Rules (Top 12)
**#1** NEVER LEAVE WORKSPACE ROOT - This is an NX monorepo, ALL commands run from /Users/<USER>/giki-ai-workspace
**#2** THOUGHTFUL FILE ORGANIZATION - NEVER dump files in root! Always place in proper directory structure
**#3** Two-Layer Workflow - Every task gets both high-level tracking AND detailed breakdown
**#4** Check Current Work - `cat docs/tasks/active-tasks.md` FIRST, then check breakdowns
**#5** Fix Root Causes - No workarounds, improve underlying systems
**#6** Production-First - Build in apps/, test with real data
**#7** Zero Warnings - `pnpm lint` must pass before commit
**#8** Batch Operations - Parallel execution, never sequential
**#9** Test Everything - Write → Run → Fix → Commit when passing
**#10** Frequent Commits - Every 15-30 minutes with meaningful messages
**#11** Customer Focus - Every change improves user experience
**#12** Ship Quality - Production-grade only, no shortcuts/TODOs
**#13** Centralized Prompts - All AI prompts managed through registry with performance tracking

## Quick Reference Links
**Detailed Workflow**: @.claude/memory/development-workflow.md
**Testing Strategy**: @.claude/memory/testing-strategy.md
**Deployment Process**: @.claude/memory/deployment-process.md
**Quick Troubleshooting**: @.claude/memory/troubleshooting-quick.md
**Prompt Management**: @.claude/memory/prompt-management.md

## Architecture Rules
**Backend Pattern**: `domains/*/models.py|service.py|agent.py`
**Frontend Pattern**: `features/*/components/|hooks/|pages/|services/`
**No Fallbacks**: Fix root causes, not symptoms
**Single E2E Test**: One comprehensive file that grows incrementally

## AI Development Rules
**ADK Compliance**: All agents inherit from `StandardGikiAgent` (Google ADK v1.3.0)
**A2A Protocol**: Agent cards for discovery, opaque communication
**Cognitive Efficiency**: 2-3 tools max per agent, purpose-aligned
**Centralized Prompts**: Use `get_prompt_registry()` for all AI prompts
**Performance Tracking**: Automatic latency and success measurement
**Financial Reliability**: 100% accuracy requirements for financial operations

## File Organization Rules
**CRITICAL: Think before creating/modifying ANY file!**

### Proper File Locations:
- **Scripts**: `scripts/` (workflow automation) or `scripts/nx/` (NX-specific)
- **Documentation**: `docs/` with proper subdirectory (never in root)
- **Config Files**: Root ONLY if required (package.json, nx.json, etc.)
- **Test Data**: `test-files/` or `test_data/` (not scattered)
- **Infrastructure**: `infrastructure/` with environment subdirs
- **Application Code**: `apps/giki-ai-api/` or `apps/giki-ai-app/`
- **Shared Code**: `apps/*/src/shared/` (not in root)
- **Screenshots**: `screenshots/` with descriptive names
- **Logs**: `logs/` (auto-cleaned, never commit)

### FORBIDDEN:
- ❌ Random files in workspace root
- ❌ Test files scattered across directories
- ❌ Config files without clear purpose
- ❌ Duplicate functionality in different locations
- ❌ Files with generic names (test.py, data.json, etc.)

## Quality Gates (Two-Layer)
**Before Marking Subtasks Complete:**
- [ ] Specific subtask objective achieved
- [ ] Quality gates pass for that subtask
- [ ] Evidence captured (screenshots, logs, metrics)
- [ ] Progress updated in breakdown file

**Before Marking Tasks Complete:**
- [ ] ALL subtasks marked complete in breakdown
- [ ] `pnpm lint` passes (zero warnings)
- [ ] All tests passing
- [ ] E2E customer simulation works
- [ ] Deployed and verified in production
- [ ] Documentation updated
- [ ] Breakdown archived with completion evidence

## Efficiency Rules
- **Batch Operations**: Parallel execution reduces time 50-70%
- **Search Patterns**: `domains/*/{models,service,agent}.py` together
- **File Operations**: Read multiple related files together
- **Tool Calls**: Multiple operations in single call when possible

## File & Documentation Standards
- **Max Size**: 500 lines per file (1000 absolute max)
- **Documentation**: Exactly 19 files in docs/ (enforced structure)
- **Breakdown Directory**: docs/tasks/breakdowns/ for all task decomposition
- **No Versioning**: Never create v2, new, improved versions
- **Cross-References**: Always use @ import syntax
- **Task Naming**: TASK-YYYYMMDD-### format with consistent breakdown files