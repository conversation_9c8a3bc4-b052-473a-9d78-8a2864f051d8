# Deployment Process

## Deployment Commands
```bash
# Development deployment
pnpm deploy:dev     # Deploy to development environment

# Production deployment
pnpm deploy:prod    # Deploy to production environment

# Individual service deployment
pnpm deploy:dev:api   # API only to development
pnpm deploy:dev:app   # Frontend only to development
pnpm deploy:prod:api  # API only to production
pnpm deploy:prod:app  # Frontend only to production
```

## Pre-Deployment Checklist
- [ ] `pnpm lint` passes (zero warnings)
- [ ] `pnpm test:e2e` passes (customer simulation)
- [ ] All commits pushed to master
- [ ] Performance targets met (<200ms API, <2s frontend)
- [ ] Database migrations applied (if any)

## Deployment Architecture
**Target Platform**: Google Cloud Run
**Registry**: Google Artifact Registry (`us-central1-docker.pkg.dev/rezolve-poc/giki-ai/`)
**Services**:
- API: `giki-ai-api` (FastAPI + uvicorn)
- Frontend: `giki-ai-frontend` (React + nginx)

## CI/CD Pipeline
1. **Trigger Check**: Deployment conditions verified
2. **Build Images**: Multi-stage Docker builds
3. **Push to Registry**: Artifact Registry storage
4. **Deploy to Cloud Run**: Container deployment
5. **Health Checks**: Endpoint verification
6. **Post-Deploy Tests**: Service accessibility validation

## Environment Configuration
**Development**:
- API: Development database and secrets
- Frontend: Points to development API
- Reduced resource limits

**Production**:
- API: Production Cloud SQL and Secret Manager
- Frontend: Points to production API
- Full resource allocation and scaling

## Production URLs
- **API**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
- **Frontend**: https://giki-ai-app-6uyufgxcxa-uc.a.run.app

## Deployment Verification
```bash
# Health check endpoints
curl https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health
curl https://giki-ai-app-6uyufgxcxa-uc.a.run.app/health

# Performance verification
time curl https://giki-ai-api-6uyufgxcxa-uc.a.run.app/api/v1/health
```

## Rollback Process
1. **Identify Issue**: Performance degradation or errors
2. **Quick Rollback**: Revert to previous container image
3. **Code Fix**: Address root cause
4. **Re-deploy**: Test and deploy fixed version

## Service Account Requirements
**API Service Account**: `<EMAIL>`
- Vertex AI access
- Cloud SQL client
- Secret Manager accessor
- Cloud Storage access

**Firebase Admin**: `<EMAIL>`
- Firebase Hosting deployment

## Security Best Practices
- Service account keys in `.gitignore`
- Secrets in Google Secret Manager
- Environment-specific configurations
- HTTPS-only communication
- CORS properly configured