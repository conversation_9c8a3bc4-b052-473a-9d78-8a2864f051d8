# File Management Quick Reference

**Complete details**: @.claude/memory/development-rules.md Rules #30-32

## Critical Requirements
- **Read files**: 1000+ line chunks (complete reading)
- **File size limits**: Max 1000 lines absolute, target 500 lines
- **Batch operations**: Read related files together

## Quick Commands
```bash
# Check file sizes
find . -name "*.{py,ts,md}" -exec wc -l {} + | sort -nr | head -20

# Size violations
scripts/check-file-sizes.sh
```

**All implementation details**: @.claude/memory/development-rules.md