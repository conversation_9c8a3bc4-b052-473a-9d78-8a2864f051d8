# Claude Code Interactive Mode Tips

## Keyboard Shortcuts
**Essential Shortcuts**:
- `Ctrl+C` - Cancel current generation
- `Ctrl+D` - Exit Claude Code session
- `Ctrl+L` - Clear terminal screen
- `Up/Down arrows` - Navigate command history

**Multiline Input**:
- `\` + `Enter` - Quick multiline escape (works everywhere)
- `Option+Enter` - macOS default multiline
- `Shift+Enter` - After running `/terminal-setup`

## Memory Management Tips
**Quick Memory Addition**:
- Start line with `#` to quickly add to memory
- Use `/memory` to edit memory files directly
- Use `/init` to bootstrap project CLAUDE.md

**Memory Organization**:
- Keep instructions specific (e.g., "Use 2-space indentation")
- Group related memories under headings
- Periodically review and update
- Use @ syntax for file imports

## Efficient Tool Usage
**Search Patterns**:
- Use parallel searches: `domains/*/{models,service,agent}.py`
- Batch file operations when possible
- Use Task tool for complex searches

**Command History**:
- Commands stored per working directory
- Use arrow keys to navigate history
- Double `Esc` to edit previous messages

## Session Management
**Resume Features**:
- `--continue` to resume recent conversation
- `--resume` to select from conversation history
- All conversations saved locally with full history

**Extended Thinking**:
- Use "think" for basic extended thinking
- Use "think harder" or "think more" for complex problems
- Best for architectural decisions and debugging

## Slash Commands
**Built-in Commands**:
- `/add-dir` - Add working directories
- `/clear` - Clear conversation history
- `/review` - Request code review
- `/model` - Select AI model
- `/memory` - Edit memory files
- `/init` - Initialize project guide
- `/bug` - Report issues

**Custom Commands** (giki.ai specific):
- `/deploy [prod]` - Deploy with health checks
- `/health [local]` - System health verification
- `/test [e2e|api|coverage]` - Run test suites
- `/setup [verify]` - Environment setup

## Performance Tips
**Efficient Workflows**:
- Start with `cat docs/tasks/active-tasks.md`
- Use batch operations for related changes
- Leverage MCP servers for specialized tasks
- Use @ references instead of duplicating content

**Token Optimization**:
- Keep CLAUDE.md focused on daily essentials
- Use focused memory files (<100 lines each)
- Reference detailed information instead of duplicating
- Use clear, specific instructions

## Integration Best Practices
**File References**:
- Use @ syntax for file imports: `@path/to/file.md`
- Maximum import depth of 5 hops
- Supports recursive memory discovery

**Image Handling**:
- Drag and drop images into Claude Code
- Paste with Ctrl+V
- Provide file paths for analysis
- Use for UI debugging and design analysis

## Development Workflow Integration
**Git Integration**:
- Claude Code can search git history
- Can create commits and PRs
- Use for code review assistance

**Output Formats**:
- `--output-format text` (default)
- `--output-format json` for structured data
- `--output-format stream-json` for real-time

## Common Issues & Solutions
**Profile Locked**: Remove singleton files and restart
**Port Conflicts**: Use `lsof` to find and kill processes
**Memory Overload**: Keep focused files, use @ references
**Slow Performance**: Reduce context size, batch operations