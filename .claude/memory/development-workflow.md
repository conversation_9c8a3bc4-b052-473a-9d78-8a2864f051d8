# Development Workflow

**🚨 WORKSPACE ROOT RULES 🚨**
1. **ALL commands must be run from /Users/<USER>/giki-ai-workspace**
2. **NEVER cd into subdirectories - NX monorepo requires root execution!**
3. **ALL packages added with `pnpm add -w` or `uv add` from root only!**
4. **NO individual app package.json dependencies - everything hoisted to root!**

## Daily Development Flow (Two-Layer Workflow)
1. **Check Current Work**: `cat docs/tasks/active-tasks.md` (ALWAYS first)
2. **Check Breakdown Status**: Review active breakdowns for incomplete subtasks
3. **Start Servers**: `pnpm serve` (NX orchestrated with dependencies)
4. **Verify Health**: `pnpm serve:status && pnpm lint`
5. **Select Task/Subtask**: Choose from active breakdowns or create new breakdown
6. **Develop**: Make changes, test locally, update breakdown progress
7. **Quality Check**: `pnpm lint && pnpm test:e2e`
8. **Commit**: Every 15-30 minutes with meaningful messages and task reference
9. **Deploy**: `pnpm deploy:dev` then `pnpm deploy:prod`

## Core Development Rules (Two-Layer Enhanced)
- **Two-Layer Workflow**: Every task needs high-level tracking AND detailed breakdown
- **Production-First**: Build directly in `apps/giki-ai-api`, `apps/giki-ai-app`
- **Search Before Creating**: Use Grep/Glob/Task tools to find existing patterns
- **Fix Root Causes**: Not symptoms (improve RAG, don't add workarounds)
- **Act Immediately**: See problem → Create breakdown → Fix → Deploy → Verify
- **Test Everything**: Write test → Run test → Fix → Commit when passing
- **Subtask Progress**: Update breakdown files as subtasks are completed
- **Quality Gates**: Each subtask has verification steps before marking complete

## NX Server Management (FROM WORKSPACE ROOT)
```bash
pnpm serve          # Start with dependency management (API needs DB)
pnpm serve:status   # Check if running
pnpm serve:stop     # Stop all servers
pnpm serve:restart  # Restart if needed
pnpm serve:api      # Start API only (alternative: pnpm -w run serve:api)
pnpm serve:app      # Start frontend only
```

## Architecture Patterns
**Backend**: `domains/*/models.py|service.py|agent.py` pattern
**Frontend**: `features/*/components/|hooks/|pages/|services/` pattern
**Shared Code**: 
- Backend: `apps/giki-ai-api/src/giki_ai_api/shared/`
- Frontend: `apps/giki-ai-app/src/shared/`

## Quality Standards
- **Zero Warnings**: `pnpm lint` must pass before commit
- **File Size**: Maximum 500 lines per file
- **Frequent Commits**: Every 15-30 minutes, push immediately
- **Python Tools**: Always use `uv run scripts/[name].py` FROM WORKSPACE ROOT
- **Testing**: Backend via pytest, Frontend via Vitest, E2E via Playwright

## File Organization
- **Documentation**: Core structure in `docs/` with milestone-driven subdirectories allowed
- **No Versioning**: Never create v2, new, improved versions
- **No Experiment Folders**: No poc/, temp/, old/ directories
- **Clean Up**: Delete unused code immediately when discovered

## Customer Journey Focus (Breakdown-Driven)
1. **Onboarding Journey**: Upload historical data WITH categories (AI training)
   - Each step gets subtask breakdown for systematic implementation
2. **Production Journey**: Upload new data WITHOUT categories (auto-categorization)
   - Performance and accuracy tracked through breakdown validation
3. **Issue Resolution**: Customer problems get immediate breakdown creation
   - Root cause analysis through systematic subtask investigation

## Efficiency Rules
- **Batch Operations**: Never sequential - parallel execution reduces time 50-70%
- **Tool Call Batching**: Multiple operations in single call
- **Parallel Task Execution**: Launch multiple agents for independent work
- **Search Patterns**: `domains/*/{models,service,agent}.py` together