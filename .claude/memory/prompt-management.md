# Prompt Management System - Memory Documentation

---
status: active
last-updated: 2025-06-27
update-frequency: as-needed
update-triggers: prompt changes, performance improvements, Langfuse integration
---

## Overview

We have implemented a comprehensive centralized prompt management system that is fully compatible with our Google ADK v1.3.0 and A2A v0.2.2 architecture. This system provides version control, performance tracking, and easy review capabilities for all AI prompts.

## Architecture Integration

### ADK v1.3.0 Compatibility ✅
- **Tool Integration**: Prompts are consumed by `LongRunningFunctionTool` instances
- **Agent Pattern**: Used within `StandardGikiAgent` subclasses like `SchemaInterpretationAgent`
- **Performance Tracking**: Automatic latency and success measurement for ADK tool functions
- **Cognitive Efficiency**: Supports minimal tool assignment principle (2-3 tools max per agent)

### A2A v0.2.2 Compliance ✅
- **Agent Cards**: Prompt versions visible in agent capability discovery
- **Version Control**: Enables opaque agent communication with versioned prompts
- **Provider Independence**: Prompts work with any LLM provider (Anthropic, OpenAI, Google)
- **Financial Reliability**: Supports 100% accuracy requirements for financial operations

## Key Components

### 1. Centralized Prompt Registry
**Location**: `apps/giki-ai-api/src/giki_ai_api/shared/ai/prompt_registry.py`

**Features**:
- Version control for all prompts
- Performance tracking (latency, success rates)
- Category organization (schema_interpretation, categorization, etc.)
- Easy prompt modification without code changes
- Statistical analysis and confidence validation

**Usage Pattern**:
```python
from giki_ai_api.shared.ai.prompt_registry import get_prompt_registry

prompt_registry = get_prompt_registry()
prompt = prompt_registry.get("schema_interpretation_main")

# Format with variables
formatted_prompt = prompt.format(
    file_name=file_name,
    file_headers=headers,
    sample_data=sample_data
)

# Track performance automatically
start_time = time.time()
# ... execute AI call ...
prompt_registry.track_performance(
    prompt_id=prompt.id,
    version=prompt.version,
    success=True,
    latency_ms=(time.time() - start_time) * 1000
)
```

### 2. Review and Management Tools
**Script**: `scripts/review-prompts.py`

**Commands**:
- `uv run scripts/review-prompts.py list` - View all prompts
- `uv run scripts/review-prompts.py show <prompt_id>` - Detailed view
- `uv run scripts/review-prompts.py markdown --output docs/prompts-review.md` - Export for review
- `uv run scripts/review-prompts.py langfuse --output docs/langfuse-prompts.json` - Prepare for Langfuse

### 3. Performance Analytics
**Built-in Metrics**:
- Success/failure rates per prompt version
- Average latency measurements
- Confidence score tracking
- Error classification and debugging

**Agent Integration**:
```python
# Automatic performance tracking in SchemaInterpretationAgent
class SchemaInterpretationAgent(StandardGikiAgent):
    async def interpret_excel_schema(self, file_name, file_headers, sample_data):
        prompt_registry = get_prompt_registry()
        prompt = prompt_registry.get("schema_interpretation_main")
        
        start_time = time.time()
        result = await model.generate_content_async(
            prompt.format(...),
            generation_config=prompt.model_config
        )
        
        # Track performance automatically
        prompt_registry.track_performance(
            prompt_id=prompt.id,
            version=prompt.version,
            success=True,
            latency_ms=(time.time() - start_time) * 1000,
            metadata={"file_name": file_name, "column_count": len(file_headers)}
        )
```

## Current Prompts (6 Total)

### Schema Interpretation Category
1. **schema_interpretation_main** (v1.2.0) - Main file interpretation prompt
2. **schema_interpretation_enhanced** (v2.0.0) - Enhanced with statistical validation

### Financial Analysis Category
3. **debit_credit_inference** (v1.1.0) - Accounting principles for debit/credit detection
4. **regional_detection** (v1.2.0) - Regional banking format detection

### User Experience Category
5. **hierarchy_detection** (v1.0.0) - Category hierarchy pattern detection
6. **correction_suggestion** (v1.1.0) - User-friendly correction suggestions

## Integration with ADK Tools

### Tool Function Pattern
Our prompts integrate seamlessly with ADK `LongRunningFunctionTool` pattern:

```python
# In schema_interpretation_agent.py
async def suggest_schema_mapping_tool_function(
    file_name: str, 
    file_headers: List[str], 
    sample_data: List[List[str]], 
    **_kwargs
) -> Dict[str, Any]:
    # Get prompt from centralized registry
    prompt_registry = get_prompt_registry()
    prompt = prompt_registry.get("schema_interpretation_main")
    
    # Use prompt with performance tracking
    start_time = time.time()
    try:
        formatted_prompt = prompt.format(
            file_name=file_name,
            file_headers=file_headers,
            sample_data=json.dumps(sample_data, indent=2, default=str)
        )
        
        response = await model.generate_content_async(
            formatted_prompt,
            generation_config=prompt.model_config
        )
        
        # Track success
        prompt_registry.track_performance(
            prompt_id=prompt.id,
            version=prompt.version,
            success=True,
            latency_ms=(time.time() - start_time) * 1000
        )
        
        return json.loads(response.text)
        
    except Exception as e:
        # Track failure
        prompt_registry.track_performance(
            prompt_id=prompt.id,
            version=prompt.version,
            success=False,
            latency_ms=(time.time() - start_time) * 1000,
            metadata={"error": str(e)}
        )
        raise
```

### Agent Integration
Prompts work within our `StandardGikiAgent` pattern:

```python
class SchemaInterpretationAgent(StandardGikiAgent):
    def __init__(self, config: SchemaInterpretationAgentConfig | None = None):
        # Minimal tool assignment for cognitive efficiency
        custom_tools = [
            suggest_schema_mapping_tool,  # Uses centralized prompts
            infer_debit_credit_logic_tool,
            validate_schema_mapping_tool,
        ]
        
        super().__init__(
            name="schema_interpretation_agent",
            description="Intelligent schema interpretation with debit/credit inference",
            custom_tools=custom_tools,
            enable_standard_tools=False,  # Cognitive efficiency
            model_name=config.model_name,
            project_id=config.project,
            location=config.location,
        )
```

## Performance Benefits

### 1. Cognitive Efficiency
- **Fewer Tools**: Each agent gets only 2-3 essential tools using centralized prompts
- **Faster Decisions**: Reduced token usage and faster LLM responses
- **Predictable Behavior**: Versioned prompts provide consistent results

### 2. Quality Control
- **Version Tracking**: Easy rollback to previous prompt versions
- **Performance Monitoring**: Track which prompts perform best
- **A/B Testing**: Compare prompt versions with performance metrics

### 3. Team Collaboration
- **Easy Review**: Markdown exports for team review
- **Centralized Changes**: Update prompts without code deployment
- **Knowledge Sharing**: Documented prompt reasoning and improvements

## Future Integration: Langfuse

### Migration Readiness ✅
We've prepared our prompts for Langfuse integration:
- **Export Format**: `docs/langfuse-prompts.json` ready for import
- **Version History**: All prompt versions preserved
- **Performance Data**: Metrics ready for Langfuse analytics
- **Team Structure**: Prompts organized for collaborative review

### Benefits When Ready
- **Hosted Management**: Cloud-based prompt management
- **Advanced Analytics**: More sophisticated performance tracking
- **Team Collaboration**: Built-in review and approval workflows
- **Production Monitoring**: Real-time prompt performance in production

## Quality Gates

### Before Prompt Changes
1. **Test with Real Data**: Validate against Nuvie/Rezolve test files
2. **Performance Baseline**: Measure current prompt performance
3. **Version Documentation**: Document changes and reasoning
4. **Backwards Compatibility**: Ensure existing workflows continue working

### After Prompt Changes
1. **Performance Validation**: Confirm improved metrics
2. **End-to-End Testing**: Full workflow validation
3. **Documentation Update**: Update prompt documentation
4. **Team Review**: Share changes via markdown export

## Current Status

✅ **System Operational**: All 6 prompts active with performance tracking
✅ **ADK Integration**: Full compatibility with Google ADK v1.3.0 patterns
✅ **A2A Compliance**: Agent cards and discovery working
✅ **Review Tools**: Markdown export and management scripts ready
✅ **Langfuse Ready**: Migration files prepared
✅ **Performance Tracking**: Automatic metrics for all prompt usage

This system provides immediate value while preparing for future Langfuse integration when needed.