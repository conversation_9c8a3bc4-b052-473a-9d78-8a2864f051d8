# Infrastructure & Configuration

See @docs/specifications/system-design-spec.md for complete architecture
See @docs/specifications/build-and-deploy-specification.md for deployment details
See @docs/audits/performance-metrics.md for current performance status

## Architecture Summary
```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Frontend  │────▶│   Backend    │────▶│  Database   │
│(React+Vite) │     │(FastAPI+NX)  │     │(PostgreSQL) │
│  Port 4200  │     │  Port 8000   │     │Cloud SQL    │
└─────────────┘     └──────────────┘     └─────────────┘
                            │
                            ▼
                    ┌──────────────┐
                    │  Vertex AI   │
                    │  (RAG/LLM)   │
                    │  us-central1 │
                    └──────────────┘
```

### NX Monorepo Structure
- **apps/giki-ai-api**: Backend FastAPI with domain-driven architecture
- **apps/giki-ai-app**: Frontend React with feature-based structure
- **scripts/**: Infrastructure and deployment scripts
- **tests/**: E2E Playwright tests and integration tests

## Database Configuration

### Connection Strings
```bash
# Development (.env)
DATABASE_URL=postgresql+asyncpg://giki_ai_user:local_dev_password@localhost:5432/giki_ai_dev

# Production (Secret Manager)
DATABASE_URL=postgresql+asyncpg://giki_ai_user:[PASSWORD]@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres-prod
```

## Production URLs
- **API**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
- **App**: https://app-giki-ai.web.app

## Test Credentials
- **Test Users**: <EMAIL>, <EMAIL> (password123)

## Priority Libraries (Context7)
- Frontend: `/reactjs/react.dev`, `/radix-ui/primitives`, `/tanstack/table`
- Testing: `/microsoft/playwright`, `/vitest-dev/vitest`  
- Backend: `/tiangolo/fastapi`, `/supabase/supabase`
- AI/ML: `/googleapis/python-aiplatform`