# Development Rules

## Rule #1: Proactive Issue Detection with <PERSON><PERSON> MCP (MANDATORY)
**CRITICAL**: ALWAYS detect and fix issues proactively before user reports them.

**MANDATORY at every session start**:
1. Navigate to http://localhost:4200 and capture system health
2. Check for JavaScript console errors
3. Monitor failed network requests (status >= 400)
4. Analyze screenshots for visual bugs, layout issues, or accessibility problems
5. Test critical user flows (login, upload, dashboard)

**REQUIRED actions when issues detected**:
1. Fix the issue immediately
2. Run verification test
3. Update relevant tests
4. Document in audit files

## Rule #2: Follow the Pipeline
Every change: Requirements → Specifications → Tasks → Implementation → Audit

## Rule #3: Task System is Sacred
- Check @docs/tasks/active-tasks.md first
- Work on highest RICE score MUST HAVE tasks
- Update task status in real-time

## Rule #4: Search Before Creating
Use Grep/Glob/Task tools to find existing implementations

## Rule #5: Fix Root Causes, Not Symptoms
Pattern matching when RAG fails? → FIX THE RAG (not add workarounds)

## Rule #6: Reuse-First Approach
Search patterns in: `domains/*/models.py`, `domains/*/service.py`, `domains/*/agent.py`

## Rule #7: Production-First Development
- ✅ Build features directly in production apps (`apps/giki-ai-api`, `apps/giki-ai-app`)
- ✅ Create reusable functions in `apps/giki-ai-api/src/giki_ai_api/shared/` for backend
- ✅ Create reusable components in `apps/giki-ai-app/src/shared/` for frontend
- ✅ Write utility scripts in `scripts/` for deployment/maintenance only
- ✅ Test features using production-like test environments

## Rule #8: Act Immediately
See problem → Fix problem → Deploy → Verify

## Rule #9: Test Everything Immediately
- Write test → Run test → Fix if broken → Commit only when passing
- Backend: `pnpm test:api` or `pnpm nx test giki-ai-api`
- Frontend: `pnpm test:app` or `pnpm nx test giki-ai-app`
- E2E: `pnpm test:e2e` (Playwright comprehensive test)

## Rule #10: Verify Library APIs
Use Context7 MCP - never assume APIs from memory

## Rule #11: Work on Master with Frequent Commits
- Always work directly on `master`
- **Commit after every meaningful change** (every 15-30 minutes of work)
- **Commit triggers**: Task completion, file compaction, bug fixes, feature additions
- **Push immediately** after each commit
- This is a solo/small team project - no fear of breaking others' work

## Rule #12: Zero Warnings Policy
```bash
pnpm lint:api    # Must show: "All files linted successfully"
pnpm lint:app    # Must show: "All files linted successfully"
pnpm lint        # Lint all projects via nx run-many
```

## Rule #13: Workspace Hygiene
```bash
pnpm cleanup              # Before every commit
pnpm cleanup:test-data    # After test sessions
pnpm cleanup:processes    # Kill all processes (EMERGENCY)
```

## Rule #14: Definition of Done
A task is ONLY complete when:
- Users can accomplish their goals
- No errors in normal workflows  
- Data flows through entire pipeline
- E2E tests pass
- Deployed to production
- Verified with Playwright
- **Documentation updated** (specs, tasks, audits)

## Rule #15: Documentation Requirements
User Stories First (@docs/requirements/user-stories.md):
- Format: "As a [role], I want [feature], so that [benefit]"
- Include acceptance criteria
- Map to tasks in @docs/tasks/

## Rule #16: Screenshot Policy
- Use Playwright for UI screenshots
- Store in `screenshots/` with descriptive names

## Rule #17: Project Ownership
- Review ALL tasks at session start
- Know the backlog (150+ tasks)
- Track progress as % of TOTAL tasks

## Rule #18: Multi-Terminal Collaboration
```bash
scripts/workflow/start-work TASK-XXX     # Claim task
scripts/workflow/finish-work TASK-XXX    # Release task
scripts/workflow/check-conflicts         # Before edits
scripts/workflow/show-active            # See all work
```

## Rule #19: Focus on Resolution
- State facts without emotion
- Skip self-critique
- Move directly to solutions

## Rule #20: Build for Autonomous Operation
Create systems for complete visibility:
- Autonomous test runners with logging
- Self-healing deployment pipelines
- Comprehensive error tracking

## Rule #21: NX Server Management
```bash
pnpm serve          # Start servers with NX orchestration at session start
pnpm serve:status   # Check servers before working
pnpm serve:stop     # Stop servers when done
pnpm serve:restart  # Restart if needed
```

## Rule #22: Customer Journey Clarity
Maintain clear distinction between:
1. **Onboarding Journey**: Upload historical data WITH categories
2. **Production Journey**: Upload new data WITHOUT categories

## Rule #23: In-Place Evolution Pattern
- ✅ Improve existing implementations directly (`edit CustomerAgent`, not `create CustomerAgentV2`)
- ✅ Use feature flags for gradual rollouts when needed
- ✅ Maintain backward compatibility during transitions
- ✅ Remove deprecated code within same session after migration

## Rule #24: Update Documentation with Every Change
**CRITICAL**: Documentation must evolve with code or become dead weight

**Update Triggers**:
- Code changes → Update relevant @docs/specifications/*.md
- Task progress → Update @docs/tasks/active-tasks.md status
- Issues found → Update @docs/audits/*.md with findings
- Patterns discovered → Update this rules file
- Feature complete → Update @docs/audits/functional-status.md
- Performance changes → Update @docs/audits/performance-metrics.md

**Documentation Health**:
- Check staleness at session start
- Flag documents not updated in 7+ days
- Include doc updates in every PR
- Track documentation drift metrics

## Rule #25: Use @ Import Syntax for All Cross-References
**CRITICAL**: All markdown files must use @ import syntax for cross-references

**Required Patterns**:
```markdown
# ✅ CORRECT
@docs/specifications/frontend-spec.md
@docs/tasks/active-tasks.md
@CLAUDE.md#development-rules
@.claude/memory/quality-standards.md

# ❌ FORBIDDEN
[Frontend Spec](./frontend-spec.md)
[Tasks](../tasks/active-tasks.md)
See CLAUDE.md sections
```

**Benefits**:
- Enables recursive imports up to 5 hops deep
- Works seamlessly with Claude Code's memory system
- Prevents broken links from file moves
- Supports section-level imports with # syntax

**Implementation**:
- Replace all `[text](relative/path)` with `@path/to/file.md`
- Replace all `See [document](path)` with `@path/to/document.md`
- Use `@file.md#section` for specific sections
- Update existing files during any modification

## Rule #26: Proactive Codebase Cleanup
**CRITICAL**: Immediately remove unused code when discovered

**Cleanup Triggers**:
- Find unused functions/classes → Delete immediately
- Discover dead imports → Remove during same session
- Identify unreferenced files → Delete after verification
- Locate commented-out code → Remove permanently
- Find duplicate implementations → Consolidate to single version

**Required Actions**:
1. Search for references using Grep/Glob tools
2. Verify no usage in tests, scripts, or configurations  
3. Delete the unused code in same session
4. Run lint and tests to ensure no breakage
5. Update any related documentation

**Clean Code Policy**:
- ✅ Use git history for reference (remove unused code immediately)
- ✅ Implement features when actually needed (not speculatively)
- ✅ Delete commented code blocks (use git for history)
- ✅ Remove unused functions during same session when discovered
- ✅ Clean up imports when editing files

**Benefits**:
- Reduces cognitive load for developers
- Prevents security vulnerabilities in unused code
- Improves build performance and bundle size
- Maintains clean architecture

## Rule #27: Incremental E2E Test Development
**CRITICAL**: Maintain a single, ever-growing E2E test file that tracks ALL system functionality

**Test Development Policy**:
- ONE comprehensive E2E test file: `tests/e2e/system-health.spec.ts`
- Incrementally extend it with EVERY fix and feature
- Document test sections with comments when not yet implemented
- Add actual test implementation progressively as features are fixed

**Structure Pattern**:
```typescript
test.describe('Complete System E2E Tests', () => {
  // 1. Authentication & Registration
  test('New user registration flow', async ({ page }) => { /* implemented */ });
  
  // 2. Complete Onboarding Flow
  test.todo('Full onboarding with historical data upload');
  
  // 3. Temporal Accuracy Validation  
  test.todo('Month-by-month accuracy for second half of data');
  
  // 4. Production Usage Flow
  test('Upload new transactions', async ({ page }) => { /* partially implemented */ });
  
  // ... continue adding sections
});
```

**Requirements**:
1. **Comments First**: Add detailed comments describing what SHOULD be tested
2. **Progressive Implementation**: Convert comments to actual tests as features work
3. **Comprehensive Coverage**: Test EVERY user journey and system capability
4. **Single Source of Truth**: This ONE file documents system health status
5. **Run Continuously**: Execute during development to track progress

**Benefits**:
- Living documentation of system capabilities
- Clear visibility of what works vs what's broken
- Regression prevention as fixes are applied
- Single file to check for overall system health
- Natural progress tracking mechanism

## Rule #28: Documentation Size Limits (CRITICAL)
**CRITICAL**: Keep all documentation files compact to reduce token usage

**Maximum File Sizes**:
- Task files (active/completed/required): Max 10KB each
- Specification files: Max 20KB each
- Audit files: Max 15KB each
- Memory files: Max 10KB each

**Compaction Requirements**:
1. **Use Single-Line Formats**: Tasks should be one line each
2. **Use Tables**: Replace verbose lists with compact tables
3. **Use @ References**: Link to details instead of duplicating
4. **Group Similar Items**: Consolidate related entries
5. **Remove Verbose Descriptions**: Keep only essential information

**When Files Exceed Limits**:
1. **Immediate Action**: Compact the file in the same session
2. **Preserve Information**: Move details to audit files with @ references
3. **Consolidate Entries**: Group similar items (e.g., "Fixed 20 auth tests")
4. **Archive Old Data**: Move historical data to archive files

**Example Formats**:
```markdown
# Task Format
TASK-001 | HIGH | RICE:85 | Fix auth system | Result: JWT working | @audit-ref

# Endpoint Format
| Endpoint | Method | Purpose | Key Params |
| /api/v1/auth | POST | Login | username, password |

# Component Format
| Page | Components | Agent Equiv | Status |
| Dashboard | MetricsGrid, Charts | view_dashboard | ✅ |
```

**Benefits**:
- Reduces token usage from 107K to <30K tokens
- Faster context loading
- Better readability
- Easier maintenance

## Rule #29: Zero Duplication Policy (CRITICAL)
**CRITICAL**: Eliminate ALL duplication across documentation and code

**Single Source of Truth Pattern**:
- **Tech Stack**: Only in @docs/specifications/system-design-spec.md
- **API Endpoints**: Only in @docs/specifications/contracts-spec.md
- **Architecture**: Only in @docs/specifications/system-design-spec.md
- **Components**: Only in @docs/specifications/frontend-spec.md
- **Performance**: Only in @docs/audits/performance-metrics.md
- **Auth Details**: Only in @docs/specifications/contracts-spec.md
- **File Structure**: Only in @docs/README.md and @docs/specifications/system-design-spec.md

**Anti-Duplication Actions**:
1. **Before Adding Info**: Search for existing location using Grep/Glob
2. **Use @ References**: Link to authoritative source instead of copying
3. **Consolidate Fragments**: Merge scattered info into single files
4. **Remove Redundant Files**: Delete duplicate or fragmented docs
5. **Update Cross-References**: Replace duplicated content with @ imports

**Correct Patterns**:
- ✅ Document each endpoint once in @docs/specifications/contracts-spec.md
- ✅ Define tech stack once in @docs/specifications/system-design-spec.md, reference with @
- ✅ List components once in @docs/specifications/frontend-spec.md, reference elsewhere
- ✅ Store performance targets in @docs/audits/performance-metrics.md only
- ✅ Centralize auth details in @docs/specifications/contracts-spec.md
- ✅ Use single comprehensive audit file per topic

**Required Consolidations**:
- 📁 **Multiple audit files** → Single topic-based audit file
- 📁 **Fragmented guides** → Merge into primary specification
- 📁 **Duplicate command files** → Single authoritative command reference
- 📁 **Scattered deployment info** → Single build-and-deploy-spec

**Enforcement**:
1. **Session Start**: Check for duplication using grep patterns
2. **Before Writing**: Search existing docs for related content
3. **After Changes**: Verify no new duplication introduced
4. **Monthly Audit**: Systematic duplication detection and elimination

## Rule #30: File Structure Governance (CRITICAL)
**CRITICAL**: Enforce strict file and folder structure rules

**Documentation Structure** (@docs/README.md):
- **Exact 18 files**: No more, no less in docs/ folder
- **No temp files**: Never create date-specific or session files
- **No duplicates**: Single file per purpose
- **Required metadata**: All files must have YAML headers

**Code Structure** (@docs/specifications/system-design-spec.md):
- **Domain-driven**: apps/giki-ai-api/src/giki_ai_api/domains/
- **Feature-driven**: apps/giki-ai-app/src/features/
- **No versioning**: Never create v2, new, improved versions
- **No experiment folders**: No poc/, temp/, old/ directories

**Correct File Patterns**:
- ✅ Use standard names: `contracts-spec.md` (not `contracts-compact.md`)
- ✅ Use living documents: `performance-metrics.md` (not `audit-2025-06-25.md`)
- ✅ Update existing files in place (not create session summaries)
- ✅ Use single comprehensive files: `ui-consistency-audit.md` (not separate ui files)
- ✅ Edit files directly (not create TEMP or DRAFT versions)
- ✅ Follow docs/README.md structure exactly (18 files only)

**Required Actions When Structure Violated**:
1. **Immediate Cleanup**: Remove violating files in same session
2. **Consolidate Content**: Merge into proper authoritative location
3. **Update References**: Fix any @ imports pointing to removed files
4. **Verify Structure**: Check docs/ contains exactly allowed files

**Structure Validation Commands**:
```bash
# Check docs structure
find docs -name '*.md' | wc -l  # Should be exactly 18
ls docs/*.md  # Should only be README.md

# Check for forbidden patterns
find . -name '*-v2*' -o -name '*-new*' -o -name '*-backup*'
find . -name '*2025-*' -o -name 'session-*' -o -name 'TEMP_*'
```

## Rule #31: Frequent Commit & Push Workflow (CRITICAL)
**CRITICAL**: Commit and push after every meaningful change

**Commit Triggers** (commit immediately when any occurs):
- ✅ Complete any task or subtask
- ✅ Fix any bug or error
- ✅ Add any new feature or functionality
- ✅ Compact any file or remove redundancy
- ✅ Update any documentation
- ✅ Refactor any code
- ✅ Pass any test suite

**Commit Workflow**:
```bash
# After ANY meaningful change
git add .
git commit -m "brief description of change"
git push
```

**Commit Message Format**:
- **Line 1**: Brief description (50 chars max)
- **Line 2**: Empty
- **Line 3+**: Claude Code attribution

**Examples**:
```bash
git commit -m "compact documentation files for token reduction"

git commit -m "fix authentication endpoint bug"
```

**Timing**: Commit every 15-30 minutes of active work, not at end of session