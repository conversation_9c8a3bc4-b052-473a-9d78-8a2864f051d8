# Troubleshooting

## Common Issues

### API Slow (10+ seconds)
```bash
# Check database connections
tail -f logs/api-server.log | grep "connection"
# Solution: Check asyncpg timeout configuration
```

### CORS Errors
```bash
# Verify CORS config includes localhost
grep CORS_ALLOWED_ORIGINS apps/giki-ai-api/src/giki_ai_api/core/config.py
```

### Tests Failing
```bash
# Run specific test with details
npx playwright test auth-performance.spec.ts --debug
```

### Process Overload
**Symptoms**: System slow, >20 node/python processes
```bash
# Check process count
ps aux | grep -E "node|python" | wc -l

# Emergency cleanup
pnpm serve:stop
./scripts/kill-all-processes.sh
```

### Authentication Errors
**JWT Token Expired**:
- Frontend stores expired tokens
- Solution: Clear localStorage and re-login

**401 Unauthorized**:
- Check token format (Bearer prefix)
- Verify OAuth2 form encoding
- Check CORS configuration

### Database Connection Issues
**Connection Pool Exhausted**:
```bash
# Check active connections
lsof -i :5432 | wc -l

# Solution: Restart API server
pnpm serve:restart
```

### Playwright MCP Issues
**Chrome Profile Locked**:
```bash
./scripts/playwright-stop.sh
rm -rf .playwright-mcp/user-data/Singleton*
./scripts/playwright-server.sh
```

**Server Won't Start**:
```bash
# Check if port 3100 is in use
lsof -i :3100
# Kill existing process
kill $(lsof -t -i:3100)
```

### Build Failures
**Out of Memory**:
```bash
# Limited parallel builds
pnpm build --parallel=1
```

**TypeScript Errors**:
```bash
# Clear cache and rebuild
rm -rf .nx/cache
pnpm cleanup
pnpm build
```

### Deployment Issues
**Cloud Run Timeout**:
- Check cold start times
- Verify warmup endpoint working
- Consider increasing memory/CPU

**Firebase Hosting 404**:
- Verify index.html routing
- Check firebase.json rewrites
- Clear CDN cache

## Performance Debug Commands
```bash
# API endpoint timing
time curl http://localhost:8000/api/v1/health

# Database query timing
tail -f logs/api-server.log | grep "SLOW REQUEST"

# Frontend bundle size
pnpm nx build giki-ai-app --stats-json
```

## Emergency Recovery
```bash
# Full system reset
rm -rf node_modules .nx/cache
pnpm install
pnpm serve

# Database reset
uv run scripts/database/reset-local.py

# Clear all caches
rm -rf .nx/
rm -rf node_modules/.cache/
rm -rf apps/*/dist/
```