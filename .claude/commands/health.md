---
description: Run comprehensive system health check for giki.ai
---

# System Health Check

Comprehensive health verification for all giki.ai services and components.

## Usage
- `/health` - Run complete system health check
- `/health local` - Check only local development servers

## Health Check Commands

### Local Development Health
```bash
echo "🏥 Running local system health check..."

# Check if servers are running
echo "Checking server status..."
pnpm serve:status

# Check API health
echo "Testing local API..."
if curl -f http://localhost:8000/health 2>/dev/null; then
  echo "✅ Local API healthy"
else
  echo "❌ Local API not responding"
fi

# Check frontend
echo "Testing local frontend..."
if curl -f http://localhost:4200/health 2>/dev/null; then
  echo "✅ Local frontend healthy"
else
  echo "❌ Local frontend not responding"
fi

# Database connection test
echo "Testing database connection..."
uv run -c "import asyncpg; print('✅ Database connection test passed')" 2>/dev/null || echo "❌ Database connection failed"
```

### Production Health (if not local)
```bash
if [[ "$ARGUMENTS" != *"local"* ]]; then
  echo "🌐 Checking production services..."
  
  # Production API health
  echo "Testing production API..."
  api_response=$(curl -s -w "%{http_code}" https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health)
  api_code="${api_response: -3}"
  if [[ "$api_code" == "200" ]]; then
    echo "✅ Production API healthy ($api_code)"
  else
    echo "❌ Production API unhealthy ($api_code)"
  fi
  
  # Production frontend health
  echo "Testing production frontend..."
  app_response=$(curl -s -w "%{http_code}" https://giki-ai-app-6uyufgxcxa-uc.a.run.app/health)
  app_code="${app_response: -3}"
  if [[ "$app_code" == "200" ]]; then
    echo "✅ Production frontend healthy ($app_code)"
  else
    echo "❌ Production frontend unhealthy ($app_code)"
  fi
  
  # API response time test
  echo "Testing API performance..."
  api_time=$(curl -o /dev/null -s -w "%{time_total}" https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health)
  if (( $(echo "$api_time < 2.0" | bc -l) )); then
    echo "✅ API response time: ${api_time}s"
  else
    echo "⚠️  API response time: ${api_time}s (>2s)"
  fi
fi
```

### Code Quality Check
```bash
echo "🔍 Running code quality checks..."

# Lint status
if pnpm lint >/dev/null 2>&1; then
  echo "✅ Code linting passed"
else
  echo "❌ Code linting failed"
fi

# Test status
echo "Checking test coverage..."
test_output=$(pnpm test:api 2>&1 | tail -3)
echo "📊 Test status: $test_output"
```

Analyze the health check results and provide recommendations for any issues found.