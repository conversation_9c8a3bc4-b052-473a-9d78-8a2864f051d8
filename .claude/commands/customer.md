# Customer-Developer Simulation - Technical Product Review

**Reference**: @docs/requirements/user-stories.md @docs/specifications/frontend-spec.md

## Purpose
Simulate realistic customer interactions from a developer-customer perspective to identify user experience issues, validate requirements satisfaction, and generate actionable technical work items. Act as technical customers who understand both user experience AND underlying implementation requirements.

## Usage
```bash
/project:customer [area_of_concern]
```

**Arguments:**
- `area_of_concern` (optional): Focus validation on specific area
  - Examples: "ui standardisation", "agent panel integration", "onboarding flow", "performance issues", "authentication problems"
  - When provided, technical customer personas focus on that specific area
  - When omitted, performs comprehensive technical customer validation across all workflows

## Context Awareness
Command adapts based on:
- **Recent Conversation**: User pain points mentioned, specific issues reported, frustrations expressed
- **Current Session**: User behavior patterns, preferences observed, workflow concerns
- Selected technical customer persona
- Current system health and performance metrics
- Requirements and specifications compliance
- Previous validation cycles and regression patterns

## Technical Customer Personas

### 1. Alex - Technical Customer/Developer (Primary)
**Background**: Full-stack developer who also manages company finances
**Tech Level**: Expert - understands both UX and technical implementation
**Unique Perspective**: Spots root causes, not just symptoms
**Pain Points**:
- Notices when API responses violate performance specifications
- Identifies when UI patterns deviate from established design system
- Catches accessibility issues and keyboard navigation problems
- Detects when user workflows don't match requirements documentation

### 2. Sam - Performance-Aware Finance Manager
**Background**: Former software engineer, now manages company books
**Tech Level**: Advanced - uses dev tools to diagnose issues
**Unique Perspective**: Technical debugging of financial workflows  
**Pain Points**:
- Opens browser dev tools when things feel slow
- Measures actual response times vs documented targets
- Spots memory leaks and connection pool issues
- Notices inconsistent state management between features

### 3. Morgan - Accessibility-Focused CFO
**Background**: CFO with accessibility expertise due to compliance requirements
**Tech Level**: Intermediate - understands WCAG and keyboard navigation
**Unique Perspective**: Technical accessibility validation
**Pain Points**:
- Tests with screen readers and keyboard-only navigation
- Validates ARIA labels and semantic HTML structure
- Ensures financial data tables meet accessibility standards
- Checks color contrast ratios for financial data presentation

### 4. Jordan - Integration-Focused Developer
**Background**: Developer implementing giki.ai integration for their company
**Tech Level**: Expert - understands APIs and data flows
**Unique Perspective**: Integration and data flow validation
**Pain Points**:
- Tests API endpoints against documented contracts
- Validates data schemas match specifications
- Checks error handling and edge cases
- Ensures agent equivalence (UI and chat accomplish same tasks)

## Quick Actions

### 1. AREA-FOCUSED PERSONA SELECTION
When area of concern provided as argument: Focus technical customer validation on "$ARGUMENTS"

**Area-Specific Validation (when arguments provided):**
- Select appropriate technical customer persona based on the nature of "$ARGUMENTS"
- Focus validation workflows on features and components related to the specified area  
- Concentrate requirements and specifications validation on sections relevant to "$ARGUMENTS"
- Generate tasks and issues specifically related to the area of concern
- Update audit documentation with findings focused on "$ARGUMENTS"

**Standard Persona Selection (when no area specified):**
- **User mentioned performance issues?** → Select Sam (performance-focused)
- **User discussed UI/UX problems?** → Select Alex (technical customer)
- **User asked about accessibility?** → Select Morgan (accessibility-focused)
- **User working on integrations?** → Select Jordan (integration-focused)
- **No specific context?** → Default to Alex (comprehensive technical review)

### 2. REQUIREMENTS VALIDATION CHECKS
Validate actual UX against documented requirements:
```bash
# Read user stories to understand intended experience
read_user_stories = "@docs/requirements/user-stories.md"
# Read business requirements for context
read_business_reqs = "@docs/requirements/requirements.md"

# Test actual workflows against acceptance criteria
for user_story in user_stories:
    test_acceptance_criteria(user_story)
    if gap_found:
        generate_task(f"US-{user_story.id} acceptance criteria not met: {gap_description}")
```

### 3. SPECIFICATIONS COMPLIANCE VALIDATION
Check implementation matches technical specifications:
```bash
# Validate against frontend specification
frontend_spec = "@docs/specifications/frontend-spec.md"
# Check system design constraints
system_spec = "@docs/specifications/system-design-spec.md"

# Test UI patterns match specification
validate_ui_patterns(frontend_spec)
validate_performance_targets(system_spec)
validate_architecture_constraints(system_spec)
```

### 4. INTEGRATED AUDIT CAPABILITIES
Built-in audit functions during customer simulation:

**Performance Monitoring:**
```bash
# Navigate to app with performance monitoring
mcp__playwright__browser_navigate("http://localhost:4200")
start_time = performance.now()

# Check against targets from performance-metrics.md
actual_load_time = measure_page_load()
target_load_time = read_performance_target("page_load")

if actual_load_time > target_load_time:
    technical_complaint(f"Page load {actual_load_time}ms exceeds target {target_load_time}ms")
    generate_task(f"TASK-PERF-{timestamp}: Page load performance regression")
```

**Visual Regression Detection:**
```bash
# Capture screenshots for baseline comparison
screenshot_current = mcp__playwright__browser_take_screenshot()
screenshot_baseline = load_baseline_screenshot()

if visual_differences_detected(screenshot_current, screenshot_baseline):
    technical_complaint("UI regression detected - layout differs from baseline")
    generate_task("TASK-UI-{timestamp}: Visual regression in layout")
```

**Console Error Analysis:**
```bash
# Check for JavaScript errors
console_errors = mcp__playwright__browser_console_messages()
for error in console_errors:
    if error.level == "error":
        technical_complaint(f"JavaScript error: {error.message}")
        generate_task(f"TASK-JS-{timestamp}: Fix console error - {error.message}")
```

### 5. TECHNICAL COMPLAINT PATTERNS
Developer-customer complaints with actionable technical details:

**Performance Issues:**
- "Auth login taking 390ms, BR-003 requires <200ms response time"
- "Dashboard API call 3.2 seconds, violates performance specification"
- "Memory usage growing during file upload, potential leak"

**UX/Implementation Gaps:**
- "US-002 says categorization should be automatic, but requires manual trigger"
- "Frontend spec requires Excel-inspired tables, current implementation doesn't match"
- "Agent equivalence broken - can't accomplish upload via chat interface"

**Specification Violations:**
- "System design spec requires tenant isolation, but tenant_id is NULL on uploads"
- "Frontend spec mandates glass morphism effects, but buttons use flat design"
- "API contract expects 'categories' array, but returning 'category' string"

### 6. ACTIONABLE TASK GENERATION
Convert technical findings into specific work items:

```python
def generate_technical_task(finding_type, details, requirements_link=None):
    task_id = f"TASK-{finding_type}-{timestamp}"
    
    task = f"""
## {task_id}: {details.title}
**Type**: {finding_type}
**Priority**: {calculate_priority(finding_type)}
**Requirements**: {requirements_link or "N/A"}

### Technical Issue
{details.technical_description}

### Expected Behavior  
{details.specification_reference}

### Acceptance Criteria
- [ ] {details.success_criteria}
- [ ] Performance meets specification targets
- [ ] No regressions in related functionality

### Evidence
- Screenshot: {details.screenshot_path}
- Console logs: {details.console_output}
- Performance data: {details.timing_data}
"""
    
    add_to_required_tasks(task)
```

### 7. REQUIREMENTS TRACEABILITY VALIDATION
Validate end-to-end requirements satisfaction:

```bash
# Test onboarding journey (US-001)
test_onboarding_flow():
    upload_historical_data()
    verify_ai_learning_progress()
    check_accuracy_above_85_percent()
    validate_against_acceptance_criteria("US-001")

# Test production workflow (US-002) 
test_production_workflow():
    upload_new_transactions()
    verify_automatic_categorization()
    check_gl_code_mapping()
    validate_against_acceptance_criteria("US-002")
```

### 8. PROGRESSIVE LEARNING ACROSS CYCLES
Track issues and improvements across customer → work → test → customer cycles:

```python
# Load previous validation results
previous_issues = load_audit_history()
current_issues = detect_current_issues()

# Identify regressions
regressions = find_regressions(previous_issues, current_issues)
if regressions:
    priority_complaint("REGRESSION DETECTED: Previously fixed issues returned")

# Track improvements
improvements = find_improvements(previous_issues, current_issues)
if improvements:
    acknowledge_improvements("Good progress on previous issues")

# Update audit history
update_audit_history(current_issues, regressions, improvements)
```

### 9. INTEGRATION WITH DOCS STRUCTURE AND TWO-LAYER WORKFLOW
Systematic updating of living documents with comprehensive task breakdown:

**Updates @docs/audits/audit-history.md:**
```markdown
## Technical Customer Validation - {timestamp}
**Persona**: Alex (Technical Customer/Developer)
**Duration**: 20 minutes
**Issues Found**: 3 new, 1 regression, 2 improvements

### Requirements Compliance
- US-001 acceptance criteria: ✅ Met
- US-002 acceptance criteria: ❌ Automatic categorization not working
- BR-003 performance: ❌ Auth endpoint 390ms (target: <200ms)

### Specification Compliance  
- Frontend spec UI patterns: ⚠️ Partial (tables need Excel-inspired styling)
- System design performance: ❌ Multiple endpoints exceed targets
- API contracts: ✅ All endpoints match specification

### Generated Tasks (Two-Layer)
- TASK-PERF-001 | 90 | MUST | Optimize auth endpoint to <200ms | Performance spec
  - Breakdown: docs/tasks/breakdowns/TASK-PERF-001-breakdown.md
- TASK-UI-002 | 75 | SHOULD | Implement Excel-inspired table styling | UI spec
  - Breakdown: docs/tasks/breakdowns/TASK-UI-002-breakdown.md  
- TASK-AUTO-001 | 85 | MUST | Fix automatic categorization trigger | US-002
  - Breakdown: docs/tasks/breakdowns/TASK-AUTO-001-breakdown.md
```

**Updates @docs/tasks/required-tasks.md:**
Automatically adds generated tasks with full technical context, requirements links, and acceptance criteria.

**Creates Two-Layer Task Breakdowns:**
For each generated task, automatically creates detailed breakdown in @docs/tasks/breakdowns/:
```markdown
# TASK-PERF-001 Breakdown

## Customer Issue
Technical customer Alex reports: "Auth endpoint responding in 390ms, specification requires <200ms"

## Performance Evidence  
- Measured: 390ms average response time
- Target: <200ms (from @docs/audits/performance-metrics.md)
- Impact: User frustration, specification violation

## Subtasks
- [ ] Profile auth endpoint with detailed timing
- [ ] Identify performance bottlenecks (DB queries, authentication flow)
- [ ] Implement specific optimizations (caching, query optimization)
- [ ] Validate <200ms response time achieved
- [ ] Add performance monitoring to prevent regression
- [ ] Update performance-metrics.md with improvement

## Acceptance Criteria
- Auth endpoint consistently responds in <200ms
- No regressions in authentication functionality
- Performance monitoring shows sustained improvement
- Customer validation confirms issue resolved
```

## Enhanced Workflow Integration with Two-Layer TODO

### Pre-Work Command Preparation
Generate specific, actionable tasks with comprehensive breakdowns:

**Layer 1 - High-Level Task Generation:**
```bash
# Generate task with RICE prioritization
task_id = generate_task_id()
rice_score = calculate_rice_score(issue_impact, issue_confidence, issue_reach, effort_estimate)
priority = determine_moscow_priority(rice_score)

# Add to required-tasks.md
add_task_to_required_tasks(task_id, rice_score, priority, description, dependencies)
```

**Layer 2 - Detailed Breakdown Creation:**
```bash
# Auto-generate breakdown based on issue type
breakdown_template = select_template(issue_type)  # build/api/performance/ui
breakdown_content = populate_template(breakdown_template, {
    "customer_issue": customer_complaint,
    "technical_evidence": evidence_data,
    "acceptance_criteria": measurable_success_criteria,
    "specifications_links": relevant_spec_sections
})

# Create breakdown file
create_breakdown_file(f"docs/tasks/breakdowns/{task_id}-breakdown.md", breakdown_content)
```

**Enhanced Task Context:**
- Technical requirements clearly defined in breakdown
- Performance targets specified with measurements
- Acceptance criteria measurable and testable
- Links to relevant specifications embedded
- Evidence of issues captured with screenshots
- Subtasks created for systematic implementation

### Post-Test Command Validation with Breakdown Verification
Verify fixes from previous cycle using breakdown completion:

**Breakdown-Based Validation:**
```bash
# Check completed tasks have all subtasks done
for completed_task in recently_completed:
    breakdown_file = f"docs/tasks/breakdowns/{completed_task}-breakdown.md"
    if not all_subtasks_marked_complete(breakdown_file):
        flag_incomplete_task(completed_task)
        
    # Re-test original customer issue
    original_issue = get_customer_issue(completed_task)
    current_state = test_customer_scenario(original_issue)
    
    if issue_still_exists(current_state, original_issue):
        create_regression_task(completed_task, "Customer issue returned")
```

**Systematic Re-validation:**
- Re-test previously failing scenarios using original breakdown evidence
- Validate specifications compliance restored per acceptance criteria
- Check for new regressions introduced with comprehensive testing
- Update living documents with results and mark breakdowns as verified
- Create new breakdown for any regressions discovered

## Customer-Developer Cycle Benefits with Two-Layer Workflow

1. **Root Cause Identification**: Not just "upload is slow" but "upload API taking 3.2s, should be <2s per performance spec" with detailed breakdown of investigation steps
2. **Specification Enforcement**: Validates implementation matches documented requirements and specifications with breakdown tracking compliance
3. **Progressive Quality**: Issues tracked across cycles with breakdown history, regressions caught immediately through subtask verification
4. **Systematic Implementation**: Every customer issue gets comprehensive breakdown ensuring nothing falls through cracks
5. **Evidence-Based Development**: Customer complaints linked to technical evidence and measurable acceptance criteria
6. **Quality Gate Integration**: Each breakdown includes quality validation steps before task completion
7. **Continuous Improvement**: Breakdown patterns improve over time based on successful issue resolution
8. **Team Alignment**: Both high-level priorities and detailed implementation steps visible to entire team
4. **Actionable Output**: Generated tasks include technical context, not just symptom descriptions
5. **Living Documentation**: All findings update permanent docs structure automatically

## When to Use

- **Start of development sessions** - Validate current system state
- **After work command completion** - Verify fixes actually work for users
- **Before deployment** - Final customer experience validation
- **Regular quality checks** - Monthly comprehensive validation
- **After specification updates** - Ensure implementation still compliant

## Output Format

### Technical Validation Report
```
TECHNICAL CUSTOMER VALIDATION REPORT
===================================
Persona: Alex (Technical Customer/Developer)
Session: 2025-06-24 14:30
Duration: 18 minutes

Requirements Compliance:
✅ US-001: Historical data upload working
❌ US-002: Automatic categorization broken
⚠️  BR-003: Performance targets partially met

Specification Compliance:
✅ System design: Architecture constraints followed
❌ Frontend spec: UI patterns need alignment
✅ API contracts: All endpoints match specification

Performance Analysis:
- Auth endpoint: 390ms (target: <200ms) ❌
- Dashboard load: 400ms (target: <500ms) ✅  
- File upload: 2.1s (target: <5s) ✅

Generated Tasks: 4
- TASK-PERF-001: Optimize auth endpoint performance
- TASK-UI-002: Align tables with Excel-inspired specification
- TASK-AUTO-001: Fix automatic categorization trigger
- TASK-REG-001: Address visual regression in dashboard layout

Next Focus: Work command should prioritize TASK-PERF-001 and TASK-AUTO-001 for user impact.
```

**This enhanced command bridges the gap between customer experience and technical implementation, generating actionable work items that address root causes while maintaining requirements traceability.**