# /todo - Two-Layer Task Management

## Purpose
Manage high-level tasks and detailed subtask breakdowns using the two-layer TODO workflow.

## Usage
- `/todo create <rice_score> <priority> "<description>" "<dependencies>"` - Create high-level task
- `/todo breakdown <TASK-ID>` - Create/edit detailed subtask breakdown  
- `/todo status [filter]` - Show task status at both layers
- `/todo complete <TASK-ID> [subtask_num]` - Mark subtask or entire task complete
- `/todo list [active|critical|pending]` - List tasks by status/priority

## Commands

### Create High-Level Task
```bash
/todo create 85 "MUST" "Fix authentication performance" "BR-003"
```
Creates a new task in `docs/tasks/required-tasks.md` with:
- Auto-generated TASK-ID (TASK-YYYYMMDD-###)
- RICE score for prioritization
- Moscow priority classification
- Dependencies tracking

### Create Breakdown
```bash
/todo breakdown TASK-BUILD-CRITICAL-001
```
Creates detailed breakdown in `docs/tasks/breakdowns/TASK-XXX-breakdown.md`:
- Auto-selects template based on task type (build/api/testing/performance)
- Pre-populates with high-level task context
- Includes metadata headers for tracking

### Status Overview
```bash
/todo status
/todo status critical    # Show only CRITICAL tasks
/todo status active      # Show only active breakdowns
```
Displays:
- High-priority tasks (CRITICAL/MUST) from required-tasks.md
- Active breakdown progress (X/Y subtasks complete)
- Blocked or failed tasks requiring attention

### Complete Subtasks
```bash
/todo complete TASK-BUILD-CRITICAL-001 3    # Mark subtask 3 complete
/todo complete TASK-BUILD-CRITICAL-001      # Mark entire task complete
```
Updates breakdown file and checks if all subtasks are done.

### List Tasks
```bash
/todo list                    # All active tasks
/todo list critical          # Only CRITICAL priority
/todo list pending           # Tasks not yet started
```

## Workflow Integration

### Morning Routine
1. `/todo status` - Review active work and breakdowns
2. Select task or create breakdown if missing
3. Work through subtasks systematically
4. Update progress frequently

### During Development
- Update breakdown file as subtasks are completed
- Run quality gates before marking subtasks complete
- Commit every 15-30 minutes with task reference

### Task Completion
- `/todo complete TASK-XXX` only when all subtasks done AND quality gates pass
- Move task from active-tasks.md to completed-tasks.md
- Update living documentation (performance-metrics.md, quality-metrics.md, etc.)

## Quality Gates Integration
Before marking ANY subtask complete:
- [ ] Run `pnpm nx lint giki-ai-api` (must pass)
- [ ] Run `pnpm nx lint giki-ai-app` (must pass)  
- [ ] Run `pnpm nx test` (all tests pass)
- [ ] No console errors in browser
- [ ] Capture Playwright screenshot if UI changes

## Templates Available
- **_TEMPLATE-BUILD.md**: Syntax errors, compilation issues, lint warnings
- **_TEMPLATE-API.md**: Endpoint implementation, routing, authentication
- **_TEMPLATE-TESTING.md**: Test coverage, E2E implementation, test infrastructure
- **_TEMPLATE-PERFORMANCE.md**: Optimization, caching, database tuning

## Benefits
- **Nothing Lost**: Two-layer system ensures comprehensive tracking
- **Clear Progress**: Visibility at both strategic and tactical levels  
- **Quality Enforcement**: Subtasks include testing and validation
- **Team Alignment**: Everyone can see current work and progress
- **Historical Record**: Completed breakdowns show problem-solving approaches

## Examples

### Complete Build Fix Workflow
```bash
# 1. Check what's critical
/todo status critical

# 2. Create breakdown for critical task
/todo breakdown TASK-BUILD-CRITICAL-001

# 3. Work through subtasks (update breakdown file manually)
# 4. Mark subtasks complete as you go
/todo complete TASK-BUILD-CRITICAL-001 1
/todo complete TASK-BUILD-CRITICAL-001 2

# 5. When all subtasks done and quality gates pass
/todo complete TASK-BUILD-CRITICAL-001
```

### Create New Task from Customer Issue
```bash
# Customer reports authentication is slow (390ms, should be <200ms)  
/todo create 85 "MUST" "Fix authentication performance taking 390ms" "BR-003"
/todo breakdown TASK-20250627-001
# Edit breakdown file to add specific investigation and optimization steps
```

## File Locations
- **High-level tasks**: `docs/tasks/required-tasks.md`, `docs/tasks/active-tasks.md`
- **Breakdowns**: `docs/tasks/breakdowns/TASK-XXX-breakdown.md`
- **Templates**: `docs/tasks/breakdowns/_TEMPLATE-*.md`
- **Quality gates**: `docs/tasks/quality-gates.md`