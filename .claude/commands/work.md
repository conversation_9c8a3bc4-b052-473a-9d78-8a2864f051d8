# Project Work - Complete Development Lifecycle

**Reference**: @docs/tasks/active-tasks.md

## Purpose
Execute complete development lifecycle from task selection through deployment with context awareness and quality gates.

## Usage
```bash
/project:work [area_of_concern]
```

**Arguments:**
- `area_of_concern` (optional): Focus work on specific area
  - Examples: "ui standardisation", "agent panel integration", "onboarding flow", "performance optimization", "authentication system"
  - When provided, prioritizes tasks and work related to that specific area
  - When omitted, follows standard conversation-driven prioritization

## Context Awareness
Command adapts based on:
- **Recent Conversation**: What user just mentioned, reported issues, immediate concerns
- **Current Session**: Recent questions, problems discussed, user frustration or urgency
- Customer command generated tasks and priorities
- Current git status and active work
- Requirements and specifications compliance needs
- Task complexity and architectural constraints
- System health metrics and quality gates

## Quick Actions

### 1. TWO-LAYER TODO WORKFLOW INTEGRATION
Integrated with the two-layer TODO workflow for comprehensive task management:

**Workflow Priority Check:**
1. **Check breakdown status**: Review active breakdowns for incomplete subtasks
2. **Critical task detection**: Identify CRITICAL tasks without breakdowns
3. **Quality gate validation**: Ensure all subtasks meet quality standards
4. **Context-aware selection**: Choose tasks based on conversation and user urgency

**Area-Specific Focus (when arguments provided):**
- Search for tasks in @docs/tasks/required-tasks.md and @docs/tasks/active-tasks.md that relate to "$ARGUMENTS"
- Filter breakdown files in @docs/tasks/breakdowns/ for relevant subtasks
- Prioritize specifications sections that relate to the area of concern
- Look for user stories and requirements that match the focus area

**Standard Prioritization (when no area specified):**
- **Active breakdowns with incomplete subtasks?** → Continue in-progress work first
- **Customer command generated tasks?** → Prioritize those first (they address real user pain)
- **CRITICAL tasks without breakdowns?** → Create breakdown and start immediately
- **User mentioned specific issues?** → Address immediate blockers before planned work
- **Performance/specification violations detected?** → Fix compliance issues first
- **Requirements gaps identified?** → Implement missing acceptance criteria

### 2. INTEGRATED TASK LIFECYCLE MANAGEMENT
Built-in consolidation of task management across docs structure with two-layer workflow:

**Task Discovery & Prioritization:**
```bash
# Read customer-generated priority tasks
customer_tasks = read_high_priority_tasks("@docs/tasks/required-tasks.md")

# Check current active work
active_work = read_current_sprint("@docs/tasks/active-tasks.md")

# Check breakdown status for active tasks
active_breakdowns = check_breakdown_status("@docs/tasks/breakdowns/")

# Prioritize based on:
# 1. Active tasks with incomplete subtasks (continue work in progress)
# 2. CRITICAL tasks without breakdowns (create breakdown first)
# 3. Customer-reported issues (high impact)
# 4. Requirements compliance gaps 
# 5. Performance/specification violations
priority_queue = prioritize_two_layer_tasks(customer_tasks, active_work, active_breakdowns, conversation_context)
```

**Real-time Task State Management with Two-Layer Workflow:**
```bash
# Start task - update both layers immediately
def start_task(task_id):
    # Layer 1: High-level task management
    move_task(from="required-tasks.md", to="active-tasks.md", task_id)
    update_task_status(task_id, "in_progress")
    set_start_timestamp(task_id)
    link_requirements_context(task_id)
    
    # Layer 2: Create breakdown if missing
    breakdown_file = f"docs/tasks/breakdowns/{task_id}-breakdown.md"
    if not exists(breakdown_file):
        create_task_breakdown(task_id)
        populate_subtasks_from_requirements(task_id)

# Complete subtask - update breakdown progress
def complete_subtask(task_id, subtask_num):
    breakdown_file = f"docs/tasks/breakdowns/{task_id}-breakdown.md"
    mark_subtask_complete(breakdown_file, subtask_num)
    check_if_all_subtasks_complete(task_id)

# Complete task - validate all subtasks done
def complete_task(task_id):
    # Validate all subtasks completed
    breakdown_complete = validate_all_subtasks_complete(task_id)
    if not breakdown_complete:
        fail_task_completion("Subtasks incomplete")
        
    validate_acceptance_criteria_met(task_id)
    run_quality_gates(task_id)
    
    # Layer 1: Move high-level task
    move_task(from="active-tasks.md", to="completed-tasks.md", task_id)
    
    # Layer 2: Archive breakdown
    archive_breakdown(task_id)
    
    update_functional_status_if_feature_complete(task_id)
    update_performance_metrics_if_applicable(task_id)
```

### 3. REQUIREMENTS & SPECIFICATIONS INTEGRATION
Ensure all implementation follows documented requirements and specifications:

**Requirements Traceability:**
```bash
# Before implementing, understand business context
def start_implementation(task_id):
    # Read linked business requirement
    business_req = get_linked_requirement(task_id)  # BR-XXX
    user_story = get_linked_user_story(task_id)     # US-XXX
    
    # Understand intended user experience
    acceptance_criteria = read_acceptance_criteria(user_story)
    business_value = read_business_value(business_req)
    
    # Set implementation context
    implementation_context = {
        "business_requirement": business_req,
        "user_story": user_story,
        "acceptance_criteria": acceptance_criteria,
        "success_metrics": business_value
    }
```

**Specifications Compliance:**
```bash
# Follow established patterns from specifications
def implement_with_compliance(task_id, task_type):
    if task_type == "frontend":
        patterns = read_ui_patterns("@docs/specifications/frontend-spec.md")
        component_standards = get_component_patterns(patterns)
        ensure_agent_equivalence(patterns.agent_parity)
        
    elif task_type == "backend":
        architecture = read_system_design("@docs/specifications/system-design-spec.md")
        api_patterns = get_api_patterns("@docs/specifications/contracts-spec.md")
        ensure_performance_targets(architecture.performance)
        
    elif task_type == "integration":
        agent_specs = read_agent_patterns("@docs/specifications/agents-and-tools-spec.md")
        ensure_tool_compliance(agent_specs)
```

### 4. INTEGRATED TESTING & QUALITY GATES
Comprehensive testing integrated into development workflow:

**Automated Testing During Development:**
```bash
# Use Playwright MCP for continuous validation
def test_during_development(task_id):
    # Start Playwright server if needed
    ensure_playwright_running()
    
    # Test affected workflows immediately
    affected_workflows = identify_affected_workflows(task_id)
    for workflow in affected_workflows:
        test_result = test_user_workflow(workflow)
        if test_result.failed:
            provide_immediate_feedback(test_result)
            
    # Validate requirements satisfaction
    validate_acceptance_criteria(task_id)
    
    # Check specification compliance
    validate_specification_compliance(task_id)
```

**Requirements Validation Testing:**
```bash
# Test implementation against documented requirements
def test_requirements_compliance(task_id):
    user_story = get_linked_user_story(task_id)
    acceptance_criteria = user_story.acceptance_criteria
    
    for criterion in acceptance_criteria:
        # Parse "Given [context], when [action], then [result]" format
        context, action, expected_result = parse_acceptance_criterion(criterion)
        
        # Execute test scenario with Playwright
        navigate_to_test_context(context)
        perform_user_action(action)
        actual_result = capture_result()
        
        # Validate against expected result
        if actual_result != expected_result:
            fail_acceptance_test(user_story.id, criterion, actual_result, expected_result)
            provide_fix_guidance(criterion)
        else:
            pass_acceptance_test(user_story.id, criterion)
```

**Performance Validation:**
```bash
# Check performance against documented targets
def validate_performance(task_id):
    performance_targets = read_performance_targets("@docs/audits/performance-metrics.md")
    
    # Test with Playwright for real user experience
    playwright_performance = measure_user_perceived_performance()
    
    if task_affects_api():
        test_api_performance(affected_endpoints)
        compare_against_targets(performance_targets)
        
    if task_affects_ui():
        test_page_load_times()
        validate_against_targets(performance_targets.frontend)
        capture_performance_screenshots()
        
    # Update performance metrics if improvements made
    if performance_improved():
        update_performance_metrics(task_id, new_measurements)
```

**Comprehensive Quality Gates:**
```bash
# Must pass before task completion
def run_quality_gates(task_id):
    # Lint must pass
    lint_result = run_lint_for_changes(task_id)
    if lint_result.failed:
        fail_task_completion("Lint errors must be fixed")
        
    # Unit tests must pass
    test_result = run_tests_for_changes(task_id)
    if test_result.failed:
        fail_task_completion("Tests must pass")
        
    # E2E tests with Playwright
    e2e_result = run_playwright_tests(task_id)
    if e2e_result.failed:
        capture_failure_screenshots()
        fail_task_completion("E2E tests must pass")
        
    # Requirements validation
    requirements_result = test_requirements_compliance(task_id)
    if requirements_result.not_satisfied:
        fail_task_completion("Acceptance criteria not met")
        
    # Specification compliance
    spec_result = validate_specification_compliance(task_id)
    if spec_result.violations:
        fail_task_completion("Specification violations detected")
        
    # Build must succeed
    build_result = run_build_for_changes(task_id)
    if build_result.failed:
        fail_task_completion("Build must succeed")
        
    # Performance must not regress
    performance_result = validate_performance(task_id)
    if performance_result.regressed:
        fail_task_completion("Performance regression detected")
        
    # Visual regression check
    visual_result = check_visual_regression(task_id)
    if visual_result.unexpected_changes:
        fail_task_completion("Visual regression detected")
```

**Evidence Collection for Validation:**
```bash
# Capture evidence during development
def collect_validation_evidence(task_id):
    evidence_dir = f"screenshots/task-{task_id}-evidence/"
    
    # Capture working implementation
    capture_implementation_screenshots(evidence_dir)
    
    # Record performance measurements
    record_performance_data(evidence_dir)
    
    # Capture console logs (should be clean)
    capture_console_logs(evidence_dir)
    
    # Document requirements satisfaction
    document_requirements_met(evidence_dir, task_id)
    
    return evidence_dir
```

### 5. LIVING DOCUMENTATION UPDATES
Automatic updates to docs structure during implementation:

**Functional Status Tracking:**
```bash
# Update feature completion when tasks complete
def update_functional_status(task_id):
    feature = get_feature_from_task(task_id)
    user_story = get_user_story_from_task(task_id)
    
    if feature_fully_implemented(feature):
        update_feature_status("@docs/audits/functional-status.md", feature, "Complete")
        
    if user_story_acceptance_criteria_met(user_story):
        update_user_story_status("@docs/audits/functional-status.md", user_story, "✅ Met")
        
    if business_requirement_satisfied(task_id):
        br_id = get_business_requirement(task_id)
        update_requirement_status("@docs/audits/functional-status.md", br_id, "Achieved")
```

**Quality Metrics Updates:**
```bash
# Track quality improvements
def update_quality_metrics(task_id):
    quality_file = "@docs/audits/quality-metrics.md"
    
    # Update test coverage if tests added
    if tests_added(task_id):
        new_coverage = calculate_test_coverage()
        update_coverage_metrics(quality_file, new_coverage)
        
    # Update lint status if warnings fixed
    if lint_warnings_fixed(task_id):
        new_lint_status = get_lint_status()
        update_lint_metrics(quality_file, new_lint_status)
        
    # Update technical debt if refactoring done
    if technical_debt_reduced(task_id):
        update_technical_debt_metrics(quality_file, task_id)
```

### 6. PROACTIVE SYSTEM HEALTH MANAGEMENT
Monitor and maintain system health during development:

**Process Management:**
```bash
# Monitor system resources during development
def monitor_system_health():
    process_count = count_node_python_processes()
    if process_count > 20:
        log_warning("High process count detected")
        run_cleanup_processes()
        
    # Ensure servers are running
    api_status = check_api_server(8000)
    frontend_status = check_frontend_server(4200)
    
    if not api_status or not frontend_status:
        restart_development_servers()
```

**Workspace Hygiene:**
```bash
# Clean workspace before major work
def maintain_workspace():
    # Clean old build artifacts
    cleanup_build_artifacts()
    
    # Remove stale test data  
    cleanup_old_test_data()
    
    # Update dependencies if needed
    check_dependency_security()
    
    # Clear nx cache if builds slow
    if build_performance_degraded():
        clear_nx_cache()
```

### 7. COMPREHENSIVE USER WORKFLOW TESTING
Test complete user journeys with Playwright during implementation:

**Onboarding Journey Testing:**
```bash
# Test complete onboarding flow against US-001
def test_onboarding_journey():
    # Phase 1: Data Upload (US-001)
    navigate_to_onboarding()
    
    # Test file upload with various formats
    test_files = ["Capital One.xlsx", "Credit Card.xlsx", "ICICI.xlsx"]
    for file in test_files:
        upload_result = upload_test_file(file)
        assert upload_result.success, f"Upload failed for {file}"
        
    # Test processing progress indication
    processing_progress = monitor_processing_progress()
    assert processing_progress.visible, "Processing progress not shown to user"
    
    # Test accuracy validation
    wait_for_processing_complete()
    accuracy_display = get_accuracy_display()
    assert accuracy_display.visible and accuracy_display.value > 85
    
    # Validate against US-001 acceptance criteria
    validate_user_story_acceptance("US-001", onboarding_result)
```

**Production Journey Testing:**
```bash
# Test production workflow against US-002
def test_production_workflow():
    # Upload new uncategorized transactions
    navigate_to_upload()
    upload_new_transactions("new_transactions.xlsx")
    
    # Test automatic categorization
    wait_for_categorization_complete()
    categorization_results = get_categorization_results()
    
    # Verify categorization is automatic
    assert categorization_results.automatic, "US-002 violated: categorization not automatic"
    
    # Test GL code mapping per BR-028
    gl_codes = get_gl_code_assignments()
    assert gl_codes.complete, "GL code mapping incomplete"
    
    # Validate against US-002 acceptance criteria
    validate_user_story_acceptance("US-002", production_result)
```

### 8. IMPLEMENTATION STRATEGY BY TASK TYPE
Adaptive approach based on work type with integrated testing:

**Bug Fixes with Test-First Approach and Breakdown Tracking:**
```bash
def implement_bug_fix(task_id):
    # Check/create breakdown for systematic approach
    breakdown = ensure_breakdown_exists(task_id, "build")
    
    # Use Playwright to reproduce issue
    playwright_reproduce_bug(task_id)
    capture_bug_evidence()
    update_subtask_progress(task_id, "investigate_root_cause", "completed")
    
    # Add failing test case FIRST
    add_regression_test(task_id)
    verify_test_fails()
    update_subtask_progress(task_id, "add_regression_test", "completed")
    
    # Find related tests
    related_tests = find_tests_for_bug(task_id)
    
    # Implement fix following specifications
    implement_fix_with_spec_compliance()
    update_subtask_progress(task_id, "implement_fix", "completed")
    
    # Verify fix with Playwright
    verify_bug_fixed_with_playwright()
    verify_no_regressions()
    update_subtask_progress(task_id, "verify_fix", "completed")
    
    # Run full test suite
    run_quality_gates(task_id)
    update_subtask_progress(task_id, "quality_gates", "completed")
    
    # Check if all subtasks complete
    if all_subtasks_complete(task_id):
        complete_task(task_id)
```

**Feature Implementation with Continuous Testing and Breakdown Tracking:**
```bash
def implement_feature(task_id):
    # Ensure breakdown exists for feature
    breakdown = ensure_breakdown_exists(task_id, "feature")
    
    # Read requirements context
    user_story = get_user_story(task_id)
    business_req = get_business_requirement(task_id)
    update_subtask_progress(task_id, "read_requirements", "completed")
    
    # Create test scenarios from acceptance criteria
    test_scenarios = create_test_scenarios(user_story.acceptance_criteria)
    update_subtask_progress(task_id, "create_test_scenarios", "completed")
    
    # Find similar existing implementations
    similar_features = find_similar_implementations()
    
    # Test-driven implementation with subtask tracking
    for increment_num, increment in enumerate(feature_increments):
        # Write test first
        write_test_for_increment(increment)
        
        # Implement following patterns
        implement_increment_with_patterns()
        
        # Test with Playwright immediately
        test_increment_with_playwright(increment)
        
        # Ensure no regressions
        run_affected_tests()
        
        # Update progress
        update_subtask_progress(task_id, f"implement_increment_{increment_num}", "completed")
    
    # Final validation against all acceptance criteria
    validate_all_acceptance_criteria(user_story)
    update_subtask_progress(task_id, "validate_acceptance_criteria", "completed")
    
    # Update functional status
    update_feature_completion_status()
    update_subtask_progress(task_id, "update_documentation", "completed")
    
    # Check if all subtasks complete
    if all_subtasks_complete(task_id):
        complete_task(task_id)
```

**Performance Optimization with Validation:**
```bash
def implement_performance_optimization(task_id):
    # Baseline current performance with Playwright
    baseline_metrics = measure_user_perceived_performance()
    api_baseline = measure_api_performance()
    
    # Read target metrics
    target_metrics = get_performance_targets(task_id)
    
    # Profile with developer tools
    profile_current_state()
    identify_bottlenecks()
    
    # Implement optimizations iteratively
    for optimization in optimization_steps:
        implement_optimization(optimization)
        
        # Measure improvement immediately
        new_metrics = measure_performance_after_change()
        
        # Ensure no functional regression
        test_functionality_preserved()
        
        # Continue if improving, rollback if not
        if not performance_improved(new_metrics, baseline_metrics):
            rollback_optimization(optimization)
    
    # Final validation against targets
    final_metrics = measure_final_performance()
    validate_meets_targets(final_metrics, target_metrics)
    
    # Update performance documentation
    update_performance_metrics(baseline_metrics, final_metrics)
```

### 9. REGRESSION TESTING & VALIDATION
Test previously identified issues to ensure fixes persist:

**Customer Issue Regression Testing:**
```bash
# Test issues identified by customer command
def test_customer_identified_issues():
    customer_issues = read_customer_issues("@docs/audits/audit-history.md")
    
    for issue in customer_issues:
        if issue.status == "fixed":
            # Regression test - ensure issue doesn't return
            regression_result = test_issue_scenario(issue)
            
            if regression_result.issue_returned:
                record_regression("Customer issue returned", issue)
                generate_regression_fix_task(issue)
            else:
                confirm_issue_resolved(issue)
                
        elif issue.status == "open":
            # Validate if work command fixed the issue
            fix_validation = test_issue_resolution(issue)
            
            if fix_validation.resolved:
                update_issue_status(issue, "resolved")
                record_successful_fix(issue)
```

**Visual Regression Prevention:**
```bash
# Test UI consistency during changes
def test_visual_regression():
    baseline_screenshots = load_baseline_screenshots()
    
    for page in critical_pages:
        navigate_to_page(page)
        current_screenshot = take_screenshot(page)
        baseline_screenshot = baseline_screenshots[page]
        
        # Compare against baseline
        visual_diff = compare_screenshots(current_screenshot, baseline_screenshot)
        
        if visual_diff.significant_changes:
            if changes_are_intentional():
                update_baseline_screenshot(page, current_screenshot)
            else:
                record_visual_regression(page, visual_diff)
                provide_fix_guidance(visual_diff)
```

### 10. CUSTOMER COMMAND INTEGRATION
Seamless handoff from customer validation to implementation:

**Priority Task Processing:**
```bash
# Process customer-identified issues first
def process_customer_tasks():
    customer_generated = get_customer_generated_tasks()
    
    for task in customer_generated:
        # High priority - real user impact
        if task.type == "user_experience_gap":
            prioritize_immediately(task)
            
        elif task.type == "specification_violation":
            fix_compliance_issue(task)
            
        elif task.type == "performance_regression":
            optimize_performance_immediately(task)
            
        # Link back to customer validation evidence
        link_customer_evidence(task)
```

**Requirements Gap Resolution:**
```bash
# Fix gaps between implementation and requirements
def resolve_requirements_gaps(task_id):
    gap_type = get_gap_type(task_id)
    
    if gap_type == "acceptance_criteria_not_met":
        implement_missing_functionality()
        
    elif gap_type == "specification_deviation":
        align_with_specification()
        
    elif gap_type == "performance_target_missed":
        optimize_to_meet_targets()
        
    # Validate gap resolved
    verify_requirements_satisfaction()
```

### 11. SMART DEPLOYMENT WORKFLOW
Integrated deployment with safety checks and verification:

**Pre-Deployment Verification:**
```bash
# Automatic pre-flight checks before deployment
def prepare_for_deployment(completed_tasks):
    # Check quality gates from living documents
    quality_status = check_quality_metrics("@docs/audits/quality-metrics.md")
    performance_status = check_performance_targets("@docs/audits/performance-metrics.md")
    security_status = check_security_status("@docs/audits/security-status.md")
    
    if not all([quality_status.passing, performance_status.within_targets, security_status.no_critical]):
        provide_deployment_blockers()
        return false
        
    # Capture pre-deployment baseline
    baseline_screenshots = capture_deployment_baseline()
    baseline_performance = measure_current_performance()
    
    return deployment_ready(baseline_screenshots, baseline_performance)
```

**Deployment Execution:**
```bash
# Smart deployment based on changes
def execute_deployment(task_ids, change_type):
    # Determine deployment strategy
    if change_type == "hotfix":
        deployment_type = "fast_track"
        checks = ["minimal_quality", "critical_tests"]
    elif change_type == "feature":
        deployment_type = "comprehensive"
        checks = ["full_quality", "regression_tests", "performance_validation"]
    else:
        deployment_type = "standard"
        checks = ["quality_gates", "unit_tests", "smoke_tests"]
    
    # Final quality gates
    run_deployment_checks(checks)
    
    # Commit with context
    commit_message = generate_deployment_commit(task_ids)
    commit_and_push(commit_message)
    
    # Trigger deployment
    deployment_result = run_deployment()
    monitor_deployment_progress(deployment_result)
    
    return deployment_result
```

**Post-Deployment Verification:**
```bash
# Verify production health after deployment
def verify_production_deployment():
    # Test production endpoints
    production_health = test_production_endpoints()
    
    # Compare performance
    production_performance = measure_production_performance()
    performance_comparison = compare_with_baseline(production_performance)
    
    # Visual verification with Playwright
    production_screenshots = capture_production_state()
    visual_comparison = compare_visual_state(production_screenshots)
    
    # Update documentation
    if deployment_successful():
        mark_tasks_as_shipped(task_ids)
        update_functional_status("@docs/audits/functional-status.md")
        record_deployment_success("@docs/audits/audit-history.md")
    else:
        record_deployment_issues()
        create_rollback_tasks()
```

### 12. CONVERSATION CONTINUITY & CONTEXT
Maintain context and respond to immediate user needs:

**Context-Aware Responses:**
- "Based on the customer validation finding [X], I'll prioritize fixing [specific issue]..."
- "Following up on your concern about [Y], I'll implement [specific solution] using [specification pattern]..."
- "Testing shows [specific workflow] failing, implementing fix now..."
- "All quality gates passed, deploying to production now..."
- "Deployment complete, production verification shows [status]..."

**Adaptive Work Strategy:**
- Customer frustration → Focus on immediate pain points first
- Test failures → Fix breaking changes before continuing
- Deployment urgency → Fast-track with comprehensive checks
- Specification compliance issues → Fix architectural problems before features
- Requirements gaps → Implement missing acceptance criteria
- Performance problems → Optimize to meet documented targets

## Integration Benefits

1. **Complete Lifecycle**: Task → Development → Testing → Deployment in one flow
2. **Unified Development Flow**: Implementation and testing in single workflow
3. **Continuous Validation**: Test-driven development with immediate feedback
4. **Smart Deployment**: Context-aware deployment strategies with safety checks
5. **Requirements Traceability**: Every task linked to business requirements and validated
6. **Specification Compliance**: All implementation tested against documented patterns
7. **Living Documentation**: Real-time updates throughout entire lifecycle
8. **Quality Assurance**: Comprehensive gates from development through production
9. **Performance Focus**: Continuous validation with production verification
10. **Evidence Generation**: Automatic collection for customer validation
11. **Regression Prevention**: Systematic testing before and after deployment
12. **Production Health**: Automated verification of deployed changes

## When to Use

- **After customer command** - Implement, test, and deploy priority fixes
- **During development sessions** - Complete lifecycle from task to production
- **For bug fixes** - Test-first approach with rapid deployment
- **For new features** - TDD with staged deployment
- **Regular development** - Maintain quality through deployment
- **Feature completion** - Deploy when acceptance criteria met
- **End of sprint** - Deploy accumulated changes
- **Hotfix needed** - Fast-track critical fixes to production

**This complete lifecycle command combines development, testing, and deployment into a single workflow, ensuring quality through continuous validation while maintaining requirements traceability and production health throughout the entire process.**