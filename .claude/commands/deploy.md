---
description: Deploy giki.ai services to development or production
---

# Deploy Services

Deploy giki.ai services with health verification.

## Usage
- `/deploy` - Deploy to development with verification
- `/deploy prod` - Deploy to production with verification

## Commands

### Development Deployment
```bash
echo "🚀 Starting development deployment..."

# Pre-deployment checks
echo "Running pre-deployment checks..."
pnpm lint || { echo "❌ Linting failed"; exit 1; }
pnpm test:e2e || { echo "❌ E2E tests failed"; exit 1; }

# Deploy to development
echo "Deploying to development..."
pnpm deploy:dev

# Health check
echo "Verifying deployment..."
sleep 10
curl -f https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health || { echo "❌ API health check failed"; exit 1; }
curl -f https://giki-ai-app-6uyufgxcxa-uc.a.run.app/health || { echo "❌ Frontend health check failed"; exit 1; }

echo "✅ Development deployment successful!"
```

### Production Deployment (if $ARGUMENTS contains "prod")
```bash
if [[ "$ARGUMENTS" == *"prod"* ]]; then
  echo "🚀 Starting PRODUCTION deployment..."
  
  # Extra verification for production
  echo "Running comprehensive checks..."
  pnpm lint || { echo "❌ Linting failed"; exit 1; }
  pnpm test:e2e || { echo "❌ E2E tests failed"; exit 1; }
  
  # Confirm production deployment
  echo "⚠️  Deploying to PRODUCTION. Continue? (y/N)"
  read -r confirm
  if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo "❌ Production deployment cancelled"
    exit 1
  fi
  
  # Deploy to production
  echo "Deploying to production..."
  pnpm deploy:prod
  
  # Production health check
  echo "Verifying production deployment..."
  sleep 15
  curl -f https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health || { echo "❌ Production API health check failed"; exit 1; }
  curl -f https://giki-ai-app-6uyufgxcxa-uc.a.run.app/health || { echo "❌ Production frontend health check failed"; exit 1; }
  
  echo "✅ Production deployment successful!"
  echo "🌐 Frontend: https://giki-ai-app-6uyufgxcxa-uc.a.run.app"
  echo "🔧 API: https://giki-ai-api-6uyufgxcxa-uc.a.run.app"
fi
```

Analyze deployment results and system status after completion.