---
description: Run comprehensive test suite for giki.ai
---

# Test Suite Runner

Execute giki.ai test suite with coverage reporting and performance analysis.

## Usage
- `/test` - Run all tests with coverage
- `/test e2e` - Run only E2E tests
- `/test api` - Run only backend tests
- `/test coverage` - Generate detailed coverage report

## Test Commands

### E2E Tests Only
```bash
if [[ "$ARGUMENTS" == *"e2e"* ]]; then
  echo "🎭 Running E2E tests..."
  
  # Ensure servers are running
  pnpm serve:status || {
    echo "Starting servers for E2E tests..."
    pnpm serve &
    sleep 10
  }
  
  # Run Playwright tests
  echo "Executing Playwright test suite..."
  pnpm test:e2e
  
  echo "✅ E2E tests completed"
  exit 0
fi
```

### Backend Tests Only
```bash
if [[ "$ARGUMENTS" == *"api"* ]]; then
  echo "🐍 Running backend tests..."
  
  cd apps/giki-ai-api
  
  # Run pytest with coverage
  echo "Executing pytest test suite..."
  uv run pytest --cov=src/giki_ai_api --cov-report=term-missing --cov-report=html
  
  echo "✅ Backend tests completed"
  echo "📊 Coverage report: apps/giki-ai-api/htmlcov/index.html"
  cd ../..
  exit 0
fi
```

### Full Test Suite
```bash
echo "🧪 Running comprehensive test suite..."

# Start servers if needed
echo "Ensuring servers are running..."
pnpm serve:status || {
  echo "Starting development servers..."
  pnpm serve &
  sleep 10
}

# Run linting first
echo "1️⃣  Running code quality checks..."
if pnpm lint; then
  echo "✅ Linting passed"
else
  echo "❌ Linting failed - fix issues before testing"
  exit 1
fi

# Run backend tests
echo "2️⃣  Running backend tests..."
cd apps/giki-ai-api
if uv run pytest --cov=src/giki_ai_api --cov-report=term-missing; then
  backend_tests="✅ PASSED"
else
  backend_tests="❌ FAILED"
fi
cd ../..

# Run frontend tests (when implemented)
echo "3️⃣  Running frontend tests..."
frontend_tests="⚠️  NOT IMPLEMENTED"

# Run E2E tests
echo "4️⃣  Running E2E tests..."
if pnpm test:e2e; then
  e2e_tests="✅ PASSED"
else
  e2e_tests="❌ FAILED"
fi

# Generate coverage report (if requested)
if [[ "$ARGUMENTS" == *"coverage"* ]]; then
  echo "5️⃣  Generating detailed coverage report..."
  cd apps/giki-ai-api
  uv run pytest --cov=src/giki_ai_api --cov-report=html --cov-report=term
  cd ../..
  echo "📊 Detailed coverage: apps/giki-ai-api/htmlcov/index.html"
fi

# Test summary
echo ""
echo "🏁 Test Suite Summary:"
echo "   Linting: ✅ PASSED"
echo "   Backend: $backend_tests"
echo "   Frontend: $frontend_tests"
echo "   E2E: $e2e_tests"
echo ""

# Performance check
echo "⚡ Running performance check..."
api_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:8000/health 2>/dev/null || echo "N/A")
echo "   API Response Time: ${api_time}s"

if [[ "$backend_tests" == *"FAILED"* ]] || [[ "$e2e_tests" == *"FAILED"* ]]; then
  echo "❌ Some tests failed - review and fix before deploying"
  exit 1
else
  echo "✅ All tests passed - ready for deployment!"
fi
```

Analyze test results, identify any failures, and provide recommendations for fixing issues or improving test coverage.