# /task - Milestone-Driven Project Task Management

## Purpose
Manage persistent project tasks with milestone-driven prioritization, focusing on real tenant validation scenarios (Nuvie, Rezolve, giki.ai).

## Usage
- `/task milestone <M1|M2|M3>` - Show milestone-specific task progress
- `/task create:milestone <M1|M2|M3> <rice> <priority> "<description>"` - Create milestone task
- `/task breakdown <TASK-ID>` - Create/edit detailed subtask breakdown
- `/task status [filter]` - Show task status across milestones
- `/task complete <TASK-ID>` - Mark persistent task complete
- `/task dashboard` - Show all milestone progress
- `/task accuracy <M1|M2|M3>` - Show accuracy progress for milestone

## Milestone Context

### M1: Nuvie Zero-Onboarding (Week 1)
**Tenant**: Nuvie | **Scenario**: Zero-onboarding | **Target**: >85% business-appropriateness
**Challenge**: Prove AI categorization without any training data

### M2: Rezolve Complete Onboarding (Week 2)
**Tenant**: Rezolve | **Scenario**: Historical data training | **Target**: >85% improvement-over-original
**Challenge**: Validate temporal accuracy and production readiness

### M3: giki.ai Category Import (Week 3)
**Tenant**: giki.ai | **Scenario**: Hierarchy import | **Target**: 90% compliance
**Challenge**: Category import and GL code mapping functionality

## Commands

### Milestone-Specific Task Management
```bash
/task milestone M1
```
Shows M1-specific tasks with progress:
- TASK-M1-FOUNDATION: Critical fixes for M1 enablement
- TASK-M1-NUVIE-LOAD: Load 900+ transactions
- TASK-M1-ACCURACY-UNIFIED: Fix accuracy measurement
- TASK-M1-NUVIE-CATEGORIZE: Zero-onboarding categorization
- TASK-M1-NUVIE-ACCURACY: Achieve 85% accuracy

### Create Milestone Tasks
```bash
/task create:milestone M1 95 CRITICAL "Nuvie category hierarchy generation"
```
Creates persistent task in docs/tasks/required-tasks.md with:
- Milestone context (M1, M2, or M3)
- Real tenant association (Nuvie, Rezolve, giki.ai)
- RICE score and priority for sprint planning
- Dependencies on other milestone tasks

### Breakdown Management
```bash
/task breakdown TASK-M1-FOUNDATION
```
Creates/edits detailed breakdown in docs/tasks/breakdowns/:
- Milestone-specific metadata (tenant, scenario, accuracy-target)
- Subtasks focused on milestone completion
- Real customer validation criteria
- Performance targets and success metrics

### Accuracy Tracking
```bash
/task accuracy M1
```
Shows M1-specific accuracy progress:
- Business-appropriateness evaluation status
- Batch processing performance metrics
- Confidence interval calculations
- Progress toward 85% target

### Dashboard Overview
```bash
/task dashboard
```
Displays milestone progress dashboard:
- Current milestone focus (M1 this week)
- Progress toward milestone completion
- Accuracy achievements and targets
- Next milestone preparation status

## Workflow Integration

### Daily Milestone-Focused Routine
1. `/task milestone M1` - Check current milestone progress
2. Select highest priority M1 task from breakdown
3. Create TODOs for implementation steps
4. Work on M1 task, update breakdown progress
5. Mark subtasks complete in breakdown file
6. `/task complete TASK-M1-XXX` when all subtasks done

### Milestone Transition
1. Complete all M1 tasks and achieve 85% accuracy
2. Update milestone dashboard with M1 completion
3. `/task milestone M2` - Begin M2 tasks
4. Focus exclusively on M2 until complete
5. Repeat for M3

### Quality Gates for Task Completion
Before marking any milestone task complete:
- [ ] All subtasks in breakdown marked complete
- [ ] Quality gates pass for that task's scope
- [ ] Evidence captured (screenshots, metrics, API responses)
- [ ] Milestone progress updated in dashboard
- [ ] Next milestone foundation prepared

## Task Types by Milestone

### M1 Task Categories
- **Foundation**: Critical fixes enabling M1 testing
- **Data Loading**: Nuvie 900+ transaction preparation
- **Accuracy**: Business-appropriateness measurement
- **Zero-Onboarding**: Categorization without training
- **Validation**: 85% accuracy achievement

### M2 Task Categories  
- **Historical Data**: Rezolve data with original categories
- **Temporal Processing**: Month-by-month validation
- **Improvement Measurement**: Better-than-original evaluation
- **Production Approval**: >85% temporal accuracy
- **RAG Corpus**: Building from historical categorized data

### M3 Task Categories
- **Hierarchy Import**: Category structure and GL codes
- **Compliance Measurement**: Exact + fuzzy matching
- **Export Functionality**: QuickBooks and accounting integration
- **Agent Integration**: Query category structure intelligently
- **Final Validation**: All customer scenarios complete

## Real Customer Data Focus

### M1: Nuvie Data Structure
```
data/milestones/M1-nuvie/
├── transactions_900plus.xlsx      # Real/realistic transaction data
├── expected_categories.csv        # Business-appropriate categories
└── accuracy_criteria.yaml         # Zero-onboarding evaluation rules
```

### M2: Rezolve Data Structure  
```
data/milestones/M2-rezolve/
├── historical_2024_with_categories.xlsx  # Customer's original categories
├── temporal_validation_plan.yaml         # Month-by-month testing
└── improvement_criteria.yaml             # "Better than original" rules
```

### M3: giki.ai Data Structure
```
data/milestones/M3-giki/
├── category_hierarchy.xlsx        # Desired category structure
├── sample_transactions.xlsx       # Test categorization data
└── compliance_rules.yaml          # Exact + fuzzy matching rules
```

## Success Metrics Integration

### Milestone Completion Criteria
- **M1 Complete**: 85% business-appropriateness on Nuvie 900+ transactions
- **M2 Complete**: 85% improvement-over-original on Rezolve temporal data
- **M3 Complete**: 90% hierarchy compliance on giki.ai imported structure

### Performance Integration
- **M1**: <3s per transaction, 150 transactions/batch
- **M2**: Progressive accuracy improvement across months
- **M3**: Exact hierarchy matching with export functionality

## Benefits of Milestone-Driven Task Management

### Strategic Direction
- All tasks consolidate toward milestone completion
- Real customer scenarios drive prioritization
- No fragmented work - everything advances milestones

### Quality Assurance
- Two-layer workflow (TODOs → Tasks) ensures systematic implementation
- Milestone completion proves entire system capability
- Real tenant data provides ultimate validation

### Measurable Progress
- Clear completion criteria for each milestone
- Accuracy targets with statistical significance
- Performance metrics with real data volumes

## Examples

### Complete M1 Task Workflow
```bash
# 1. Check milestone progress
/task milestone M1

# 2. Work on highest priority M1 task
/task breakdown TASK-M1-FOUNDATION

# 3. Create working TODOs for implementation
TodoWrite: ["Fix Badge syntax errors", "Test API endpoints", "Validate tenant isolation"]

# 4. Complete subtasks, update breakdown file
# 5. Mark task complete when all subtasks done
/task complete TASK-M1-FOUNDATION

# 6. Move to next M1 task
/task milestone M1  # Check what's next
```

### Create New Milestone Task
```bash
# Found new requirement during M1 work
/task create:milestone M1 85 HIGH "Nuvie transaction deduplication validation"

# Creates persistent task with M1 context
# Auto-generates breakdown template
# Adds to M1 milestone tracking
```

## File Locations
- **Milestone Dashboard**: `docs/milestones/milestone-dashboard.md`
- **Task List**: `docs/tasks/required-tasks.md` (milestone-organized)
- **Breakdowns**: `docs/tasks/breakdowns/TASK-MX-*-breakdown.md`
- **Test Data**: `data/milestones/M1-nuvie/`, `M2-rezolve/`, `M3-giki/`

This milestone-driven task management ensures all work consolidates toward proving the system works with real customer scenarios while maintaining systematic quality through the two-layer workflow.