---
description: Set up development environment for giki.ai
---

# Development Environment Setup

Quick setup and verification of giki.ai development environment.

## Usage
- `/setup` - Full development environment setup
- `/setup verify` - Verify existing setup only

## Setup Commands

### Environment Verification
```bash
echo "🔧 Verifying development environment..."

# Check Node.js and pnpm
echo "Checking Node.js..."
node --version || { echo "❌ Node.js not installed"; exit 1; }
echo "Checking pnpm..."
pnpm --version || { echo "❌ pnpm not installed"; exit 1; }

# Check Python and uv
echo "Checking Python..."
python3 --version || { echo "❌ Python not installed"; exit 1; }
echo "Checking uv..."
uv --version || { echo "❌ uv not installed"; exit 1; }

# Check PostgreSQL (if local development)
echo "Checking PostgreSQL..."
if command -v psql >/dev/null 2>&1; then
  echo "✅ PostgreSQL available"
else
  echo "⚠️  PostgreSQL not found (using Cloud SQL)"
fi

echo "✅ Environment verification complete"
```

### Dependencies Installation (if not verify-only)
```bash
if [[ "$ARGUMENTS" != *"verify"* ]]; then
  echo "📦 Installing dependencies..."
  
  # Install Node.js dependencies
  echo "Installing Node.js dependencies..."
  pnpm install || { echo "❌ pnpm install failed"; exit 1; }
  
  # Install Python dependencies
  echo "Installing Python dependencies..."
  cd apps/giki-ai-api && uv sync || { echo "❌ Python dependencies failed"; exit 1; }
  cd ../..
  
  echo "✅ Dependencies installed"
fi
```

### Environment File Setup
```bash
echo "🔑 Setting up environment files..."

# Check .env files exist
if [[ ! -f "apps/giki-ai-api/.env" ]]; then
  echo "Creating API .env file..."
  cat > apps/giki-ai-api/.env << 'EOF'
# Development environment
ENVIRONMENT=development
DATABASE_URL=postgresql+asyncpg://giki_ai_user:local_dev_password@localhost:5432/giki_ai_dev
SECRET_KEY=dev-secret-key-change-in-production
AUTH_SECRET_KEY=dev-auth-secret-change-in-production
ADMIN_API_KEY=dev-admin-key-change-in-production
GCP_PROJECT_ID=rezolve-poc
VERTEX_AI_LOCATION=us-central1
CORS_ALLOWED_ORIGINS=http://localhost:4200,http://127.0.0.1:4200
EOF
  echo "✅ API .env file created"
else
  echo "✅ API .env file exists"
fi

if [[ ! -f "apps/giki-ai-app/.env" ]]; then
  echo "Creating frontend .env file..."
  cat > apps/giki-ai-app/.env << 'EOF'
# Development environment
VITE_API_BASE_URL=http://localhost:8000
VITE_ENVIRONMENT=development
EOF
  echo "✅ Frontend .env file created"
else
  echo "✅ Frontend .env file exists"
fi
```

### Test Setup
```bash
echo "🧪 Testing setup..."

# Build and start servers to verify
echo "Testing server startup..."
pnpm serve &
sleep 10

# Wait for servers to start
echo "Waiting for servers to initialize..."
sleep 5

# Test API
if curl -f http://localhost:8000/health 2>/dev/null; then
  echo "✅ API server working"
else
  echo "❌ API server not responding"
fi

# Test frontend
if curl -f http://localhost:4200 2>/dev/null; then
  echo "✅ Frontend server working"
else
  echo "❌ Frontend server not responding"
fi

# Stop test servers
pnpm serve:stop

echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Run 'pnpm serve' to start development servers"
echo "2. Run 'pnpm test:e2e' to verify E2E tests"
echo "3. Check '/health' for system status"
```

Analyze the setup results and provide guidance for next steps or troubleshooting any issues.