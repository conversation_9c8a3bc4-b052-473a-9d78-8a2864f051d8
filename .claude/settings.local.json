{"permissions": {"allow": ["Write(*)", "Edit(*)", "MultiEdit(*)", "NotebookEdit(*)", "WebFetch(*)", "WebSearch(*)", "TodoWrite(*)", "mcp__*", "WebFetch(domain:localhost)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:docs.astral.sh)", "WebFetch(domain:supabase.com)", "WebFetch(domain:nx.dev)", "WebFetch(domain:vitejs.dev)", "WebFetch(domain:pnpm.io)", "WebFetch(domain:cloud.google.com)", "WebFetch(domain:console.cloud.google.com)", "WebFetch(domain:playwright.dev)", "WebFetch(domain:tailwindcss.com)", "Bash(ls:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(pwd:*)", "Bash(cd:*)", "Bash(tree:*)", "Bash(ps:*)", "Bash(kill:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(nohup:*)", "<PERSON><PERSON>(curl:*)", "Bash(nc:*)", "<PERSON><PERSON>(netstat:*)", "Bash(pnpm:*)", "Bash(pnpm nx:*)", "Bash(pnpm dev:*)", "Bash(pnpm build:*)", "Bash(pnpm test:*)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(uv run:*)", "Bash(git:*)", "Bash(gh:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(gcloud:*)", "Bash(node:*)", "Bash(nx:*)", "<PERSON><PERSON>(make:*)", "Bash(pytest:*)", "Bash(vitest:*)", "<PERSON><PERSON>(playwright:*)", "Bash(bash:*)", "<PERSON><PERSON>(sh:*)", "<PERSON><PERSON>(source:*)", "Bash(export:*)", "Bash(env:*)", "<PERSON><PERSON>(echo:*)", "Bash(which:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(sort:*)", "Bash(wc:*)", "Bash(xargs:*)", "Bash(eslint:*)", "<PERSON><PERSON>(prettier:*)", "Bash(ruff:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(date:*)", "<PERSON><PERSON>(time:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true:*)", "<PERSON><PERSON>(false:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(screencapture:*)", "<PERSON><PERSON>(jq:*)", "Bash(concurrently:*)", "<PERSON><PERSON>(scripts/*:*)", "Bash(./scripts/*:*)", "Bash(vite:*)", "Bash(serve:*)", "Bash(PGPASSWORD=rezolve-poc psql -h db.ngnskaiazefujixbdkkk.supabase.co -U postgres -d postgres -c \"SELECT COUNT(*) FROM transactions WHERE original_category_label IS NOT NULL;\")", "Bash(./scripts/test-api.sh:*)", "Bash(--image us-central1-docker.pkg.dev/rezolve-poc/giki-ai/giki-ai-app:latest )", "Bash(--region us-central1 )", "Bash(--platform managed )", "Bash(--port 8080 )", "Bash(--memory 512Mi )", "Bash(--cpu 1 )", "<PERSON><PERSON>(--timeout 300 )", "Bash(--concurrency 1000 )", "Bash(--max-instances 10 )", "Bash(--set-env-vars \"ENVIRONMENT=production\" )", "Bash(--allow-unauthenticated)", "Bash(--region=us-central1 )", "Bash(--member=allUsers )", "Bash(--role=roles/run.invoker)", "Bash(--region=us-central1 )", "Bash(--member=\"allUsers\" )", "<PERSON><PERSON>(--role=\"roles/run.invoker\" )", "Bash(--condition=None)", "Bash(--region=us-central1 )", "Bash(--member=\"allUsers\" )", "<PERSON><PERSON>(--role=\"roles/run.invoker\")", "Bash(--region=us-central1 )", "Bash(--member=\"allUsers\" )", "Bash(npm install:*)", "<PERSON><PERSON>(firebase login:*)", "Bash(firebase projects:list:*)", "Bash(firebase init:*)", "Bash(firebase use:*)", "Bash(firebase deploy:*)", "Bash(VITE_API_BASE_URL=https://giki-ai-api-6uyufgxcxa-uc.a.run.app VITE_SUPABASE_URL=https://ngnskaiazefujixbdkkk.supabase.co VITE_SUPABASE_ANON_KEY=placeholder VITE_ENVIRONMENT=production pnpm nx build giki-ai-app)", "Bash(VITE_API_BASE_URL=https://giki-ai-api-6uyufgxcxa-uc.a.run.app VITE_SUPABASE_URL=https://ngnskaiazefujixbdkkk.supabase.co VITE_SUPABASE_ANON_KEY=placeholder VITE_ENVIRONMENT=production pnpm nx build giki-ai-app --verbose)", "Bash(VITE_API_BASE_URL=https://giki-ai-api-6uyufgxcxa-uc.a.run.app VITE_SUPABASE_URL=https://ngnskaiazefujixbdkkk.supabase.co VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nbnNrYWlhemVmdWppeGJka2trIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzEzNjgxMjQsImV4cCI6MjA0Njk0NDEyNH0.T_65X8NLjLT5wYbsrqlzfU-3D1OHMYyHcWHDnhT-zQ4 VITE_ENVIRONMENT=production vite build)", "Bash(VITE_API_BASE_URL=https://giki-ai-api-6uyufgxcxa-uc.a.run.app VITE_SUPABASE_URL=https://ngnskaiazefujixbdkkk.supabase.co VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nbnNrYWlhemVmdWppeGJka2trIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzEzNjgxMjQsImV4cCI6MjA0Njk0NDEyNH0.T_65X8NLjLT5wYbsrqlzfU-3D1OHMYyHcWHDnhT-zQ4 VITE_ENVIRONMENT=production pnpm exec vite build)", "Bash(firebase projects:create:*)", "Bash(firebase hosting:sites:create:*)", "Bash(firebase hosting:channel:deploy:*)", "Bash(firebase hosting:sites:get:*)", "Bash(firebase hosting:sites:domain:create:*)", "<PERSON><PERSON>(firebase hosting:channel:list:*)", "Bash(firebase hosting:sites:list:*)", "Bash(firebase target:apply:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_console_messages", "Bash(VITE_API_BASE_URL=https://giki-ai-api-6uyufgxcxa-uc.a.run.app VITE_SUPABASE_URL=https://ngnskaiazefujixbdkkk.supabase.co VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nbnNrYWlhemVmdWppeGJka2trIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzEzNjgxMjQsImV4cCI6MjA0Njk0NDEyNH0.T_65X8NLjLT5wYbsrqlzfU-3D1OHMYyHcWHDnhT-zQ4 VITE_ENVIRONMENT=production pnpm exec vite build --mode production)", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "Bash(npx tsc:*)", "mcp__playwright__browser_press_key", "mcp__playwright__browser_file_upload", "mcp__filesystem__search_files", "mcp__filesystem__read_file", "mcp__filesystem__edit_file", "<PERSON><PERSON>(npx prettier:*)", "Bash(npx firebase deploy:*)", "mcp__playwright__browser_close", "Bash(CORS_ALLOWED_ORIGINS=\"https://app1.example.com,https://app2.example.com\" uv run python -c \"from src.giki_ai_api.core.config import settings; print('CORS Origins:', settings.get_cors_origins())\")", "Bash(/Users/<USER>/giki-ai-workspace/scripts/analyze_column_interpretation.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/analyze_sample_data_accuracy.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/call_real_temporal_validation.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/complete_monthly_accuracy.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/create_default_categories.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/create_rag_corpus_from_customer_data.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/debug-performance.py)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/monthly_accuracy_analysis.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_accuracy_final.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_accuracy_measurement.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_accuracy_measurement_final.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_accuracy_with_schema_discovery.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_input_file_accuracy.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/real_monthly_accuracy_measurement.py)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/temporal_accuracy_measurement.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/test-api-performance.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/test-cors-config.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/upload_all_files.py)", "Bash(./scripts/manual-deploy.sh:*)", "<PERSON><PERSON>(gcloud run:*)", "Bash(gcloud beta run:*)", "<PERSON><PERSON>(gcloud:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(gcloud run services:*", "Bash(nohup pnpm:*)", "<PERSON><PERSON>(python test:*)", "mcp__google-search__google_search", "Bash(./scripts/update-cors.sh:*)", "Bash(--member=\"allUsers\" )", "Bash(--region=us-central1 )", "Bash(--project=rezolve-poc)", "Bash(--region=us-central1 )", "Bash(--image us-central1-docker.pkg.dev/rezolve-poc/giki-ai/giki-ai-api:latest )", "Bash(--region us-central1 )", "Bash(--platform managed )", "Bash(--service-account <EMAIL>)", "Bash(--update-env-vars \"CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-************.us-central1.run.app,https://app-giki-ai.web.app,https://rezolve-poc.web.app\" )", "Bash(--region us-central1)", "Bash(--set-env-vars CORS_ALLOWED_ORIGINS=\"http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-************.us-central1.run.app,https://app-giki-ai.web.app,https://rezolve-poc.web.app\" )", "Bash(--env-vars-file=cloud-run-env.yaml )", "Bash(--global )", "Bash(--global )", "Bash(--format=\"value(address)\")", "Bash(--region=us-central1 )", "Bash(--network-endpoint-type=serverless )", "Bash(--cloud-run-service=giki-ai-api )", "Bash(--global )", "Bash(--global )", "Bash(--network-endpoint-group=giki-ai-api-neg )", "Bash(--network-endpoint-group-region=us-central1 )", "Bash(--default-service=giki-ai-backend )", "Bash(--url-map=giki-ai-lb )", "Bash(--global )", "Bash(--ssl-certificates=giki-ai-cert)", "Bash(# First, let's create an HTTP target proxy and forwarding rule for initial setup\ngcloud compute target-http-proxies create giki-ai-http-proxy \\\n  --url-map=giki-ai-lb \\\n  --global \\\n  --project=rezolve-poc)", "Bash(--address=giki-ai-lb-ip )", "Bash(--target-http-proxy=giki-ai-http-proxy )", "Bash(--global )", "Bash(--ports=80 )", "Bash(--security-policy=giki-ai-security-policy )", "Bash(--expression=\"true\" )", "Bash(--action=\"rate-based-ban\" )", "Bash(--rate-limit-threshold-count=100 )", "Bash(--rate-limit-threshold-interval-sec=60 )", "Bash(--ban-duration-sec=300 )", "Bash(--conform-action=\"allow\" )", "Bash(--exceed-action=\"deny-429\" )", "Bash(--enforce-on-key=\"IP\" )", "Bash(--security-policy=giki-ai-security-policy )", "Bash(--global )", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "Bash(--region=us-central1 )", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "Bash(--region=us-central1 )", "Bash(/dev/null)", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "Bash(--region=us-central1 )", "Bash(# Generate a self-signed certificate for testing\nopenssl req -x509 -nodes -days 365 -newkey rsa:2048 \\\n  -keyout /tmp/giki-ai-lb.key \\\n  -out /tmp/giki-ai-lb.crt \\\n  -subj \"/C=US/ST=California/L=San Francisco/O=Giki AI/CN=*************\")", "Bash(# Grant compute permissions to the service account\ngcloud projects add-iam-policy-binding rezolve-poc \\\n  --member=\"serviceAccount:<EMAIL>\" \\\n  --role=\"roles/compute.admin\")", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON><PERSON>(--role=\"roles/compute.admin\" )", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON>sh(--role=\"roles/compute.loadBalancerAdmin\" )", "Bash(--url-map=giki-ai-lb )", "Bash(--ssl-certificates=giki-ai-lb-cert )", "Bash(--global )", "Bash(--address=giki-ai-lb-ip )", "Bash(--target-https-proxy=giki-ai-https-proxy )", "Bash(--global )", "Bash(--ports=443 )", "Bash(--service=giki-ai-backend )", "Bash(--image us-central1-docker.pkg.dev/rezolve-poc/giki-ai/giki-ai-api:latest )", "Bash(--region=us-central1 )", "Bash(--member=allUsers )", "Bash(NODE_ENV=production pnpm nx build giki-ai-app)", "<PERSON><PERSON>(python3:*)", "mcp__supabase__list_projects", "mcp__supabase__get_project", "Bash(python scripts/execute_temporal_validation.py:*)", "Bash(scripts/server-status.sh:*)", "Bash(VITE_API_BASE_URL=http://localhost:8000 vite dev:*)", "Bash(# Kill the backend server\nlsof -t -i tcp:8000 | xargs kill -9 2>/dev/null || true\n\n# Give it a moment to clean up\nsleep 2\n\n# Restart the backend server\nnohup pnpm dev:api > logs/api-server.log 2>&1 &\n\n# Wait for server to start\nsleep 10\n\necho \"Backend server restarted\")", "WebFetch(domain:app-giki-ai.web.app)", "Bash(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON><PERSON>(--role=\"roles/logging.viewer\")", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON><PERSON>(--role=\"roles/secretmanager.admin\")", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "<PERSON>sh(--member=\"serviceAccount:<EMAIL>\" )", "Bash(pip3 install:*)", "Bash(./scripts/deploy.sh:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(./scripts/deploy-realtime.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/deploy-and-watch.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/watch-deployment.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/deploy-realtime.sh:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/stream-gh-logs.py )", "Bash(/Users/<USER>/giki-ai-workspace/scripts/push:*)", "Bash(./go \"fix: deployment configuration\")", "Bash(./go \"feat: unified nx-style deployment with real-time logs\")", "Bash(./go \"fix: remove tests from deployment workflow - tests are separate\")", "Bash(./go \"refactor: clean command structure - everything has consistent :api/:app pattern\")", "Bash(./go \"fix: add Firebase config and use production build\")", "Bash(./go \"fix: remove containerize flag breaking vite build\")", "Bash(npm run dev:*)", "mcp__playwright__browser_tab_list", "mcp__nx-mcp__nx_available_plugins", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(DATABASE_URL='postgresql://postgres.ngnskaiazefujixbdkkk:<EMAIL>:5432/postgres' uv run python scripts/test_db_connection.py)", "Bash(DATABASE_URL='postgresql://postgres.ngnskaiazefujixbdkkk:<EMAIL>:5432/postgres' uv run python scripts/test_psycopg_direct.py)", "Bash(.claude/commands/show-active:*)", "Bash(.claude/commands/roadmap-sync:*)", "Bash(npx eslint:*)", "mcp__screenshot__analyze_screenshot_comprehensively", "mcp__screenshot__validate_multiple_screenshots", "Bash(gtimeout 30 uv run python scripts/load-test-data-sql.py 2 >& 1)", "mcp__supabase__execute_sql", "mcp__github__list_commits", "mcp__supabase__list_tables", "Bash(./scripts/cleanup-test-processes.sh:*)", "Bash(./scripts/run-e2e-tests-autonomous.sh:*)", "Bash(./scripts/workflow/show-active:*)", "mcp__fetch-markdown__fetch_markdown_from_url", "Bash(PYTHONPATH=/Users/<USER>/giki-ai-workspace python -c \"\nimport sys\nsys.path.insert(0, '/Users/<USER>/giki-ai-workspace/apps/giki-ai-api/src')\nfrom giki_ai_api.core.database import get_database_config\nimport asyncio\nfrom sqlalchemy import text\nimport time\n\nasync def test():\n    print('Testing SQLite database configuration...')\n    start = time.perf_counter()\n    session_factory = get_database_config()\n    async with session_factory() as session:\n        result = await session.execute(text('SELECT 1'))\n        row = result.fetchone()\n    duration = (time.perf_counter() - start) * 1000\n    print(f'SQLite test: {duration:.3f}ms ✅')\n\nasyncio.run(test())\n\")", "Bash(npm view:*)", "Bash(npm search:*)", "Bash(./scripts/cloud-sql-production-setup.sh:*)", "Bash(gcloud config configurations activate default)", "Bash(./scripts/test-cloud-sql-local.sh:*)", "Bash(--clear-env-vars )", "Bash(--add-cloudsql-instances rezolve-poc:us-central1:giki-ai-postgres-prod)", "Bash(./scripts/deploy-frontend-cloudrun.sh:*)", "Bash(PLAYWRIGHT_SCREENSHOTS=true npx playwright test --project=chromium --reporter=list)", "Bash(ENVIRONMENT=development uv run scripts/add_transaction_columns_migration.py)", "Bash(npm search excel)", "Bash(alembic upgrade:*)", "mcp__playwright__browser_screen_click", "mcp__playwright__browser_screen_capture", "mcp__filesystem__list_directory", "mcp__filesystem__read_multiple_files", "<PERSON><PERSON>(./scripts/playwright-status.sh:*)", "mcp__playwright__browser_screen_type", "mcp__postgres__describe_table", "mcp__postgres__list_schemas", "mcp__postgres__list_tables", "Bash(pnpm exec playwright test --grep \"Database connection\" --reporter=list)", "Bash(grep -n \"npm install\\|yarn\\|pip install\" /Users/<USER>/giki-ai-workspace/README.md /Users/<USER>/giki-ai-workspace/CONTRIBUTING.md /Users/<USER>/giki-ai-workspace/docs/specifications/system-design-spec.md /Users/<USER>/giki-ai-workspace/docs/specifications/agents-and-tools-spec.md /Users/<USER>/giki-ai-workspace/docs/requirements/requirements.md)", "Bash(git commit -m \"fix database health endpoint by implementing asyncpg connection\n\n🤖 Generated with <PERSON>\n\nCo-Authored-By: <PERSON> <<EMAIL>>\")", "Bash(pnpm exec playwright test --grep \"Existing user login\" --reporter=list)", "Bash(grep -r \"npm install\\|yarn\\|pip install\\|python -m\\|npm run\\|yarn run\\|jest\\|mocha\" --include=\"*.md\" .)", "Bash(pnpm exec playwright test --grep \"Dashboard displays\" --reporter=list)", "Bash(npm run build:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(npx nx build:*)", "Bash(firebase logout:*)", "<PERSON><PERSON>(gcloud:*)", "<PERSON><PERSON>(gcloud run:*)", "Bash(gcloud run deploy:*)", "Bash(gsutil ls:*)", "Bash(bq ls:*)", "Bash(/Users/<USER>/giki-ai-workspace/scripts/create-database-user.sh:*)", "Bash(/opt/homebrew/opt/postgresql@15/bin/psql:*)", "Bash(brew services:*)", "Bash(/opt/homebrew/opt/postgresql@15/bin/createdb nikhilsingh)", "Bash(gcloud logging:*)", "Bash(gcloud logging read:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm:*)", "Bash(npx nx lint:*)", "Bash(npx tsc --noEmit:*)", "Bash(npx eslint --fix:*)", "<PERSON><PERSON>(npx prettier --write:*)", "Bash(alembic revision:*)", "<PERSON><PERSON>(alembic current:*)", "<PERSON><PERSON>(alembic history:*)", "Bash(DATABASE_URL=* uv run python:*)", "Bash(ENVIRONMENT=* uv run:*)", "Bash(uv add:*)", "Bash(uv remove:*)", "Bash(uv sync:*)", "Ba<PERSON>(uv lock:*)", "Bash(pnpm add:*)", "Bash(pnpm remove:*)", "Bash(pnpm update:*)", "Bash(pnpm outdated:*)", "Bash(pnpm audit:*)", "Bash(pnpm audit fix:*)", "Bash(git status:*)", "Bash(git diff:*)", "Bash(git log:*)", "<PERSON><PERSON>(git show:*)", "Bash(git branch:*)", "Bash(git stash:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(git rebase:*)", "Bash(gh pr:*)", "Bash(gh issue:*)", "Bash(gh repo:*)", "Bash(gh workflow:*)", "Bash(gh run:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker start:*)", "<PERSON><PERSON>(docker restart:*)", "Ba<PERSON>(docker images:*)", "Bash(docker system:*)", "<PERSON><PERSON>(gcloud auth:*)", "<PERSON>sh(gcloud config:*)", "Bash(gcloud projects:*)", "Bash(gcloud services:*)", "<PERSON><PERSON>(gcloud sql:*)", "Bash(gcloud compute:*)", "Bash(gcloud storage:*)", "Bash(gcloud container:*)", "Bash(gcloud artifacts:*)", "<PERSON><PERSON>(gsutil cp:*)", "Bash(gsutil ls:*)", "<PERSON>sh(gsutil rm:*)", "Bash(gsutil rsync:*)", "Bash(bq query:*)", "<PERSON><PERSON>(bq show:*)", "Bash(bq mk:*)", "Bash(bq ls:*)", "Bash(psql:*)", "Bash(pg_dump:*)", "Bash(pg_restore:*)", "<PERSON><PERSON>(createdb:*)", "<PERSON><PERSON>(dropdb:*)", "Bash(redis-cli:*)", "<PERSON>sh(redis-server:*)", "Bash(npm whoami:*)", "Bash(npm config:*)", "Bash(yarn:*)", "Bash(yarn add:*)", "Bash(yarn remove:*)", "Bash(yarn upgrade:*)", "Ba<PERSON>(pip list:*)", "<PERSON><PERSON>(pip show:*)", "Bash(pip freeze:*)", "<PERSON><PERSON>(python -m pip:*)", "<PERSON><PERSON>(python -c:*)", "<PERSON><PERSON>(python3 -c:*)", "<PERSON><PERSON>(uv run python -c:*)", "Bash(./scripts/db-*:*)", "Bash(./scripts/test-*:*)", "Bash(./scripts/deploy-*:*)", "Bash(./scripts/performance-*:*)", "Bash(./scripts/migration-*:*)", "Bash(./scripts/backup-*:*)", "Bash(./scripts/monitoring-*:*)", "Bash(./scripts/health-*:*)", "<PERSON><PERSON>(npx playwright test --project=*)", "<PERSON><PERSON>(npx playwright test --grep=*)", "<PERSON><PERSON>(npx playwright test --reporter=*)", "<PERSON><PERSON>(npx playwright show-report:*)", "<PERSON><PERSON>(npx playwright codegen:*)", "Bash(npx vitest:*)", "Bash(npx vitest run:*)", "<PERSON><PERSON>(npx jest:*)", "<PERSON><PERSON>(npx jest --watch:*)", "Bash(pytest:*)", "Bash(pytest -v:*)", "Bash(pytest --cov:*)", "<PERSON><PERSON>(coverage:*)", "<PERSON><PERSON>(coverage report:*)", "Bash(coverage html:*)", "Bash(black:*)", "Bash(isort:*)", "<PERSON><PERSON>(mypy:*)", "Bash(flake8:*)", "<PERSON><PERSON>(bandit:*)", "<PERSON><PERSON>(safety:*)", "Bash(pre-commit:*)", "<PERSON><PERSON>(pre-commit run:*)", "Bash(pre-commit install:*)", "<PERSON><PERSON>(commitizen:*)", "Bash(cz:*)", "Bash(semantic-release:*)", "Bash(VITE_*:*)", "Bash(NODE_ENV=* pnpm:*)", "Bash(NODE_ENV=* npm:*)", "Bash(NODE_ENV=* yarn:*)", "Bash(POSTGRES_*:*)", "Bash(REDIS_*:*)", "Bash(API_*:*)", "<PERSON><PERSON>(AUTH_*:*)", "Bash(SUPABASE_*:*)", "Bash(GOOGLE_*:*)", "Bash(OPENAI_*:*)", "<PERSON><PERSON>(ANTHROPIC_*:*)", "<PERSON><PERSON>(./scripts/playwright-server.sh:*)", "Bash(./scripts/ensure-postgres.sh:*)", "Bash(./tests/integration/the-one-integration-test.sh:*)", "Bash(terraform:*)", "mcp__nx-mcp__nx_workspace", "Bash(find /Users/<USER>/giki-ai-workspace -name \"project.json\" -exec grep -l \"scripts/\" {} \\;)", "Bash(ENVIRONMENT=development uv run python -c \"\nimport asyncio\nimport os\nfrom sqlalchemy.ext.asyncio import create_async_engine, AsyncSession\nfrom sqlalchemy.orm import sessionmaker\nfrom sqlalchemy import text\n\n# Use development database\nos.environ['DATABASE_URL'] = 'postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@localhost:5432/giki_ai_db'\n\nasync def check_local_db():\n    try:\n        engine = create_async_engine(os.environ['DATABASE_URL'])\n        async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)\n        \n        async with async_session() as session:\n            # Check if users table exists and get users\n            result = await session.execute(text('SELECT id, username, email, tenant_id FROM users LIMIT 5;'))\n            users = result.fetchall()\n            \n            if users:\n                print('=== EXISTING USERS ===')\n                for user in users:\n                    print(f'User {user[0]}: {user[1]} ({user[2]}) - Tenant: {user[3]}')\n            else:\n                print('No users found in local database')\n                \n            # Check tenants\n            result = await session.execute(text('SELECT id, name FROM tenants LIMIT 5;'))\n            tenants = result.fetchall()\n            \n            if tenants:\n                print('\\n=== EXISTING TENANTS ===')\n                for tenant in tenants:\n                    print(f'Tenant {tenant[0]}: {tenant[1]}')\n            else:\n                print('No tenants found in local database')\n                \n    except Exception as e:\n        print(f'Local database connection failed: {e}')\n        print('Local PostgreSQL may not be running or accessible')\n\nasyncio.run(check_local_db())\n\")", "Bash(ENVIRONMENT=development pnpm serve)", "mcp__postgres__query", "mcp__postgres-dev__list_tables", "mcp__postgres-prod__list_tables", "mcp__postgres-dev__query", "mcp__postgres-dev__describe_table"], "deny": []}, "enabledMcpjsonServers": ["playwright", "screenshot-validator", "context8"]}