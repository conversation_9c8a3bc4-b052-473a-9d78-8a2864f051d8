# Multi-Terminal Collaboration System

## Overview

This system enables safe parallel development across multiple terminals with Google Sheets integration for real-time visibility.

## Quick Start

1. **Setup Google Sheets** (one time):
   ```bash
   .claude/commands/sheet-setup
   ```

2. **Start work on a task**:
   ```bash
   .claude/commands/start-work TASK-XXX
   ```

3. **Check active work across all terminals**:
   ```bash
   .claude/commands/show-active
   ```

4. **Finish work**:
   ```bash
   .claude/commands/finish-work TASK-XXX completed
   # or
   .claude/commands/finish-work TASK-XXX released
   ```

## Key Features

### Dynamic Session Management
- Unlimited concurrent terminals
- Unique session IDs (e.g., term-a7f3)
- Automatic conflict detection
- 2-hour automatic task release

### Google Sheets Integration
- Real-time dashboard at: https://docs.google.com/spreadsheets/d/1UwuQFqWta99M25gNqdfdRr3pOhyGsT-6Ez6R1ID4SdI/
- Two-way sync (content from markdown, status from sheets)
- Executive reporting and metrics
- Works offline with local markdown as backup

### Conflict Prevention
- File-level conflict detection
- Git status awareness
- Domain-based task suggestions
- Smart task routing

## Commands Reference

| Command | Description | Example |
|---------|-------------|---------|
| `start-work` | Claim a task | `.claude/commands/start-work TASK-001` |
| `finish-work` | Complete or release | `.claude/commands/finish-work TASK-001 completed` |
| `show-active` | View all active work | `.claude/commands/show-active` |
| `check-conflicts` | Check file conflicts | `.claude/commands/check-conflicts src/auth` |
| `stale-check` | Find abandoned tasks | `.claude/commands/stale-check 2` |
| `sync-tasks` | Sync with Google Sheets | `.claude/commands/sync-tasks` |
| `sheet-setup` | Configure Google Sheets | `.claude/commands/sheet-setup` |

## Task Status Format

Tasks follow this status pattern:
- `Pending` - Available for anyone
- `Active:term-a7f3:2025-06-19-14:30` - Being worked on
- `Completed` - Done

## File Structure

```
.claude/
├── commands/           # Executable command scripts
├── .sessions/         # Active session tracking
├── .task-file-map     # Task to file mapping
├── google-sheets-key.json  # Google API credentials (git ignored)
└── README.md          # This file
```

## Best Practices

1. **Always check conflicts** before starting major work
2. **Release tasks** if you need to switch focus
3. **Sync regularly** with Google Sheets for visibility
4. **Run stale-check** daily to clean up abandoned tasks
5. **Update task-file-map** when creating new task types

## Troubleshooting

**Task already claimed?**
```bash
.claude/commands/show-active  # See who has it
.claude/commands/stale-check  # Check if it's abandoned
```

**Google Sheets not syncing?**
```bash
.claude/commands/sheet-setup  # Re-run setup
# Check that sheet is shared with service account
```

**Conflicts detected?**
```bash
git status  # Check uncommitted changes
.claude/commands/check-conflicts path/to/files
```

## Architecture

```
Local Markdown Files (Source of Truth)
         ↓
    Task Parser
         ↓
  Google Sheets API
         ↓
    Dashboard View
         ↓
  Status Updates Only
         ↓
    Back to Markdown
```

This creates a professional task management system that scales with any number of developers while maintaining the simplicity of markdown files as the source of truth.