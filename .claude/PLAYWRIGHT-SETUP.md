# Playwright MCP Setup for Customer Simulation

## Overview
Uses Microsoft's official Playwright MCP server with persistent profile for customer simulation and UI testing.

## Current Configuration
The Playwright MCP server is already configured in `/Users/<USER>/.claude.json`:

```json
"playwright": {
  "transport": ["sse"],
  "url": "http://localhost:3100/sse"
}
```

## Starting the Server
Use the corrected script to start with SSE transport:

```bash
./scripts/playwright-server.sh
```

This runs:
```bash
npx @playwright/mcp@latest \
  --port 3100 \
  --user-data-dir /Users/<USER>/giki-ai-workspace/.playwright-mcp/user-data \
  --vision \
  --output-dir /Users/<USER>/giki-ai-workspace/screenshots
```

## Key Features for Customer Simulation
1. **Persistent Profile**: Maintains login state across sessions
2. **Vision Mode**: Uses screenshots for visual interactions
3. **Customer Journey Testing**: Simulate real user interactions
4. **Screenshot Capture**: Document customer experience issues

## Available Tools for Customer Simulation
- `browser_navigate` - Navigate to app pages
- `browser_take_screenshot` - Capture customer view
- `browser_click` - Simulate customer clicks
- `browser_type` - Simulate customer input
- `browser_snapshot` - Get page structure (accessibility)
- `browser_wait_for` - Wait for page loads (customer patience)

## Customer Simulation Usage
1. Start server: `./scripts/playwright-server.sh`
2. Navigate to app: Use `browser_navigate` to http://localhost:4200
3. Take screenshots: Document what customers see
4. Simulate interactions: Click, type, upload files
5. Measure timing: Customer patience for load times

## Troubleshooting
- Server won't start: Kill existing processes on port 3100
- Profile locked: Clear `.playwright-mcp/user-data/Singleton*`
- No screenshots: Check `screenshots/` directory permissions