# Task to File Mapping
# Format: TASK-ID: file-patterns (space-separated)
# This helps detect conflicts when multiple terminals work on tasks

# Authentication & Security
TASK-AUTH-001: src/features/auth/* apps/giki-ai-api/domains/auth/* apps/giki-ai-api/core/secure_*
TASK-SECURE-AUTH-001: apps/giki-ai-api/core/secure_* apps/giki-ai-api/domains/auth/*

# Frontend UI Tasks
TASK-UI-001: src/features/dashboard/* src/shared/components/*
TASK-DESIGN-*: src/styles/* src/shared/components/* tailwind.config.ts
TASK-LINT-001: apps/giki-ai-app/src/* eslint.config.mjs

# Backend Tasks
TASK-DB-*: apps/giki-ai-api/core/database.py migrations/* scripts/*database*
TASK-API-*: apps/giki-ai-api/domains/*/router.py apps/giki-ai-api/core/main.py
TASK-AGENT-*: apps/giki-ai-api/domains/*/agent.py apps/giki-ai-api/ai/*

# Data Processing
TASK-DATA-*: apps/giki-ai-api/domains/transactions/* apps/giki-ai-api/domains/files/*
TASK-TRANSACTION-*: apps/giki-ai-api/domains/transactions/* src/features/transactions/*

# Categories
TASK-CATEGORY-*: apps/giki-ai-api/domains/categories/* src/features/categories/*
TASK-CAT-*: apps/giki-ai-api/domains/categories/* scripts/*category*

# Deployment & Infrastructure
TASK-DEPLOY-*: Dockerfile* .github/workflows/* cloud-run-* firebase.json
TASK-CICD-*: .github/workflows/* scripts/deploy*
TASK-BUILD-*: nx.json package.json pyproject.toml vite.config.ts

# Testing
TASK-TEST-*: tests/* apps/giki-ai-app/src/**/*.test.* apps/giki-ai-api/tests/*
TASK-E2E-*: tests/e2e/* playwright.config.ts

# Documentation
TASK-DOC-*: docs/* README.md CLAUDE.md
TASK-AUDIT-*: docs/audits/*

# Multi-tenant
TASK-TENANT-*: apps/giki-ai-api/domains/auth/* scripts/*tenant* migrations/*tenant*

# Reports
TASK-REPORT-*: apps/giki-ai-api/domains/reports/* src/features/reports/*

# Onboarding
TASK-ONBOARDING-*: apps/giki-ai-api/domains/onboarding/* src/features/onboarding/*