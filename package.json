{"name": "giki-ai-workspace", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "libs/*"], "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "form-data": "^4.0.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.510.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.28.0", "recharts": "^2.15.3", "serve": "^14.2.4", "tailwind-merge": "^3.3.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "zod": "^3.25.1", "zustand": "^5.0.4"}, "devDependencies": {"@axe-core/cli": "^4.10.1", "@axe-core/playwright": "^4.10.1", "@eslint/js": "^9.27.0", "@nx/eslint": "^21.2.1", "@nx/eslint-plugin": "^21.2.1", "@nx/react": "^21.2.1", "@nx/storybook": "^21.2.1", "@nx/vite": "^21.2.1", "@nx/workspace": "^21.2.1", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@types/testing-library__jest-dom": "^6.0.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "3.2.4", "axe-core": "^4.10.3", "concurrently": "^9.1.2", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "lighthouse": "^12.6.0", "nx": "^21.2.1", "playwright-lighthouse": "^4.0.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.33.0", "vite": "^5.4.19", "vitest": "^3.2.4"}, "scripts": {"preinstall": "node -e \"if (process.env.npm_execpath && !process.env.npm_execpath.includes('pnpm')) { console.error('\\n\\nERROR: This project requires pnpm. Please install it with: npm install -g pnpm\\n\\n'); process.exit(1); }\"", "serve": "./scripts/start-servers.sh", "serve:stop": "./scripts/stop-servers.sh", "serve:restart": "./scripts/restart-servers.sh", "serve:status": "./scripts/check-servers.sh", "logs:manage": "./scripts/log-manager.sh manage", "check-logs:dev:api": "nx check-logs giki-ai-api --configuration=development", "check-logs:dev:app": "nx check-logs giki-ai-app --configuration=development", "check-logs:prod:api": "nx check-logs giki-ai-api --configuration=production", "check-logs:prod:app": "nx check-logs giki-ai-app --configuration=production", "serve:api": "nx serve giki-ai-api", "serve:app": "nx serve giki-ai-app", "test": "nx run-many --target=test --projects=giki-ai-api,giki-ai-app", "test:api": "nx test giki-ai-api", "test:app": "nx test giki-ai-app", "test:e2e": "npx playwright test --headed", "lint": "nx run-many --target=lint --projects=giki-ai-api,giki-ai-app", "lint:api": "nx lint giki-ai-api", "lint:app": "nx lint giki-ai-app", "deploy:dev": "nx deploy --configuration=development", "deploy:prod": "nx deploy --configuration=production", "deploy:dev:api": "nx deploy giki-ai-api --configuration=development", "deploy:dev:app": "nx deploy giki-ai-app --configuration=development", "deploy:prod:api": "nx deploy giki-ai-api --configuration=production", "deploy:prod:app": "nx deploy giki-ai-app --configuration=production"}}