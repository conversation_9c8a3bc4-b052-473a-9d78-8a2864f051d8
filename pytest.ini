[tool:pytest]
# Comprehensive Test Configuration for Enhanced ADK Services
# 
# Configures pytest for testing:
# - FileValidationService
# - RAGManagementService  
# - OnboardingService
# - ADK tool function integration
# - Performance requirements
# - Coverage thresholds (>85%)

# Test discovery
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test paths
testpaths = 
    apps/giki-ai-api/tests
    tests

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=85
    --durations=10
    --maxfail=5

# Coverage configuration
[coverage:run]
source = 
    apps/giki-ai-api/src/giki_ai_api/services/data/file_signature
    apps/giki-ai-api/src/giki_ai_api/services/data/rag_management
    apps/giki-ai-api/src/giki_ai_api/services/customer/onboarding

omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */.venv/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = true
precision = 2
fail_under = 85

[coverage:html]
directory = htmlcov
title = Enhanced ADK Services Coverage Report

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests between services
    performance: Performance requirement tests
    adk_tools: ADK tool function tests
    file_validation: FileValidationService tests
    rag_management: RAGManagementService tests
    onboarding: OnboardingService tests
    frontend: Frontend service tests
    slow: Tests that take longer to run
    requires_db: Tests that require database connection
    requires_api: Tests that require API server
    agent_test: marks tests as agent integration tests  
    service_test: marks tests as service integration tests
    router_test: marks tests as router integration tests
    error_test: marks tests as error handling tests
    e2e_test: marks tests as end-to-end workflow tests
    real_ai: marks tests that require real AI services
    security: marks tests as security tests
    accuracy: marks tests as AI accuracy validation tests

# Asyncio configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout configuration
timeout = 300
timeout_method = thread

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel execution with pytest-xdist

# Filter warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    error::pytest.PytestUnraisableExceptionWarning

# Environment variables for testing
env = 
    TESTING = true
    DATABASE_URL = sqlite+aiosqlite:///:memory:
    ENVIRONMENT = test
    LOG_LEVEL = INFO
    JWT_SECRET_KEY = test-secret-key-for-api-tests-very-long-secure-key
    VERTEX_PROJECT_ID = giki-ai-platform
    GOOGLE_APPLICATION_CREDENTIALS = ./dev-service-account.json
