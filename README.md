# Giki AI Workspace

Advanced financial data processing system with intelligent categorization and analysis capabilities.

## Technical Architecture

> **📋 Complete Architecture & Tech Stack**: @docs/specifications/system-design-spec.md
> **📋 Performance Requirements**: @docs/audits/performance-metrics.md
> **📋 Quality Standards**: @docs/audits/quality-metrics.md

## Development Setup

### Prerequisites
```bash
# Node.js Environment
pnpm install

# Python Environment
uv venv
source .venv/bin/activate
uv sync

# Database
# Local PostgreSQL required for development
# Production uses Google Cloud SQL
```

### Essential Commands
```bash
# Development
pnpm dev                 # Start development environment
pnpm test:watch         # Watch mode testing
pnpm env:dev            # Set development environment

# Production
pnpm build:prod         # Production build
pnpm deploy             # Deploy to production
pnpm env:prod           # Set production environment
pnpm deploy:dry-run     # Test deployment
```

## Project Structure

> **📋 Complete Directory Structure**: @docs/specifications/system-design-spec.md#codebase-structure
> **📋 Documentation Structure**: @docs/README.md

## Service Architecture

> **📋 Domain-Driven Architecture**: @docs/specifications/system-design-spec.md#backend-architecture
> **📋 Agent Implementation**: @docs/specifications/agents-and-tools-spec.md

## Technical Capabilities

### Data Processing
- Multi-sheet Excel support
- Automatic column interpretation
- Different schemas per sheet
- Hierarchical category detection
- Credit/debit transaction mapping
- Real-time processing

### AI Features (✅ REAL AI IMPLEMENTATION)
- **Intelligent Transaction Categorization**: Vertex AI Gemini 2.0 Flash with >85% accuracy
- **AI Reasoning Engine**: Detailed explanations for categorization decisions
- **Confidence Scoring**: Real-time confidence assessment (0.0-1.0) for all AI decisions
- **Natural Language Processing**: Intelligent query parsing and conversation context
- **Entity Extraction**: Hybrid approach with LLM fallback for complex patterns
- **RAG-Enhanced Processing**: Vector-based similarity search for context retrieval
- **Real-time Learning**: Adaptive categorization based on transaction patterns

### Performance Metrics
- File Processing: <60s for 1MB files
- Bulk Processing: <30s for 10,000 transactions
- Schema Operations: <5s
- RAG Operations: <100ms similarity search
- File Validation: <2s
- Customer Onboarding: <5min complete process

## Core Technical Rules

### Development Environment
1. NEVER use containers for local development
2. ALWAYS use Cloud Run for production
3. NEVER create intermediate environments
4. ALWAYS use PostgreSQL (local for dev, Cloud SQL for prod)
5. NEVER manually verify deployments

### Technical Documentation
- Specs/ directory uses standard markdown
- All other docs/ subdirectories use YAML-like format
- All metrics must be concrete and measurable
- All changes require validation evidence
- No theoretical estimates or subjective assessments

### Testing Requirements
1. Screenshot validation with Playwright
2. Automated test coverage verification
3. Performance metrics validation
4. Specific test commands for each feature
5. E2E test coverage must be 100%

## Environment URLs

### Production
- Frontend: https://giki-ai-frontend-************.us-central1.run.app
- API: https://dev-giki-ai-api-************.us-central1.run.app
- API Docs: https://dev-giki-ai-api-************.us-central1.run.app/docs

### Development
- Frontend: http://localhost:4200
- API: http://localhost:8000
- API Docs: http://localhost:8000/docs
- Database: PostgreSQL on localhost:5433

## UI Architecture
- Excel-inspired interface
- Three-column layout
  - Navigation
  - Content
  - Agent panel
- Branding color: #0D4F12 (systematic color mapping from logo to dark variants)
- Frontend Status: Optimized codebase with 15+ unused components removed
- Key sections:
  - Upload Data
  - Reports
  - Knowledge Hub
  - Customer Onboarding
  - Settings

## Multi-Tenant Setup ✅ VERIFIED
**Three Required Tenants Configured:**
- **Giki AI** (ID: 1) - Primary development tenant
- **Rezolve AI** (ID: 2) - Enterprise client tenant
- **Nuvie** (ID: 3) - Enterprise client tenant

**Test Accounts (All use password: `gikiai-dev-password`):**
- `<EMAIL>` (Giki AI - Primary Admin)
- `<EMAIL>` (Giki AI - Secondary User)
- `<EMAIL>` (Rezolve AI - Admin)
- `<EMAIL>` (Rezolve AI - Test User)
- `<EMAIL>` (Nuvie - Admin)
- `<EMAIL>` (Nuvie - Test User)

**Database Status:** Complete tenant isolation with proper foreign key relationships

## Contributing
Refer to `.augment-guidelines` for detailed technical standards and requirements.

## Support
- Technical Documentation: `.augment-guidelines`
- API Health: https://dev-giki-ai-api-************.us-central1.run.app/health
- API Documentation: https://dev-giki-ai-api-************.us-central1.run.app/docs
# Test Deploy Trigger
