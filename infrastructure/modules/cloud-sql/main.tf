# Cloud SQL Module
# Creates environment-specific PostgreSQL instances

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
}

locals {
  instance_name = "giki-ai-postgres-${var.environment}"
  database_name = "giki_ai_db"
  username      = "giki_ai_user"
  
  labels = {
    environment = var.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# Generate random password
resource "random_password" "db_password" {
  length  = 32
  special = true
}

# Cloud SQL Instance
resource "google_sql_database_instance" "postgres" {
  name             = local.instance_name
  database_version = "POSTGRES_15"
  region           = var.region
  project          = var.project_id
  
  deletion_protection = var.environment == "production" ? true : false
  
  settings {
    tier                        = var.database_tier
    availability_type          = var.environment == "production" ? "REGIONAL" : "ZONAL"
    disk_type                  = "PD_SSD"
    disk_size                  = var.disk_size
    disk_autoresize           = true
    disk_autoresize_limit     = var.max_disk_size
    
    # Performance settings
    database_flags {
      name  = "max_connections"
      value = var.max_connections
    }
    
    database_flags {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements"
    }
    
    # Backup configuration
    backup_configuration {
      enabled                        = true
      start_time                    = "02:00"
      location                      = var.backup_location
      point_in_time_recovery_enabled = var.environment == "production"
      backup_retention_settings {
        retained_backups = var.environment == "production" ? 30 : 7
      }
      transaction_log_retention_days = var.environment == "production" ? 7 : 3
    }
    
    # IP configuration
    ip_configuration {
      ipv4_enabled    = true
      private_network = var.vpc_network
      require_ssl     = true
      
      # Allow Cloud Run access
      dynamic "authorized_networks" {
        for_each = var.authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.value
        }
      }
    }
    
    # Maintenance window
    maintenance_window {
      day  = 7  # Sunday
      hour = 3  # 3 AM
    }
    
    user_labels = local.labels
  }
}

# Database
resource "google_sql_database" "database" {
  name      = local.database_name
  instance  = google_sql_database_instance.postgres.name
  charset   = "UTF8"
  collation = "en_US.UTF8"
}

# Database user
resource "google_sql_user" "user" {
  name     = local.username
  instance = google_sql_database_instance.postgres.name
  password = random_password.db_password.result
}

# Store database credentials in Secret Manager
resource "google_secret_manager_secret" "db_password" {
  secret_id = "giki-ai-${var.environment}-db-password"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "db_password" {
  secret      = google_secret_manager_secret.db_password.id
  secret_data = random_password.db_password.result
}

# Database URL for application
locals {
  connection_name = google_sql_database_instance.postgres.connection_name
  database_url = var.environment == "production" ? "postgresql+asyncpg://${local.username}:${random_password.db_password.result}@/${local.database_name}?host=/cloudsql/${local.connection_name}" : "postgresql+asyncpg://${local.username}:${random_password.db_password.result}@${google_sql_database_instance.postgres.private_ip_address}:5432/${local.database_name}"
}

resource "google_secret_manager_secret" "database_url" {
  secret_id = "giki-ai-${var.environment}-database-url"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "database_url" {
  secret      = google_secret_manager_secret.database_url.id
  secret_data = local.database_url
}