#!/bin/bash
# Infrastructure Deployment Script
# Deploys Terraform infrastructure for specified environment

set -e

ENVIRONMENT=${1:-development}
PROJECT_ID=${PROJECT_ID:-rezolve-poc}
REGION=${REGION:-us-central1}

echo "🚀 Deploying infrastructure for environment: $ENVIRONMENT"

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Environment must be 'development' or 'production'"
    exit 1
fi

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "❌ Error: Not authenticated with gcloud. Run: gcloud auth login"
    exit 1
fi

# Set project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "📋 Enabling required Google Cloud APIs..."
gcloud services enable \
    cloudsql.googleapis.com \
    run.googleapis.com \
    secretmanager.googleapis.com \
    storage.googleapis.com \
    aiplatform.googleapis.com \
    firebase.googleapis.com \
    iam.googleapis.com

# Create Terraform state bucket if it doesn't exist
BUCKET_NAME="giki-ai-terraform-state"
if ! gsutil ls -b gs://$BUCKET_NAME >/dev/null 2>&1; then
    echo "📦 Creating Terraform state bucket..."
    gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME
    gsutil versioning set on gs://$BUCKET_NAME
fi

# Navigate to environment directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_DIR="$SCRIPT_DIR/../environments/$ENVIRONMENT"

if [[ ! -d "$ENV_DIR" ]]; then
    echo "❌ Error: Environment directory not found: $ENV_DIR"
    exit 1
fi

cd "$ENV_DIR"

# Initialize Terraform
echo "🔧 Initializing Terraform..."
terraform init

# Plan deployment
echo "📋 Planning Terraform deployment..."
terraform plan \
    -var="project_id=$PROJECT_ID" \
    -var="region=$REGION" \
    -out=tfplan

# Apply deployment
echo "🚀 Applying Terraform configuration..."
terraform apply tfplan

# Clean up plan file
rm -f tfplan

echo "✅ Infrastructure deployment completed for $ENVIRONMENT environment"

# Output important information
echo ""
echo "📋 Deployment Summary:"
echo "   Environment: $ENVIRONMENT"
echo "   Project ID:  $PROJECT_ID"
echo "   Region:      $REGION"
echo ""
echo "🔑 Next Steps:"
echo "   1. Update nx deployment configuration to use new service accounts"
echo "   2. Update application configuration to use Secret Manager"
echo "   3. Deploy applications using: nx deploy giki-ai-api --configuration=$ENVIRONMENT"
echo ""