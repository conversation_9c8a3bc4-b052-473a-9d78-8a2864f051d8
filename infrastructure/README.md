# Infrastructure as Code - Terraform

## Overview
This directory contains Terraform configurations for managing giki.ai infrastructure across development and production environments.

## Structure
```
infrastructure/
├── environments/
│   ├── development/     # Dev environment specific configs
│   └── production/      # Prod environment specific configs
├── modules/             # Reusable Terraform modules
│   ├── service-accounts/
│   ├── cloud-sql/
│   ├── cloud-run/
│   ├── firebase/
│   └── secrets/
├── shared/              # Shared resources across environments
└── scripts/             # Helper scripts for deployment
```

## Usage

### Initialize Environment
```bash
cd infrastructure/environments/development
terraform init
terraform plan
terraform apply
```

### Deploy to Production
```bash
cd infrastructure/environments/production
terraform init
terraform plan
terraform apply
```

## Environment Variables Required
- `TF_VAR_project_id` - GCP Project ID
- `TF_VAR_region` - GCP Region (us-central1)
- `TF_VAR_environment` - Environment name (development/production)

## Security
- All sensitive values stored in Google Secret Manager
- Service accounts follow principle of least privilege
- Separate resources per environment for isolation