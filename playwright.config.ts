import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright Configuration for giki.ai Platform
 *
 * This configuration supports both local development and production testing.
 * Production testing targets: https://giki-ai-frontend-273348121056.us-central1.run.app
 * Local testing targets: http://localhost:4200
 */
export default defineConfig({
  // Test directory
  testDir: './tests/e2e',

  // Run tests in files in parallel - DISABLED for authentication stability
  fullyParallel: false,

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // Retry on CI only
  retries: process.env.CI ? 2 : 0,

  // Single worker to avoid auth conflicts
  workers: 1,

  // Reporter to use - fully autonomous reporting
  reporter: [
    ['dot'], // Minimal console output during test run
    ['json', { outputFile: 'test-results/playwright-results.json' }],
    ['html', { outputFolder: 'playwright-report', open: 'never' }] // Generate report but never auto-open
  ],

  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4200',

    // Collect trace when retrying the failed test
    trace: 'on-first-retry',

    // Take screenshot on failure
    screenshot: 'only-on-failure',

    // Record video only on failure (fully autonomous)
    video: 'retain-on-failure',

    // Global timeout for each action
    actionTimeout: 30000,

    // Global timeout for navigation
    navigationTimeout: 30000,

    // Run with visible browser (for debugging and monitoring)
    headless: false,
  },

  // Configure projects for major browsers
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    /* Other browsers temporarily disabled for faster testing
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },

    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    */
  ],

  // Global setup and teardown
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),

  // Output directory for test artifacts
  outputDir: 'test-results/playwright-artifacts',

  // Timeout for each test
  timeout: 120000, // Increased to 2 minutes for file upload operations

  // Expect timeout
  expect: {
    timeout: 10000,
  },

  // Disable automatic report opening
  reportSlowTests: null,

  // Web server configuration - use existing servers or start new ones
  webServer: [
    {
      command: 'pnpm serve:api',
      port: 8000,
      timeout: 120 * 1000, // 2 minutes startup time
      reuseExistingServer: true, // Always reuse if already running
      stdout: 'ignore',
      stderr: 'ignore',
      env: {
        NODE_ENV: 'development',
        DEBUG: 'false', // Ensure DEBUG is a valid boolean for API
      },
    },
    {
      command: 'pnpm serve:app',
      port: 4200,
      timeout: 120 * 1000, // 2 minutes startup time
      reuseExistingServer: true, // Always reuse if already running
      stdout: 'ignore',
      stderr: 'ignore',
      env: {
        NODE_ENV: 'development',
        DEBUG: 'false', // Ensure DEBUG is a valid boolean for API
      },
    },
  ],
});
