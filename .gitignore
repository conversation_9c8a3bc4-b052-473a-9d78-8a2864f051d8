.venv
node_modules
__pycache__
.ruff_cache
.mypy_cache
.pytest_cache
.nx/

# Development Server Logs (multi-agent log management)
**/*.log
**/*.log.*
**/logs/
**/server.log
**/api.log
**/frontend.log
**/dev-server.log
**/error.log
**/access.log

# Zellij Session Data
**/.zellij/
**/zellij-logs/

# Build and Cache Directories
**/*.terraform*
**/*cache*
gcp/.terraform
.vscode
.vite-cache/
**/dist/
**/build/
**/coverage_html_report/
**/htmlcov/

# Ignore JS/map/declaration files in giki-ai-app src to prevent stale artifacts
apps/giki-ai-app/src/**/*.js
apps/giki-ai-app/src/**/*.js.map
apps/giki-ai-app/src/**/*.d.ts
apps/giki-ai-api/local_wheels/

# Service account files
service-accounts/
**/service-account*.json
**/terraform-service-account*.json
**/*-key.json

# Environment Files
**/.env
**/.env.local
**/.env.production
**/.env.development

# Database Files (local development)
**/*.db
**/*.sqlite
**/*.sqlite3
.coverage

# Large Upload Files (keep structure, ignore files)
apps/giki-ai-api/uploads/*.xlsx
apps/giki-ai-api/uploads/*.csv
apps/giki-ai-api/uploads/*.parquet
uploads/*.xlsx
uploads/*.csv
uploads/*.parquet

# AI Agent Temporary Files
**/.augment/
**/.cursor-tmp/
**/.claude-tmp/
# Multi-terminal collaboration
.claude/google-sheets-key.json
.claude/.sessions/
.claude/google-sheets-config
**/.roo-tmp/

# Test Results and Screenshots
**/test-results/
**/playwright-report/
screenshots/*.png
screenshots/

# Test Scripts
test-*.js
test-*.py
test-*.ts

# Log Files at Root
/*.log
/*.log.*

# NX Workspace Data (should not be tracked)
.nx/workspace-data/

# Temporary Repositories
temp_repos/

# All Upload Files (keep directory structure)
apps/giki-ai-api/uploads/*
!apps/giki-ai-api/uploads/.gitkeep

storybook-static

# Package Manager Artifacts (enforce pnpm only)
package-lock.json
yarn.lock
.yarn/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
