# M1 Nuvie Zero-Onboarding Accuracy Criteria

milestone: M1
tenant: Nuvie
scenario: zero-onboarding
accuracy_target: 85%
evaluation_method: business_appropriateness

# What defines "correct" categorization for zero-onboarding
business_appropriateness_criteria:
  definition: "Categories must be business-logical and consistent across similar transactions"
  
  appropriate_examples:
    - description: "Staples office supplies purchase"
      appropriate_categories: ["Office Supplies", "Business Supplies", "Administrative Expenses"]
      inappropriate_categories: ["Entertainment", "Travel", "Personal"]
    
    - description: "Client lunch meeting at restaurant"
      appropriate_categories: ["Meals & Entertainment", "Client Entertainment", "Business Meals"]
      inappropriate_categories: ["Office Supplies", "Travel", "Equipment"]
    
    - description: "Uber ride to client meeting"
      appropriate_categories: ["Travel", "Transportation", "Business Travel"]
      inappropriate_categories: ["Office Supplies", "Entertainment", "Equipment"]

  consistency_requirements:
    similar_descriptions_same_category: true
    similar_amounts_considered: false  # Amount shouldn't drive category
    merchant_patterns_recognized: true  # Same merchant should get consistent categorization
    
  evaluation_prompts:
    business_logic: "Does this category make sense for a business expense of this type?"
    consistency_check: "Would a business owner consistently categorize similar transactions this way?"
    professional_appropriateness: "Is this category appropriate for financial reporting?"

# Batch processing configuration for M1
batch_processing:
  batch_size: 150  # Optimized for zero-onboarding volume
  processing_time_target: 30  # seconds per batch
  confidence_threshold: 0.7  # Minimum confidence for acceptance
  
# Success criteria
success_metrics:
  accuracy_target: 85%  # Percentage of transactions judged business-appropriate
  confidence_interval: "±5%"  # Statistical significance requirement
  processing_performance: "<3s per transaction average"
  consistency_score: ">80%"  # Similar transactions get similar categories

# AI judge evaluation configuration
ai_judge_config:
  model: "gemini-2.0-flash-001"
  temperature: 0.1  # Low temperature for consistent evaluation
  evaluation_criteria:
    - business_logic_appropriateness
    - category_consistency_across_similar_transactions
    - professional_financial_reporting_standards
  
  scoring_method: "binary"  # appropriate (1) or inappropriate (0)
  confidence_reporting: true
  batch_evaluation: true  # Evaluate transactions in context of each other