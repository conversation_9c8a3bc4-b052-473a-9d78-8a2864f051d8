# M2 Rezolve Improvement-Over-Original Criteria

milestone: M2
tenant: Rezolve
scenario: historical_data_training
accuracy_target: 85%
evaluation_method: improvement_over_original

# What defines "better" categorization compared to customer's original
improvement_criteria:
  definition: "Our AI categories should be more specific, accurate, and business-useful than customer's original categories"
  
  improvement_examples:
    - original_category: "Misc Expense"
      ai_category: "Office Supplies"
      improvement_type: "specificity"
      judgment: "better"  # More specific and useful
    
    - original_category: "General Business"
      ai_category: "Software Subscriptions"
      improvement_type: "accuracy"
      judgment: "better"  # More accurate categorization
    
    - original_category: "Office Supplies"
      ai_category: "Office Supplies"
      improvement_type: "maintained"
      judgment: "equivalent"  # Already good, no regression

  regression_examples:
    - original_category: "Software Subscriptions"
      ai_category: "Misc Expense"
      improvement_type: "regression"
      judgment: "worse"  # Less specific than original

# Temporal validation approach
temporal_validation:
  training_months: ["2024-01", "2024-02", "2024-03", "2024-04", "2024-05", "2024-06"]
  testing_months: ["2024-07", "2024-08", "2024-09", "2024-10", "2024-11", "2024-12"]
  
  progressive_testing:
    month_1: "Train on Jan-Jun, test on Jul"
    month_2: "Train on Jan-Jul, test on Aug"
    month_3: "Train on Jan-Aug, test on Sep"
    # Continue pattern through December
    
  success_criteria:
    overall_improvement: ">85% of our categories judged better or equivalent"
    temporal_consistency: "Accuracy should improve or maintain across months"
    no_major_regressions: "<5% of categories significantly worse than original"

# Batch processing for careful comparison
batch_processing:
  batch_size: 75  # Smaller batches for careful evaluation
  processing_time_target: 20  # seconds per batch
  comparison_method: "side_by_side"  # Show original vs AI category to judge

# AI judge configuration for improvement evaluation
ai_judge_config:
  model: "gemini-2.0-flash-001"
  temperature: 0.1
  evaluation_prompt: |
    Original Category: {original_category}
    AI Category: {ai_category}
    Transaction: {description} | Amount: {amount}
    
    Compare the AI category to the original category:
    - Is the AI category more specific and useful?
    - Is the AI category more accurate for this transaction?
    - Would a business prefer the AI category for reporting?
    
    Judgment: better | equivalent | worse
    Reasoning: [brief explanation]
  
  scoring_method: "comparative"  # better (1), equivalent (0.8), worse (0)
  confidence_reporting: true