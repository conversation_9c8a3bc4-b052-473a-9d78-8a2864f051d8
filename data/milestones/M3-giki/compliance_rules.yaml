# M3 giki.ai Hierarchy Compliance Rules

milestone: M3
tenant: giki.ai
scenario: category_hierarchy_import
accuracy_target: 90%
evaluation_method: hierarchy_compliance

# What defines "correct" categorization against imported hierarchy
hierarchy_compliance:
  definition: "Categories must match imported hierarchy exactly when possible, with intelligent fuzzy matching for edge cases"
  
  exact_match_examples:
    - transaction: "Backend developer salary payment"
      hierarchy_path: "Engineering > Salaries > Backend Team"
      expected_category: "Backend Team"
      match_type: "exact"
      accuracy_score: 1.0
    
    - transaction: "Google Ads campaign spending"
      hierarchy_path: "Marketing > Advertising > Digital"
      expected_category: "Digital"
      match_type: "exact"
      accuracy_score: 1.0

  fuzzy_match_examples:
    - transaction: "Freelance frontend developer payment"
      hierarchy_path: "Engineering > Salaries > Frontend Team"
      ai_category: "Frontend Contractor"  # Not exact but appropriate
      match_type: "fuzzy_appropriate"
      accuracy_score: 0.85
    
    - transaction: "Office building rent payment"
      hierarchy_path: "Operations > Facilities > Rent"
      ai_category: "Facilities"  # Parent category, acceptable
      match_type: "fuzzy_parent"
      accuracy_score: 0.75

# Imported hierarchy structure example
example_hierarchy:
  Engineering:
    Salaries:
      - Backend Team
      - Frontend Team
      - DevOps Team
    Tools:
      - Development Software
      - Infrastructure Services
    Equipment:
      - Laptops
      - Monitors
      
  Marketing:
    Advertising:
      - Digital Campaigns
      - Print Materials
    Content:
      - Blog Writing
      - Video Production
      
  Operations:
    Facilities:
      - Rent
      - Utilities
      - Office Supplies
    Legal:
      - Legal Fees
      - Compliance

# GL code mapping requirements
gl_code_mapping:
  requirement: "Each category must have unique GL code"
  validation_rules:
    - no_duplicate_gl_codes: true
    - gl_code_format: "4-digit numeric (e.g., 5010, 6020)"
    - parent_child_gl_relationship: "Child codes should be logical extensions of parent"
  
  example_mapping:
    "Engineering": "5000"
    "Engineering > Salaries": "5010"
    "Engineering > Salaries > Backend Team": "5011"
    "Engineering > Salaries > Frontend Team": "5012"

# Batch processing for precision
batch_processing:
  batch_size: 50  # Smaller batches for highest precision
  processing_time_target: 15  # seconds per batch
  exact_match_priority: true  # Try exact match first, then fuzzy

# Success criteria
success_metrics:
  exact_match_target: 90%  # 90% of transactions should match hierarchy exactly
  fuzzy_match_threshold: 85%  # Fuzzy matches should score at least 85%
  gl_code_accuracy: 100%  # All categories must have correct GL codes
  export_functionality: "QuickBooks, CSV, JSON formats working"

# Export validation
export_requirements:
  quickbooks_format:
    - category_name
    - gl_code
    - parent_category
    - hierarchy_level
  
  csv_format:
    - transaction_id
    - category
    - gl_code
    - confidence_score
  
  json_format:
    - nested_hierarchy_structure
    - transaction_categorizations
    - metadata

# AI judge configuration for compliance
ai_judge_config:
  model: "gemini-2.0-flash-001"
  temperature: 0.0  # Zero temperature for exact matching
  evaluation_method: "hierarchy_aware"
  
  exact_match_logic: "Direct string matching against hierarchy leaves"
  fuzzy_match_logic: "Semantic similarity to hierarchy nodes with parent-child awareness"
  confidence_scoring: "1.0 for exact, 0.6-0.9 for fuzzy based on appropriateness"