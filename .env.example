
# Database Configuration
# Database Configuration - PostgreSQL everywhere for consistency
# DEVELOPMENT: Local PostgreSQL 15
DATABASE_URL=postgresql://giki_ai_user:YOUR_PASSWORD@localhost:5432/giki_ai_db

# PRODUCTION: Google Cloud SQL PostgreSQL
# DATABASE_URL=postgresql+asyncpg://giki_ai_user:SECURE_PASSWORD@/giki_ai_db?host=/cloudsql/rezolve-poc:us-central1:giki-ai-postgres

# Environment-specific settings
ENVIRONMENT=development
DEBUG=true
# TABLE_PREFIX system removed - using test tenant approach only
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration - React Vite (Development)
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_ENVIRONMENT=development

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=./service-accounts/dev-service-account.json
GOOGLE_APPLICATION_CREDENTIALS=./service-accounts/dev-service-account.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc
GEMINI_API_KEY=AIzaSyBAX9U-SIvlGFSql92v8WiFqIu9mXQCKbs

# Authentication settings
SECRET_KEY=dev_secret_key_replace_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Admin Configuration
ADMIN_API_KEY=giki-admin-dev-key-2025

# Service integrations
OPENAI_API_KEY=********************************************************************************************************************************************************************
GITHUB_PERSONAL_ACCESS_TOKEN=*********************************************************************************************

# AI Configuration
GCS_BUCKET_NAME_RAG=giki-ai-gcs
FILE_INGESTION_AI_MODEL_NAME=gemini-2.0-flash-001
CATEGORIZATION_MODEL_ID=gemini-2.0-flash-001
VERTEX_CHAT_MODEL_NAME=gemini-2.0-flash-001
VERTEX_AI_GEMINI_MODEL_ID=gemini-2.0-flash-001

# Zoho Configuration
ZOHO_CLIENT_ID=1000.WTPGF3M38TV5HBDYCQK2V02TJ1AGBT
ZOHO_CLIENT_SECRET=a96bfd81de434ed33026facaf08e221ad59ab1c998
ZOHO_REFRESH_TOKEN=**********************************************************************
ZOHO_USER_EMAIL=<EMAIL>
ZOHO_REDIRECT_URI=http://localhost:8080/auth/zoho/callback

# App Configuration
APP_HOST=0.0.0.0
APP_PORT=8080

