{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "test", "lint"], "parallel": 1, "maxParallel": 1, "cacheDirectory": ".nx/cache", "useDaemonProcess": false}}}, "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "serve", "startTargetName": "start", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps"}}, {"plugin": "@nx/storybook/plugin", "options": {"serveStorybookTargetName": "storybook", "buildStorybookTargetName": "build-storybook", "testStorybookTargetName": "test-storybook", "staticStorybookTargetName": "static-storybook"}}], "targetDefaults": {"cleanup": {"executor": "nx:run-commands", "options": {"command": "./scripts/cleanup-processes.sh"}}, "e2e-ci--**/**": {"dependsOn": ["^build"]}, "@nx/js:swc": {"cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default"]}, "test": {"cache": true, "inputs": ["default", "^default"], "configurations": {"development": {}, "production": {"optimization": true}, "real-ai": {"description": "Run tests with real AI validation", "dependsOn": ["validate:real-ai"]}}}, "validate:real-ai": {"cache": false, "executor": "nx:run-commands", "options": {"command": "scripts/validate-real-ai-tests.sh"}}, "serve": {"cache": false, "parallel": false, "dependsOn": ["build"], "configurations": {"development": {}, "production": {}}}, "build": {"cache": true, "parallel": false, "dependsOn": ["^build"], "inputs": ["default", "^default"], "configurations": {"development": {}, "production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "docker": {"cache": true, "dependsOn": ["build:production"], "inputs": ["production", "^production"], "configurations": {"production": {"registry": "cloud"}}}, "deploy": {"cache": false, "dependsOn": ["build:production", "docker:production"], "configurations": {"production": {"useTerraform": true}}}, "infra": {"cache": false, "configurations": {"production": {"useTerraform": true}}}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "development": ["default"], "sharedGlobals": []}, "generators": {"@nx/react": {"library": {"style": "css", "linter": "eslint", "unitTestRunner": "vitest"}, "application": {"babel": true, "style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}}}, "tui": {"enabled": false}}