# Augment Editor Multi-Agent Workspace Ignore
# Follows gitignore format to prevent workspace indexing of large files

# Development Server Logs
**/logs/
**/*.log
**/*.log.*
**/server.log
**/api.log
**/frontend.log

# Runtime Caches and Temporary Data
**/.nx/workspace-data/
**/.nx/cache/
**/node_modules/.pnpm/.cache/
**/.pytest_cache/
**/.ruff_cache/
**/.mypy_cache/
**/.vite-cache/

# Upload and Temporary Files
apps/giki-ai-api/uploads/
apps/giki-ai-api/temp_uploads/
uploads/

# Build and Coverage Artifacts
**/dist/
**/build/
**/coverage_html_report/
**/htmlcov/
**/test-results/

# Database Files
**/*.db
**/*.sqlite
**/*.sqlite3

# Service Account Keys
**/service-account*.json
**/*-key.json
service-accounts/

# Environment Files
**/.env
**/.env.local
**/.env.production

# Large Data Files
**/*.xlsx
**/*.csv
**/*.parquet

# Additional patterns from .rooignore (consolidated)
# All patterns above already cover the .rooignore content