"""
Simple database integration test.
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def test_database_connection():
    """Test basic database connection."""
    load_dotenv()
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")
    
    conn = await asyncpg.connect(DATABASE_URL)
    try:
        result = await conn.fetchval("SELECT 1")
        print(f"✅ Database connection successful: {result}")
        
        # Check users table
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        print(f"✅ Users table has {user_count} rows")
        
        # Check test user
        test_user = await conn.fetchrow("SELECT email, is_active FROM users WHERE email = $1", "<EMAIL>")
        if test_user:
            print(f"✅ Test user found: {test_user['email']}, active: {test_user['is_active']}")
        else:
            print("❌ Test user not found")
            
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(test_database_connection())