#!/bin/bash

# =============================================================================
# GIKI AI - Complete Customer Journey Integration Test
# =============================================================================
# 
# Backend demonstration of giki.ai platform showcasing complete customer workflow
# from business onboarding through AI-powered transaction categorization to 
# financial reporting. Uses real API endpoints and simulates actual user journey.
#
# USAGE:
#   ./tests/integration/the-one-integration-test.sh
#   ./tests/integration/the-one-integration-test.sh --production
#   ./tests/integration/the-one-integration-test.sh --debug
#   ./tests/integration/the-one-integration-test.sh --phase auth
#
# =============================================================================

set -e  # Exit on any error
# set -x  # Uncomment for debug mode

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="password123"
JWT_TOKEN=""
TENANT_ID=""
API_BASE_URL=""
DEBUG_MODE=false
PRODUCTION_MODE=false
SPECIFIC_PHASE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --production)
            PRODUCTION_MODE=true
            shift
            ;;
        --debug)
            DEBUG_MODE=true
            set -x
            shift
            ;;
        --phase)
            SPECIFIC_PHASE="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--production] [--debug] [--phase <phase_name>]"
            exit 1
            ;;
    esac
done

# Logging functions
log_header() {
    echo -e "\n${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}\n"
}

log_step() {
    echo -e "${BLUE}🔸 $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Debug logging
debug_log() {
    if [ "$DEBUG_MODE" = true ]; then
        echo -e "${YELLOW}[DEBUG] $1${NC}"
    fi
}

# Error handler
handle_error() {
    log_error "Test failed at line $1"
    log_info "Run with --debug for more details"
    exit 1
}

trap 'handle_error $LINENO' ERR

# =============================================================================
# PHASE 1: ENVIRONMENT SETUP & API DISCOVERY
# =============================================================================

setup_environment() {
    log_header "PHASE 1: ENVIRONMENT SETUP & API DISCOVERY"
    
    log_step "Detecting API endpoint..."
    
    # Define possible API endpoints
    PRODUCTION_URL="https://giki-ai-api-6uyufgxcxa-uc.a.run.app"
    LOCAL_URL="http://localhost:8000"
    
    if [ "$PRODUCTION_MODE" = true ]; then
        API_BASE_URL="$PRODUCTION_URL"
        log_info "Using production endpoint: $API_BASE_URL"
    else
        # Try local first, then production
        log_step "Testing local endpoint..."
        if curl -s --connect-timeout 5 "$LOCAL_URL/api/v1/health" > /dev/null 2>&1; then
            API_BASE_URL="$LOCAL_URL"
            log_success "Local API detected: $API_BASE_URL"
        else
            log_warning "Local API not available, trying production..."
            if curl -s --connect-timeout 10 "$PRODUCTION_URL/api/v1/health" > /dev/null 2>&1; then
                API_BASE_URL="$PRODUCTION_URL"
                log_success "Production API detected: $API_BASE_URL"
            else
                log_error "No API endpoint available. Please start local server or check production."
                exit 1
            fi
        fi
    fi
    
    log_step "Verifying API health..."
    HEALTH_RESPONSE=$(curl -s "$API_BASE_URL/api/v1/health" || echo "FAILED")
    
    if echo "$HEALTH_RESPONSE" | grep -q "healthy\|ok"; then
        log_success "API health check passed"
        debug_log "Health response: $HEALTH_RESPONSE"
    else
        log_error "API health check failed"
        debug_log "Health response: $HEALTH_RESPONSE"
        exit 1
    fi
    
    log_step "Checking test data availability..."
    
    # Check if sample data files exist
    SAMPLE_DATA_DIR="./data/input_files"
    if [ -d "$SAMPLE_DATA_DIR" ]; then
        SAMPLE_FILES=$(find "$SAMPLE_DATA_DIR" -name "*.csv" -o -name "*.xlsx" | head -3)
        if [ -n "$SAMPLE_FILES" ]; then
            log_success "Sample data files found:"
            echo "$SAMPLE_FILES" | while read -r file; do
                log_info "  📁 $(basename "$file")"
            done
        else
            log_warning "No sample data files found in $SAMPLE_DATA_DIR"
        fi
    else
        log_warning "Sample data directory not found: $SAMPLE_DATA_DIR"
    fi
    
    # Create a simple test CSV file for uploads
    create_test_data
    
    log_success "Environment setup complete"
    log_info "API Endpoint: $API_BASE_URL"
    log_info "Test Email: $TEST_EMAIL"
}

create_test_data() {
    log_step "Verifying real test data files..."
    
    # Check that actual test files exist
    TEST_FILES_DIR="./test-files"
    if [ ! -d "$TEST_FILES_DIR" ]; then
        log_error "Test files directory not found: $TEST_FILES_DIR"
        exit 1
    fi
    
    CAPITAL_ONE_FILE="$TEST_FILES_DIR/Capital One.xlsx"
    CREDIT_CARD_FILE="$TEST_FILES_DIR/Credit Card.xlsx"
    
    if [ ! -f "$CAPITAL_ONE_FILE" ]; then
        log_error "Capital One test file not found: $CAPITAL_ONE_FILE"
        exit 1
    fi
    
    if [ ! -f "$CREDIT_CARD_FILE" ]; then
        log_error "Credit Card test file not found: $CREDIT_CARD_FILE"
        exit 1
    fi
    
    log_success "Real test data files verified"
    log_info "  📁 Capital One.xlsx (for onboarding with categories)"
    log_info "  📁 Credit Card.xlsx (for production without categories)"
}

# =============================================================================
# PHASE 2: AUTHENTICATION FLOW
# =============================================================================

test_authentication() {
    log_header "PHASE 2: AUTHENTICATION FLOW"
    
    log_step "Testing user authentication..."
    
    # OAuth2 password flow
    AUTH_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/auth/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -H "Accept: application/json" \
        -d "grant_type=password&username=$TEST_EMAIL&password=$TEST_PASSWORD")
    
    debug_log "Auth response: $AUTH_RESPONSE"
    
    # Extract access token
    JWT_TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$JWT_TOKEN" ] && [ "$JWT_TOKEN" != "null" ]; then
        log_success "Authentication successful"
        log_info "Token length: ${#JWT_TOKEN} characters"
        debug_log "JWT Token: ${JWT_TOKEN:0:20}..."
    else
        log_error "Authentication failed"
        log_info "Response: $AUTH_RESPONSE"
        exit 1
    fi
    
    log_step "Verifying token with user profile..."
    
    USER_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/auth/me" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "User response: $USER_RESPONSE"
    
    # Extract tenant ID for multi-tenant operations
    TENANT_ID=$(echo "$USER_RESPONSE" | grep -o '"tenant_id":[^,}]*' | cut -d':' -f2 | tr -d ' "')
    
    if echo "$USER_RESPONSE" | grep -q "email\|user_id"; then
        log_success "Token verification successful"
        if [ -n "$TENANT_ID" ]; then
            log_info "Tenant ID: $TENANT_ID"
        fi
    else
        log_error "Token verification failed"
        log_info "Response: $USER_RESPONSE"
        exit 1
    fi
    
    log_success "Authentication flow complete"
}

# =============================================================================
# PHASE 3: ONBOARDING JOURNEY (HISTORICAL DATA WITH CATEGORIES)
# =============================================================================

test_onboarding_journey() {
    log_header "PHASE 3: ONBOARDING JOURNEY (HISTORICAL DATA WITH CATEGORIES)"
    
    log_step "Starting onboarding process..."
    
    # Check onboarding status
    ONBOARDING_STATUS=$(curl -s -X GET "$API_BASE_URL/api/v1/onboarding/status" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Onboarding status: $ONBOARDING_STATUS"
    
    log_step "Uploading historical data with categories..."
    
    # Upload historical data file with correct parameters
    # Use real Capital One test file instead of generated CSV
    UPLOAD_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/onboarding/upload-historical-data" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -F "file=@./test-files/Capital One.xlsx" \
        -F "year=2024" \
        -F "has_category_labels=true")
    
    debug_log "Upload response: $UPLOAD_RESPONSE"
    
    # Extract upload ID
    UPLOAD_ID=$(echo "$UPLOAD_RESPONSE" | grep -o '"upload_id":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$UPLOAD_ID" ] && [ "$UPLOAD_ID" != "null" ]; then
        log_success "Historical data uploaded successfully"
        log_info "Upload ID: $UPLOAD_ID"
    else
        log_error "Historical data upload failed"
        log_info "Response: $UPLOAD_RESPONSE"
        return 1
    fi
    
    log_step "Monitoring file processing..."
    
    # Wait for processing to complete
    PROCESSING_TIMEOUT=120  # seconds - increased for onboarding
    PROCESSING_START=$(date +%s)
    
    while true; do
        PROCESSING_STATUS=$(curl -s -X GET "$API_BASE_URL/api/v1/uploads/$UPLOAD_ID" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json")
        
        STATUS=$(echo "$PROCESSING_STATUS" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        
        debug_log "Processing status: $STATUS"
        
        case "$STATUS" in
            "completed"|"processed"|"ready")
                log_success "File processing completed"
                break
                ;;
            "failed"|"error")
                log_error "File processing failed"
                log_info "Status: $PROCESSING_STATUS"
                return 1
                ;;
            *)
                CURRENT_TIME=$(date +%s)
                ELAPSED_TIME=$((CURRENT_TIME - PROCESSING_START))
                
                if [ $ELAPSED_TIME -gt $PROCESSING_TIMEOUT ]; then
                    log_warning "Processing timeout reached"
                    break
                fi
                
                log_info "Processing... (${ELAPSED_TIME}s elapsed)"
                sleep 5
                ;;
        esac
    done
    
    log_step "Checking transaction count from upload..."
    
    # Get transactions created from the upload
    TRANSACTIONS_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/uploads/$UPLOAD_ID/transactions" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Transactions response: $TRANSACTIONS_RESPONSE"
    
    TRANSACTION_COUNT=$(echo "$TRANSACTIONS_RESPONSE" | grep -o '"id":' | wc -l)
    
    if [ "$TRANSACTION_COUNT" -gt 0 ]; then
        log_success "Transactions created from upload: $TRANSACTION_COUNT"
    else
        log_warning "No transactions found from upload"
    fi
    
    log_step "Running temporal accuracy validation..."
    
    # Start temporal validation
    VALIDATION_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/onboarding/validate-temporal-accuracy" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"tenant_id\": $TENANT_ID}")
    
    debug_log "Validation response: $VALIDATION_RESPONSE"
    
    VALIDATION_ID=$(echo "$VALIDATION_RESPONSE" | grep -o '"validation_id":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$VALIDATION_ID" ] && [ "$VALIDATION_ID" != "null" ]; then
        log_success "Temporal validation started"
        log_info "Validation ID: $VALIDATION_ID"
        
        # Wait for validation to complete
        log_step "Waiting for accuracy validation results..."
        
        VALIDATION_TIMEOUT=120  # seconds
        VALIDATION_START=$(date +%s)
        
        while true; do
            VALIDATION_RESULT=$(curl -s -X GET "$API_BASE_URL/api/v1/onboarding/validation-results/$VALIDATION_ID" \
                -H "Authorization: Bearer $JWT_TOKEN" \
                -H "Content-Type: application/json")
            
            VALIDATION_STATUS=$(echo "$VALIDATION_RESULT" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
            ACCURACY=$(echo "$VALIDATION_RESULT" | grep -o '"accuracy":[^,}]*' | cut -d':' -f2 | tr -d ' ')
            
            debug_log "Validation status: $VALIDATION_STATUS, Accuracy: $ACCURACY"
            
            case "$VALIDATION_STATUS" in
                "completed"|"success")
                    log_success "Temporal validation completed"
                    if [ -n "$ACCURACY" ]; then
                        log_info "Accuracy achieved: ${ACCURACY}%"
                        
                        # Check if accuracy meets threshold
                        if (( $(echo "$ACCURACY >= 85" | bc -l) )); then
                            log_success "✨ Accuracy exceeds 85% threshold!"
                        else
                            log_warning "Accuracy below 85% threshold"
                        fi
                    fi
                    break
                    ;;
                "failed"|"error")
                    log_error "Temporal validation failed"
                    return 1
                    ;;
                *)
                    CURRENT_TIME=$(date +%s)
                    ELAPSED_TIME=$((CURRENT_TIME - VALIDATION_START))
                    
                    if [ $ELAPSED_TIME -gt $VALIDATION_TIMEOUT ]; then
                        log_warning "Validation timeout reached"
                        break
                    fi
                    
                    log_info "Validating... (${ELAPSED_TIME}s elapsed)"
                    sleep 10
                    ;;
            esac
        done
    else
        log_warning "Temporal validation may not have started properly"
    fi
    
    log_step "Completing onboarding approval..."
    
    # Approve for production use
    APPROVAL_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/onboarding/approve-for-production" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"tenant_id\": $TENANT_ID, \"validation_id\": \"$VALIDATION_ID\"}")
    
    debug_log "Approval response: $APPROVAL_RESPONSE"
    
    if echo "$APPROVAL_RESPONSE" | grep -q "success\|approved"; then
        log_success "Onboarding completed - approved for production!"
    else
        log_warning "Onboarding approval may have issues"
    fi
    
    log_success "Onboarding journey complete"
}

# =============================================================================
# PHASE 4: PRODUCTION USAGE (NEW DATA WITHOUT CATEGORIES)
# =============================================================================

test_production_usage() {
    log_header "PHASE 4: PRODUCTION USAGE (NEW DATA WITHOUT CATEGORIES)"
    
    log_step "Uploading new transactions for auto-categorization..."
    
    # Upload production data WITHOUT categories  
    # Use real Credit Card test file for production upload
    PROD_UPLOAD_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/uploads/upload-production-data" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -F "files[]=@./test-files/Credit Card.xlsx")
    
    debug_log "Production upload response: $PROD_UPLOAD_RESPONSE"
    
    # Extract upload ID
    PROD_UPLOAD_ID=$(echo "$PROD_UPLOAD_RESPONSE" | grep -o '"upload_id":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$PROD_UPLOAD_ID" ] && [ "$PROD_UPLOAD_ID" != "null" ]; then
        log_success "Production data uploaded successfully"
        log_info "Upload ID: $PROD_UPLOAD_ID"
    else
        log_error "Production data upload failed"
        log_info "Response: $PROD_UPLOAD_RESPONSE"
        return 1
    fi
    
    log_step "Waiting for auto-categorization to complete..."
    
    # Wait for processing and auto-categorization
    PROCESSING_TIMEOUT=60
    PROCESSING_START=$(date +%s)
    
    while true; do
        PROCESSING_STATUS=$(curl -s -X GET "$API_BASE_URL/api/v1/uploads/$PROD_UPLOAD_ID" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -H "Content-Type: application/json")
        
        STATUS=$(echo "$PROCESSING_STATUS" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        
        debug_log "Production processing status: $STATUS"
        
        case "$STATUS" in
            "completed"|"processed"|"categorized")
                log_success "Auto-categorization completed"
                break
                ;;
            "failed"|"error")
                log_error "Auto-categorization failed"
                log_info "Status: $PROCESSING_STATUS"
                return 1
                ;;
            *)
                CURRENT_TIME=$(date +%s)
                ELAPSED_TIME=$((CURRENT_TIME - PROCESSING_START))
                
                if [ $ELAPSED_TIME -gt $PROCESSING_TIMEOUT ]; then
                    log_warning "Processing timeout reached"
                    break
                fi
                
                log_info "Auto-categorizing... (${ELAPSED_TIME}s elapsed)"
                sleep 5
                ;;
        esac
    done
    
    log_step "Testing AI categorization accuracy on new transactions..."
    
    # Get the auto-categorized transactions
    TRANSACTIONS_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/transactions?upload_id=$PROD_UPLOAD_ID&limit=20" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Transactions response: $TRANSACTIONS_RESPONSE"
    
    # Count transactions with categories
    CATEGORIZED_COUNT=$(echo "$TRANSACTIONS_RESPONSE" | grep -o '"category":"[^"]*"' | grep -v '"category":""' | wc -l)
    TOTAL_COUNT=$(echo "$TRANSACTIONS_RESPONSE" | grep -o '"id":' | wc -l)
    
    if [ "$TOTAL_COUNT" -gt 0 ]; then
        CATEGORIZATION_RATE=$((CATEGORIZED_COUNT * 100 / TOTAL_COUNT))
        log_success "Transactions processed: $TOTAL_COUNT"
        log_info "Successfully categorized: $CATEGORIZED_COUNT ($CATEGORIZATION_RATE%)"
        
        if [ "$CATEGORIZATION_RATE" -ge 80 ]; then
            log_success "✨ Categorization rate exceeds 80% threshold!"
        else
            log_warning "Categorization rate below 80%"
        fi
    else
        log_warning "No transactions found for upload"
    fi
    
    log_step "Testing individual transaction categorization API..."
    
    # Test real-time categorization API
    TEST_CATEGORIZATION=$(curl -s -X POST "$API_BASE_URL/api/v1/categories/categorize" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "description": "STARBUCKS COFFEE SHOP",
            "amount": -4.75,
            "tenant_id": '$TENANT_ID'
        }')
    
    debug_log "Test categorization response: $TEST_CATEGORIZATION"
    
    PREDICTED_CATEGORY=$(echo "$TEST_CATEGORIZATION" | grep -o '"category":"[^"]*"' | cut -d'"' -f4)
    CONFIDENCE=$(echo "$TEST_CATEGORIZATION" | grep -o '"confidence":[^,}]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ -n "$PREDICTED_CATEGORY" ] && [ "$PREDICTED_CATEGORY" != "null" ]; then
        log_success "Real-time categorization working"
        log_info "STARBUCKS → $PREDICTED_CATEGORY (confidence: $CONFIDENCE)"
    else
        log_warning "Real-time categorization may have issues"
    fi
    
    log_success "Production usage testing complete"
}

# =============================================================================
# PHASE 5: DASHBOARD & ANALYTICS VALIDATION
# =============================================================================

test_dashboard_analytics() {
    log_header "PHASE 5: DASHBOARD & ANALYTICS VALIDATION"
    
    log_step "Fetching dashboard metrics..."
    
    # Get dashboard summary
    DASHBOARD_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/dashboard/metrics" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Dashboard response: $DASHBOARD_RESPONSE"
    
    # Extract key metrics
    TOTAL_TRANSACTIONS=$(echo "$DASHBOARD_RESPONSE" | grep -o '"total_transactions":[^,}]*' | cut -d':' -f2 | tr -d ' ')
    TOTAL_AMOUNT=$(echo "$DASHBOARD_RESPONSE" | grep -o '"total_amount":[^,}]*' | cut -d':' -f2 | tr -d ' ')
    CATEGORIZED_PERCENTAGE=$(echo "$DASHBOARD_RESPONSE" | grep -o '"categorized_percentage":[^,}]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ -n "$TOTAL_TRANSACTIONS" ] && [ "$TOTAL_TRANSACTIONS" != "null" ]; then
        log_success "Dashboard metrics retrieved"
        log_info "Total Transactions: $TOTAL_TRANSACTIONS"
        log_info "Total Amount: \$${TOTAL_AMOUNT}"
        log_info "Categorized: ${CATEGORIZED_PERCENTAGE}%"
    else
        log_warning "Dashboard metrics may be incomplete"
    fi
    
    log_step "Testing spending by category report..."
    
    # Get spending breakdown
    SPENDING_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/reports/spending-by-category" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Spending response: $SPENDING_RESPONSE"
    
    CATEGORY_COUNT=$(echo "$SPENDING_RESPONSE" | grep -o '"category":"[^"]*"' | wc -l)
    
    if [ "$CATEGORY_COUNT" -gt 0 ]; then
        log_success "Spending by category report generated"
        log_info "Categories found: $CATEGORY_COUNT"
        
        # Show top categories
        echo "$SPENDING_RESPONSE" | grep -o '"category":"[^"]*"' | head -3 | while read -r category; do
            CATEGORY_NAME=$(echo "$category" | cut -d'"' -f4)
            log_info "  📊 $CATEGORY_NAME"
        done
    else
        log_warning "No spending categories found"
    fi
    
    log_step "Testing data export functionality..."
    
    # Test CSV export
    CSV_EXPORT=$(curl -s -X POST "$API_BASE_URL/api/v1/reports/export/csv" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"format": "standard", "date_range": "last_30_days"}')
    
    debug_log "CSV export response: ${CSV_EXPORT:0:200}..."
    
    if echo "$CSV_EXPORT" | head -1 | grep -q "Date\|Transaction\|Category"; then
        log_success "CSV export functioning"
        EXPORT_LINES=$(echo "$CSV_EXPORT" | wc -l)
        log_info "Export contains $EXPORT_LINES lines"
    else
        log_warning "CSV export may have issues"
    fi
    
    log_step "Testing recent transactions API..."
    
    # Get recent transactions
    RECENT_RESPONSE=$(curl -s -X GET "$API_BASE_URL/api/v1/dashboard/recent-transactions?limit=5" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    debug_log "Recent transactions response: $RECENT_RESPONSE"
    
    RECENT_COUNT=$(echo "$RECENT_RESPONSE" | grep -o '"id":' | wc -l)
    
    if [ "$RECENT_COUNT" -gt 0 ]; then
        log_success "Recent transactions retrieved"
        log_info "Recent transactions: $RECENT_COUNT"
    else
        log_warning "No recent transactions found"
    fi
    
    log_success "Dashboard and analytics validation complete"
}

# =============================================================================
# PHASE 6: AGENT SYSTEM & CONVERSATIONAL AI
# =============================================================================

test_agent_system() {
    log_header "PHASE 6: AGENT SYSTEM & CONVERSATIONAL AI"
    
    log_step "Testing agent chat interface..."
    
    # Test basic agent query
    AGENT_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/intelligence/chat" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "message": "Show me my spending summary",
            "context": "dashboard"
        }')
    
    debug_log "Agent response: $AGENT_RESPONSE"
    
    AGENT_MESSAGE=$(echo "$AGENT_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$AGENT_MESSAGE" ] && [ "$AGENT_MESSAGE" != "null" ]; then
        log_success "Agent system responding"
        log_info "Agent response: ${AGENT_MESSAGE:0:100}..."
    else
        log_warning "Agent system may not be responding"
    fi
    
    log_step "Testing agent file upload command..."
    
    # Test agent command processing
    COMMAND_RESPONSE=$(curl -s -X POST "$API_BASE_URL/api/v1/agent/command" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "command": "/help",
            "context": "general"
        }')
    
    debug_log "Command response: $COMMAND_RESPONSE"
    
    if echo "$COMMAND_RESPONSE" | grep -q "command\|help\|available"; then
        log_success "Agent command processing working"
    else
        log_warning "Agent commands may not be functioning"
    fi
    
    log_success "Agent system testing complete"
}

# =============================================================================
# PHASE 7: SYSTEM HEALTH & PERFORMANCE VALIDATION
# =============================================================================

test_system_health() {
    log_header "PHASE 7: SYSTEM HEALTH & PERFORMANCE VALIDATION"
    
    log_step "Testing API response times..."
    
    # Test various endpoint performance
    ENDPOINTS=(
        "/health"
        "/api/v1/auth/me"
        "/api/v1/dashboard/metrics"
        "/api/v1/transactions?limit=10"
    )
    
    for endpoint in "${ENDPOINTS[@]}"; do
        START_TIME=$(date +%s%3N)
        
        if [[ "$endpoint" == "/health" ]]; then
            RESPONSE=$(curl -s "$API_BASE_URL$endpoint")
        else
            RESPONSE=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" "$API_BASE_URL$endpoint")
        fi
        
        END_TIME=$(date +%s%3N)
        RESPONSE_TIME=$((END_TIME - START_TIME))
        
        if [ $RESPONSE_TIME -lt 2000 ]; then
            log_success "$endpoint: ${RESPONSE_TIME}ms ✓"
        else
            log_warning "$endpoint: ${RESPONSE_TIME}ms (slow)"
        fi
    done
    
    log_step "Checking database health..."
    
    DB_HEALTH=$(curl -s "$API_BASE_URL/health/db")
    
    if echo "$DB_HEALTH" | grep -q "healthy\|connected"; then
        log_success "Database connection healthy"
    else
        log_warning "Database connection issues detected"
    fi
    
    log_step "Validating data consistency..."
    
    # Check if uploaded transactions match expected counts
    ALL_TRANSACTIONS=$(curl -s -X GET "$API_BASE_URL/api/v1/transactions?limit=100" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -H "Content-Type: application/json")
    
    TRANSACTION_COUNT=$(echo "$ALL_TRANSACTIONS" | grep -o '"id":' | wc -l)
    
    log_info "Total transactions in system: $TRANSACTION_COUNT"
    
    if [ "$TRANSACTION_COUNT" -ge 20 ]; then
        log_success "Data consistency validated (expected ~25 transactions)"
    else
        log_warning "Transaction count lower than expected"
    fi
    
    log_success "System health validation complete"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    log_header "GIKI AI - Complete Customer Journey Test"
    log_info "Simulating real business customer workflow"
    log_info "Test Account: $TEST_EMAIL"
    log_info "Debug Mode: $DEBUG_MODE"
    log_info "Production Mode: $PRODUCTION_MODE"
    
    if [ -n "$SPECIFIC_PHASE" ]; then
        log_info "Running specific phase: $SPECIFIC_PHASE"
    fi
    
    # Execute phases based on arguments
    case "$SPECIFIC_PHASE" in
        "setup"|"environment")
            setup_environment
            ;;
        "auth"|"authentication")
            setup_environment
            test_authentication
            ;;
        "onboarding")
            setup_environment
            test_authentication
            test_onboarding_journey
            ;;
        "production")
            setup_environment
            test_authentication
            test_production_usage
            ;;
        "dashboard")
            setup_environment
            test_authentication
            test_dashboard_analytics
            ;;
        "agent")
            setup_environment
            test_authentication
            test_agent_system
            ;;
        "health")
            setup_environment
            test_authentication
            test_system_health
            ;;
        *)
            # Run complete customer journey
            log_info "🚀 Executing complete customer journey simulation..."
            setup_environment
            test_authentication
            test_onboarding_journey
            test_production_usage
            test_dashboard_analytics
            test_agent_system
            test_system_health
            ;;
    esac
    
    log_header "🎉 GIKI AI CUSTOMER JOURNEY COMPLETE"
    log_success "Business successfully onboarded and using AI categorization!"
    log_info "✨ Platform ready for production financial workflows"
    
    if [ -n "$JWT_TOKEN" ]; then
        log_info "🔑 Auth token available for manual API testing: ${JWT_TOKEN:0:20}..."
    fi
}

# Execute main function
main "$@"