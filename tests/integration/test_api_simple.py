"""
Simple API integration tests using requests.
"""
import requests
import os
from dotenv import load_dotenv

def test_api_health():
    """Test API health endpoint."""
    response = requests.get("http://localhost:8000/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    print("✅ API health check passed")

def test_database_health():
    """Test database health endpoint."""
    response = requests.get("http://localhost:8000/health/db")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"
    print("✅ Database health check passed")

def test_user_authentication():
    """Test user authentication."""
    response = requests.post(
        "http://localhost:8000/api/v1/auth/token",
        data={
            "grant_type": "password",
            "username": "<EMAIL>",
            "password": "password123"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    print("✅ User authentication test passed")
    return data["access_token"]

def test_protected_endpoint():
    """Test protected endpoint with token."""
    # Get token first
    token = test_user_authentication()
    
    # Use token to access protected endpoint
    response = requests.get(
        "http://localhost:8000/api/v1/transactions",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Should not get 401 unauthorized
    assert response.status_code != 401
    print("✅ Protected endpoint test passed")

def test_categories_endpoint():
    """Test categories endpoint."""
    # Get token first
    token = test_user_authentication()
    
    response = requests.get(
        "http://localhost:8000/api/v1/categories",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list) or "categories" in data
    print("✅ Categories endpoint test passed")

def run_all_tests():
    """Run all integration tests."""
    print("🧪 Running API Integration Tests...")
    
    test_api_health()
    test_database_health()
    test_user_authentication()
    test_protected_endpoint()
    test_categories_endpoint()
    
    print("🎉 All integration tests passed!")

if __name__ == "__main__":
    run_all_tests()