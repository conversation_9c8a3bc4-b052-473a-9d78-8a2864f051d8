"""
Integration tests for file upload domain.
Tests file processing, column mapping, and database storage.
"""
import asyncio
import pytest
import asyncpg
from httpx import AsyncClient
from fastapi.testclient import TestClient
import os
import tempfile
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@pytest.fixture
async def db_connection():
    """Get database connection for tests."""
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")
    conn = await asyncpg.connect(DATABASE_URL)
    yield conn
    await conn.close()

@pytest.fixture
def api_client():
    """Get API client for tests."""
    from apps.giki_ai_api.src.giki_ai_api.core.main import app
    return TestClient(app)

@pytest.fixture
def auth_token(api_client):
    """Get authentication token for tests."""
    response = api_client.post(
        "/api/v1/auth/token",
        data={
            "grant_type": "password",
            "username": "<EMAIL>",
            "password": "password123"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    return response.json()["access_token"]

@pytest.fixture
def sample_csv_file():
    """Create a sample CSV file for testing."""
    data = {
        "Date": ["2024-01-01", "2024-01-02", "2024-01-03"],
        "Description": ["Coffee Shop", "Gas Station", "Grocery Store"],
        "Amount": [-4.50, -35.00, -67.25],
        "Category": ["Food & Dining", "Transportation", "Groceries"]
    }
    df = pd.DataFrame(data)
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        return f.name

class TestUploadIntegration:
    """File upload integration tests."""

    def test_file_upload_endpoint_exists(self, api_client, auth_token):
        """Test that file upload endpoint is accessible."""
        response = api_client.get(
            "/api/v1/files/upload-status",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        # Should not be 404
        assert response.status_code != 404

    def test_upload_csv_file(self, api_client, auth_token, sample_csv_file):
        """Test uploading a CSV file."""
        with open(sample_csv_file, 'rb') as f:
            files = {"file": ("test.csv", f, "text/csv")}
            response = api_client.post(
                "/api/v1/files/upload",
                files=files,
                headers={"Authorization": f"Bearer {auth_token}"},
                data={"file_type": "csv", "currency": "USD"}
            )
        
        # Clean up
        os.unlink(sample_csv_file)
        
        # Should accept the upload
        assert response.status_code in [200, 201, 202]

    @pytest.mark.asyncio
    async def test_uploads_table_exists(self, db_connection):
        """Test that uploads table exists and has correct structure."""
        # Check table exists
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'uploads'
        """)
        assert result == 1
        
        # Check key columns exist
        columns = await db_connection.fetch("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'uploads'
        """)
        column_names = [col['column_name'] for col in columns]
        
        expected_columns = ['id', 'tenant_id', 'filename', 'file_type', 'status']
        for col in expected_columns:
            assert col in column_names

    @pytest.mark.asyncio
    async def test_transactions_table_exists(self, db_connection):
        """Test that transactions table exists and has correct structure."""
        # Check table exists
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'transactions'
        """)
        assert result == 1
        
        # Check key columns exist
        columns = await db_connection.fetch("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'transactions'
        """)
        column_names = [col['column_name'] for col in columns]
        
        expected_columns = ['id', 'tenant_id', 'date', 'description', 'amount', 'category_id']
        for col in expected_columns:
            assert col in column_names

    @pytest.mark.asyncio
    async def test_column_mappings_table_exists(self, db_connection):
        """Test that column_mappings table exists (created earlier)."""
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'column_mappings'
        """)
        assert result == 1

    def test_file_processing_status_endpoint(self, api_client, auth_token):
        """Test file processing status endpoint."""
        response = api_client.get(
            "/api/v1/files/processing-status",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        # Should be accessible
        assert response.status_code != 404

    @pytest.mark.asyncio
    async def test_file_processing_reports_table(self, db_connection):
        """Test that file processing reports table exists."""
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'file_processing_reports'
        """)
        assert result == 1

    def test_supported_file_types(self, api_client, auth_token):
        """Test that API supports expected file types."""
        # This should return supported file types
        response = api_client.get(
            "/api/v1/files/supported-types",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        if response.status_code == 200:
            data = response.json()
            # Should support at least CSV and Excel
            supported_types = data.get("supported_types", [])
            assert "csv" in str(supported_types).lower() or "excel" in str(supported_types).lower()

    @pytest.mark.asyncio
    async def test_tenant_isolation(self, db_connection):
        """Test that uploads are properly isolated by tenant."""
        # Check that uploads table has tenant_id column for isolation
        result = await db_connection.fetchval("""
            SELECT data_type FROM information_schema.columns 
            WHERE table_name = 'uploads' AND column_name = 'tenant_id'
        """)
        assert result == "integer"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])