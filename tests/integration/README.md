# Integration Tests

## Milestone-Driven Testing

### M1: Nuvie Zero-Onboarding Tests
- Business-appropriateness accuracy validation
- 900+ transaction processing tests
- Zero-onboarding categorization tests

### M2: Rezolve Temporal Accuracy Tests  
- Historical data processing tests
- Improvement-over-original validation
- Progressive monthly accuracy tests

### M3: giki.ai Hierarchy Compliance Tests
- Category hierarchy import tests
- Hierarchy compliance validation
- Export functionality tests

## Test Execution
```bash
# Run all integration tests
pnpm test:api

# Run milestone-specific tests (when implemented)
pnpm test:api --grep "M1"
pnpm test:api --grep "M2" 
pnpm test:api --grep "M3"
```

## Test Data
- M1: `data/milestones/M1-nuvie/` 
- M2: `data/milestones/M2-rezolve/`
- M3: `data/milestones/M3-giki/`