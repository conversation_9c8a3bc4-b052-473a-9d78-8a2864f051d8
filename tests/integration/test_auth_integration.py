"""
Integration tests for authentication domain.
Tests the complete auth flow including database operations.
"""
import asyncio
import pytest
import asyncpg
from httpx import AsyncClient
from fastapi.testclient import TestClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@pytest.fixture
async def db_connection():
    """Get database connection for tests."""
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")
    conn = await asyncpg.connect(DATABASE_URL)
    try:
        yield conn
    finally:
        await conn.close()

@pytest.fixture
def api_client():
    """Get API client for tests."""
    # Use the running server instead of importing the app
    import httpx
    return httpx.Client(base_url="http://localhost:8000")

class TestAuthIntegration:
    """Authentication integration tests."""

    def test_user_login_success(self, api_client):
        """Test successful user login."""
        response = api_client.post(
            "/api/v1/auth/token",
            data={
                "grant_type": "password",
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data

    def test_user_login_invalid_credentials(self, api_client):
        """Test login with invalid credentials."""
        response = api_client.post(
            "/api/v1/auth/token",
            data={
                "grant_type": "password",
                "username": "<EMAIL>",
                "password": "wrongpassword"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data

    def test_protected_endpoint_with_token(self, api_client):
        """Test accessing protected endpoint with valid token."""
        # First, get a token
        login_response = api_client.post(
            "/api/v1/auth/token",
            data={
                "grant_type": "password",
                "username": "<EMAIL>",
                "password": "password123"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert login_response.status_code == 200
        token = login_response.json()["access_token"]
        
        # Use token to access protected endpoint
        response = api_client.get(
            "/api/v1/transactions",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        # Should not get 401 unauthorized
        assert response.status_code != 401

    def test_protected_endpoint_without_token(self, api_client):
        """Test accessing protected endpoint without token."""
        response = api_client.get("/api/v1/transactions")
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_user_exists_in_database(self, db_connection):
        """Test that test user exists in database."""
        user = await db_connection.fetchrow(
            "SELECT * FROM users WHERE email = $1", "<EMAIL>"
        )
        
        assert user is not None
        assert user["email"] == "<EMAIL>"
        assert user["is_active"] is True

    @pytest.mark.asyncio
    async def test_tenant_exists_for_user(self, db_connection):
        """Test that tenant exists for test user."""
        result = await db_connection.fetchrow("""
            SELECT u.email, t.name as tenant_name 
            FROM users u 
            JOIN tenants t ON u.tenant_id = t.id 
            WHERE u.email = $1
        """, "<EMAIL>")
        
        assert result is not None
        assert result["email"] == "<EMAIL>"
        assert result["tenant_name"] is not None

    @pytest.mark.asyncio
    async def test_database_connection_health(self, db_connection):
        """Test database connection is healthy."""
        result = await db_connection.fetchval("SELECT 1")
        assert result == 1

if __name__ == "__main__":
    pytest.main([__file__, "-v"])