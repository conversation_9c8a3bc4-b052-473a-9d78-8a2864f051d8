"""
Integration tests for categorization domain.
Tests AI categorization, category management, and accuracy tracking.
"""
import asyncio
import pytest
import asyncpg
from httpx import AsyncClient
from fastapi.testclient import TestClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@pytest.fixture
async def db_connection():
    """Get database connection for tests."""
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://giki_ai_user:securepass123@localhost:5432/giki_ai_db")
    conn = await asyncpg.connect(DATABASE_URL)
    yield conn
    await conn.close()

@pytest.fixture
def api_client():
    """Get API client for tests."""
    from apps.giki_ai_api.src.giki_ai_api.core.main import app
    return TestClient(app)

@pytest.fixture
def auth_token(api_client):
    """Get authentication token for tests."""
    response = api_client.post(
        "/api/v1/auth/token",
        data={
            "grant_type": "password",
            "username": "<EMAIL>",
            "password": "password123"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    return response.json()["access_token"]

class TestCategorizationIntegration:
    """Categorization integration tests."""

    @pytest.mark.asyncio
    async def test_categories_table_exists(self, db_connection):
        """Test that categories table exists and has data."""
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'categories'
        """)
        assert result == 1
        
        # Check that we have some categories
        category_count = await db_connection.fetchval("SELECT COUNT(*) FROM categories")
        assert category_count > 0

    @pytest.mark.asyncio
    async def test_categories_have_required_fields(self, db_connection):
        """Test that categories have required fields."""
        # Get a sample category
        category = await db_connection.fetchrow("SELECT * FROM categories LIMIT 1")
        assert category is not None
        
        # Check required fields exist
        assert 'id' in category.keys()
        assert 'name' in category.keys()
        assert 'tenant_id' in category.keys()

    def test_categories_endpoint(self, api_client, auth_token):
        """Test categories API endpoint."""
        response = api_client.get(
            "/api/v1/categories",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list) or "categories" in data

    def test_categorize_transaction_endpoint(self, api_client, auth_token):
        """Test transaction categorization endpoint."""
        # Test categorization request
        test_transaction = {
            "description": "Starbucks Coffee Shop",
            "amount": -4.50,
            "date": "2024-01-01"
        }
        
        response = api_client.post(
            "/api/v1/categories/categorize",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=test_transaction
        )
        
        # Should accept the request (even if AI is not configured)
        assert response.status_code in [200, 201, 400, 503]  # 400/503 if AI not available

    def test_ai_agent_responses_table_exists(self, api_client, auth_token):
        """Test that AI agent responses are tracked."""
        # Make a categorization request to potentially create an AI response
        test_transaction = {
            "description": "Test Transaction",
            "amount": -10.00
        }
        
        response = api_client.post(
            "/api/v1/categories/categorize",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=test_transaction
        )
        
        # The endpoint should exist and respond
        assert response.status_code != 404

    @pytest.mark.asyncio
    async def test_ai_agent_responses_table_structure(self, db_connection):
        """Test AI agent responses table structure."""
        # Check table exists (we created it earlier)
        result = await db_connection.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'ai_agent_responses'
        """)
        assert result == 1
        
        # Check key columns
        columns = await db_connection.fetch("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'ai_agent_responses'
        """)
        column_names = [col['column_name'] for col in columns]
        
        expected_columns = ['id', 'tenant_id', 'transaction_id', 'agent_type', 'confidence_score']
        for col in expected_columns:
            assert col in column_names

    def test_batch_categorization_endpoint(self, api_client, auth_token):
        """Test batch categorization endpoint."""
        test_transactions = [
            {"description": "Starbucks", "amount": -4.50, "date": "2024-01-01"},
            {"description": "Shell Gas", "amount": -35.00, "date": "2024-01-02"}
        ]
        
        response = api_client.post(
            "/api/v1/categories/batch-categorize",
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"transactions": test_transactions}
        )
        
        # Should accept batch requests
        assert response.status_code in [200, 201, 400, 503]

    @pytest.mark.asyncio
    async def test_tenant_category_isolation(self, db_connection):
        """Test that categories are properly isolated by tenant."""
        # Check that categories table has tenant_id for isolation
        result = await db_connection.fetchval("""
            SELECT data_type FROM information_schema.columns 
            WHERE table_name = 'categories' AND column_name = 'tenant_id'
        """)
        assert result == "integer"

    def test_category_accuracy_tracking(self, api_client, auth_token):
        """Test that categorization accuracy can be tracked."""
        # Test feedback endpoint for accuracy tracking
        feedback_data = {
            "transaction_id": "test-transaction-123",
            "suggested_category": "Food & Dining",
            "actual_category": "Food & Dining",
            "is_correct": True
        }
        
        response = api_client.post(
            "/api/v1/categories/feedback",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=feedback_data
        )
        
        # Should accept feedback (endpoint should exist)
        assert response.status_code != 404

    def test_category_performance_metrics(self, api_client, auth_token):
        """Test category performance metrics endpoint."""
        response = api_client.get(
            "/api/v1/categories/metrics",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        # Should be accessible
        assert response.status_code != 404

    @pytest.mark.asyncio
    async def test_default_categories_exist(self, db_connection):
        """Test that default categories are seeded."""
        # Check for some common categories
        common_categories = ["Food & Dining", "Transportation", "Shopping", "Income"]
        
        for category_name in common_categories:
            result = await db_connection.fetchval(
                "SELECT COUNT(*) FROM categories WHERE name ILIKE $1", 
                f"%{category_name}%"
            )
            # At least one matching category should exist
            if result > 0:
                break
        else:
            # If none of the common categories exist, that's unusual but not necessarily wrong
            # Just check that we have some categories at all
            total_categories = await db_connection.fetchval("SELECT COUNT(*) FROM categories")
            assert total_categories > 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])