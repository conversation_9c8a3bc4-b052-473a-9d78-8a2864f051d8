# CLAUDE.md - giki.ai Development Guide

## Project Overview
**giki.ai** - AI-powered financial transaction categorization and reporting platform
**Tech Stack**: React + FastAPI + PostgreSQL + Vertex AI (NX/pnpm monorepo)
**Architecture**: Domain-driven backend, feature-driven frontend
**Package Management**: pnpm workspaces (JS/TS), uv workspace (Python) - ALL FROM ROOT

## Essential Daily Workflow
```bash
# CRITICAL: Always work from workspace root (/Users/<USER>/giki-ai-workspace)
# NEVER cd into other directories!

# 1. Check current tasks
cat docs/tasks/active-tasks.md

# 2. Start development (NX orchestrated)
pnpm serve  # Starts both API and frontend

# 3. Verify system health
pnpm serve:status && pnpm lint

# 4. Run customer simulation
pnpm test:e2e
```

## Memory System
**Core Rules**: @.claude/memory/core-rules.md (development standards)
**Commands**: @.claude/memory/common-commands.md (quick reference)
**Current Work**: @docs/tasks/active-tasks.md (active sprint)
**Test Data & Auth**: @docs/auth-credentials.md (user credentials and test data files)

## Quick Commands (Frequently Used)
```bash
# WORKSPACE COMMANDS (always use from root, never cd into subdirs)
# Development
pnpm serve           # Start both servers (API:8000, App:4200)
pnpm serve:api       # Start API only (alternative: pnpm -w run serve:api)
pnpm serve:app       # Start frontend only
pnpm serve:status    # Check if servers running
pnpm serve:stop      # Stop all servers

# Quality
pnpm lint            # Lint all code (MUST pass before commit)
pnpm test:api        # Backend tests
pnpm test:e2e        # Playwright E2E tests

# Deployment
pnpm deploy:dev      # Deploy to development
pnpm deploy:prod     # Deploy to production

# Python scripts (always use uv from workspace root)
uv run scripts/[script-name].py
```

## Package Management (CRITICAL MONOREPO RULES)
```bash
# ADDING PACKAGES - ALWAYS FROM WORKSPACE ROOT!

# TypeScript/JavaScript packages:
pnpm add <package> -w                 # ALWAYS add to workspace root
pnpm add -D <package> -w              # Dev dependencies to workspace root
# NEVER use --filter to add to specific apps! All deps in root!

# Python packages:
uv add <package>                      # Add to workspace root pyproject.toml
# NEVER: cd apps/giki-ai-api && uv add  # WRONG! Creates duplicate venv

# Development dependencies:
pnpm add -D <package> -w              # TypeScript dev deps to workspace
uv add --dev <package>                # Python dev deps to workspace
```

## Architecture Patterns
**Backend**: `apps/giki-ai-api/src/giki_ai_api/domains/*/models.py|service.py|agent.py`
**Frontend**: `apps/giki-ai-app/src/features/*/components/|hooks/|pages/|services/`
**Shared Code**: Backend has `shared/` for reusable functions, Frontend has `shared/` for components
**Testing**: Single comprehensive E2E test (`tests/e2e/the-one-e2e-test.spec.ts`)

## Code Style & Quality
- **Zero Warnings**: `pnpm lint` must pass before commit
- **File Size**: Max 500 lines per file
- **Commits**: Every 15-30 minutes with meaningful messages
- **Python**: Always use `uv run` for script execution
- **Testing**: Write test → Run test → Fix → Commit when passing

## Database Access (FASTEST METHOD - MCP Servers)
**CRITICAL**: Use MCP postgres servers for instant database queries instead of Python scripts!

```bash
# Development Database (localhost)
mcp__postgres-dev__query       # Query local dev database
mcp__postgres-dev__list_tables # List tables in dev
mcp__postgres-dev__describe_table # Get table structure

# Production Database (Cloud SQL)
mcp__postgres-prod__query      # Query production database
mcp__postgres-prod__list_tables # List tables in prod
mcp__postgres-prod__describe_table # Get table structure
```

**Example Usage**:
```sql
-- Check users in dev
mcp__postgres-dev__query: SELECT * FROM users WHERE email LIKE '%@test.local'

-- Check production data (BE CAREFUL - READ ONLY!)
mcp__postgres-prod__query: SELECT COUNT(*) FROM transactions WHERE tenant_id = 3
```

## Claude Code Optimization
```bash
# Interactive mode shortcuts
Ctrl+C          # Cancel generation
Ctrl+D          # Exit session
Up/Down arrows  # Command history
\               # Multiline input (then Enter)

# Memory shortcuts
#               # Quick memory addition
/memory         # Edit memory files
/               # Slash commands
```

## Enhanced Slash Commands
- `/deploy [prod]` - Deploy with health verification
- `/health [local]` - System health check  
- `/test [e2e|api|coverage]` - Run test suites
- `/setup [verify]` - Environment setup
- `/task [milestone|create|breakdown|status]` - Milestone-driven task management

## Reference Links
**Development Workflow**: @.claude/memory/development-workflow.md
**Testing Strategy**: @.claude/memory/testing-strategy.md
**Deployment Process**: @.claude/memory/deployment-process.md
**Quick Troubleshooting**: @.claude/memory/troubleshooting-quick.md
**Claude Code Tips**: @.claude/memory/claude-code-tips.md
**Task Documentation**: @docs/README.md

## Production URLs
- **API**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
- **Frontend**: https://giki-ai-app-6uyufgxcxa-uc.a.run.app

---
**Updated**: 2025-06-27 | **Focus**: Milestone-driven development (M1: Nuvie, M2: Rezolve, M3: giki.ai), two-layer workflow, accuracy optimization