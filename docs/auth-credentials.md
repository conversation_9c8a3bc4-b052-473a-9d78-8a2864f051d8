# Authentication Credentials Documentation

---
status: active
last-updated: 2025-06-27
update-frequency: as-needed
update-triggers: user creation, password changes, environment updates
---

## Overview
This document contains all test user credentials for the giki.ai platform. These credentials are for development and testing purposes only.

## Password Hashing Details
- **Algorithm**: bcrypt
- **Development Rounds**: 4 (optimized for faster development)
- **Production Rounds**: 12 (standard security)
- **Library**: passlib with CryptContext

## Development Test Users

| Email | Password | Tenant | Admin | Purpose |
|-------|----------|--------|-------|---------|
| <EMAIL> | GikiDev2025#Secure | Test Tenant (1) | No | Basic user testing |
| <EMAIL> | RezolveDev2025#Auth | Rezolve AI (2) | No | Rezolve tenant testing |
| <EMAIL> | NuvieDev2025#Test | Nuvie (3) | No | Nuvie tenant testing |
| <EMAIL> | AdminDev2025#Super | Test Tenant (1) | Yes | Admin operations |

## Quick Test Commands

### Development Environment Testing
```bash
# Test giki.ai User
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=GikiDev2025#Secure"

# Test Rezolve User
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=RezolveDev2025#Auth"

# Test Nuvie User (M1 Testing)
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=NuvieDev2025#Test"

# Test Admin User
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=AdminDev2025#Super"
```

## Token Usage Example
```bash
# Store token in variable
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=GikiDev2025#Secure" | \
  grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

# Use token for authenticated requests
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/dashboard/metrics
```

## Important Notes
1. These credentials are for testing only - NEVER use in actual production
2. Passwords follow a consistent pattern for easy memorization
3. Dev passwords use 4 bcrypt rounds for faster testing
4. All passwords contain uppercase, lowercase, numbers, and special characters
5. Each tenant has its own test user for proper isolation testing

## Setup Scripts
- Development: `scripts/auth/setup-dev-users.py`
- Verification: `scripts/auth/verify-auth.py`

## Using MCP PostgreSQL Servers
For quick database operations, use the MCP postgres servers:

```bash
# Check users in development
mcp__postgres-dev__query: SELECT * FROM users

# Update user (dev only - prod is read-only)
mcp__postgres-dev__query: UPDATE users SET is_active = true WHERE email = '<EMAIL>'
```

## Test Data Files by Tenant

### M1: Nuvie (Zero-Onboarding)
- **File**: `data/milestones/M1-nuvie/nuvie_expense_ledger.xlsx`
- **Size**: 175KB
- **Records**: 900+ transactions
- **Purpose**: Test AI categorization without any historical training data
- **Key Fields**: Date, Description, Amount, Vendor (NO categories)
- **Test User**: <EMAIL>

### M2: Rezolve (Temporal Accuracy)
- **Files**: 
  - `data/milestones/M2-rezolve/Capital One.xlsx` (18KB)
  - `data/milestones/M2-rezolve/Credit Card.xlsx` (72KB)
  - `data/milestones/M2-rezolve/ICICI.xlsx` (133KB)
  - `data/milestones/M2-rezolve/SVB.xlsx` (14KB)
- **Total Records**: ~1,200 transactions (6 months: Jan-Jun)
- **Purpose**: Test improvement over original categorizations
- **Key Fields**: Date, Description, Amount, Vendor, Original_Category
- **Test User**: <EMAIL>

### M3: giki.ai (Hierarchy Compliance)
- **Status**: Files not yet available
- **Purpose**: Test custom category hierarchy compliance
- **Expected Fields**: Date, Description, Amount, Vendor, Category_Path, GL_Code
- **Test User**: <EMAIL>

## Test Data Upload Commands

```bash
# M1: Upload Nuvie zero-onboarding data
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=NuvieDev2025#Test" | \
  python3 -m json.tool | grep access_token | cut -d'"' -f4)

curl -X POST http://localhost:8000/api/v1/files/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@data/milestones/M1-nuvie/nuvie_expense_ledger.xlsx" \
  -F "file_type=transactions"

# M2: Upload Rezolve historical data
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=RezolveDev2025#Auth" | \
  python3 -m json.tool | grep access_token | cut -d'"' -f4)

# Upload each file
for file in "Capital One.xlsx" "Credit Card.xlsx" "ICICI.xlsx" "SVB.xlsx"; do
  curl -X POST http://localhost:8000/api/v1/files/upload \
    -H "Authorization: Bearer $TOKEN" \
    -F "file=@data/milestones/M2-rezolve/$file" \
    -F "file_type=transactions"
done
```