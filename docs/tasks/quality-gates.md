# Quality Gates Checklist

---
status: active
last-updated: 2025-06-25
update-frequency: monthly
update-triggers: quality process changes, new gate additions
---

## Before Marking ANY Task as Completed

**Code Quality**:
- [ ] Run: `pnpm nx lint giki-ai-api` - Must show "All files linted successfully"
- [ ] Run: `pnpm nx lint giki-ai-app` - Must show "All files linted successfully"
- [ ] Run: `pnpm nx test` - All tests must pass
- [ ] No console errors in browser (check DevTools)

**Deployment**:
- [ ] Code deployed to production successfully
- [ ] No deployment errors or rollbacks
- [ ] Production logs checked for errors

**Validation**:
- [ ] Playwright validation complete (capture screenshot)
- [ ] Manual testing confirms feature works
- [ ] No regression in existing features

**Documentation**:
- [ ] Code comments added where needed
- [ ] README updated if new setup required
- [ ] API documentation updated if endpoints changed

## Before Running /ship Command

**Pre-flight Checks**:
1. Run: `pnpm nx lint giki-ai-api`
2. Run: `pnpm nx lint giki-ai-app`  
3. Run: `pnpm nx test`
4. Run: `git status` - ensure clean working directory
5. Check current branch: must be on correct release branch

**Evidence Collection**:
- [ ] Capture Playwright screenshot of working feature
- [ ] Save deployment logs
- [ ] Note production URL for verification

## Task Status Transitions

**Pending → Active**:
- Prerequisites completed
- No blocking dependencies
- Resources available

**Active → Completed**:
- ALL quality gates checked above
- Evidence documented
- No known issues

**Active → Blocked**:
- Document specific blocker
- Create action plan to unblock
- Update blockers section in active-tasks.md

**Active → Failed**:
- Document failure reason
- Create recovery plan
- Consider breaking into smaller tasks

## Efficiency Metrics

**Tool Usage Efficiency**:
- [ ] Tool calls per task: Target <10 for simple tasks, <20 for complex
- [ ] Parallel operations ratio: >50% of operations should be batched/parallel
- [ ] Sequential penalty: Each unnecessary sequential operation = 1 inefficiency point
- [ ] Batch efficiency: Single Read/MultiEdit operations preferred over multiple

**Parallel Execution Patterns**:
- [ ] Investigation: All related files read in single operation
- [ ] Implementation: MultiEdit used for all changes to same file
- [ ] Verification: Tests run concurrently with development
- [ ] Deployment: Documentation updates while code deploys

**Performance Indicators**:
- **Excellent**: <5 tool calls for simple fix, heavy use of MultiEdit
- **Good**: <10 tool calls, some batching, minimal sequential ops
- **Needs Improvement**: >15 tool calls, many sequential operations
- **Inefficient**: >20 tool calls, no batching, all sequential

**Common Efficiency Patterns**:
1. **Search Once**: `find . -name "*.{py,ts}" | xargs grep -l "pattern"`
2. **Read Batch**: Read component + tests + types in one operation
3. **Edit Batch**: All changes to file in single MultiEdit
4. **Test Parallel**: `pnpm lint & pnpm test & pnpm build`
5. **Deploy Smart**: Update docs while deployment runs

## Common Quality Issues to Avoid

1. **Marking tasks complete with "will fix later"** - If it needs fixing, it's not complete
2. **Skipping Playwright validation** - Always verify in production
3. **Ignoring lint warnings** - Zero warnings policy means zero
4. **Partial deployments** - Either fully deployed or not
5. **Missing evidence** - No screenshot = not validated
6. **Sequential operations** - Multiple Edits instead of MultiEdit
7. **Redundant searches** - Not using comprehensive patterns
8. **Serial testing** - Running tests one by one instead of parallel