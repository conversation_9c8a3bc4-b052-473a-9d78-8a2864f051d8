# giki.ai Prompt Registry

Generated: 2025-06-27 14:25:21

## Table of Contents

- [schema_interpretation](#schema-interpretation)
- [debit_credit_inference](#debit-credit-inference)
- [regional_detection](#regional-detection)
- [hierarchy_detection](#hierarchy-detection)
- [correction_suggestion](#correction-suggestion)

---

## Schema Interpretation

### Schema Interpretation Main

**ID:** `schema_interpretation_main`  
**Version:** 1.2.0  
**Variables:** `file_name`, `file_headers`, `sample_data`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  
- performance_notes: Works well with Gemini 2.0 Flash  

**Model Config:**  
```json
{
  "temperature": 0.1,
  "max_output_tokens": 2000,
  "top_p": 0.8
}
```

**Template:**  
```
You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 5 rows):
{sample_data}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

ANALYSIS INSTRUCTIONS:
1. Analyze each column header and sample data intelligently
2. Look for semantic meaning, not just exact matches
3. Consider common variations (e.g., "Trans Date" = "date", "Memo" = "description")
4. For amount columns, check if they contain parentheses or negative values
5. Identify if the file uses separate debit/credit columns or a single amount column
6. Provide confidence scores (0.0-1.0) for each mapping
7. Include reasoning for each mapping decision

SPECIAL CONSIDERATIONS:
- Indian formats: Look for "Cr" and "Dr" indicators
- US formats: Look for parentheses for negative amounts
- European formats: Check for comma as decimal separator
- If you see "Withdrawal Amt." and "Deposit Amt.", these are debit/credit columns

Return JSON format:
{{
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Clear explanation of why this mapping was chosen"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative"
    }},
    "summary": "Brief analysis summary"
}}

JSON:
```

---

### Schema Interpretation Enhanced

**ID:** `schema_interpretation_enhanced`  
**Version:** 2.0.0  
**Variables:** `file_name`, `file_headers`, `sample_data`, `column_stats`, `previous_patterns`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  
- improvements: Added statistical validation and learning from history  

**Model Config:**  
```json
{
  "temperature": 0.1,
  "max_output_tokens": 2500,
  "top_p": 0.8
}
```

**Template:**  
```
You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 20 rows):
{sample_data}

Column Statistics:
{column_stats}

Previous Successful Patterns for this Customer:
{previous_patterns}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

ENHANCED ANALYSIS INSTRUCTIONS:
1. Use column statistics to validate your mappings:
   - Date columns should have date-like patterns
   - Amount columns should be mostly numeric
   - Description columns should have high unique value ratios
2. Learn from previous successful patterns if available
3. Pay special attention to columns that were successfully mapped before
4. For amount detection, use these enhanced rules:
   - Check "all_positive", "all_negative", or "mixed_signs" statistics
   - If two columns are all positive, they're likely debit/credit columns
   - If one column has mixed signs, it's likely a single amount column
5. Validate your confidence based on pattern matching in statistics

Return JSON format:
{{
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Explanation including statistical validation"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative",
        "statistical_validation": "explanation of how stats support this"
    }},
    "learned_patterns_applied": ["list of patterns used from history"],
    "summary": "Brief analysis summary with confidence factors"
}}

JSON:
```

---

## Debit Credit Inference

### Debit/Credit Logic Inference

**ID:** `debit_credit_inference`  
**Version:** 1.1.0  
**Variables:** `column_mappings`, `transaction_samples`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  

**Model Config:**  
```json
{
  "temperature": 0.1,
  "max_output_tokens": 1500,
  "top_p": 0.8
}
```

**Template:**  
```
You are an expert in accounting principles and banking conventions.
Analyze these transaction samples to determine debit/credit direction.

Column Mappings: {column_mappings}
Transaction Samples (first 10):
{transaction_samples}

ANALYSIS REQUIREMENTS:
1. Identify if transactions have separate debit/credit columns
2. If single amount column, determine sign convention (negative for debits or credits)
3. Look for transaction type indicators (DR/CR, Debit/Credit, Withdrawal/Deposit)
4. Consider regional banking conventions
5. Analyze description patterns for transaction direction clues

ACCOUNTING PRINCIPLES:
- Debits: Expenses, withdrawals, payments out, purchases
- Credits: Income, deposits, payments in, refunds, reversals
- Sign conventions vary by bank and region

REGIONAL VARIATIONS:
- US: Often uses parentheses for negative values
- India: May use "Dr" and "Cr" suffixes
- Europe: May use different decimal separators

Return JSON format:
{{
    "debit_credit_structure": "separate_columns|single_column_signed|single_column_with_type",
    "sign_convention": "negative_is_debit|negative_is_credit|absolute_with_type",
    "debit_indicators": ["list of terms/patterns indicating debits"],
    "credit_indicators": ["list of terms/patterns indicating credits"],
    "confidence": 0.95,
    "reasoning": "Detailed explanation of detected pattern",
    "examples": [
        {{
            "description": "sample transaction",
            "classification": "debit|credit",
            "evidence": "what indicated this classification"
        }}
    ],
    "recommendations": {{
        "amount_handling": "use_signed_values|split_by_type|use_absolute_with_indicator",
        "type_detection": "column_based|description_based|sign_based"
    }}
}}

JSON:
```

---

## Regional Detection

### Regional Format Detection

**ID:** `regional_detection`  
**Version:** 1.2.0  
**Variables:** `file_data`, `detected_region`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  

**Model Config:**  
```json
{
  "temperature": 0.1,
  "max_output_tokens": 2000,
  "top_p": 0.8
}
```

**Template:**  
```
You are an expert in global banking formats and regional variations.
Analyze this financial data to detect and handle regional conventions.

File Data Sample: {file_data}
Detected Region: {detected_region}

ANALYSIS REQUIREMENTS:
1. Detect date format (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, DD-MMM-YYYY, etc.)
2. Identify currency symbols and formatting
3. Recognize regional transaction description patterns
4. Identify regional banking terminology
5. Detect decimal/thousand separators (1,234.56 vs 1.234,56 vs 1 234.56)

REGIONAL PATTERNS TO DETECT:
- US: MM/DD/YYYY dates, $ currency, decimal point, comma thousands
- Europe: DD/MM/YYYY dates, € currency, comma decimal, period thousands
- India: DD-MM-YYYY dates, ₹ currency, lakhs/crores notation
- UK: DD/MM/YYYY dates, £ currency, varied formats
- International: ISO dates (YYYY-MM-DD), varied currencies

TERMINOLOGY PATTERNS:
- US: "Check #", "ATM Withdrawal", "Direct Deposit"
- India: "NEFT", "IMPS", "UPI", "Cheque"
- Europe: "SEPA", "IBAN", "Standing Order"
- UK: "BACS", "CHAPS", "Standing Order"

Return JSON format:
{{
    "detected_region": "US|Europe|India|UK|International|Other",
    "confidence": 0.95,
    "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD|DD-MM-YYYY|DD-MMM-YYYY",
    "date_examples": ["examples from data"],
    "currency": {{
        "symbol": "$|€|₹|£|Other",
        "position": "prefix|suffix",
        "decimal_separator": ".|,",
        "thousand_separator": ",|.|space|none"
    }},
    "regional_terms": {{
        "payment_methods": ["detected payment method terms"],
        "transaction_types": ["detected transaction type terms"],
        "banking_terms": ["region-specific banking terms found"]
    }},
    "parsing_instructions": {{
        "date_parser": "strptime format string",
        "amount_parser": {{
            "regex": "pattern to extract amounts",
            "conversion": "steps to convert to float"
        }},
        "description_cleaner": ["list of cleaning steps needed"]
    }},
    "validation_rules": {{
        "date_range": "expected date range for transactions",
        "amount_range": "typical amount ranges",
        "common_merchants": ["frequently seen merchant patterns"]
    }}
}}

JSON:
```

---

## Hierarchy Detection

### Category Hierarchy Detection

**ID:** `hierarchy_detection`  
**Version:** 1.0.0  
**Variables:** `category_samples`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  

**Model Config:**  
```json
{
  "temperature": 0.1,
  "max_output_tokens": 1000,
  "top_p": 0.8
}
```

**Template:**  
```
You are an expert at analyzing financial categorization patterns and detecting hierarchical structures.

Category Samples: {category_samples}

Analyze these categories and determine:
1. Are they hierarchical (contain parent > child relationships)?
2. What separator is used (if any)?
3. What type of categorization pattern do they follow?
4. How many levels deep is the hierarchy?

COMMON HIERARCHY PATTERNS:
- Separator-based: "Food > Restaurants > Fast Food"
- Nested: "Food:Dining:Restaurants"
- Path-based: "Expenses/Food/Restaurants"
- Code-based: "***********" (account codes)
- Mixed: "EXPENSE-Food-Restaurant"

ANALYSIS FOCUS:
- Consistency of separators across samples
- Number of hierarchy levels
- Parent-child relationship logic
- Any accompanying codes or IDs

Return JSON format:
{{
    "is_hierarchical": true|false,
    "hierarchy_details": {{
        "separator": ">|:|/|-|_|.",
        "max_depth": 3,
        "consistent_depth": true|false,
        "has_codes": true|false
    }},
    "pattern_type": "hierarchical|flat|mixed|coded",
    "examples": [
        {{
            "original": "sample category",
            "parsed_levels": ["Level1", "Level2", "Level3"],
            "depth": 3
        }}
    ],
    "confidence": 0.95,
    "reasoning": "Detailed explanation of detected pattern",
    "recommendations": {{
        "storage_method": "hierarchical_tree|flat_with_path|separate_levels",
        "query_strategy": "exact_match|prefix_match|level_based"
    }}
}}

JSON:
```

---

## Correction Suggestion

### Schema Correction Suggestions

**ID:** `correction_suggestion`  
**Version:** 1.1.0  
**Variables:** `original_mappings`, `validation_errors`, `user_feedback`, `available_columns`  
**Metadata:**  
- author: giki.ai team  
- last_updated: 2025-06-27  

**Model Config:**  
```json
{
  "temperature": 0.3,
  "max_output_tokens": 2000,
  "top_p": 0.9
}
```

**Template:**  
```
You are an expert at helping users correct schema mapping issues.
Generate helpful suggestions to fix the mapping problems.

Original Mappings: {original_mappings}
Validation Errors: {validation_errors}
User Feedback: {user_feedback}
Available Columns: {available_columns}

SUGGESTION REQUIREMENTS:
1. Provide clear, actionable correction suggestions
2. Explain why each correction is needed
3. Offer alternative mapping options
4. Prioritize user-friendly explanations
5. Include examples where helpful
6. Consider the user's feedback if provided

COMMON MAPPING ISSUES:
- Missing required fields (date, description, amount)
- Incorrect field types (mapping text column to amount)
- Ambiguous column names needing clarification
- Regional format mismatches
- Debit/credit confusion

Return JSON format:
{{
    "correction_suggestions": [
        {{
            "issue": "Specific problem identified",
            "severity": "critical|warning|info",
            "suggestion": "Clear action to fix the issue",
            "explanation": "Why this correction is needed",
            "alternatives": ["Other possible solutions"],
            "example": "Example of correct mapping",
            "confidence": 0.9
        }}
    ],
    "quick_fixes": [
        {{
            "field": "unmapped field name",
            "recommended_column": "best matching column",
            "confidence": 0.9,
            "reasoning": "why this match is recommended"
        }}
    ],
    "general_advice": "Overall tips for successful mapping",
    "next_steps": ["Ordered list of actions to resolve all issues"]
}}

JSON:
```

---

