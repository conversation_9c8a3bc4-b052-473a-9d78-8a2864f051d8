# User Stories - giki.ai Platform

---
status: active
last-updated: 2025-06-25
update-frequency: weekly
update-triggers: new features, user feedback, requirement changes
---

**Purpose**: Define features from the user's perspective using the standard user story format.
**Format**: "As a [role], I want [feature], so that [benefit]"

## Story Template

```
## US-XXX: [Story Title]
**As a** [role]  
**I want** [feature/capability]  
**So that** [business value/benefit]

### Acceptance Criteria
- [ ] Given [context], when [action], then [result]
- [ ] Given [context], when [action], then [result]

### Requirements
- Links to: BR-XXX
- Priority: MUST HAVE / SHOULD HAVE / COULD HAVE
```

## Core User Stories

### US-001: Upload Historical Transactions for AI Training
**As a** Finance Manager  
**I want** to upload my historical transaction data with categories  
**So that** the AI can learn my company's specific categorization patterns

### Acceptance Criteria
- [ ] Given I have 12 months of categorized data, when I upload it, then the system processes all transactions
- [ ] Given diverse file formats, when uploaded, then AI correctly interprets columns and schemas
- [ ] Given the upload completes, when I check status, then I see accuracy metrics >85%

### Requirements
- Links to: BR-002 (85% Accuracy), FR-001 (File Upload), FR-002 (Onboarding)
- Priority: MUST HAVE

---

### US-002: Categorize New Transactions Automatically
**As a** Finance Team Member  
**I want** to upload new uncategorized transactions  
**So that** they are automatically categorized based on learned patterns

### Acceptance Criteria
- [ ] Given trained AI model, when I upload new data, then categorization happens automatically
- [ ] Given categorized data, when I review, then I can approve or correct categories
- [ ] Given corrections made, when saved, then AI learns from feedback

### Requirements
- Links to: BR-001 (Multi-level Categorization), FR-004 (Reliable Categorization)
- Priority: MUST HAVE

---

### US-003: Generate Financial Reports
**As a** CFO  
**I want** to generate standard financial reports from categorized data  
**So that** I can make informed business decisions

### Acceptance Criteria
- [ ] Given categorized transactions, when I request P&L, then report generates in <5 seconds
- [ ] Given report generated, when I export, then it's compatible with Excel/accounting software
- [ ] Given multi-period data, when compared, then trends are clearly visible

### Requirements
- Links to: FR-006 (Report Generation), BR-028 (GL Code Management)
- Priority: MUST HAVE

---

### US-004: Chat with AI About Finances
**As a** Business Owner  
**I want** to ask questions about my financial data in natural language  
**So that** I can get insights without navigating complex interfaces

### Acceptance Criteria
- [ ] Given I ask "What were my top expenses last month?", when processed, then accurate answer provided
- [ ] Given agent generates a chart, when displayed, then I can download it
- [ ] Given I request a report via chat, when generated, then it matches UI-generated version

### Requirements
- Links to: BR-004 (Hybrid UI), FR-005 (Agent Interface), FR-009 (Agent Tools)
- Priority: MUST HAVE

---

### US-005: Manage GL Codes for Accounting Integration
**As a** Accounting Manager  
**I want** to map categories to GL codes  
**So that** I can export data directly to my accounting software

### Acceptance Criteria
- [ ] Given category hierarchy, when I assign GL codes, then mapping persists
- [ ] Given GL mappings exist, when I export, then format matches QuickBooks/SAP requirements
- [ ] Given parent category has GL code, when viewing children, then inheritance is clear

### Requirements
- Links to: BR-028 (GL Code Management), FR-003 (GL Integration)
- Priority: MUST HAVE

---

### US-006: Switch AI Providers During Outages
**As a** System Administrator  
**I want** the system to automatically switch AI providers  
**So that** operations continue even when primary provider is down

### Acceptance Criteria
- [ ] Given primary provider fails, when detected, then switch happens in <30 seconds
- [ ] Given provider switched, when categorizing, then accuracy remains at 100%
- [ ] Given outage resolved, when checked, then option to switch back available

### Requirements
- Links to: NFR-003 (Financial Reliability), TR-002 (Provider Flexibility)
- Priority: SHOULD HAVE

---

### US-007: View Real-time Performance Metrics
**As a** IT Manager  
**I want** to monitor system performance and accuracy  
**So that** I can ensure SLAs are met

### Acceptance Criteria
- [ ] Given dashboard loaded, when viewing, then see <200ms API response times
- [ ] Given categorization running, when monitored, then see success rate in real-time
- [ ] Given issues detected, when alerted, then notification received immediately

### Requirements
- Links to: NFR-001 (Performance), VC-002 (Performance Validation)
- Priority: SHOULD HAVE

---

### US-008: Start Using System Without Historical Data (Zero-Onboarding)
**As a** New Business Owner  
**I want** to categorize transactions without providing historical data  
**So that** I can start using the system immediately

### Acceptance Criteria
- [ ] Given I'm a new company with no historical data, when I upload transactions, then AI suggests categories
- [ ] Given AI suggestions, when I review them, then I can approve or modify categories
- [ ] Given I make corrections, when saved, then system learns for future transactions
- [ ] Given no patterns exist, when categorizing, then system uses standard GL hierarchy

### Requirements
- Links to: BR-030 (Zero-Onboarding), FR-004B (Zero-Onboarding Categorization)
- Priority: MUST HAVE

---

### US-009: AI-Driven Category Creation
**As a** Finance Manager  
**I want** the system to automatically create new categories when transactions don't match existing ones  
**So that** I don't have to manually create categories for every new transaction type

### Acceptance Criteria
- [ ] Given unmatched transaction, when processed, then AI suggests appropriate category name
- [ ] Given suggested category, when accepted, then hierarchy is automatically created
- [ ] Given similar categories exist, when creating new ones, then system suggests consolidation
- [ ] Given naming patterns, when detected, then parent-child relationships are established

### Requirements
- Links to: FR-004 (Reliable Categorization), BR-030 (Zero-Onboarding)
- Priority: MUST HAVE

---

### US-010: Hierarchy Detection and Management
**As a** Accounting Manager  
**I want** the system to detect and create category hierarchies automatically  
**So that** my chart of accounts is properly structured

### Acceptance Criteria
- [ ] Given category names with separators (>, :, -), when processed, then hierarchy is created
- [ ] Given similar category names, when detected, then consolidation is suggested
- [ ] Given GL code standards, when creating categories, then proper structure is maintained
- [ ] Given existing hierarchies, when adding new categories, then proper placement is suggested

### Requirements
- Links to: FR-003 (GL Integration), FR-004 (Reliable Categorization)
- Priority: SHOULD HAVE

---

## Story Prioritization

### MUST HAVE (Core MVP)
- US-001: Historical Upload & Training
- US-002: Automatic Categorization
- US-003: Financial Reports
- US-004: AI Chat Interface
- US-005: GL Code Management
- US-008: Zero-Onboarding Categorization (New Companies)
- US-009: AI-Driven Category Creation

### SHOULD HAVE (Enhanced Value)
- US-006: Provider Switching
- US-007: Performance Monitoring
- US-010: Hierarchy Detection and Management

### COULD HAVE (Future Enhancement)
- Advanced analytics
- Multi-currency support
- Audit trail visualization
- Custom report builder

## Story Dependencies

```
Path A (Traditional):
US-001 (Training) → US-002 (Categorization) → US-003 (Reports)
                                           ↘
                                             US-004 (Chat)
                                           ↗
                 US-005 (GL Codes) ────────

Path B (Zero-Onboarding):
US-008 (Zero-Onboarding) → US-009 (AI Category Creation) → US-003 (Reports)
                                                        ↘
                                                          US-004 (Chat)
                                                        ↗
                         US-005 (GL Codes) ────────────

US-006 & US-007 are independent infrastructure stories
US-010 supports both paths
```

## Acceptance Testing

Each story requires:
1. Unit tests for backend logic
2. Integration tests for API endpoints
3. E2E tests for user workflows
4. Performance tests for targets
5. User acceptance sign-off