# Financial Transaction Categorization System Requirements - giki.ai Platform

---
status: active
last-updated: 2025-06-25
update-frequency: weekly
update-triggers: new requirements, status changes, roadmap updates
---

Version: 2.0
Last Updated: 2025-01-13
Purpose: B2B financial transaction categorization and reporting system where businesses upload historical data and AI learns categorization patterns for automated financial reporting

## Pipeline Flow
```
Plan Sheet → Requirements (this file) → Specifications → Tasks → Audits
```

## Generating Tasks from Requirements

When a BR-XXX is approved:
1. Create specification in @docs/specifications/[domain]-spec.md
2. Break down into tasks in @docs/tasks/required-tasks.md
3. Reference BR-XXX in each task using format:
   ```
   ## TASK-XXX: [Title]
   **Requirement**: BR-XXX
   ```

## Requirement Status Tracking

### Status Definitions

- **Draft**: User requested, not yet specified
- **Specified**: Has detailed specification section
- **Tasked**: Tasks generated in required-tasks.md
- **Active**: Currently under development
- **Shipped**: Implementation complete
- **Audited**: Quality verified and in production

### Tracking Format

Each requirement includes:

- Status: [Current status]
- Specification: [Link to spec file#section]
- Tasks: [List of task IDs]
- Audit: [Audit report ID]

## 1. Business Requirements Tracking

### BR-001: Multi-level Categorization with GL Codes

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#categorization, frontend-spec.md#gl-codes
- **Tasks**: TASK-001, TASK-002, TASK-003
- **Audit**: AUDIT-2025-01-15-001

### BR-002: >85% Categorization Accuracy

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#accuracy-metrics
- **Tasks**: TASK-SCHEMA-001, RAG implementation tasks
- **Audit**: AUDIT-2025-01-13-001

### BR-003: Multi-tenant Performance <200ms

- **Status**: Active
- **Specification**: system-design-spec.md#performance
- **Tasks**: TASK-PERF-001
- **Audit**: AUDIT-2025-01-15-001

### BR-004: Hybrid UI (Traditional + Unified Agent)

- **Status**: Active
- **Specification**: frontend-spec.md#hybrid-interface, agents-and-tools-spec.md#unified-agent
- **Tasks**: TASK-UI-001, TASK-004 (agent completeness)
- **Audit**: Pending

### BR-028: GL Code Management (New)

- **Status**: Tasked
- **Specification**: frontend-spec.md#gl-code-management
- **Tasks**: TASK-GL-001, TASK-GL-002, TASK-GL-003
- **Audit**: Not started

### BR-029: Production Build and Deployment Pipeline

- **Status**: Tasked
- **Specification**: build-and-deploy-specification.md
- **Tasks**: TASK-BUILD-001, TASK-BUILD-002, TASK-BUILD-003
- **Audit**: Not started

### BR-030: Zero-Onboarding Categorization (New Companies)

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#zero-onboarding-categorization
- **Tasks**: TASK-CAT-001, TASK-CAT-002, TASK-CAT-003
- **Audit**: Not started
- **Description**: Support new companies without historical data to use categorization immediately
- **Use Cases**: Startups, new businesses, companies switching from manual processes

## 2. Core Business Objectives

### 1.1 Target Market

- **Primary Region**: India (DPDPA compliance required)
- **Target Users**: Finance teams in growing startups
- **Initial Scale**: 3,000+ transactions per customer
- **Supported Currencies**: INR (primary), USD, EUR (secondary)

### 1.2 Core Value Proposition

- **75% reduction** in manual transaction categorization effort
- **Under 15 minutes** from file upload to usable categorized data
- **100% financial accuracy** through pattern matching from labeled historical data - 99% = 0% in finance
- **Multi-tenant B2B system** where businesses are tenants and employees are users
- **Cloud-agnostic deployment** with ability to switch LLM providers during outages

### 1.3 Customer Journey Phases

#### Path A: Traditional Onboarding (Companies WITH Historical Data)

##### Phase 1: Onboarding (One-Time Historical Data Training)
- **Duration**: 1-2 hours initial setup
- **Data Required**: 12+ months of historical transactions WITH customer's original categories
- **Purpose**: Train AI to understand customer's specific categorization patterns
- **Process Flow**:
  1. Upload historical data files with category labels
  2. System learns categorization patterns (first 6 months)
  3. Temporal validation tests accuracy (next 6 months)
  4. Progressive improvement simulation month-by-month
  5. Production approval when >85% accuracy achieved
- **Success Metric**: AI matches customer's historical categorization patterns

##### Alternative: Direct Category Entry (No Historical Data)
- **Duration**: 15-30 minutes setup
- **Data Required**: List of categories customer wants to use (can be entered manually)
- **Purpose**: Set up category hierarchy without requiring historical transaction data
- **Process Flow**:
  1. Customer enters or uploads their category structure (e.g., spreadsheet)
  2. System creates category hierarchy in database
  3. AI uses these categories for future transaction categorization
  4. System learns patterns from ongoing corrections
- **Success Metric**: Category structure ready for immediate use

##### Phase 2: Production Usage (Ongoing Monthly Operations)
- **Frequency**: Monthly or as-needed uploads
- **Data Required**: Raw transaction data WITHOUT categories
- **Purpose**: Enable financial reporting, analytics, and business insights
- **Process Flow**:
  1. Upload new transaction files (no categories)
  2. AI categorizes based on learned patterns
  3. Review and approve categorizations
  4. Generate financial reports and analytics
  5. Use agent for insights and queries
- **Value Delivery**: Categorization is NOT the end goal - it enables:
  - Financial reports (P&L, cash flow, etc.)
  - GL code assignment for accounting software
  - Spending analytics and trends
  - Intelligent agent conversations about finances
  - Budget tracking and forecasting

#### Path B: Zero-Onboarding (New Companies WITHOUT Historical Data)

##### Option 1: Immediate Usage (No Setup Required)
- **Duration**: Start using immediately
- **Data Required**: Raw transaction data WITHOUT categories
- **Purpose**: Enable instant categorization for companies starting fresh
- **Process Flow**:
  1. Upload transaction files (no categories needed)
  2. AI analyzes descriptions and suggests categories on-the-fly
  3. System creates category hierarchy as transactions are processed
  4. User reviews and approves/modifies suggested categories
  5. System learns patterns from each correction
- **Key Features**:
  - Zero setup time - works out of the box
  - AI-driven category creation from transaction descriptions
  - Smart merchant name recognition
  - Progressive learning from user feedback
  - Standard financial hierarchy fallbacks

##### Option 2: Quick Category Setup (5-10 minutes)
- **Duration**: 5-10 minutes initial setup
- **Data Required**: Basic category preferences (manual entry)
- **Purpose**: Light setup for companies with category preferences
- **Process Flow**:
  1. Enter a few top-level categories manually (e.g., "Office Expenses", "Travel", "Marketing")
  2. System auto-expands into full hierarchy using financial best practices
  3. Upload transactions and AI maps to established categories
  4. System suggests new subcategories as needed
- **Key Features**:
  - Minimal manual setup
  - AI hierarchy expansion
  - Category template suggestions
  - Smart subcategory creation

**Success Metric**: Useful categorization from first upload, improving with each correction

## 3. Functional Requirements

### 2.1 Data Processing Pipeline

**FR-001**: Excel/CSV file upload and processing

- **Status**: Active
- **Specification**: contracts-spec.md#upload-endpoint, frontend-spec.md#upload-wizard
- **Tasks**: TASK-001, TASK-UI-001, TASK-SCHEMA-001
- **Audit**: AUDIT-2025-01-13-001
- Support formats: .xlsx, .xls, .csv (potentially multi-sheet)
- File size limit: 10MB
- **Data Location**: Files located in `apps/giki-ai-api/data/input_files/`
- **Required Data**: Transaction columns, date info, multi-level categorization labels, currency specification
- **Diverse Schema Support**: Handle varying schemas from different sheets/files automatically
- **AI-Powered Date Parsing**: 
  - Handles ANY date format humans use (MM/DD/YY, DD-MM-YYYY, Jan-15-2024, etc.)
  - 100% accuracy requirement - if even one date fails standard parsing, AI is used
  - Privacy-preserving - only dates sent to AI without transaction context
  - 2-digit year interpretation (00-29 → 2000-2029, 30-99 → 1930-1999)

**FR-001A**: Intelligent Schema Interpretation

- **AI Column Mapping**: Automatically detect and map customer file columns to standard format
- **Customer Confirmation**: Users can correct interpretation but shouldn't have to
- **Unified Schema Output**: Convert all diverse customer schemas into single standardized transaction format
- **Debit/Credit Inference**: Intelligently determine transaction direction using accounting principles
- **Cultural Awareness**: Handle regional banking terminology and formats

**FR-001B**: Database-First Architecture

- **Unified Transaction Schema**: All customer data standardized into single database schema
- **Multi-level Categorization**: Include complete category hierarchies in database storage
- **Tenant Isolation**: Complete data isolation between business tenants
- **RAG Creation**: Build vector/RAG artifacts ONLY from database (never from raw files)
- **Processing Sequence**: Files → Schema Interpretation → Database Storage → RAG Creation

**FR-002**: Customer onboarding with 100% accuracy validation (PHASE 1 ONLY)

- **Status**: Active
- **Specification**: system-design-spec.md#onboarding-workflow
- **Tasks**: Onboarding implementation tasks
- **Audit**: AUDIT-2025-01-13-001
- **Purpose**: ONE-TIME training process to learn customer's categorization patterns
- **Critical Distinction**: This is NOT regular file upload - requires LABELED historical data
- **Critical Sequence**: Files → Database Storage → RAG Creation → Temporal Validation → Production
- **Bulk Data Upload**: Customer uploads full year (Jan-Dec 2024) transaction data WITH EXISTING CATEGORY LABELS
- **Why Labels Required**: AI needs to learn what "Payroll" or "Marketing" means to THIS customer
- **Database-First Processing**: All interpretation and processing done after database storage
- **RAG Corpus Creation**: Build knowledge base from customer's database-stored patterns (not raw files)
- **Temporal Accuracy Simulation** (AFTER database storage):
  - **Data Split**: Jan-June 2024 (training), July-Dec 2024 (testing)
  - **July Simulation**: Train on Jan-June database data, test on July transactions vs original labels
  - **August Simulation**: Train on Jan-July data, test on August (simulating customer feedback loop)
  - **Progressive Testing**: Continue through December with expanding training corpus
  - **Customer Feedback Simulation**: Corrections from previous months improve next month's training
  - **KEY INSIGHT**: This simulates real-world usage where AI improves monthly
- **Accuracy Requirement**: >85% accuracy through matching patterns learned from labeled historical data
- **Financial Reliability**: Must demonstrate consistent accuracy before production
- **Onboarding Gate**: Customer approved for production ONLY after temporal validation passes
- **Test Files**: Capital One.xlsx, Credit Card.xlsx, ICICI.xlsx, SVB.xlsx (in `input_files` directory)
- **After Onboarding**: Customer moves to Phase 2 - regular uploads WITHOUT labels

### 2.2 Categorization System

**FR-003**: General Ledger (GL) Code Integration

- **Status**: Active
- **Specification**: frontend-spec.md#gl-code-management
- **Tasks**: TASK-016 (GL Code Backend), frontend GL tasks
- **Audit**: Pending
- **Category-to-Ledger Equivalence**: Multi-level categories = General Ledger structure
- **Unique GL Codes**: Each branch in category hierarchy = unique GL code identifier
- **Ledger Rollup**: Support parent-child relationships for GL code rollups
- **23 Top-Level Structure**: Map to standard 23 top-level financial reporting items
- **Excel Pivot Compatibility**: Categories mirror finance team Excel pivot table structures
- **Hierarchical Properties**: Enable rollup functionality while maintaining unique branch identification
- **Customer Schema Unification**: Standardize diverse customer categorization into unified GL structure

**FR-004**: 100% Reliable Categorization (Pattern-Based from Historical Labels)

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#categorization-agent
- **Tasks**: TASK-SCHEMA-001, categorization implementation
- **Audit**: AUDIT-2025-01-13-001
- **Pattern-Based System**: Match transactions to patterns learned from customer's labeled historical data for 100% accuracy
- **Database-Driven**: All categorization based on database-stored patterns (not raw files)
- **Learning System**: Improve through customer feedback without reducing reliability
- **Batch Processing**: Handle similar transactions efficiently
- **Real-time Metrics**: Display categorization success rates and system health
- **Automatic Category Creation**: When no high-certainty matches found, AI suggests appropriate new categories and hierarchies
- **Hierarchy Detection**: Automatically detect and create parent-child relationships from category naming patterns
- **Category Consolidation**: Merge similar categories and handle variations (e.g., "Food & Dining" vs "Food and Dining")

**FR-004A**: Production File Upload (PHASE 2 - Post-Onboarding)

- **Purpose**: Process NEW transactions for financial reporting and insights
- **Critical Distinction**: This is regular usage - requires RAW data WITHOUT labels
- **File Requirements**: 
  - Date, Description, Amount, Account columns
  - NO category columns (AI will categorize)
  - Standard formats: Excel, CSV
- **Process Flow**:
  1. Upload raw transaction files via `/upload` page
  2. AI categorizes by matching patterns learned from labeled historical data during onboarding
  3. Review and approve categorizations
  4. Generate reports and analytics
- **Value Delivery**: Categorization enables business value:
  - Financial statements (P&L, Balance Sheet, Cash Flow)
  - GL code assignment for accounting software export
  - Spending analytics and trend analysis
  - Agent conversations about financial insights
  - Budget vs actual comparisons
- **Key Point**: Categorization is NOT the end goal - it's a prerequisite for reports/insights

**FR-004B**: Zero-Onboarding Categorization (New Companies Without Historical Data)

- **Purpose**: Enable immediate categorization for companies without historical data or pre-existing hierarchies
- **Target Users**: Startups, new businesses, companies new to digital accounting
- **Critical Distinction**: No training data or category setup required - works immediately
- **Immediate Usage Process Flow**:
  1. Upload raw transaction files (no categories or setup needed)
  2. AI analyzes each transaction and suggests category name in real-time
  3. System automatically creates category hierarchy as suggestions are approved
  4. User can accept, modify, or replace AI suggestions
  5. System learns patterns from each decision for future accuracy
- **Category Setup Process Flow** (Optional):
  1. User enters basic category names manually (5-10 categories)
  2. System auto-expands into full hierarchy using financial best practices
  3. AI maps future transactions to established structure
  4. System suggests new subcategories as needed
- **Real-Time Category Creation Strategy**:
  - Analyze transaction descriptions, merchant names, amounts in real-time
  - Generate appropriate category names using financial terminology
  - Detect hierarchy patterns from naming conventions
  - Create parent-child relationships automatically
  - Suggest category consolidation when similar names detected
- **No Pre-Existing Hierarchy Required**: UI works with empty category database
- **Fallback Strategy**: When AI can't determine category, offer:
  - Standard financial templates (Expenses, Revenue, etc.)
  - Industry-specific category suggestions
  - User-defined category creation
- **Progressive Learning**: System becomes more accurate with each user decision

### 2.3 Runtime Capabilities and Agent Architecture

**FR-005**: Comprehensive Agent Interface

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#unified-agent
- **Tasks**: TASK-UI-001, TASK-004
- **Audit**: Pending completion
- **Agent Equivalence**: Agents can do everything the traditional UI allows
- **File Upload via Chat**: Users can upload files through agent conversation
- **Report Generation**: Agents create plots, tables, reports and display in chat window
- **Navigation Assistance**: Agents guide users to correct pages when tasks are ready
- **Download Capabilities**: All agent-generated content (images, reports, tables) downloadable
- **Chat Integration**: Agents display results directly in chat interface

**FR-006**: Standard Report Generation

- **Pre-built Reports**: Standard financial reports available via UI and agents
- **Custom Report Builder**: UI for creating custom reports, accessible via agents
- **Real-time Generation**: Reports generated on demand from database transactions
- **Export Formats**: Support accounting software formats (QuickBooks, SAP, Xero)

**FR-007**: Hybrid Interaction Paradigm

- **Traditional Web Interface**: Forms, tables, direct manipulation for users who prefer clicking
- **Conversational Agent**: Natural language interface for users who prefer talking
- **Shared Backend**: Both methods access identical backend services and data
- **Real-time Sync**: Changes reflected immediately across both interfaces
- **Agent Preference**: Users can accomplish entire workflows through agent conversation only

### 2.4 Customer Onboarding Workflow

**FR-008**: Database-First Onboarding Process

- **Phase 1 - Data Ingestion**:
  1. Customer uploads bulk transaction files from `input_files` directory (full year with labels)
  2. AI column interpretation with debit/credit inference
  3. Unified schema transformation (standardize diverse formats)
  4. Database storage with complete tenant isolation
- **Phase 2 - Knowledge Base Creation**: 5. RAG corpus generation from database-stored patterns (never from raw files) 6. Multi-level category hierarchy extraction and GL code mapping 7. 23 top-level financial structure integration
- **Phase 3 - Temporal Accuracy Validation**: 8. Progressive monthly simulation using database data (July-December 2024) 9. Train on Jan-June, test on July; train on Jan-July, test on August, etc. 10. Compare lookup results vs customer's original category labels 11. Simulate customer feedback loop improving knowledge base
- **Phase 4 - Production Approval**: 12. Validate 100% accuracy through lookup/retrieval system 13. Generate onboarding completion report with reliability metrics 14. Approve customer for production usage (no tolerance for <100% financial accuracy)
- **Phase 5 - Production Deployment**: 15. Enable ongoing file uploads and 100% reliable categorization 16. Activate standard and custom reporting features 17. Enable conversational agent with full transaction context and tool access

### 2.5 Agent Tool Development Requirements

**FR-009**: Reliable Agent Tool Suite

- **Vector/RAG Search Tools**: For transaction lookup and pattern matching
- **Direct Database Query Tools**: SQL-based tools for reliable data access
- **Report Generation Tools**: Programmatic creation of financial reports
- **Visualization Tools**: Chart and graph generation with downloadable outputs
- **File Navigation Tools**: Help users upload and process files via conversation
- **Tool Reliability**: Tools must be reliable enough to not depend on LLM intelligence
- **Model Agnostic**: Support switching between any LLM provider (Anthropic, OpenAI, Google, custom models)

**FR-010**: Production Build and Deployment Pipeline

- **Status**: Tasked
- **Specification**: build-and-deploy-specification.md
- **Tasks**: TASK-BUILD-001, TASK-BUILD-002, TASK-BUILD-003
- **Audit**: Not started
- **Multi-stage Docker Builds**: Optimized production containers
- **CI/CD Pipeline**: Automated deployment to Google Cloud Run
- **Container Registry**: Google Artifact Registry integration
- **Health Checks**: Automated deployment verification
- **Environment Management**: Proper secrets and configuration handling

### 2.6 Accuracy Measurement Framework

**FR-011**: Comprehensive Accuracy Testing System

- **Status**: Active
- **Specification**: agents-and-tools-spec.md#accuracy-measurement
- **Tasks**: TASK-ACCURACY-001, TASK-ACCURACY-002, TASK-ACCURACY-003
- **Audit**: Implementation in progress
- **Purpose**: Production-grade accuracy measurement across all categorization scenarios
- **Three Scenario Framework**:
  1. **Historical Data Scenario**: RAG-based categorization with onboarding corpus
  2. **Schema-Only Scenario**: Category schema provided without transaction history
  3. **Zero-Onboarding Scenario**: No schema, no historical data (pure AI-driven)

**FR-011A**: AI Judge Evaluation System

- **AI Judge Agent**: Specialized agent for evaluating categorization correctness
- **Evaluation Criteria**:
  - Exact Match: AI category exactly matches expected/original
  - Semantic Match: AI category captures same business intent
  - Partial Match: Correct general area but not specific enough
  - Incorrect: Completely wrong or nonsensical categorization
- **Judgment Confidence**: AI judge provides confidence score (0.0-1.0) for each evaluation
- **Detailed Reasoning**: Comprehensive explanation of judgment rationale
- **Batch Processing**: Efficient evaluation of multiple categorizations

**FR-011B**: Accuracy Metrics Calculation

- **Precision Calculation**: TP / (TP + FP) - measures categorization exactness
- **Recall Calculation**: TP / (TP + FN) - measures categorization completeness
- **F1-Score Calculation**: 2 × (Precision × Recall) / (Precision + Recall) - balanced measure
- **Overall Accuracy**: (Correct + 0.5 × Partial) / Total × 100% - weighted accuracy
- **Confidence Analysis**: Metrics broken down by AI confidence ranges
- **Hierarchy Analysis**: Accuracy metrics by category hierarchy levels
- **Category-Specific Metrics**: Individual precision/recall per category type

**FR-011C**: Category Schema Import and Management

- **Multi-Format Support**: Excel, CSV, JSON, YAML category schema import
- **Intelligent Hierarchy Detection**: Auto-detect hierarchy from flat category lists
- **GL Code Generation**: Automatic GL code assignment for imported categories
- **Schema Validation**: Comprehensive validation with warnings and error reporting
- **Naming Pattern Recognition**: Support for various hierarchy naming conventions:
  - Path-based: "Parent/Child/Grandchild"
  - Arrow-based: "Parent > Child > Grandchild"
  - Numeric: "1.1.1 Category Name"
- **Conflict Resolution**: Handle duplicate names and inconsistent structures

**FR-011D**: Comprehensive Accuracy Reporting

- **Real-Time Dashboards**: Live accuracy metrics and trend visualization
- **Excel Report Generation**: Detailed reports with original vs AI categories
- **Human Verification Column**: AI judge correctness evaluation in Excel
- **Scenario Comparison**: Side-by-side accuracy comparison across scenarios
- **Historical Tracking**: Accuracy trends over time and improvements
- **Quality Indicators**:
  - Non-generic category rate (avoid "Miscellaneous", "Other")
  - Hierarchical structure quality
  - GL code mapping completeness
  - Business logic soundness

**FR-011E**: Production Integration Features

- **API-First Design**: All accuracy testing available via REST endpoints
- **Multi-Tenant Isolation**: Complete tenant separation for accuracy data
- **Background Processing**: Long-running accuracy tests as background tasks
- **Automated Scheduling**: Periodic accuracy validation for production systems
- **Alert System**: Notifications when accuracy drops below thresholds
- **Agent Integration**: Conversational accuracy testing via agent interface

**FR-011F**: Test Configuration and Management

- **Test Templates**: Pre-configured tests for common scenarios
- **Sample Size Configuration**: Flexible transaction sample sizes (1-10,000)
- **Data Source Management**: Support for various test data formats and sources
- **Test Status Tracking**: Pending, running, completed, failed status management
- **Error Recovery**: Graceful handling and recovery from test failures
- **Result Archival**: Long-term storage of accuracy test results

## 4. Non-Functional Requirements

### 3.1 Performance Requirements (Single Definition)

**NFR-001**: System performance targets

- API response time: <200ms (non-AI endpoints)
- AI categorization: <3s per transaction
- Authentication: <200ms target
- Page load time: <3 seconds
- File processing: <30 seconds for 1,000 transactions
- Database queries: <50ms
- UI interactions: <100ms

### 3.2 Scalability Requirements

**NFR-002**: Multi-tenant architecture

- Initial: 3,000 transactions per customer
- Target: Support up to 10M transactions per tenant
- Concurrent users: 10,000+
- Data isolation between tenants
- Auto-scaling infrastructure

### 3.3 Financial-Grade Reliability Requirements

**NFR-003**: System availability and financial reliability

- **Financial Accuracy**: 100% (99% = 0%, 99.9% = 0% in finance)
- **Cloud-Agnostic Deployment**: Deploy on any cloud provider (GCP, AWS, Azure)
- **LLM Provider Switching**: Switch between providers during outages (Anthropic, OpenAI, Google, custom)
- **Lookup-Based System**: No prediction-based ML for financial categorization
- **Outage Resilience**: Continue operations when primary cloud provider is down
- **System Uptime**: 99.9% (infrastructure level)
- **Data Accuracy**: 100% (financial level)
- **Automatic Recovery**: From cloud provider outages by switching providers

### 3.4 Security Requirements

**NFR-004**: Data protection

- DPDPA compliance (India)
- Encrypted file storage
- JWT-based authentication
- Tenant data isolation
- Audit trails for all operations

## 4. Technical Requirements

### 4.1 Technology Stack (Unified)

**TR-001**: Core technologies

- **Backend**: Python + FastAPI + Supabase PostgreSQL
- **Frontend**: TypeScript + React + Vite + TailwindCSS
- **AI/ML**: Google Vertex AI Gemini 2.0 Flash
- **Infrastructure**: Supabase + Google Cloud Platform
- **Package Management**: uv (Python), pnpm (JavaScript)
- **Monorepo**: Nx + pnpm + uv (NO npm/yarn/pip/poetry)

### 4.2 Agent Architecture Requirements

**TR-002**: ADK and A2A Integration

- **Framework**: Agent Development Kit (ADK) for building reliable agents
- **Protocol**: Agent-to-Agent (A2A) communication protocol for interoperability
- **Pattern**: StandardGikiAgent inheritance from ADK base classes
- **Tool Reliability**: Tools reliable enough to not depend on underlying LLM intelligence
- **Provider Flexibility**: Support any LLM provider (cloud-agnostic)
- **Financial Grade**: Agents must achieve 100% reliability for financial operations

### 4.3 Database and Infrastructure

**TR-003**: Database integration

- Database: PostgreSQL (Cloud SQL for production, local for development)
- Multi-tenant isolation: Row-level security
- Authentication: JWT with tenant context

### 4.4 UI/UX Requirements

**TR-004**: Panel management

- Three-column layout: LeftNav (220-320px), MainContent, AgentPanel (320-480px)
- Automatic collapse for screens under 1400px
- Persistent state management with localStorage
- Agent panel visible on all pages

**TR-005**: Visual consistency

- Excel-inspired design patterns
- Monospace fonts for data display
- Brand color: #1A5F2F (derived from logo)
- Standardized button and error component styling

## 5. Validation Criteria

### 5.1 Financial Accuracy Validation (100% Requirement)

**VC-001**: Transaction categorization reliability and onboarding validation

- **Onboarding Target**: 100% accuracy through pattern matching against customer's labeled historical data
- **Production Target**: 100% financial accuracy on ongoing transaction categorization
- **Method**: Complete match validation for all categorization levels (partial = incorrect)
- **Levels**: 100% accuracy at Level-1, Level-2, and full multilevel hierarchy
- **Evidence**: Transaction-level drill-down with lookup success/failure tracking
- **Temporal Testing**:
  - Month-by-month reliability validation using database-stored patterns
  - Customer feedback simulation improving lookup knowledge base
  - RAG corpus evolution tracking (knowledge base expansion without accuracy loss)
- **Financial Reality**: 99% = 0%, 99.9% = 0% (finance requires perfection)
- **Deployment Gate**: Customer cannot proceed until 100% lookup reliability achieved

### 5.2 Performance Validation

**VC-002**: Response time verification

- Measure all API endpoints against <200ms target (non-AI endpoints)
- AI categorization: <3s per transaction
- Authentication: <200ms target
- Test file processing with 1,000 transaction files
- Verify page load times across all workflows
- Monitor memory usage (<200MB target)

### 5.3 User Acceptance

**VC-003**: Business value delivery

- Customer completes onboarding in under 15 minutes
- 75% reduction in manual categorization measured
- Successful export to accounting software
- Positive feedback on UI/UX usability

## 6. Success Metrics Dashboard

### 6.1 Core Metrics (Financial-Grade Standards)

- **Financial Accuracy**: 100% (pattern matching against labeled historical data, measured continuously)
- **Agent Tool Reliability**: 100% (tools must not depend on LLM intelligence)
- **API Performance**: <200ms average (non-AI endpoints, measured continuously)
- **Database Query Performance**: <50ms (measured continuously)
- **Authentication Performance**: <200ms target (measured continuously)
- **System Uptime**: 99.9% (infrastructure level, measured monthly)
- **Cloud Provider Switching**: <30 seconds (outage recovery time)
- **Processing Speed**: <30 seconds for 1,000 transactions (measured per upload)
- **Agent Equivalence**: 100% (agents can do everything UI allows)

### 6.2 Feature Completion Tracking

- **Schema Interpretation**: Intelligent column mapping with debit/credit inference
- **Database-First Architecture**: All processing from unified database schema
- **Pattern-Based Categorization**: 100% accuracy through matching patterns learned from customer's labeled historical data
- **GL Code Integration**: 23 top-level financial structure with rollup capabilities
- **Agent Tool Suite**: Vector search, database query, report generation, visualization
- **Agent Equivalence**: Conversational interface can do everything UI allows
- **Cloud-Agnostic Deployment**: Multi-provider support with outage resilience

## 7. Requirements Traceability

### 7.1 Business Value Mapping

Each requirement maps to specific customer outcomes:

- **Database-First Processing**: Unified data handling regardless of source format
- **100% Financial Accuracy**: Reliable categorization for accounting compliance
- **GL Code Integration**: Direct integration with accounting software and ledger systems
- **Agent Interface**: Natural language access to all financial operations
- **Cloud-Agnostic Architecture**: Business continuity during provider outages

### 7.2 Implementation Priority

1. **Critical**: Schema interpretation, database storage, lookup-based categorization
2. **High**: GL code mapping, agent tool development, 100% accuracy validation
3. **Medium**: Advanced reporting, conversational interface, multi-provider switching
4. **Low**: Additional export formats, advanced analytics, customization options

## 8. Financial-Grade System Design Principles

### 8.1 Core Principles

- **No Prediction Tolerance**: 99% = 0%, 99.9% = 0% in financial systems
- **Pattern-Based Categorization**: Match against patterns learned from customer's labeled historical data
- **Tool Reliability**: Agent tools must work independently of LLM intelligence
- **Provider Independence**: Switch between any LLM provider or cloud without data loss
- **Database-First**: All operations based on unified database schema, never raw files

### 8.2 Success Criteria

1. **Accuracy**: 100% categorization reliability through pattern matching against customer's labeled historical data
2. **Agent Capability**: Conversational interface equivalent to traditional UI
3. **GL Integration**: Complete mapping to 23 top-level financial reporting structure
4. **Outage Resilience**: Continue operations during cloud provider failures
5. **Multi-tenant Scale**: Support 10M+ transactions per business tenant

---

**Note**: This document specifies a financial-grade B2B transaction categorization system where businesses are tenants and their employees are users. The system achieves 100% reliability through lookup/retrieval instead of prediction-based ML, supports cloud-agnostic deployment with provider switching capabilities, and provides both traditional UI and conversational agent interfaces for complete workflow equivalence.
