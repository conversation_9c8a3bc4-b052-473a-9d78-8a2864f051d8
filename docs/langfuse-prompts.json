[{"name": "schema_interpretation_main", "prompt": "You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.\n\nFile Name: {file_name}\nColumn Headers: {file_headers}\nSample Data (first 5 rows):\n{sample_data}\n\nREQUIRED TRANSACTION SCHEMA FIELDS:\n- date: Transaction date\n- description: Transaction description/memo/particulars\n- amount: Transaction amount (single amount or net amount)\n\nOPTIONAL TRANSACTION SCHEMA FIELDS:\n- debit_amount: Debit amount (if separate debit/credit columns)\n- credit_amount: Credit amount (if separate debit/credit columns)\n- balance: Account balance\n- account: Account identifier\n- transaction_type: Transaction type (debit/credit indicator)\n- reference_number: Transaction reference/ID\n- currency: Transaction currency\n- category: Customer-assigned category (if present)\n- vendor: Vendor/merchant name\n\nANALYSIS INSTRUCTIONS:\n1. Analyze each column header and sample data intelligently\n2. Look for semantic meaning, not just exact matches\n3. Consider common variations (e.g., \"Trans Date\" = \"date\", \"Memo\" = \"description\")\n4. For amount columns, check if they contain parentheses or negative values\n5. Identify if the file uses separate debit/credit columns or a single amount column\n6. Provide confidence scores (0.0-1.0) for each mapping\n7. Include reasoning for each mapping decision\n\nSPECIAL CONSIDERATIONS:\n- Indian formats: Look for \"Cr\" and \"Dr\" indicators\n- US formats: Look for parentheses for negative amounts\n- European formats: Check for comma as decimal separator\n- If you see \"Withdrawal Amt.\" and \"Deposit Amt.\", these are debit/credit columns\n\nReturn JSON format:\n{{\n    \"file_type_identified\": \"Bank Statement|Credit Card Statement|Transaction Export\",\n    \"column_mappings\": [\n        {{\n            \"original_name\": \"column name from file\",\n            \"mapped_field\": \"standard field name\",\n            \"confidence\": 0.95,\n            \"reasoning\": \"Clear explanation of why this mapping was chosen\"\n        }}\n    ],\n    \"overall_confidence\": 0.92,\n    \"debit_credit_inference\": {{\n        \"structure\": \"dual_columns|single_column\",\n        \"debit_column\": \"column name if dual\",\n        \"credit_column\": \"column name if dual\",\n        \"sign_convention\": \"negative_is_debit|negative_is_credit|parentheses_is_negative\"\n    }},\n    \"summary\": \"Brief analysis summary\"\n}}\n\nJSON:", "version": "1.2.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.1, "max_output_tokens": 2000, "top_p": 0.8}, "labels": ["schema_interpretation"], "tags": []}, {"name": "schema_interpretation_enhanced", "prompt": "You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.\n\nFile Name: {file_name}\nColumn Headers: {file_headers}\nSample Data (first 20 rows):\n{sample_data}\n\nColumn Statistics:\n{column_stats}\n\nPrevious Successful Patterns for this Customer:\n{previous_patterns}\n\nREQUIRED TRANSACTION SCHEMA FIELDS:\n- date: Transaction date\n- description: Transaction description/memo/particulars\n- amount: Transaction amount (single amount or net amount)\n\nOPTIONAL TRANSACTION SCHEMA FIELDS:\n- debit_amount: Debit amount (if separate debit/credit columns)\n- credit_amount: Credit amount (if separate debit/credit columns)\n- balance: Account balance\n- account: Account identifier\n- transaction_type: Transaction type (debit/credit indicator)\n- reference_number: Transaction reference/ID\n- currency: Transaction currency\n- category: Customer-assigned category (if present)\n- vendor: Vendor/merchant name\n\nENHANCED ANALYSIS INSTRUCTIONS:\n1. Use column statistics to validate your mappings:\n   - Date columns should have date-like patterns\n   - Amount columns should be mostly numeric\n   - Description columns should have high unique value ratios\n2. Learn from previous successful patterns if available\n3. Pay special attention to columns that were successfully mapped before\n4. For amount detection, use these enhanced rules:\n   - Check \"all_positive\", \"all_negative\", or \"mixed_signs\" statistics\n   - If two columns are all positive, they're likely debit/credit columns\n   - If one column has mixed signs, it's likely a single amount column\n5. Validate your confidence based on pattern matching in statistics\n\nReturn JSON format:\n{{\n    \"file_type_identified\": \"Bank Statement|Credit Card Statement|Transaction Export\",\n    \"column_mappings\": [\n        {{\n            \"original_name\": \"column name from file\",\n            \"mapped_field\": \"standard field name\",\n            \"confidence\": 0.95,\n            \"reasoning\": \"Explanation including statistical validation\"\n        }}\n    ],\n    \"overall_confidence\": 0.92,\n    \"debit_credit_inference\": {{\n        \"structure\": \"dual_columns|single_column\",\n        \"debit_column\": \"column name if dual\",\n        \"credit_column\": \"column name if dual\",\n        \"sign_convention\": \"negative_is_debit|negative_is_credit|parentheses_is_negative\",\n        \"statistical_validation\": \"explanation of how stats support this\"\n    }},\n    \"learned_patterns_applied\": [\"list of patterns used from history\"],\n    \"summary\": \"Brief analysis summary with confidence factors\"\n}}\n\nJSON:", "version": "2.0.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.1, "max_output_tokens": 2500, "top_p": 0.8}, "labels": ["schema_interpretation"], "tags": []}, {"name": "debit_credit_inference", "prompt": "You are an expert in accounting principles and banking conventions.\nAnalyze these transaction samples to determine debit/credit direction.\n\nColumn Mappings: {column_mappings}\nTransaction Samples (first 10):\n{transaction_samples}\n\nANALYSIS REQUIREMENTS:\n1. Identify if transactions have separate debit/credit columns\n2. If single amount column, determine sign convention (negative for debits or credits)\n3. Look for transaction type indicators (DR/CR, Debit/Credit, Withdrawal/Deposit)\n4. Consider regional banking conventions\n5. Analyze description patterns for transaction direction clues\n\nACCOUNTING PRINCIPLES:\n- Debits: Expenses, withdrawals, payments out, purchases\n- Credits: Income, deposits, payments in, refunds, reversals\n- Sign conventions vary by bank and region\n\nREGIONAL VARIATIONS:\n- US: Often uses parentheses for negative values\n- India: May use \"Dr\" and \"Cr\" suffixes\n- Europe: May use different decimal separators\n\nReturn JSON format:\n{{\n    \"debit_credit_structure\": \"separate_columns|single_column_signed|single_column_with_type\",\n    \"sign_convention\": \"negative_is_debit|negative_is_credit|absolute_with_type\",\n    \"debit_indicators\": [\"list of terms/patterns indicating debits\"],\n    \"credit_indicators\": [\"list of terms/patterns indicating credits\"],\n    \"confidence\": 0.95,\n    \"reasoning\": \"Detailed explanation of detected pattern\",\n    \"examples\": [\n        {{\n            \"description\": \"sample transaction\",\n            \"classification\": \"debit|credit\",\n            \"evidence\": \"what indicated this classification\"\n        }}\n    ],\n    \"recommendations\": {{\n        \"amount_handling\": \"use_signed_values|split_by_type|use_absolute_with_indicator\",\n        \"type_detection\": \"column_based|description_based|sign_based\"\n    }}\n}}\n\nJSON:", "version": "1.1.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.1, "max_output_tokens": 1500, "top_p": 0.8}, "labels": ["debit_credit_inference"], "tags": []}, {"name": "regional_detection", "prompt": "You are an expert in global banking formats and regional variations.\nAnalyze this financial data to detect and handle regional conventions.\n\nFile Data Sample: {file_data}\nDetected Region: {detected_region}\n\nANALYSIS REQUIREMENTS:\n1. Detect date format (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, DD-MMM-YYYY, etc.)\n2. Identify currency symbols and formatting\n3. Recognize regional transaction description patterns\n4. Identify regional banking terminology\n5. Detect decimal/thousand separators (1,234.56 vs 1.234,56 vs 1 234.56)\n\nREGIONAL PATTERNS TO DETECT:\n- US: MM/DD/YYYY dates, $ currency, decimal point, comma thousands\n- Europe: DD/MM/YYYY dates, € currency, comma decimal, period thousands\n- India: DD-MM-YYYY dates, ₹ currency, lakhs/crores notation\n- UK: DD/MM/YYYY dates, £ currency, varied formats\n- International: ISO dates (YYYY-MM-DD), varied currencies\n\nTERMINOLOGY PATTERNS:\n- US: \"Check #\", \"ATM Withdrawal\", \"Direct Deposit\"\n- India: \"NEFT\", \"IMPS\", \"UPI\", \"Cheque\"\n- Europe: \"SEPA\", \"IBAN\", \"Standing Order\"\n- UK: \"BACS\", \"CHAPS\", \"Standing Order\"\n\nReturn JSON format:\n{{\n    \"detected_region\": \"US|Europe|India|UK|International|Other\",\n    \"confidence\": 0.95,\n    \"date_format\": \"MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD|DD-MM-YYYY|DD-MMM-YYYY\",\n    \"date_examples\": [\"examples from data\"],\n    \"currency\": {{\n        \"symbol\": \"$|€|₹|£|Other\",\n        \"position\": \"prefix|suffix\",\n        \"decimal_separator\": \".|,\",\n        \"thousand_separator\": \",|.|space|none\"\n    }},\n    \"regional_terms\": {{\n        \"payment_methods\": [\"detected payment method terms\"],\n        \"transaction_types\": [\"detected transaction type terms\"],\n        \"banking_terms\": [\"region-specific banking terms found\"]\n    }},\n    \"parsing_instructions\": {{\n        \"date_parser\": \"strptime format string\",\n        \"amount_parser\": {{\n            \"regex\": \"pattern to extract amounts\",\n            \"conversion\": \"steps to convert to float\"\n        }},\n        \"description_cleaner\": [\"list of cleaning steps needed\"]\n    }},\n    \"validation_rules\": {{\n        \"date_range\": \"expected date range for transactions\",\n        \"amount_range\": \"typical amount ranges\",\n        \"common_merchants\": [\"frequently seen merchant patterns\"]\n    }}\n}}\n\nJSON:", "version": "1.2.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.1, "max_output_tokens": 2000, "top_p": 0.8}, "labels": ["regional_detection"], "tags": []}, {"name": "hierarchy_detection", "prompt": "You are an expert at analyzing financial categorization patterns and detecting hierarchical structures.\n\nCategory Samples: {category_samples}\n\nAnalyze these categories and determine:\n1. Are they hierarchical (contain parent > child relationships)?\n2. What separator is used (if any)?\n3. What type of categorization pattern do they follow?\n4. How many levels deep is the hierarchy?\n\nCOMMON HIERARCHY PATTERNS:\n- Separator-based: \"Food > Restaurants > Fast Food\"\n- Nested: \"Food:Dining:Restaurants\"\n- Path-based: \"Expenses/Food/Restaurants\"\n- Code-based: \"***********\" (account codes)\n- Mixed: \"EXPENSE-Food-Restaurant\"\n\nANALYSIS FOCUS:\n- Consistency of separators across samples\n- Number of hierarchy levels\n- Parent-child relationship logic\n- Any accompanying codes or IDs\n\nReturn JSON format:\n{{\n    \"is_hierarchical\": true|false,\n    \"hierarchy_details\": {{\n        \"separator\": \">|:|/|-|_|.\",\n        \"max_depth\": 3,\n        \"consistent_depth\": true|false,\n        \"has_codes\": true|false\n    }},\n    \"pattern_type\": \"hierarchical|flat|mixed|coded\",\n    \"examples\": [\n        {{\n            \"original\": \"sample category\",\n            \"parsed_levels\": [\"Level1\", \"Level2\", \"Level3\"],\n            \"depth\": 3\n        }}\n    ],\n    \"confidence\": 0.95,\n    \"reasoning\": \"Detailed explanation of detected pattern\",\n    \"recommendations\": {{\n        \"storage_method\": \"hierarchical_tree|flat_with_path|separate_levels\",\n        \"query_strategy\": \"exact_match|prefix_match|level_based\"\n    }}\n}}\n\nJSON:", "version": "1.0.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.1, "max_output_tokens": 1000, "top_p": 0.8}, "labels": ["hierarchy_detection"], "tags": []}, {"name": "correction_suggestion", "prompt": "You are an expert at helping users correct schema mapping issues.\nGenerate helpful suggestions to fix the mapping problems.\n\nOriginal Mappings: {original_mappings}\nValidation Errors: {validation_errors}\nUser Feedback: {user_feedback}\nAvailable Columns: {available_columns}\n\nSUGGESTION REQUIREMENTS:\n1. Provide clear, actionable correction suggestions\n2. Explain why each correction is needed\n3. Offer alternative mapping options\n4. Prioritize user-friendly explanations\n5. Include examples where helpful\n6. Consider the user's feedback if provided\n\nCOMMON MAPPING ISSUES:\n- Missing required fields (date, description, amount)\n- Incorrect field types (mapping text column to amount)\n- Ambiguous column names needing clarification\n- Regional format mismatches\n- Debit/credit confusion\n\nReturn JSON format:\n{{\n    \"correction_suggestions\": [\n        {{\n            \"issue\": \"Specific problem identified\",\n            \"severity\": \"critical|warning|info\",\n            \"suggestion\": \"Clear action to fix the issue\",\n            \"explanation\": \"Why this correction is needed\",\n            \"alternatives\": [\"Other possible solutions\"],\n            \"example\": \"Example of correct mapping\",\n            \"confidence\": 0.9\n        }}\n    ],\n    \"quick_fixes\": [\n        {{\n            \"field\": \"unmapped field name\",\n            \"recommended_column\": \"best matching column\",\n            \"confidence\": 0.9,\n            \"reasoning\": \"why this match is recommended\"\n        }}\n    ],\n    \"general_advice\": \"Overall tips for successful mapping\",\n    \"next_steps\": [\"Ordered list of actions to resolve all issues\"]\n}}\n\nJSON:", "version": "1.1.0", "config": {"model": "gemini-2.0-flash-001", "temperature": 0.3, "max_output_tokens": 2000, "top_p": 0.9}, "labels": ["correction_suggestion"], "tags": []}]