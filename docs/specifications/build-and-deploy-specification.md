# Build and Deploy Specification

---
status: active
last-updated: 2025-06-25
update-frequency: monthly
update-triggers: CI/CD changes, deployment updates, infrastructure modifications
---

## Overview

This document specifies the build and deployment architecture for the Giki AI workspace.

## Build Architecture

### Frontend Build
- **Tool**: Vite + pnpm
- **Output**: Static files in `dist/apps/giki-ai-app/`
- **Production Build**: `pnpm build:app` or `pnpm nx build giki-ai-app --configuration=production`
- **Containerization**: Multi-stage Dockerfile with nginx

### Backend Build  
- **Tool**: uv + hatchling
- **Output**: Python wheel package
- **Production Build**: `uv build` in app directory
- **Containerization**: Multi-stage Dockerfile with uvicorn

## Deployment Architecture

### Primary: Google Cloud Run
- **Registry**: Google Artifact Registry (`us-central1-docker.pkg.dev/rezolve-poc/giki-ai/`)
- **Services**:
  - API: `giki-ai-api` 
  - Frontend: `giki-ai-frontend`
- **Deployment Trigger**: Push to master with "deploy:" prefix or manual workflow dispatch

### CI/CD Pipeline

1. **Check Deploy Trigger**: Verify if deployment should proceed
2. **Build and Push Images**: 
   - Build Docker images using production Dockerfiles
   - Push to Google Artifact Registry
3. **Deploy to Cloud Run**:
   - Deploy pre-built images
   - Configure environment variables
   - Set resource limits
4. **Post-Deploy Verification**:
   - Health check endpoints
   - Service accessibility tests

### Environment Variables

#### API Service
- `ENVIRONMENT`: production
- `DATABASE_URL`: From secrets (Cloud SQL connection string)
- `AUTH_SECRET_KEY`: From secrets
- `SECRET_KEY`: From secrets
- `ADMIN_API_KEY`: From secrets
- `GCP_PROJECT_ID`: rezolve-poc
- `VERTEX_AI_LOCATION`: us-central1
- **Redis Configuration (Production)**:
  - `REDIS_ENABLED`: true
  - `REDIS_HOST`: ************ (Google Memorystore internal IP)
  - `REDIS_PORT`: 6379
  - `REDIS_URL`: redis://************:6379

#### Frontend Service
- `VITE_API_BASE_URL`: Dynamic from API deployment
- `VITE_ENVIRONMENT`: production

### Docker Configuration

#### Backend Dockerfile.production
- Base: python:3.12-slim
- Package Manager: uv
- Build: Multi-stage with dependency caching
- Runtime: uvicorn with 4 workers
- User: Non-root (appuser)
- Health Check: /health endpoint

#### Frontend Dockerfile.production  
- Base: node:20-alpine (build), nginx:alpine (runtime)
- Package Manager: pnpm
- Build: Multi-stage with production optimizations
- Runtime: nginx with dynamic PORT configuration
- User: Non-root (appuser)
- Health Check: /health endpoint

### Development Commands

```bash
# Development
pnpm dev         # Start both servers
pnpm dev:api     # Start backend only
pnpm dev:app     # Start frontend only

# Building
pnpm build       # Build all projects
pnpm build:api   # Build backend only
pnpm build:app   # Build frontend only

# Testing
pnpm nx test     # Run all tests
pnpm nx test:api # Backend tests only
pnpm nx test:app # Frontend tests only

# Linting
pnpm lint        # Lint all code
pnpm lint:api    # Backend linting only
pnpm lint:app    # Frontend linting only

# Deployment (includes real-time logs)
pnpm deploy      # Deploy everything
pnpm deploy:api  # Deploy backend only
pnpm deploy:app  # Deploy frontend only
```

### Required GitHub Secrets
- `GCP_SERVICE_ACCOUNT`: Service account JSON for GCP authentication
- `CLOUD_RUN_SERVICE_ACCOUNT`: Service account email for Cloud Run
- `DATABASE_URL`: PostgreSQL connection string
- `AUTH_SECRET_KEY`: Authentication secret key
- `SECRET_KEY`: Application secret key
- `ADMIN_API_KEY`: Admin API key

## Service Accounts

### 1. API Service Account (Cloud Run)
- **Email**: `<EMAIL>`
- **Purpose**: Running the API on Google Cloud Run
- **Permissions**:
  - `roles/aiplatform.user` - For Vertex AI access
  - `roles/cloudsql.client` - For Cloud SQL connections
  - `roles/cloudsql.editor` - For Cloud SQL management
  - `roles/cloudsql.instanceUser` - For Cloud SQL instance access
  - `roles/secretmanager.secretAccessor` - For accessing secrets
  - `roles/storage.objectUser` - For Cloud Storage access
  - `roles/run.admin` - For Cloud Run management
- **Used By**: Cloud Run API service

### 2. Firebase Admin Service Account
- **Email**: `<EMAIL>`
- **Key Location**: `service-accounts/firebase-admin-key.json`
- **Purpose**: Deploying frontend to Firebase Hosting
- **Permissions**:
  - `roles/firebase.sdkAdminServiceAccount` - Firebase admin access
  - Firebase Hosting deployment permissions
- **Used By**: Firebase Hosting deployments

### 3. GitHub Actions Service Account
- **Purpose**: CI/CD pipeline deployments
- **Stored As**: GitHub Secret `GCP_SA_KEY`
- **Permissions**: Combined permissions for both API and frontend deployment

## Security Best Practices

1. **Never commit service account keys** - All keys are in `.gitignore`
2. **Rotate keys regularly** - At least every 90 days
3. **Use least privilege** - Only grant necessary permissions
4. **Monitor usage** - Check Cloud Console for unusual activity

## Deployment Troubleshooting

### Permission Denied Errors
1. Check service account has necessary roles
2. Verify key file exists and is valid
3. Ensure GOOGLE_APPLICATION_CREDENTIALS points to correct file

### Deployment Failures
1. Run deployment with `--dry-run` flag first
2. Check service account permissions in Cloud Console
3. Verify all secrets exist in Secret Manager

### Verification

Run `scripts/verify-build.sh` to verify the build setup is correctly configured.

## Performance Targets
- API response time: <200ms
- Frontend load time: <2 seconds  
- Container startup: <30 seconds
- Health check response: <1 second