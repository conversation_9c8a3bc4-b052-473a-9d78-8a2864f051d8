# Financial Frontend Implementation Specification with Agent Equivalence

---
status: active
last-updated: 2025-06-25
update-frequency: weekly
update-triggers: component changes, UI updates, agent parity changes
---

**Version**: 5.0  
**Status**: Active  
**Date**: January 2025

## Overview

This document maps UI components to agent capabilities, ensuring complete workflow parity between traditional UI and conversational interface. For duplicate component prevention and agent-UI equivalence requirements.

> **Architecture Details**: @docs/specifications/system-design-spec.md

## Customer Journey Summary

### Part A: Onboarding (One-time)
1. **Data Ingestion**: Upload historical data WITH categories
2. **Knowledge Base**: Build from patterns, map GL codes
3. **Validation**: Monthly accuracy testing (Jul-Dec 2024)
4. **Production**: >85% accuracy requirement

### Part B: Production Usage
- **Daily**: Upload new transactions WITHOUT categories → AI categorizes → Review
- **Weekly**: Financial review, spending analysis, reports
- **Monthly**: Financial statements, GL reports, accounting exports
- **Agent**: Natural language for ANY workflow
- **Categories**: Manage hierarchy, assign GL codes
- **Analytics**: Custom reports, pivot tables, forecasting

## Anti-Duplication Rules

**NEVER Create**: Custom UI primitives (buttons, inputs, tables, modals, loading, layouts, date pickers, charts)  
**ALWAYS Reuse**: shadcn/ui components, shared layouts, form controls, Recharts, existing financial components

## Unified Agent Interface ("Giki")

### Core Principle: Single Agent with 100% UI Equivalence

| **UI Workflow** | **Agent Command** | **Implementation** |
|---|---|---|
| File Upload | "Upload my Excel file" | Chat file upload |
| Reports | "Generate spending report" | Downloadable reports |
| Data View | "Show transactions" | Tables/charts in chat |
| Navigation | "Take me to [page]" | UI navigation |
| Forms | Natural language | Same as form data |
| Workflows | Conversational | Multi-step guidance |

### Implementation Rules
- ONE agent identity ("Giki") - no agent selection
- Hidden backend complexity - seamless handoffs
- All capabilities appear native to single agent

## Related Specifications
- **Architecture**: @docs/specifications/system-design-spec.md
- **Agents**: @docs/specifications/agents-and-tools-spec.md
- **API**: @docs/specifications/contracts-spec.md

## UI/UX Design System

### Design Principles
- **Modern Financial Software**: Tailwind CSS + shadcn/ui
- **Spreadsheet Power**: Live editing, filtering, calculations
- **Dual-Mode Parity**: Every workflow via UI OR agent
- **Complete Interactivity**: No static elements

### Authentication
- `/login`: Email/password form
- `/register`: Multi-tenant registration
- Protected routes with token validation

## Page → Component Mapping

### Core Layouts

| **Layout** | **Location** | **Structure** |
|---|---|---|
| **AppLayout** | `/shared/components/layout/AppLayout.tsx` | ThemeToggle + LeftNav + MainContent + UnifiedAgentInterface |
| **AuthLayout** | `/shared/components/layout/AuthLayout.tsx` | Logo + ThemeToggle + Card (centered) |

### Navigation Routes
- `/dashboard` - Financial overview
- `/upload` - Data upload (no categories)
- `/transactions` - Transaction management
- `/categories` - GL code management
- `/reports` - Financial reports
- `/onboarding` - One-time setup
- `/knowledge-hub` - RAG corpus
- `/settings` - Configuration

### Dual-Mode Capabilities
**Agent Mode**: File upload, reports, data viz, navigation via "Giki" chat  
**UI Mode**: Direct interaction with all components  
**Requirement**: Every component must be interactive - no static elements

### Authentication Pages
- **/login**: LoginForm (email/password)
- **/register**: RegistrationForm (multi-tenant)
- **Shared**: shadcn/ui components (Button, Input, Card, Alert, Label, Link)

### Dashboard Page (`/dashboard`)
**Key Components**: FinancialMetricsGrid, CategoryBreakdownChart, AccuracyValidationChart, RecentTransactionsList, QuickActionsGrid
**Agent Equivalent**: "Show my metrics", "What's my spending?", "Show recent transactions"
**Layout**: 2-column responsive grid with Recharts

### Upload Page (`/upload`) - Production Use
**Purpose**: Process NEW transactions WITHOUT categories (after onboarding)
**Key Components**: FinancialFileUpload, SchemaInterpretationPanel, AICategorizationPreview, ProcessingStatusTracker
**Agent Equivalent**: "Upload my transactions", "Show column mapping", "Show processing status"
**Key Rule**: Production files must NOT have category columns

### Transaction Pages
- **/transaction-analysis**: TransactionTable, EntityExtractionDisplay, AccuracyMetricsCard (analysis & insights)
- **/transactions**: TransactionTable, BulkOperationsPanel, CategoryAssignmentPanel (review & categorize)
**Agent Commands**: "Show transactions for [period]", "Analyze spending patterns", "Bulk categorize [type]"

### Category Management (`/categories`)
**Key Components**: CategoryManagementView, GLCodeBulkManager, BatchCategorizationPanel, GLCodeAnalyticsDashboard
**Agent Commands**: "Create category [X]", "Export chart of accounts", "Bulk categorize uncategorized"
**Features**: GL code mapping, QuickBooks/SAP/Xero export, 23 top-level structure

### Reports Page (`/reports`)
**Report Types**: Spending, Statements, GL Reports, Accuracy, Custom
**Key Components**: SpendingByCategoryReport, FinancialStatementGenerator, GLCodeReportSuite, AccuracyValidationReports, CustomReportBuilder
**Agent Commands**: "Generate spending report", "Generate financial statements", "Show accuracy by month"
**Export Formats**: Excel, QuickBooks, SAP, Xero

### Intelligence Pages
- **/knowledge-hub**: EntityTable, EntityDetailSheet, MerchantIntelligencePanel (entity & vendor analysis)
- **/rag-corpus-management**: RagCorpusManagement, CorpusAccuracyValidation (knowledge base management)
**Features**: Entity extraction, pattern analysis, RAG corpus validation, 100% lookup accuracy

### Admin Pages
- **/settings**: TenantConfigurationPanel, GLCodeSystemSettings, AccountingSoftwareIntegration (system configuration)
- **/onboarding**: OnboardingWorkflowStepper, TemporalAccuracyValidation, AccuracySimulationChart (one-time setup)
**Features**: Multi-tenant, GL codes, QuickBooks/SAP/Xero, DPDPA compliance, >85% accuracy validation

## Shared Component Library (shadcn/ui)

### Core Components
| **Category** | **Components** |
|---|---|
| **Layout** | Card, Tabs, Sheet, Dialog, Popover, ScrollArea |
| **Forms** | Input, Textarea, Select, Checkbox, Switch, Label, DateRangePicker |
| **Data** | Table, Badge, Alert, Loading, Progress |
| **Charts** | BarChart, PieChart, LineChart, AreaChart (Recharts) |
| **Buttons** | default, secondary, outline, ghost, destructive |

### Component Rules
**Create New**: Business logic (TransactionTable), Feature containers (UploadPage), Complex compositions  
**Never Create**: UI primitives, layout wrappers, simple displays - use shadcn/ui

## Feature Module Summary

| **Module** | **Key Components** | **Status** |
|---|---|---|
| **File Upload** | DataUpload, ColumnMappingModal, FileUploadWithCurrency | ✅ Complete |
| **Transactions** | TransactionTable, CategorySelector, BulkOperations | ✅ Complete |
| **Categories** | CategoryManagementView, GLCodeManager | ✅ GL codes implemented |
| **Reports** | CustomReportBuilder, SpendingReports, PivotTable | ✅ Custom builder done |
| **Agent** | AgentPanel, VoiceInput, FileAttachments | ✅ Single "Giki" agent |

## UI/UX Patterns

### Colors
- Primary: #8B5CF6 (Purple)
- Secondary: #10B981 (Green)
- Danger: #EF4444, Warning: #F59E0B

### Responsive
- Breakpoints: 640/768/1024/1280px
- Mobile-first, touch-friendly

## Technical Patterns

### State Management
- **Auth**: User, token, login/logout
- **Data**: Custom hooks with loading/error
- **Global**: Theme, navigation, notifications

### Performance
- Code splitting by route
- API caching (5min)
- React.memo, useMemo, virtual scrolling

### Quality
- Error boundaries
- WCAG 2.1 compliance
- 80% test coverage target
- E2E with Playwright

## Component Decision Tree

1. **Basic UI?** → Use shadcn/ui
2. **Layout?** → Use AppLayout/AuthLayout
3. **Data display?** → Use Table/Badge/Chart
4. **Business logic?** → Create in /features/
5. **Shared?** → Place in /shared/

### Naming Convention
`[Feature][Action][Type]`: TransactionTable, CategoryManagementView, CustomReportBuilder

### File Structure
```
features/[feature]/
  ├── components/
  ├── pages/
  └── services/
shared/
  ├── components/
  ├── services/
  └── types/
```

## Implementation Status

### Complete ✅
- Authentication (login/register)
- Dashboard (metrics, charts)
- Upload (AI schema interpretation)
- Categories (GL code CRUD)
- Reports (custom builder)
- Accuracy validation (86.2% avg)

### Partial 🟡
- Transactions (needs filtering)
- Intelligence (entity management)
- Admin (basic settings)

### Missing ❌
- Onboarding wizard
- Predictive analytics
- Mobile optimization

## Quality Checklist

**Before Creating Components**:
- Check shadcn/ui first
- Search existing components
- Verify business need
- Follow naming conventions

**Development Standards**:
- TypeScript strict mode
- Error boundaries
- Loading/empty states
- Accessibility (WCAG 2.1)
- Responsive design

**Component Naming**:
- Functional names (ChatInterface not AgentPanel)
- Purpose-driven (UserProfileEditor not UserPanel)
- Consistent patterns (*Tool, *Interface, *Modal)

---

**Version**: 5.0
**Last Updated**: 2025-06-25
**Status**: Active