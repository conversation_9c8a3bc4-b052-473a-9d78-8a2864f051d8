# Financial-Grade API Contracts Specification

---
status: active
last-updated: 2025-06-25
update-frequency: weekly
update-triggers: API changes, endpoint updates, contract modifications
---

**Version**: 3.0  
**Status**: Active  
**Date**: January 2025

## Overview

This document defines all API contracts for the Giki AI financial-grade B2B transaction categorization system, including REST endpoints, A2A protocol agent interfaces, request/response formats, authentication requirements, and error handling. These contracts enforce 100% financial reliability through lookup-based operations and support both traditional web UI and conversational agent interfaces with complete equivalence.

### Financial-Grade Requirements
- **100% Accuracy**: All categorization endpoints match patterns learned from customer's labeled historical data
- **Database-First**: All operations from unified transaction schema (never raw files)
- **Agent Equivalence**: Complete API coverage for both UI and agent interfaces
- **Multi-Tenant Security**: Complete data isolation with tenant-aware authentication
- **Cloud-Agnostic**: Provider-independent deployment with switching capabilities
- **GL Code Integration**: Full support for 23 top-level financial reporting structure

## Related Specifications
- **Financial System Architecture**: @docs/specifications/system-design-spec.md
- **Agent Implementation & ADK Patterns**: @docs/specifications/agents-and-tools-spec.md
- **Frontend UI & Agent Equivalence**: @docs/specifications/frontend-spec.md
- **Financial Requirements**: @docs/requirements/requirements.md

## API Configuration

### REST API Configuration
- **Base URL**: `http://localhost:8000/api/v1`
- **Format**: JSON
- **Authentication**: Bearer token (JWT with tenant context)
- **Versioning**: URL path (`/api/v1/`)
- **Performance Target**: <2s (non-AI endpoints, development-friendly)
- **Financial Accuracy**: >90% (lookup-based operations)

### CORS Configuration
- **Flexible Origins**: Environment variable-based configuration
- **Default Origins**: 
  - `http://localhost:4200` (Development frontend)
  - `http://localhost:3000` (Alternative development port)
  - `https://giki-ai-frontend-273348121056.us-central1.run.app` (Production frontend)
- **Methods**: GET, POST, PUT, DELETE, OPTIONS
- **Headers**: Authorization, Content-Type
- **Credentials**: Enabled for authenticated requests
- **Configuration**: Via `CORS_ALLOWED_ORIGINS` environment variable (comma-separated)

### A2A Agent Protocol Configuration (JSON-RPC 2.0)
- **Base URL**: `http://localhost:8000/api/v1/agents`
- **Protocol**: A2A v0.2.2 compliant JSON-RPC 2.0 over HTTP(S)
- **Format**: JSON-RPC 2.0 with Agent Cards for discovery
- **Authentication**: Bearer token with agent context
- **Reliability**: 100% financial-grade tool operations
- **Model Support**: Anthropic Claude, OpenAI GPT, Google Gemini, custom models

## Common Headers

### REST API Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
X-Tenant-ID: <tenant_uuid>
X-Financial-Grade: true
```

### A2A Agent Protocol Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
X-Agent-Protocol: A2A-v0.2.2
X-Agent-ID: <agent_uuid>
X-Financial-Reliability: 100-percent
```

## Common Response Format

### REST API Response Format
```json
{
  "status": "success|error",
  "data": {...},
  "message": "Human readable message",
  "errors": [...], // Only on validation errors
  "financial_metadata": {
    "accuracy_level": "100_percent",
    "lookup_source": "database|rag_corpus",
    "gl_code_validated": true,
    "tenant_isolated": true
  },
  "performance": {
    "response_time_ms": 150,
    "database_queries": 3,
    "cache_hit": true
  }
}
```

### A2A Agent Protocol Response Format (JSON-RPC 2.0)
```json
{
  "jsonrpc": "2.0",
  "id": "request_id",
  "result": {
    "success": true,
    "data": {...},
    "agent_metadata": {
      "tool_reliability": "100_percent",
      "financial_accuracy": "lookup_based",
      "ui_equivalent": true
    }
  }
}
```

## Financial Authentication Endpoints

### POST /auth/login
Multi-tenant financial authentication with <2s development target

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "financial_context": {
    "require_gl_access": true,
    "require_accuracy_validation": true
  }
}
```

**Response:**
```json
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user",
    "tenant_id": 1,
    "tenant_uuid": "550e8400-e29b-41d4-a716-************",
    "permissions": {
      "gl_code_management": true,
      "categorization_validation": true,
      "agent_interface_access": true,
      "financial_reporting": true
    }
  },
  "financial_capabilities": {
    "accuracy_requirement": "100_percent",
    "lookup_access": true,
    "agent_equivalence": true,
    "gl_code_access": true,
    "multi_currency": ["USD", "EUR", "INR"]
  },
  "performance": {
    "auth_time_ms": 145,
    "tenant_validation_ms": 23
  }
}
```

### POST /auth/register
Create new B2B tenant with financial-grade configuration

**Request:**
```json
{
  "email": "<EMAIL>",
  "username": "admin", 
  "password": "SecurePass123!",
  "tenant_configuration": {
    "tenant_name": "New Financial Company",
    "primary_currency": "USD",
    "accounting_system": "quickbooks",
    "gl_code_structure": "23_toplevel_standard",
    "accuracy_requirement": "100_percent",
    "compliance_region": "india_dpdpa"
  },
  "financial_setup": {
    "fiscal_year_start": "2024-04-01",
    "chart_of_accounts_template": "standard_business",
    "multi_currency_enabled": true,
    "agent_interface_enabled": true
  }
}
```

**Response:**
```json
{
  "message": "B2B financial tenant created successfully",
  "user_id": 1,
  "tenant_id": 1,
  "tenant_uuid": "550e8400-e29b-41d4-a716-************",
  "onboarding_required": true,
  "next_steps": [
    "Upload historical transaction data",
    "Complete temporal accuracy validation",
    "Configure GL code mappings",
    "Set up agent interface preferences"
  ],
  "financial_setup": {
    "gl_codes_initialized": true,
    "accuracy_validation_pending": true,
    "database_schema_ready": true,
    "agent_tools_available": true
  }
}
```

### GET /auth/me
Get current user and financial tenant context (from JWT token)

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "user",
  "tenant_id": 1,
  "tenant_uuid": "550e8400-e29b-41d4-a716-************",
  "tenant_name": "Giki Financial Services",
  "is_active": true,
  "financial_profile": {
    "accuracy_level_achieved": "100_percent",
    "onboarding_completed": true,
    "production_approved": true,
    "gl_codes_configured": true,
    "agent_interface_enabled": true,
    "temporal_validation_passed": true
  },
  "permissions": {
    "transaction_management": true,
    "category_management": true,
    "gl_code_management": true,
    "financial_reporting": true,
    "agent_interface": true,
    "accuracy_validation": true
  },
  "tenant_capabilities": {
    "supported_currencies": ["USD", "EUR", "INR"],
    "accounting_integrations": ["quickbooks", "xero"],
    "max_transactions": ********,
    "rag_corpus_enabled": true,
    "lookup_categorization": true
  }
}
```

## Financial File Management Endpoints (Database-First Architecture)

### POST /uploads/ (PRODUCTION USE - Phase 2)
Upload NEW transaction files WITHOUT categories for processing and reporting

**Purpose**: Regular monthly/periodic uploads after onboarding is complete
**Critical**: Files must NOT contain category columns - AI will categorize

**Request:**
```http
POST /uploads/
Content-Type: multipart/form-data

file: <binary data>
file_type: transactions
currency: USD
accounting_period: 2024-Q1
expected_format: bank_export
debit_credit_detection: auto
upload_type: production  # Validates NO category columns present
```

**Response:**
```json
{
  "file_id": "550e8400-e29b-41d4-a716-************",
  "status": "schema_analysis_complete",
  "message": "File uploaded and schema interpreted successfully",
  "file_metadata": {
    "original_filename": "Bank_Export_Q1_2024.xlsx",
    "file_size_bytes": 2048000,
    "rows_detected": 1247,
    "upload_time": "2024-01-15T10:30:00Z"
  },
  "schema_interpretation": {
    "columns_detected": {
      "date": "Transaction Date",
      "amount": "Amount",
      "description": "Description",
      "debit_credit_indicator": "Type",
      "balance": "Running Balance",
      "reference_number": "Ref #"
    },
    "financial_metadata": {
      "date_format": "MM/DD/YYYY",
      "currency": "USD",
      "decimal_separator": ".",
      "thousands_separator": ",",
      "debit_credit_method": "amount_sign_based",
      "accounting_convention": "credit_positive"
    },
    "ai_analysis": {
      "confidence": 0.97,
      "schema_type": "standard_bank_export",
      "reasoning": "Standard banking export format detected with clear debit/credit indicators and running balance",
      "debit_credit_inference": {
        "method": "amount_sign_analysis",
        "confidence": 0.95,
        "patterns_detected": [
          "Negative amounts indicate debits (expenses)",
          "Positive amounts indicate credits (income)",
          "Running balance validates transaction flow"
        ]
      }
    },
    "validation_status": {
      "schema_valid": true,
      "debit_credit_detected": true,
      "balance_reconciliation": true,
      "ready_for_database_storage": true
    }
  },
  "next_steps": {
    "user_confirmation_required": false,
    "automatic_processing": true,
    "estimated_processing_time": "30 seconds"
  }
}
```

### GET /uploads/{file_id}/schema-interpretation
Get comprehensive AI schema interpretation with debit/credit inference

**Response:**
```json
{
  "file_id": "550e8400-e29b-41d4-a716-************",
  "schema_interpretation": {
    "unified_schema_mapping": {
      "date": {
        "source_column": "Transaction Date",
        "target_field": "transaction_date",
        "data_type": "date",
        "format": "MM/DD/YYYY",
        "validation_passed": true
      },
      "amount": {
        "source_column": "Amount",
        "target_field": "amount",
        "data_type": "decimal",
        "currency": "USD",
        "validation_passed": true
      },
      "description": {
        "source_column": "Description",
        "target_field": "description",
        "data_type": "text",
        "entity_extraction_enabled": true
      },
      "debit_credit": {
        "source_column": "Type",
        "target_field": "transaction_type",
        "inference_method": "amount_sign_based",
        "confidence": 0.95
      }
    },
    "financial_analysis": {
      "debit_credit_inference": {
        "method": "accounting_principles_based",
        "patterns_identified": [
          "Negative amounts = Debits (money going out)",
          "Positive amounts = Credits (money coming in)",
          "Account type: Checking (asset account)"
        ],
        "sample_transactions": [
          {
            "description": "STARBUCKS #1234",
            "amount": -5.75,
            "inferred_type": "debit",
            "reasoning": "Negative amount indicates expense/debit"
          },
          {
            "description": "SALARY DEPOSIT",
            "amount": 3500.00,
            "inferred_type": "credit",
            "reasoning": "Positive amount indicates income/credit"
          }
        ]
      },
      "currency_detection": {
        "primary_currency": "USD",
        "confidence": 0.99,
        "multi_currency_detected": false
      },
      "date_range_analysis": {
        "earliest_transaction": "2024-01-01",
        "latest_transaction": "2024-03-31",
        "total_days": 90,
        "transaction_frequency": "daily"
      }
    },
    "database_readiness": {
      "unified_schema_compatible": true,
      "gl_code_mapping_ready": true,
      "categorization_corpus_ready": true,
      "temporal_validation_ready": true
    },
    "ai_metadata": {
      "confidence": 0.97,
      "processing_time_ms": 2150,
      "reasoning": "Standard bank export format with clear debit/credit patterns and comprehensive transaction metadata"
    }
  }
}
```

### POST /uploads/{file_id}/process-mapped
Process file into unified database schema with financial validation

**Request:**
```json
{
  "unified_schema_mapping": {
    "date": "Transaction Date",
    "amount": "Amount",
    "description": "Description",
    "debit_credit": "Type",
    "reference_number": "Ref #"
  },
  "financial_settings": {
    "currency": "USD",
    "date_format": "MM/DD/YYYY",
    "debit_credit_method": "amount_sign_based",
    "accounting_convention": "credit_positive",
    "fiscal_year_start": "2024-04-01"
  },
  "processing_options": {
    "create_rag_corpus": true,
    "enable_entity_extraction": true,
    "validate_gl_code_mapping": true,
    "temporal_accuracy_prep": true
  }
}
```

**Response:**
```json
{
  "file_id": "550e8400-e29b-41d4-a716-************",
  "status": "database_storage_completed",
  "processing_results": {
    "transactions_created": 1247,
    "processing_time": "28.5s",
    "database_operations": {
      "unified_schema_storage": "completed",
      "tenant_isolation_applied": true,
      "gl_code_mapping_applied": true,
      "entity_extraction_completed": true
    }
  },
  "financial_validation": {
    "debit_credit_balance": {
      "total_debits": 12450.75,
      "total_credits": 15230.50,
      "net_change": 2779.75,
      "balance_verified": true
    },
    "data_integrity": {
      "missing_dates": 0,
      "invalid_amounts": 0,
      "duplicate_transactions": 2,
      "data_quality_score": 99.8
    }
  },
  "rag_corpus_creation": {
    "status": "completed",
    "patterns_indexed": 1247,
    "categorization_ready": true,
    "lookup_accuracy_baseline": "100_percent"
  },
  "next_steps": {
    "temporal_accuracy_validation": "ready",
    "production_categorization": "ready",
    "agent_interface": "enabled",
    "financial_reporting": "available"
  },
  "errors": [],
  "warnings": [
    "2 duplicate transactions detected and marked for review"
  ]
}
```

### GET /uploads/{file_id}/status
Check financial file processing status with comprehensive validation

**Response:**
```json
{
  "file_id": "550e8400-e29b-41d4-a716-************",
  "overall_status": "database_storage_completed",
  "processing_stages": {
    "file_upload": {
      "status": "completed",
      "timestamp": "2024-01-15T10:30:00Z",
      "duration_ms": 1250
    },
    "schema_interpretation": {
      "status": "completed",
      "timestamp": "2024-01-15T10:30:02Z",
      "duration_ms": 2150,
      "confidence": 0.97
    },
    "database_storage": {
      "status": "completed",
      "timestamp": "2024-01-15T10:30:30Z",
      "duration_ms": 28500,
      "transactions_stored": 1247
    },
    "rag_corpus_creation": {
      "status": "completed",
      "timestamp": "2024-01-15T10:31:00Z",
      "duration_ms": 15000,
      "patterns_indexed": 1247
    },
    "financial_validation": {
      "status": "completed",
      "timestamp": "2024-01-15T10:31:05Z",
      "validation_passed": true
    }
  },
  "transaction_metrics": {
    "total_transactions": 1247,
    "successfully_processed": 1247,
    "failed_transactions": 0,
    "duplicate_transactions": 2,
    "progress_percentage": 100
  },
  "financial_validation_results": {
    "debit_credit_balance_verified": true,
    "total_debits": 12450.75,
    "total_credits": 15230.50,
    "net_change": 2779.75,
    "data_integrity_score": 99.8,
    "gl_code_mapping_ready": true
  },
  "readiness_status": {
    "categorization_ready": true,
    "temporal_validation_ready": true,
    "agent_interface_ready": true,
    "financial_reporting_ready": true,
    "production_deployment_ready": false,
    "onboarding_completion_required": true
  },
  "errors": [],
  "warnings": [
    "2 duplicate transactions detected - manual review recommended"
  ],
  "performance_metrics": {
    "total_processing_time_ms": 47000,
    "database_queries_executed": 1250,
    "ai_analysis_time_ms": 2150,
    "storage_efficiency": "optimal"
  }
}
```

## Financial Transaction Endpoints (100% Lookup-Based)

### GET /transactions/
List financial transactions with advanced filtering and GL code integration

**Query Parameters:**
```http
GET /transactions/?
  category_id=5&
  gl_code=6150&
  date_from=2024-01-01&
  date_to=2024-12-31&
  transaction_type=debit&
  amount_min=10.00&
  amount_max=1000.00&
  currency=USD&
  entity_name=starbucks&
  accuracy_validated=true&
  page=1&
  limit=50&
  sort_by=date&
  sort_direction=desc&
  search=starbucks&
  include_metadata=true
```

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "date": "2024-01-15",
      "description": "STARBUCKS #1234 - DOWNTOWN LOCATION",
      "amount": -5.75,
      "currency": "USD",
      "transaction_type": "debit",
      "category_assignment": {
        "category_id": 5,
        "category_name": "Dining",
        "category_path": "Expenses > Dining > Coffee Shops",
        "gl_code": "6150",
        "gl_description": "Meals and Entertainment",
        "level_1_category": "Operating Expenses",
        "level_2_category": "Dining",
        "level_3_category": "Coffee Shops"
      },
      "financial_metadata": {
        "lookup_accuracy": "100_percent",
        "categorization_method": "database_lookup",
        "confidence_score": 100,
        "rag_corpus_match": true,
        "historical_pattern_id": "pattern_starbucks_dining_001",
        "validation_status": "accuracy_verified"
      },
      "extracted_entities": {
        "merchant": "Starbucks",
        "location": "Downtown",
        "merchant_id": "merchant_12345",
        "entity_category": "Food & Beverage",
        "entity_confidence": 0.98
      },
      "accounting_details": {
        "debit_account": "6150 - Meals and Entertainment",
        "credit_account": "1000 - Checking Account",
        "fiscal_period": "2024-Q1",
        "accounting_date": "2024-01-15",
        "reference_number": "TXN-001-2024"
      },
      "agent_accessibility": {
        "ui_equivalent": true,
        "agent_query_ready": true,
        "natural_language_accessible": true
      },
      "audit_trail": {
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "last_categorized_at": "2024-01-15T10:30:00Z",
        "categorization_source": "automated_lookup",
        "tenant_id": 1
      }
    }
  ],
  "pagination": {
    "total_records": 1247,
    "page": 1,
    "per_page": 50,
    "total_pages": 25,
    "has_next_page": true,
    "has_previous_page": false
  },
  "summary_statistics": {
    "total_amount": -450.75,
    "debit_count": 35,
    "credit_count": 15,
    "average_transaction_amount": -9.02,
    "currency_breakdown": {
      "USD": 50
    },
    "accuracy_statistics": {
      "lookup_success_rate": "100_percent",
      "validated_transactions": 50,
      "pending_validation": 0
    }
  },
  "financial_performance": {
    "query_time_ms": 145,
    "database_queries": 3,
    "cache_hit_rate": 0.85
  }
}
```

### PUT /transactions/{id}
Update transaction with financial validation and GL code management

**Request:**
```json
{
  "category_assignment": {
    "category_id": 10,
    "gl_code": "6200",
    "reason": "Reclassified as business expense per accounting review"
  },
  "financial_updates": {
    "description": "CLIENT MEETING - STARBUCKS DOWNTOWN",
    "transaction_type": "debit",
    "tags": ["business", "client_meeting", "deductible"],
    "accounting_period": "2024-Q1"
  },
  "validation_requirements": {
    "require_accuracy_validation": true,
    "update_rag_corpus": true,
    "maintain_audit_trail": true
  }
}
```

**Response:**
```json
{
  "message": "Transaction updated successfully with financial validation",
  "transaction": {
    "id": 1,
    "date": "2024-01-15",
    "description": "CLIENT MEETING - STARBUCKS DOWNTOWN",
    "amount": -5.75,
    "currency": "USD",
    "transaction_type": "debit",
    "category_assignment": {
      "category_id": 10,
      "category_name": "Business Expenses",
      "category_path": "Expenses > Business > Client Entertainment",
      "gl_code": "6200",
      "gl_description": "Business Entertainment",
      "previous_category_id": 5,
      "reclassification_reason": "Reclassified as business expense per accounting review"
    },
    "financial_validation": {
      "accuracy_level": "100_percent",
      "validation_method": "manual_override_with_lookup_verification",
      "confidence_score": 100,
      "gl_code_validated": true,
      "debit_credit_verified": true
    },
    "rag_corpus_update": {
      "pattern_updated": true,
      "learning_incorporated": true,
      "similar_transactions_affected": 3
    },
    "audit_trail": {
      "updated_at": "2024-01-15T14:30:00Z",
      "updated_by": "<EMAIL>",
      "update_reason": "Category reclassification",
      "previous_values": {
        "category_id": 5,
        "gl_code": "6150",
        "description": "STARBUCKS #1234"
      }
    }
  },
  "impact_analysis": {
    "gl_code_rollup_affected": true,
    "financial_reports_updated": true,
    "similar_transactions_identified": 3,
    "categorization_pattern_learned": true
  }
}
```

### GET /transactions/accuracy
Get comprehensive financial accuracy validation and temporal analysis

**Query Parameters:**
```http
GET /transactions/accuracy?
  temporal_analysis=true&
  period=2024-q1&
  category_level=all&
  include_gl_codes=true&
  validation_method=lookup_based
```

**Response:**
```json
{
  "financial_accuracy_overview": {
    "current_accuracy_level": "100_percent",
    "lookup_success_rate": 100.0,
    "prediction_dependency": "zero_percent",
    "financial_grade_compliance": true,
    "validation_period": "2024-Q1",
    "total_transactions_validated": 1247
  },
  "temporal_accuracy_validation": {
    "onboarding_simulation_results": [
      {
        "validation_month": "2024-07",
        "training_period": "2024-01 to 2024-06",
        "test_transactions": 185,
        "training_transactions": 1250,
        "lookup_accuracy": 100.0,
        "database_patterns_used": 1250,
        "rag_corpus_matches": 185,
        "financial_compliance": "achieved"
      },
      {
        "validation_month": "2024-08",
        "training_period": "2024-01 to 2024-07",
        "test_transactions": 203,
        "training_transactions": 1435,
        "lookup_accuracy": 100.0,
        "customer_feedback_simulation": "incorporated",
        "financial_compliance": "achieved"
      }
    ],
    "progressive_learning_validation": {
      "knowledge_base_growth": "25% increase per month",
      "pattern_recognition_improvement": "maintained_100_percent",
      "customer_feedback_integration": "seamless"
    }
  },
  "category_level_accuracy": {
    "level_1_categories": [
      {
        "category_id": 1,
        "category_name": "Operating Expenses",
        "gl_code_range": "6000-6999",
        "lookup_accuracy": 100.0,
        "transaction_count": 845,
        "pattern_matches": 845,
        "financial_validation": "passed"
      }
    ],
    "level_2_categories": [
      {
        "category_id": 5,
        "category_name": "Dining",
        "parent_category": "Operating Expenses",
        "gl_code": "6150",
        "lookup_accuracy": 100.0,
        "transaction_count": 245,
        "entity_patterns": ["Starbucks", "McDonald's", "Subway"],
        "financial_validation": "passed"
      }
    ],
    "level_3_categories": [
      {
        "category_id": 15,
        "category_name": "Coffee Shops",
        "parent_category": "Dining",
        "gl_code": "6151",
        "lookup_accuracy": 100.0,
        "transaction_count": 87,
        "merchant_patterns": ["Starbucks", "Dunkin", "Local Coffee"],
        "financial_validation": "passed"
      }
    ]
  },
  "gl_code_accuracy_validation": {
    "total_gl_codes_validated": 23,
    "top_level_structure_compliance": true,
    "rollup_accuracy": 100.0,
    "accounting_software_compatibility": ["QuickBooks", "Xero", "SAP"],
    "chart_of_accounts_integrity": "validated"
  },
  "lookup_methodology_validation": {
    "database_lookup_success": 100.0,
    "rag_corpus_effectiveness": 100.0,
    "historical_pattern_matching": 100.0,
    "entity_recognition_accuracy": 98.7,
    "debit_credit_inference_accuracy": 100.0,
    "no_prediction_dependency": true
  },
  "production_readiness_assessment": {
    "accuracy_requirement_met": true,
    "financial_grade_achieved": true,
    "temporal_validation_passed": true,
    "onboarding_completion_approved": true,
    "agent_interface_validated": true,
    "ready_for_production": true
  },
  "performance_metrics": {
    "average_lookup_time_ms": 45,
    "database_query_efficiency": "optimal",
    "rag_corpus_query_time_ms": 120,
    "overall_categorization_time_ms": 165
  }
}
```

## Financial Category Management Endpoints (GL Code Integration)

### GET /categories/
List all categories with complete GL code hierarchy and 23 top-level structure

**Query Parameters:**
```http
GET /categories/?
  include_gl_codes=true&
  include_hierarchy=true&
  include_usage_stats=true&
  level=all&
  active_only=true&
  top_level_structure=23_standard
```

**Response:**
```json
{
  "financial_category_structure": {
    "total_categories": 156,
    "gl_code_compliance": "23_toplevel_standard",
    "hierarchy_levels": 4,
    "accounting_system_compatible": ["QuickBooks", "Xero", "SAP"]
  },
  "top_level_structure": [
    {
      "structure_id": 1,
      "name": "Assets",
      "gl_code_range": "1000-1999",
      "description": "Current and Fixed Assets",
      "financial_statement": "Balance Sheet",
      "debit_normal_balance": true,
      "category_count": 25
    },
    {
      "structure_id": 2,
      "name": "Liabilities",
      "gl_code_range": "2000-2999",
      "description": "Current and Long-term Liabilities",
      "financial_statement": "Balance Sheet",
      "debit_normal_balance": false,
      "category_count": 18
    },
    {
      "structure_id": 3,
      "name": "Equity",
      "gl_code_range": "3000-3999",
      "description": "Owner's Equity and Retained Earnings",
      "financial_statement": "Balance Sheet",
      "debit_normal_balance": false,
      "category_count": 8
    },
    {
      "structure_id": 4,
      "name": "Revenue",
      "gl_code_range": "4000-4999",
      "description": "Operating and Non-operating Revenue",
      "financial_statement": "Income Statement",
      "debit_normal_balance": false,
      "category_count": 15
    },
    {
      "structure_id": 5,
      "name": "Operating Expenses",
      "gl_code_range": "6000-6999",
      "description": "Cost of Goods Sold and Operating Expenses",
      "financial_statement": "Income Statement",
      "debit_normal_balance": true,
      "category_count": 90
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Revenue",
      "gl_code": "4000",
      "gl_description": "Total Revenue",
      "parent_id": null,
      "level": 1,
      "top_level_structure_id": 4,
      "financial_statement": "Income Statement",
      "normal_balance": "credit",
      "is_active": true,
      "color": "#10B981",
      "usage_statistics": {
        "transaction_count": 245,
        "total_amount": 125000.00,
        "last_used": "2024-01-15T10:30:00Z",
        "frequency_score": 8.5
      },
      "rollup_totals": {
        "current_period": 125000.00,
        "previous_period": 118000.00,
        "ytd_total": 125000.00
      },
      "children": [
        {
          "id": 2,
          "name": "Service Revenue",
          "gl_code": "4100",
          "gl_description": "Revenue from Services",
          "parent_id": 1,
          "level": 2,
          "top_level_structure_id": 4,
          "is_active": true,
          "color": "#10B981",
          "usage_statistics": {
            "transaction_count": 180,
            "total_amount": 95000.00,
            "categorization_accuracy": "100_percent",
            "lookup_success_rate": 100.0
          },
          "children": [
            {
              "id": 15,
              "name": "Consulting Services",
              "gl_code": "4110",
              "gl_description": "Revenue from Consulting",
              "parent_id": 2,
              "level": 3,
              "entity_patterns": ["CLIENT-001", "CLIENT-002"],
              "lookup_patterns": ["consulting", "advisory", "professional services"]
            }
          ]
        }
      ]
    }
  ],
  "agent_accessibility": {
    "natural_language_queryable": true,
    "ui_equivalent_operations": true,
    "bulk_operations_supported": true
  },
  "financial_validation": {
    "gl_code_uniqueness_verified": true,
    "rollup_integrity_maintained": true,
    "accounting_standards_compliant": true,
    "chart_of_accounts_valid": true
  }
}
```

### POST /categories/
Create new category with GL code validation and financial structure compliance

**Request:**
```json
{
  "category_definition": {
    "name": "Software Subscriptions",
    "description": "Monthly software and SaaS subscriptions",
    "parent_id": 5,
    "color": "#F59E0B"
  },
  "gl_code_assignment": {
    "gl_code": "6250",
    "gl_description": "Software and Technology Subscriptions",
    "verify_uniqueness": true,
    "validate_hierarchy": true
  },
  "financial_configuration": {
    "normal_balance": "debit",
    "financial_statement": "Income Statement",
    "tax_category": "business_expense",
    "depreciation_applicable": false
  },
  "categorization_rules": {
    "entity_patterns": ["Microsoft", "Adobe", "Salesforce", "Slack"],
    "description_keywords": ["subscription", "monthly", "software", "saas"],
    "amount_ranges": {
      "typical_min": 10.00,
      "typical_max": 500.00
    }
  }
}
```

**Response:**
```json
{
  "message": "Financial category created successfully with GL code validation",
  "category": {
    "id": 87,
    "name": "Software Subscriptions",
    "description": "Monthly software and SaaS subscriptions",
    "gl_code": "6250",
    "gl_description": "Software and Technology Subscriptions",
    "parent_id": 5,
    "parent_name": "Operating Expenses",
    "level": 3,
    "top_level_structure_id": 5,
    "hierarchy_path": "Operating Expenses > Technology > Software Subscriptions",
    "color": "#F59E0B",
    "financial_properties": {
      "normal_balance": "debit",
      "financial_statement": "Income Statement",
      "gl_code_validated": true,
      "hierarchy_compliant": true,
      "accounting_standards_aligned": true
    },
    "categorization_configuration": {
      "lookup_patterns_created": 4,
      "entity_patterns_registered": 4,
      "auto_categorization_ready": true,
      "rag_corpus_updated": true
    },
    "rollup_configuration": {
      "parent_rollup_enabled": true,
      "gl_code_rollup_active": true,
      "financial_statement_integration": true
    }
  },
  "validation_results": {
    "gl_code_uniqueness": "verified",
    "hierarchy_integrity": "maintained",
    "financial_structure_compliance": "achieved",
    "chart_of_accounts_updated": true
  },
  "impact_analysis": {
    "similar_transactions_identified": 23,
    "auto_categorization_candidates": 23,
    "financial_reports_affected": ["Income Statement", "Expense Detail"]
  }
}
```

### POST /categories/batch-categorize
Batch categorize transactions using 100% lookup-based methodology

**Request:**
```json
{
  "transaction_ids": [1, 2, 3, 4, 5],
  "categorization_options": {
    "method": "lookup_only",
    "accuracy_requirement": "100_percent",
    "update_rag_corpus": true,
    "validate_gl_codes": true
  },
  "processing_preferences": {
    "parallel_processing": true,
    "include_confidence_analysis": true,
    "generate_audit_trail": true
  }
}
```

**Response:**
```json
{
  "batch_categorization_results": {
    "total_transactions": 5,
    "successfully_categorized": 5,
    "failed_categorizations": 0,
    "lookup_success_rate": "100_percent",
    "processing_time": "1.2s",
    "method_used": "database_lookup_with_rag_corpus"
  },
  "transaction_results": [
    {
      "transaction_id": 1,
      "categorization_result": {
        "category_id": 5,
        "category_name": "Dining",
        "gl_code": "6150",
        "confidence": 100,
        "method": "database_pattern_match",
        "lookup_source": "historical_pattern_starbucks_dining",
        "financial_validation": "passed"
      },
      "reasoning": {
        "primary_match": "Merchant 'Starbucks' has 100% historical pattern match for Dining category",
        "supporting_evidence": [
          "Amount range typical for coffee purchases",
          "Time pattern matches meal periods",
          "Location consistent with dining establishments"
        ],
        "lookup_accuracy": "guaranteed_100_percent"
      }
    }
  ],
  "financial_impact": {
    "gl_code_rollups_updated": true,
    "financial_statements_affected": ["Income Statement"],
    "accounting_period_impacted": "2024-Q1"
  },
  "learning_updates": {
    "rag_corpus_patterns_reinforced": 5,
    "new_patterns_learned": 0,
    "categorization_accuracy_maintained": "100_percent"
  }
}
```

## A2A Agent Protocol Endpoints (JSON-RPC 2.0 Financial Interface)

### POST /agents/customer/query
Customer-facing agent with complete UI equivalence (A2A v0.2.2 compliant)

**Request (JSON-RPC 2.0 Format):**
```json
{
  "jsonrpc": "2.0",
  "method": "customer_agent.process_financial_query",
  "params": {
    "message": "Upload my Q1 bank statements and categorize all transactions",
    "session_context": {
      "session_id": "550e8400-e29b-41d4-a716-************",
      "user_preferences": {
        "preferred_currency": "USD",
        "fiscal_year_start": "2024-04-01",
        "agent_interface_preferred": true
      }
    },
    "financial_context": {
      "require_100_percent_accuracy": true,
      "gl_code_validation": true,
      "agent_ui_equivalence": true
    }
  },
  "id": "req_001"
}
```

**Response (JSON-RPC 2.0 Format):**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "agent_response": {
      "message": "I'll help you upload and categorize your Q1 bank statements. Please drag and drop your files here, and I'll process them with 100% financial accuracy using our lookup-based categorization system.",
      "ui_equivalent_action": "file_upload_interface_activated",
      "capabilities_demonstrated": [
        "File upload via chat interface",
        "Schema interpretation with debit/credit inference",
        "100% lookup-based categorization",
        "Real-time processing status",
        "Downloadable results and reports"
      ]
    },
    "tool_activations": [
      {
        "tool_name": "upload_files_via_chat",
        "status": "ready",
        "ui_equivalent": "/upload page functionality"
      },
      {
        "tool_name": "schema_interpretation_agent",
        "status": "standby",
        "capability": "intelligent_column_mapping"
      },
      {
        "tool_name": "lookup_categorization_tool",
        "status": "ready",
        "accuracy_level": "100_percent"
      }
    ],
    "financial_metadata": {
      "accuracy_guarantee": "100_percent_lookup_based",
      "ui_feature_parity": "complete",
      "agent_capabilities": "equivalent_to_traditional_interface",
      "financial_compliance": "production_grade"
    }
  },
  "id": "req_001"
}
```

### POST /agents/customer/file-upload
File upload through agent interface with UI equivalence

**Request (JSON-RPC 2.0 with Multipart Support):**
```json
{
  "jsonrpc": "2.0",
  "method": "customer_agent.upload_and_process_files",
  "params": {
    "files": [
      {
        "filename": "Q1_Bank_Export.xlsx",
        "file_data": "base64_encoded_file_content",
        "expected_format": "bank_export",
        "currency": "USD"
      }
    ],
    "processing_preferences": {
      "auto_schema_interpretation": true,
      "debit_credit_inference": "auto",
      "immediate_categorization": true,
      "generate_preview": true
    },
    "session_id": "550e8400-e29b-41d4-a716-************"
  },
  "id": "file_upload_001"
}
```

**Response (JSON-RPC 2.0 Streaming):**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "upload_status": "completed",
    "file_processing": {
      "file_id": "550e8400-e29b-41d4-a716-************",
      "schema_interpretation": {
        "confidence": 0.97,
        "debit_credit_method": "amount_sign_based",
        "columns_mapped": 6,
        "transactions_detected": 1247
      },
      "database_storage": "completed",
      "categorization_preview": {
        "sample_categorizations": [
          {
            "description": "STARBUCKS #1234",
            "amount": -5.75,
            "suggested_category": "Dining > Coffee Shops",
            "gl_code": "6151",
            "confidence": 100
          }
        ],
        "accuracy_level": "100_percent_lookup"
      }
    },
    "agent_display": {
      "chat_message": "Perfect! I've successfully uploaded and analyzed your Q1 bank statements. I found 1,247 transactions and have categorized them with 100% accuracy using our lookup system. Here's a preview of the categorization...",
      "interactive_elements": [
        {
          "type": "transaction_table",
          "data": "sample_transactions",
          "downloadable": true
        },
        {
          "type": "categorization_chart",
          "data": "spending_by_category",
          "downloadable": true
        }
      ],
      "next_actions": [
        "View detailed categorization report",
        "Download processed transactions",
        "Generate financial statements",
        "Navigate to dashboard"
      ]
    },
    "ui_equivalence_achieved": {
      "file_upload_equivalent": true,
      "schema_interpretation_equivalent": true,
      "categorization_equivalent": true,
      "preview_display_equivalent": true,
      "download_capabilities_equivalent": true
    }
  },
  "id": "file_upload_001"
}
```

### POST /agents/customer/generate-report
Report generation through agent interface with download capability

**Request (JSON-RPC 2.0):**
```json
{
  "jsonrpc": "2.0",
  "method": "customer_agent.generate_financial_report",
  "params": {
    "report_request": "Generate a comprehensive spending report by category for Q1 2024 with GL code rollups",
    "report_configuration": {
      "period": "2024-Q1",
      "report_type": "spending_by_category",
      "include_gl_codes": true,
      "format": "excel_with_charts",
      "currency": "USD"
    },
    "display_preferences": {
      "show_in_chat": true,
      "enable_download": true,
      "interactive_charts": true
    },
    "session_id": "550e8400-e29b-41d4-a716-************"
  },
  "id": "report_gen_001"
}
```

**Response (JSON-RPC 2.0):**
```json
{
  "jsonrpc": "2.0",
  "result": {
    "report_generation": {
      "status": "completed",
      "processing_time_ms": 3200,
      "report_id": "report_550e8400-e29b-41d4-a716-************"
    },
    "agent_display": {
      "chat_message": "I've generated your comprehensive Q1 2024 spending report with GL code rollups. Here's an interactive preview with download options:",
      "embedded_content": [
        {
          "type": "spending_chart",
          "title": "Spending by Category - Q1 2024",
          "chart_data": {
            "categories": ["Dining", "Office Supplies", "Software"],
            "amounts": [1250.00, 890.00, 2100.00],
            "gl_codes": ["6150", "6200", "6250"]
          },
          "downloadable": true,
          "format": "interactive_chart"
        },
        {
          "type": "summary_table",
          "title": "GL Code Rollup Summary",
          "data": {
            "total_expenses": 12450.00,
            "category_count": 15,
            "transaction_count": 1247
          },
          "downloadable": true,
          "format": "data_table"
        }
      ],
      "download_options": [
        {
          "format": "Excel",
          "filename": "Q1_2024_Spending_Report.xlsx",
          "download_url": "/api/v1/reports/download/report_550e8400-e29b-41d4-a716-************.xlsx"
        },
        {
          "format": "PDF",
          "filename": "Q1_2024_Spending_Report.pdf", 
          "download_url": "/api/v1/reports/download/report_550e8400-e29b-41d4-a716-************.pdf"
        }
      ]
    },
    "ui_equivalence_validation": {
      "report_builder_equivalent": true,
      "chart_generation_equivalent": true,
      "export_functionality_equivalent": true,
      "data_accuracy_equivalent": "100_percent",
      "user_experience_equivalent": true
    },
    "financial_metadata": {
      "gl_code_accuracy": "100_percent",
      "rollup_calculations_verified": true,
      "accounting_standards_compliant": true,
      "export_formats_available": ["Excel", "PDF", "CSV", "QuickBooks"]
    }
  },
  "id": "report_gen_001"
}
```

### GET /agents/discovery
A2A Agent Discovery (Agent Cards)

**Response:**
```json
{
  "agent_cards": [
    {
      "agent_id": "giki-ai-customer-agent",
      "name": "Giki AI Customer Agent",
      "description": "Financial-grade customer-facing agent with complete UI equivalence",
      "version": "1.0.0",
      "capabilities": [
        "file_upload_via_chat",
        "schema_interpretation",
        "100_percent_lookup_categorization",
        "financial_report_generation",
        "chart_and_table_display",
        "navigation_assistance",
        "download_management"
      ],
      "financial_features": {
        "accuracy_level": "100_percent",
        "ui_equivalence": "complete",
        "gl_code_support": true,
        "multi_currency": true,
        "accounting_integration": true
      },
      "communication": {
        "protocol": "A2A-v0.2.2",
        "format": "JSON-RPC-2.0",
        "base_url": "/api/v1/agents/customer",
        "authentication": "Bearer JWT"
      },
      "reliability": {
        "financial_grade": true,
        "tool_independence": true,
        "provider_agnostic": true,
        "outage_resilient": true
      }
    }
  ]
}
```

## Financial Reports Endpoints

### GET /reports/spending-by-category
Comprehensive category spending analysis with GL code rollups

**Query Parameters:**
```http
GET /reports/spending-by-category?
  date_from=2024-01-01&
  date_to=2024-12-31&
  category_level=all&
  include_gl_rollups=true&
  include_trend_analysis=true&
  currency=USD&
  fiscal_period=2024-Q1&
  export_format=json
```

**Response:**
```json
{
  "financial_report_metadata": {
    "report_type": "spending_by_category_with_gl_rollups",
    "period_analyzed": {
      "from": "2024-01-01",
      "to": "2024-12-31",
      "fiscal_period": "2024-Q1",
      "days_included": 90
    },
    "data_accuracy": "100_percent_lookup_based",
    "gl_code_compliance": "23_toplevel_structure",
    "currency": "USD",
    "generation_time": "2024-01-15T10:30:00Z"
  },
  "executive_summary": {
    "total_spending": 12450.00,
    "total_categories": 15,
    "total_transactions": 1247,
    "largest_category": "Operating Expenses",
    "spending_trend": "increasing_5_percent"
  },
  "top_level_gl_structure": [
    {
      "structure_id": 5,
      "name": "Operating Expenses",
      "gl_code_range": "6000-6999",
      "total_amount": 10200.00,
      "percentage_of_total": 81.9,
      "transaction_count": 1050,
      "trend_vs_previous_period": "+5.2%"
    }
  ],
  "detailed_categories": [
    {
      "category_hierarchy": {
        "level_1": "Operating Expenses",
        "level_2": "Dining",
        "level_3": "Coffee Shops",
        "full_path": "Operating Expenses > Dining > Coffee Shops"
      },
      "financial_details": {
        "category_id": 15,
        "gl_code": "6151",
        "gl_description": "Coffee and Beverage Expenses",
        "total_amount": 450.75,
        "percentage_of_total": 3.6,
        "transaction_count": 87
      },
      "performance_metrics": {
        "average_transaction_amount": 5.18,
        "largest_transaction": 12.50,
        "smallest_transaction": 3.75,
        "frequency_per_day": 0.97
      },
      "trend_analysis": {
        "vs_previous_period": "+8.5%",
        "monthly_trend": "steady_increase",
        "seasonal_pattern": "workday_concentration"
      },
      "entity_breakdown": [
        {
          "entity_name": "Starbucks",
          "amount": 245.50,
          "transaction_count": 45,
          "percentage": 54.5
        }
      ],
      "categorization_metadata": {
        "lookup_accuracy": "100_percent",
        "pattern_matches": 87,
        "manual_overrides": 0,
        "confidence_level": "guaranteed"
      }
    }
  ],
  "gl_code_rollups": {
    "6000_operating_expenses": {
      "total": 10200.00,
      "subcategories": {
        "6100_professional_services": 2100.00,
        "6150_meals_entertainment": 1250.00,
        "6200_office_supplies": 890.00,
        "6250_software_subscriptions": 2100.00
      }
    }
  },
  "export_options": {
    "available_formats": ["Excel", "PDF", "CSV", "QuickBooks", "Xero"],
    "chart_exports": ["PNG", "SVG", "PDF"],
    "agent_downloadable": true,
    "ui_equivalent": true
  },
  "agent_accessibility": {
    "natural_language_queryable": true,
    "chat_display_ready": true,
    "download_via_conversation": true,
    "ui_feature_parity": "complete"
  }
}
```

### GET /reports/spending-by-entity
Entity spending breakdown

**Response:**
```json
{
  "items": [
    {
      "entity_name": "Starbucks",
      "total_amount": 245.50,
      "transaction_count": 18,
      "category_path": "Expenses > Dining > Coffee"
    }
  ],
  "total_records": 25
}
```

### GET /reports/income-expense-summary
Income vs expense summary

**Response:**
```json
{
  "total_income": 5000.00,
  "total_expenses": 3500.00,
  "net_income_loss": 1500.00,
  "savings_rate": 30.0,
  "period": {
    "from": "2024-01-01",
    "to": "2024-12-31"
  }
}
```

### GET /reports/monthly-trends
Monthly financial trends

**Response:**
```json
{
  "items": [
    {
      "month": "2024-01",
      "total_income": 5000.00,
      "total_expenses": 3200.00,
      "net_amount": 1800.00,
      "transaction_count": 125
    }
  ],
  "total_months": 12
}
```

### POST /reports/export
Export report data

**Request:**
```json
{
  "type": "csv",
  "report_name": "spending_by_category",
  "filters": {
    "date_from": "2024-01-01",
    "date_to": "2024-12-31"
  }
}
```

**Response:**
```
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="spending_by_category_2024.csv"

<binary CSV data>
```

## Intelligence/Agent Endpoints

### POST /intelligence/agent/customer/query
Customer-facing agent queries

**Request:**
```json
{
  "message": "Show me my dining expenses last month",
  "session_id": "optional-session-id",
  "context": {
    "current_page": "dashboard",
    "filters": {}
  }
}
```

**Response:**
```json
{
  "response": "You spent $245.50 on dining last month across 18 transactions. Your largest dining expense was $25.00 at The Cheesecake Factory.",
  "data": {
    "total": 245.50,
    "transaction_count": 18,
    "transactions": [...],
    "insights": [
      "Dining expenses increased 15% compared to previous month"
    ]
  },
  "suggested_actions": [
    "View detailed report",
    "Set dining budget",
    "Analyze spending patterns"
  ],
  "session_id": "uuid-session-id"
}
```

### POST /intelligence/agent/customer/audio
Audio input processing

**Request:**
```http
POST /intelligence/agent/customer/audio
Content-Type: multipart/form-data

audio: <binary audio data>
format: wav
session_id: optional-uuid
```

**Response:**
```json
{
  "transcription": "Show me my dining expenses last month",
  "response": "You spent $245.50 on dining last month...",
  "data": {...},
  "audio_response": "base64-encoded-audio",
  "session_id": "uuid-session-id"
}
```

### POST /intelligence/agent/data/process
Data processing agent endpoint

**Request:**
```json
{
  "operation": "categorize_transactions",
  "data": {
    "transaction_ids": [1, 2, 3],
    "options": {
      "confidence_threshold": 0.8
    }
  }
}
```

**Response:**
```json
{
  "operation": "categorize_transactions",
  "status": "completed",
  "results": [
    {
      "transaction_id": 1,
      "category_assigned": "Dining",
      "confidence": 0.95
    }
  ],
  "processing_time": "1.2s"
}
```

## GL Code Endpoints

### GET /gl-codes/
List GL codes with filtering

**Query Parameters:**
```http
GET /gl-codes/?
  search=office&
  category_id=5&
  page=1&
  per_page=50
```

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "code": "6200",
      "name": "Office Supplies",
      "description": "General office supplies and equipment",
      "category_id": 5,
      "category_name": "Office Expenses",
      "is_active": true
    }
  ],
  "total_records": 25,
  "page": 1,
  "per_page": 50
}
```

### POST /gl-codes/
Create new GL code

**Request:**
```json
{
  "code": "6250",
  "name": "Software Subscriptions",
  "description": "Monthly software and SaaS subscriptions",
  "category_id": 8
}
```

**Response:**
```json
{
  "message": "GL code created successfully",
  "gl_code": {
    "id": 25,
    "code": "6250",
    "name": "Software Subscriptions",
    "description": "Monthly software and SaaS subscriptions",
    "category_id": 8,
    "is_active": true
  }
}
```

## Onboarding Endpoints (PHASE 1 - One-Time Training)

### POST /onboarding/upload
Upload historical data WITH category labels for AI training

**Purpose**: ONE-TIME upload of historical transactions to train AI on customer patterns
**Critical**: Files MUST contain category columns for training

**Request:**
```http
POST /onboarding/upload
Content-Type: multipart/form-data

file: <binary data>
file_type: historical_transactions
contains_categories: true  # Required validation
training_period: 2024-full-year
```

**Date Parsing Features:**
- **Intelligent Date Recognition**: System handles ANY date format humans use
- **100% Accuracy Requirement**: If even one date fails standard parsing, AI is used
- **Privacy-Preserving**: Only date strings sent to AI, no transaction context
- **Format Support**:
  - MM/DD/YYYY, DD/MM/YYYY (with 2 or 4 digit years)
  - Month names (Jan, January, etc.)
  - Various separators (/, -, ., space)
  - 2-digit year interpretation (00-29 → 2000-2029, 30-99 → 1930-1999)
- **Multi-Sheet Processing**: Each Excel sheet processed with independent date detection

**Validation Response (if no categories):**
```json
{
  "status": "error",
  "message": "Historical data must include category labels for training",
  "details": {
    "error_code": "MISSING_CATEGORY_COLUMNS",
    "required_columns": ["category", "category_label", "original_category"],
    "detected_columns": ["date", "description", "amount"],
    "suggestion": "Please upload historical data that includes your existing categorization"
  }
}
```

**Success Response:**
```json
{
  "file_id": "550e8400-e29b-41d4-a716-************",
  "status": "historical_data_accepted",
  "message": "Historical data with categories uploaded successfully",
  "training_metadata": {
    "total_transactions": 2924,
    "transactions_with_categories": 2924,
    "unique_categories_found": 47,
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    }
  },
  "next_steps": [
    "Schema interpretation and mapping",
    "Database storage with original categories",
    "RAG corpus training on first 6 months",
    "Temporal validation on remaining 6 months"
  ]
}
```

### GET /onboarding/status
Get current onboarding workflow status

**Response:**
```json
{
  "tenant_id": 1,
  "onboarding_phase": "temporal_validation",
  "phases_completed": {
    "data_upload": true,
    "schema_interpretation": true,
    "database_storage": true,
    "rag_corpus_creation": true,
    "temporal_validation": false
  },
  "temporal_validation_progress": {
    "current_month": "September 2024",
    "months_completed": ["July", "August"],
    "months_remaining": ["September", "October", "November", "December"],
    "current_accuracy": 87.5,
    "target_accuracy": 85.0
  },
  "production_readiness": {
    "accuracy_threshold_met": true,
    "all_months_validated": false,
    "estimated_completion": "2 hours"
  }
}
```

### POST /onboarding/temporal-validation
Start or continue temporal accuracy validation

**Request:**
```json
{
  "validation_month": "2024-09",
  "options": {
    "simulate_customer_feedback": true,
    "update_rag_corpus": true,
    "accuracy_threshold": 85.0
  }
}
```

**Response:**
```json
{
  "validation_results": {
    "month": "September 2024",
    "transactions_tested": 245,
    "correct_predictions": 214,
    "accuracy_percentage": 87.3,
    "threshold_met": true
  },
  "category_performance": [
    {
      "category": "Payroll",
      "accuracy": 100.0,
      "transactions": 12
    },
    {
      "category": "Marketing", 
      "accuracy": 82.5,
      "transactions": 40,
      "improvement_needed": true
    }
  ],
  "next_month_ready": true,
  "corpus_updated": true
}
```

### POST /onboarding/complete
Complete onboarding and approve for production

**Request:**
```json
{
  "confirm_production_ready": true,
  "final_validations": {
    "accuracy_achieved": 88.2,
    "all_months_validated": true,
    "customer_approved": true
  }
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Onboarding completed successfully - production access granted",
  "production_configuration": {
    "categorization_enabled": true,
    "rag_corpus_active": true,
    "accuracy_baseline": 88.2,
    "gl_codes_configured": true
  },
  "next_steps": [
    "Upload new transactions via /uploads endpoint",
    "AI will categorize using learned patterns",
    "Generate financial reports",
    "Use agent for insights"
  ]
}
```

## Provider Management Endpoints

### GET /providers/status
Get current LLM provider status and availability

**Response:**
```json
{
  "current_provider": "anthropic",
  "provider_status": {
    "anthropic": {
      "status": "healthy",
      "response_time_ms": 145,
      "quota_remaining": 98500,
      "model": "claude-3-opus"
    },
    "openai": {
      "status": "healthy", 
      "response_time_ms": 189,
      "quota_remaining": 45000,
      "model": "gpt-4"
    },
    "google": {
      "status": "quota_exceeded",
      "response_time_ms": null,
      "quota_remaining": 0,
      "model": "gemini-2.0-flash",
      "error": "429 - Rate limit exceeded"
    }
  },
  "switching_enabled": true,
  "automatic_failover": true,
  "last_switch_event": {
    "timestamp": "2024-01-15T10:30:00Z",
    "from_provider": "google",
    "to_provider": "anthropic",
    "reason": "quota_exceeded"
  }
}
```

### POST /providers/switch
Manually switch to a different LLM provider

**Request:**
```json
{
  "target_provider": "openai",
  "reason": "manual_switch",
  "force_switch": false
}
```

**Response:**
```json
{
  "message": "Provider switched successfully",
  "previous_provider": "anthropic",
  "current_provider": "openai",
  "switch_timestamp": "2024-01-15T10:35:00Z",
  "provider_health": {
    "status": "healthy",
    "model": "gpt-4",
    "response_time_ms": 189
  }
}
```

## SSE (Server-Sent Events) Endpoints

### GET /sse/file-processing/{file_id}
Real-time file processing progress updates

**Response (SSE Stream):**
```
event: progress
data: {"stage": "schema_interpretation", "progress": 25, "message": "Analyzing file structure..."}

event: progress
data: {"stage": "database_storage", "progress": 50, "message": "Storing transactions..."}

event: progress
data: {"stage": "rag_corpus_creation", "progress": 75, "message": "Creating knowledge base..."}

event: complete
data: {"stage": "completed", "progress": 100, "message": "File processing completed successfully"}
```

### GET /sse/categorization-progress
Real-time categorization progress for batch operations

**Query Parameters:**
```http
GET /sse/categorization-progress?batch_id=550e8400-e29b-41d4-a716-446655440005
```

**Response (SSE Stream):**
```
event: progress
data: {"processed": 100, "total": 1247, "success": 100, "failed": 0, "current_transaction": "STARBUCKS #1234"}

event: progress
data: {"processed": 200, "total": 1247, "success": 200, "failed": 0, "current_transaction": "AMAZON PURCHASE"}

event: complete
data: {"processed": 1247, "total": 1247, "success": 1247, "failed": 0, "accuracy": "100_percent"}
```

### GET /sse/onboarding-validation
Real-time temporal validation progress during onboarding

**Response (SSE Stream):**
```
event: validation_progress
data: {"month": "2024-07", "progress": 16.7, "accuracy": 88.5, "status": "processing"}

event: validation_progress
data: {"month": "2024-08", "progress": 33.3, "accuracy": 89.2, "status": "processing"}

event: validation_complete
data: {"overall_accuracy": 86.2, "months_passed": 4, "production_ready": true}
```

## Export Service Endpoints

### POST /export/transactions
Export transactions in multiple formats with advanced filtering

**Request:**
```json
{
  "format": "excel",
  "filters": {
    "date_from": "2024-01-01",
    "date_to": "2024-12-31",
    "categories": [5, 10, 15],
    "gl_codes": ["6150", "6200"],
    "include_metadata": true
  },
  "options": {
    "include_gl_rollups": true,
    "include_entity_details": true,
    "group_by": "category",
    "currency_conversion": "USD"
  }
}
```

**Response:**
```json
{
  "export_id": "export_550e8400-e29b-41d4-a716-446655440006",
  "status": "processing",
  "estimated_time_seconds": 30,
  "download_url": "/api/v1/export/download/export_550e8400-e29b-41d4-a716-446655440006",
  "expires_at": "2024-01-15T11:30:00Z"
}
```

### GET /export/download/{export_id}
Download completed export file

**Response:**
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="transactions_export_2024.xlsx"

<binary data>
```

### POST /export/financial-statements
Export complete financial statements

**Request:**
```json
{
  "statement_types": ["income_statement", "balance_sheet", "cash_flow"],
  "period": "2024-Q1",
  "format": "pdf",
  "accounting_standard": "GAAP",
  "include_notes": true
}
```

**Response:**
```json
{
  "export_id": "export_550e8400-e29b-41d4-a716-************",
  "statements_included": ["income_statement", "balance_sheet", "cash_flow"],
  "download_urls": {
    "combined": "/api/v1/export/download/export_550e8400-e29b-41d4-a716-************",
    "income_statement": "/api/v1/export/download/export_550e8400-e29b-41d4-a716-************-is",
    "balance_sheet": "/api/v1/export/download/export_550e8400-e29b-41d4-a716-************-bs",
    "cash_flow": "/api/v1/export/download/export_550e8400-e29b-41d4-a716-************-cf"
  }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    },
    {
      "field": "amount",
      "message": "Amount must be a valid number"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "status": "error",
  "message": "Authentication required",
  "details": {
    "error_code": "INVALID_TOKEN",
    "description": "JWT token is invalid or expired"
  }
}
```

### 403 Forbidden
```json
{
  "status": "error", 
  "message": "Access denied to this resource",
  "details": {
    "error_code": "INSUFFICIENT_PERMISSIONS",
    "required_permission": "transactions:read"
  }
}
```

### 404 Not Found
```json
{
  "status": "error",
  "message": "Resource not found",
  "details": {
    "error_code": "RESOURCE_NOT_FOUND",
    "resource_type": "transaction",
    "resource_id": "123"
  }
}
```

### 422 Unprocessable Entity
```json
{
  "status": "error",
  "message": "Business logic validation failed",
  "details": {
    "error_code": "BUSINESS_RULE_VIOLATION",
    "description": "Cannot categorize transaction with confidence below threshold"
  }
}
```

### 500 Internal Server Error
```json
{
  "status": "error",
  "message": "An unexpected error occurred",
  "request_id": "uuid-for-debugging"
}
```

## Rate Limiting

### Rate Limits
- **Authenticated users**: 100 requests/minute
- **Unauthenticated**: 20 requests/minute
- **File uploads**: 10 per hour
- **AI operations**: 50 per hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1640995200
```

### Rate Limit Exceeded Response
```json
{
  "status": "error",
  "message": "Rate limit exceeded",
  "details": {
    "error_code": "RATE_LIMIT_EXCEEDED",
    "retry_after": 60,
    "limit": 100,
    "window": "1 minute"
  }
}
```

## Authentication Details

### JWT Token Structure
```json
{
  "sub": "user_id",
  "email": "<EMAIL>", 
  "tenant_id": 1,
  "tid": "tenant_uuid",
  "exp": 1640995200,
  "iat": 1640908800
}
```

### Token Validation
- All protected endpoints require valid JWT in Authorization header
- Token must not be expired
- Tenant context is extracted from token
- User must be active in database

### Multi-Tenant Security
- Every database query automatically filters by tenant_id
- Users can only access data within their tenant
- No cross-tenant data leakage possible
- Tenant ID extracted from JWT token claims

## Webhook Events (Future)

### Event Types
- `transaction.categorized` - When AI categorizes transactions
- `file.processed` - When file upload completes  
- `insight.generated` - When new insight is created
- `corpus.updated` - When RAG corpus is updated

### Webhook Payload Format
```json
{
  "event": "transaction.categorized",
  "data": {
    "transaction_id": 123,
    "category_id": 5,
    "confidence": 0.95
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "tenant_id": 1
}
```

## API Versioning

### Current Version: v1
- All endpoints prefixed with `/api/v1/`
- Breaking changes will increment major version
- Backward compatibility maintained within major version
- Deprecation notices provided 90 days before removal

### Version Headers
```http
API-Version: v1
Deprecated: false
Sunset: null
```