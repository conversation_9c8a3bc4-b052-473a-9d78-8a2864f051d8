# Agents and Tools Specification

---
status: active
last-updated: 2025-06-25
update-frequency: weekly
update-triggers: agent updates, tool changes, ADK/A2A updates
---

## Overview

This document defines the financial-grade agent-based architecture for Giki AI, including agent implementations, tool functions, and AI workflows optimized for 100% reliability in financial operations. All agents follow official Google ADK v1.3.0 patterns and integrate with A2A v0.2.2 protocol for secure agent interoperability.

**DUAL-MODE CUSTOMER EXPERIENCE**: Customers have complete choice freedom - every workflow can be accomplished via conversational AI OR direct UI interaction. A single, unified intelligent agent is available as an option for those preferring conversational interface. All backend agent specialization operates transparently regardless of customer interaction mode.

## Official Guiding Principles

### Google Agent Development Kit (ADK) v1.3.0
- **Official Repository**: https://github.com/google/adk-python
- **Documentation**: https://google.github.io/adk-docs/
- **Current Version**: v1.3.0 (Production Ready, June 2025)
- **Pattern**: All agents MUST inherit from `google.adk.agents.LlmAgent` or `BaseAgent`
- **Installation**: `uv add google-adk`

### Agent2Agent (A2A) Protocol v0.2.2  
- **Official Repository**: https://github.com/google-a2a/A2A
- **Documentation**: https://google-a2a.github.io/A2A/
- **Current Version**: v0.2.2 (Open Standard, June 2025)
- **Purpose**: JSON-RPC 2.0 over HTTP(S) communication between opaque agentic applications
- **Installation**: `uv add a2a-sdk`

### Financial-Grade Implementation Rules
1. **100% Reliability Requirement**: Tools must achieve financial-grade reliability (99% = 0%)
2. **Lookup-First Operations**: Use retrieval/lookup for financial categorization when historical data exists
3. **Bootstrap Exception**: Zero-onboarding companies use AI until sufficient data exists for lookup
4. **Tool Independence**: Tools must not depend on LLM intelligence except during bootstrap phase
5. **Cloud-Agnostic Design**: Support switching between any LLM provider (Anthropic, OpenAI, Google, custom)
6. **Provider Switching**: Enable real-time switching during cloud provider outages
7. **A2A Agent Cards**: All agents must provide capability discovery for interoperability
8. **Database-First Operations**: All financial operations based on database lookup, never raw files

### Agent Tool Efficiency Principles (Critical for Performance)
Based on research of Google ADK best practices and cognitive efficiency requirements:

8. **Minimal Tool Assignment**: Each agent gets ONLY tools essential for its specific purpose (maximum 5 tools, ideally 2-3)
9. **Tool-Purpose Alignment**: Tools must directly map to the agent's core function - no generic "kitchen sink" approaches
10. **Cognitive Efficiency**: Fewer tools = faster decisions, more predictable behavior, reduced token usage
11. **No Tool Overlap**: Avoid giving the same tools to multiple agents unless absolutely necessary
12. **Specialized Agent Roles**: Each agent has a narrow, well-defined purpose with matching tool set

### Customer Experience Principles
13. **Single Agent Interface**: Customers interact with ONE unified agent ("Giki") - multi-agent architecture is hidden
14. **UI Equivalence**: Every web app action must have an agent equivalent - complete choice freedom
15. **Invisible Orchestration**: Backend agent coordination happens transparently without user awareness

## Related Specifications
- **System Architecture & Technology**: @docs/specifications/system-design-spec.md
- **Frontend Implementation**: @docs/specifications/frontend-spec.md
- **API Contracts**: @docs/specifications/contracts-spec.md

## Financial-Grade Architectural Principles

### 1. Financial Reliability as Core Architecture
The GIKI AI system achieves financial-grade reliability (100% accuracy) through lookup/retrieval patterns, not prediction-based ML. This architectural decision ensures 99% accuracy = 0% compliance with financial requirements.

**ADK v1.3.0 Financial Patterns Applied**:
- **Code-First Development**: Agents defined as Python classes inheriting from `LlmAgent`
- **Multi-Agent Systems**: Specialized agents with `sub_agents` for complex workflows
- **Model-Agnostic Design**: Switch between any LLM provider (cloud-agnostic deployment)
- **Tool Reliability**: Tools work independently of LLM intelligence
- **Database-First Operations**: All financial operations from unified database schema

### 2. A2A v0.2.2 Protocol Architecture (Opaque Agent Communication)
```
UI Layer (React) ←→ Agent Panel (Real-time sync)
    ↓
API Layer (FastAPI - A2A JSON-RPC 2.0 Endpoints)
    ↓
Agent Layer (LlmAgent subclasses with Agent Cards for discovery)
    ↓
Tool Functions (FunctionTool with 100% reliability patterns)
    ↓
Service Layer (Database lookup services - no prediction logic)
    ↓
Database Layer (Unified transaction schema with tenant isolation)
```

**A2A v0.2.2 Compliance Features**:
- **Agent Discovery**: Agent Cards declare capabilities and connection info
- **Opaque Communication**: Agents don't expose internal memory/tools/state
- **JSON-RPC 2.0**: Standardized communication over HTTP(S)
- **Flexible Interaction**: Synchronous, streaming (SSE), and async push notifications
- **Enterprise Security**: Authentication, authorization, and observability built-in

### 3. Financial Tool Reliability Requirements
- **No LLM Dependence**: Tools must function reliably without depending on LLM intelligence
- **Lookup-Only Operations**: Use database retrieval for 100% accuracy (no prediction)
- **Provider Independence**: Tools work with any LLM provider (Anthropic, OpenAI, Google, custom)
- **Outage Resilience**: Switch providers during cloud outages without data loss
- **Tenant Isolation**: All operations within tenant boundaries using database security

### 4. Database-First Financial Operations
- **Unified Schema**: All customer data standardized into single transaction schema
- **No Raw Files**: All operations from database, never from uploaded files
- **GL Code Integration**: Multi-level categories mapped to General Ledger codes
- **Debit/Credit Intelligence**: Smart inference using accounting principles
- **100% Accuracy**: Lookup-based categorization with historical pattern matching

### 5. StandardGikiAgent Financial Pattern (ADK v1.3.0 Compliant)
- **LlmAgent Inheritance**: All agents inherit from `google.adk.agents.LlmAgent`
- **FunctionTool Pattern**: All tools use `google.adk.tools.FunctionTool`
- **Agent Cards**: Provide A2A discovery with capability declarations
- **Financial Reliability**: Tools designed for 100% accuracy through lookup patterns
- **Cloud Provider Switching**: Runtime switching capability for outage resilience

## Financial Agent Hierarchy

The GIKI AI financial system is built on specialized agents for 100% reliable financial operations:

```
StandardGikiAgent (Base Class - extends google.adk.agents.LlmAgent)
├── CustomerAgent (Customer-facing conversational interface with UI equivalence)
├── SchemaInterpretationAgent (Intelligent Excel/CSV schema interpretation)
├── CategorizationAgent (100% lookup-based transaction categorization)
├── TransactionsAgent (Database transaction analysis and entity extraction)
├── GLCodeAgent (General Ledger code assignment and hierarchy management)
├── ReportsAgent (Financial reports with accounting integration)
├── DebitCreditAgent (Intelligent debit/credit inference using accounting principles)
├── OnboardingAgent (Customer onboarding with temporal accuracy validation)
└── CoordinatorAgent (Multi-agent orchestration for complex financial workflows)
```

## Agent-Tool Mapping Matrix (Optimized for Cognitive Efficiency)

Based on Google ADK best practices and minimal tool assignment principles:

| Agent | Core Tools (2-3 Max) | UI Equivalence Tools | Purpose Alignment |
|-------|----------------------|---------------------|-------------------|
| **CustomerAgent** (User-Facing Only) | `process_natural_query`<br>`transfer_to_agent`<br>`format_unified_response` | `navigate_to_page`<br>`upload_files_via_agent`<br>`generate_report_via_agent`<br>`search_transactions_via_agent` | Single interface for all user interactions |
| **CategorizationAgent** (Backend Only) | `lookup_transaction_category`<br>`get_category_taxonomy` | None | 100% lookup-based categorization |
| **SchemaInterpretationAgent** (Backend Only) | `interpret_excel_schema`<br>`infer_debit_credit_logic`<br>`validate_schema_mapping` | None | Excel/CSV processing |
| **TransactionsAgent** (Backend Only) | `extract_transaction_entities`<br>`analyze_transaction_patterns`<br>`calculate_accuracy_metrics` | None | Transaction analysis |
| **ReportsAgent** (Backend Only) | `generate_spending_report`<br>`create_visualization`<br>`export_report_data` | None | Report generation |
| **GLCodeAgent** (Backend Only) | `assign_gl_codes`<br>`create_category_hierarchy` | None | GL code management |
| **OnboardingAgent** (Backend Only) | `process_historical_files`<br>`run_temporal_validation`<br>`assess_production_readiness` | None | Customer onboarding |
| **CoordinatorAgent** (Backend Only) | `route_query_to_agent`<br>`coordinate_multi_agent_task`<br>`combine_agent_responses` | None | Multi-agent orchestration |

### Tool Assignment Anti-Patterns (Avoid These)
**Kitchen Sink Approach**: Giving all tools to all agents  
**Generic Tools Everywhere**: `google_search`, `memory_tools` in every agent  
**Tool Overlap**: Multiple agents with same tools  
**User-Facing Specialists**: Exposing backend agents to customers  
**Over-Tooling**: More than 5 tools per agent  

### Tool Assignment Best Practices (Follow These)
**Minimal Assignment**: 2-3 core tools per backend agent  
**Purpose Alignment**: Tools directly match agent's function  
**Single Interface**: Only CustomerAgent faces users  
**Specialized Roles**: Each agent has narrow, focused purpose  
**Hidden Orchestration**: Backend coordination invisible to users

### Agent Responsibilities by Financial Function
- **Schema Processing**: SchemaInterpretationAgent + DebitCreditAgent
- **Data Standardization**: TransactionsAgent (unified database schema)
- **Financial Categorization**: CategorizationAgent (lookup-only, 100% accuracy)
- **Accounting Integration**: GLCodeAgent (23 top-level structure mapping)
- **Customer Experience**: CustomerAgent (UI equivalence + natural language)
- **Onboarding & Validation**: OnboardingAgent (temporal accuracy, production readiness)
- **Complex Workflows**: CoordinatorAgent (multi-agent coordination)

## StandardGikiAgent Base Class (ADK v1.3.0 Compliant)

### Financial-Grade Implementation
```python
# apps/giki-ai-api/src/giki_ai_api/shared/ai/standard_giki_agent.py

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from typing import List, Dict, Optional
import logging

class StandardGikiAgent(LlmAgent):
    """
    Financial-grade base class for all Giki AI agents following ADK v1.3.0 patterns.
    Inherits from google.adk.agents.LlmAgent for official ADK compliance.
    Provides 100% reliability patterns for financial operations.
    """
    
    def __init__(
        self,
        name: str,
        model: str = "gemini-2.0-flash",
        instruction: str = "",
        description: str = "",
        tools: List[FunctionTool] = None,
        sub_agents: List[LlmAgent] = None,
        enable_code_execution: bool = False
    ):
        # Initialize with official ADK v1.3.0 pattern
        super().__init__(
            name=name,
            model=model,
            instruction=instruction,
            description=description,
            tools=tools or [],
            sub_agents=sub_agents or []
        )
        
        self.enable_code_execution = enable_code_execution
        self._financial_reliability_mode = True
        
        # Register financial-grade tools
        self._register_financial_tools()
        
    def _register_financial_tools(self):
        """Register core financial reliability tools for all agents"""
        financial_tools = [
            FunctionTool(func=self.lookup_historical_pattern),
            FunctionTool(func=self.verify_data_integrity),
            FunctionTool(func=self.log_financial_operation),
            FunctionTool(func=self.handle_provider_switching)
        ]
        self.tools.extend(financial_tools)
    
    async def financial_generate(
        self,
        prompt: str,
        context: Dict = None,
        require_100_percent_accuracy: bool = True
    ) -> Dict:
        """
        Financial-grade generation with 100% reliability requirement.
        Uses lookup/retrieval patterns instead of prediction for financial operations.
        """
        try:
            if require_100_percent_accuracy:
                # Use database lookup instead of generation for financial accuracy
                return await self._financial_lookup_operation(prompt, context)
            else:
                # Standard ADK generation for non-financial operations
                return await self.generate(prompt, context)
                
        except Exception as e:
            logger.error(f"Financial agent {self.name} operation failed: {e}")
            # In financial systems, errors must be transparent (no fallbacks)
            raise FinancialOperationError(f"Financial operation failed: {str(e)}")
    
    async def _financial_lookup_operation(self, prompt: str, context: Dict) -> Dict:
        """
        Financial operations use database lookup for 100% accuracy.
        Never use prediction-based AI for financial categorization.
        """
        # Implementation uses database services for lookup-based operations
        pass
```

### Required Methods for All Financial Agents
```python
def _register_tools(self):
    """Register agent-specific financial tools. Must be implemented by subclasses."""
    raise NotImplementedError
    
async def process_financial_query(self, query: str, context: Dict = None) -> Dict:
    """
    Process financial query with 100% reliability requirement.
    Uses lookup-based operations for financial accuracy.
    """
    raise NotImplementedError
    
async def lookup_historical_pattern(self, pattern_key: str, context: Dict) -> Dict:
    """
    Financial-grade lookup operation using database patterns.
    Required for 100% accuracy in financial categorization.
    """
    raise NotImplementedError
    
def get_agent_card(self) -> Dict:
    """
    Return A2A v0.2.2 compliant agent card for discovery.
    Enables opaque agent communication and capability discovery.
    """
    return {
        "agent_id": f"giki-ai-{self.name}",
        "name": self.name,
        "description": self.description,
        "capabilities": self._get_financial_capabilities(),
        "tools": [tool.name for tool in self.tools],
        "model": self.model,
        "reliability_grade": "financial_100_percent",
        "communication_protocol": "a2a_v0.2.2",
        "supported_providers": ["anthropic", "openai", "google", "custom"],
        "financial_operations": True,
        "database_operations": True,
        "lookup_accuracy": "100_percent"
    }

def _get_financial_capabilities(self) -> List[str]:
    """Return financial-specific capabilities for A2A discovery."""
    return [
        "database_lookup_operations",
        "100_percent_financial_accuracy", 
        "multi_provider_support",
        "outage_resilient_switching",
        "tenant_isolated_operations"
    ]
```

## Financial Agent Implementations

### CustomerAgent (Unified Interface with Complete UI Equivalence)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/intelligence/customer_agent.py`

**Purpose**: The ONLY user-facing agent - provides complete UI equivalence where customers can accomplish ANY workflow through conversation OR traditional UI. Backend agent orchestration is completely hidden.

**Core Principle**: Customers interact with ONE agent ("Giki") and are unaware of the multi-agent architecture underneath.

```python
class CustomerAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="customer_agent",
            model="gemini-2.0-flash",
            instruction="""You are a financial assistant for Giki AI with UI equivalence.
            Users can accomplish ANY workflow through conversation that they can do in the UI:
            - Upload and process files via chat
            - Generate reports with downloadable outputs
            - Navigate users to correct pages when tasks are ready
            - Display charts, tables, and data directly in chat
            All operations must use database lookup for 100% financial accuracy.""",
            description="Customer-facing conversational agent with complete UI equivalence",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
    
    def _register_tools(self):
        """Register MINIMAL customer-facing tools optimized for cognitive efficiency"""
        # Core tools (3 max) - Essential for unified interface
        self.tools.extend([
            FunctionTool(func=self.process_natural_query),           # Core conversation processing
            FunctionTool(func=self.transfer_to_agent),               # Backend delegation (hidden)
            FunctionTool(func=self.format_unified_response),         # Response formatting
        ])
        
        # UI Equivalence tools (4 max) - Direct UI action equivalents
        self.tools.extend([
            FunctionTool(func=self.navigate_to_page),                # UI navigation
            FunctionTool(func=self.upload_files_via_agent),          # File operations
            FunctionTool(func=self.generate_report_via_agent),       # Report generation
            FunctionTool(func=self.search_transactions_via_agent),   # Transaction search
        ])
```

**Core Tools (3 Max - Optimized for Cognitive Efficiency)**:
- `process_natural_query` - Intelligent query understanding and routing
- `transfer_to_agent` - Hidden backend delegation to specialists
- `format_unified_response` - Maintain single agent identity in responses

**UI Equivalence Tools (4 Max - Direct Action Equivalents)**:
- `navigate_to_page` - Guide users to specific UI pages ("Show me my dashboard")
- `upload_files_via_agent` - Complete file upload workflow via conversation
- `generate_report_via_agent` - Full report generation with export capabilities
- `search_transactions_via_agent` - Conversational transaction filtering and search

**Total Tools: 7 (Within efficiency guidelines)**

### SchemaInterpretationAgent (Excel/CSV Processing)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/files/schema_interpretation_agent.py`

**Purpose**: Intelligent interpretation of diverse Excel/CSV schemas with debit/credit inference

```python
class SchemaInterpretationAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="schema_interpretation_agent",
            model="gemini-2.0-flash",
            instruction="""You are a financial schema interpretation expert.
            Interpret diverse Excel/CSV formats automatically and map to unified schema.
            Infer debit/credit transaction direction using accounting principles.
            Handle regional banking terminology and cultural variations.
            Customers can correct but shouldn't have to - aim for 100% accuracy.""",
            description="Intelligent schema interpretation with debit/credit inference",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
    
    def _register_tools(self):
        """Register schema interpretation tools"""
        self.tools.extend([
            FunctionTool(func=self.interpret_excel_schema),
            FunctionTool(func=self.infer_debit_credit_logic),
            FunctionTool(func=self.map_to_unified_schema),
            FunctionTool(func=self.handle_regional_variations),
            FunctionTool(func=self.validate_schema_mapping),
            FunctionTool(func=self.suggest_column_corrections)
        ])
```

**Key Schema Tools**:
- `interpret_excel_schema` - Automatic interpretation of any Excel/CSV format
- `infer_debit_credit_logic` - Smart debit/credit detection using accounting principles
- `map_to_unified_schema` - Convert diverse formats to standardized transaction schema
- `handle_regional_variations` - Support regional banking terminology
- `validate_schema_mapping` - Ensure mapping accuracy before database storage
- `suggest_column_corrections` - Enable customer corrections when needed

### CategorizationAgent (100% Lookup-Based Categorization)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/categories/categorization_agent.py`

**Purpose**: 100% accurate transaction categorization using database lookup patterns (no prediction)

```python
class CategorizationAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="categorization_agent",
            model="gemini-2.0-flash",
            instruction="""You are a financial categorization expert using LOOKUP-ONLY operations.
            NEVER use prediction or ML-based categorization for financial transactions.
            Use database patterns and historical data for 100% accurate categorization.
            If no exact match exists, return 'no_match' instead of guessing.
            99% accuracy = 0% in financial systems.""",
            description="100% accurate lookup-based transaction categorization agent",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
    
    def _register_tools(self):
        """Register lookup-based categorization tools"""
        self.tools.extend([
            FunctionTool(func=self.lookup_transaction_category),
            FunctionTool(func=self.batch_lookup_categorization),
            FunctionTool(func=self.search_historical_patterns),
            FunctionTool(func=self.update_lookup_corpus),
            FunctionTool(func=self.validate_category_accuracy),
            FunctionTool(func=self.handle_no_match_scenarios)
        ])
```

**Core Tools (2-3 Max for Cognitive Efficiency)**:
- `lookup_transaction_category` - Database pattern matching (primary method)
- `get_category_taxonomy` - Retrieve tenant's category structure  
- `ai_bootstrap_category` - AI category creation (zero-onboarding only)

**Tool Logic Flow**:
1. **Always try lookup first** - Check for existing patterns in database
2. **If no match found AND tenant has historical data** - Return "no_match" (no guessing)
3. **If no match found AND zero-onboarding tenant** - Use AI bootstrap tool
4. **AI suggestions become lookup patterns** - Future transactions use database lookup
- `suggest_new_categories` - AI-driven category creation for unmatched transactions (zero-onboarding)
- `create_category_hierarchy` - Automatic hierarchy detection from naming patterns
- `consolidate_similar_categories` - Merge variations of similar categories

## Zero-Onboarding Categorization (New Companies)

### Enhanced CategorizationAgent (Zero-Onboarding Support)

When tenants have no historical data (new companies), the categorization agent must create categories from scratch using AI analysis and standard financial hierarchies.

**Zero-Onboarding Process**:
1. **Transaction Analysis**: AI analyzes transaction descriptions, merchant names, and amounts
2. **Category Suggestion**: AI suggests appropriate category names based on transaction context
3. **Hierarchy Creation**: Automatically detect parent-child relationships from naming patterns
4. **GL Integration**: Map new categories to standard 23 top-level financial structure
5. **Progressive Learning**: System learns from user corrections for future accuracy

**Hybrid Categorization Approach (Resolves Lookup vs AI Conflict)**:

The categorization agent operates in two distinct modes to resolve the conflict between "lookup-only" financial reliability and zero-onboarding requirements:

1. **Lookup Mode** (Companies with historical data):
   - Uses database pattern matching for 100% financial reliability
   - No AI prediction - only retrieval operations
   - Follows strict financial-grade requirements

2. **Bootstrap Mode** (Zero-onboarding companies):
   - Uses AI analysis when no historical patterns exist
   - Creates initial categories that become lookup patterns
   - Transitions to lookup mode as data accumulates

```python
def _register_categorization_tools(self):
    """Register tools based on tenant data availability"""
    # Core tools (2 max - cognitive efficiency)
    self.tools.extend([
        FunctionTool(func=self.lookup_transaction_category),         # Database lookup (primary)
        FunctionTool(func=self.get_category_taxonomy),               # Tenant categories
    ])
    
    # Bootstrap tool (only for zero-onboarding)
    if self.tenant_has_no_historical_data:
        self.tools.append(
            FunctionTool(func=self.ai_bootstrap_category)            # AI category creation
        )
```

**Category Creation Strategy**:
- **Merchant Analysis**: Extract business type from merchant names (e.g., "Starbucks" → "Food & Dining > Coffee Shops")
- **Amount Patterns**: Use transaction amounts to infer category types (e.g., recurring $500 → "Rent", $25 lunch → "Food")
- **Description Keywords**: Parse transaction descriptions for category clues
- **Standard Hierarchies**: Apply financial best practices for category structure
- **User Validation**: Present suggestions for user approval/modification

**Fallback Hierarchies**: When patterns aren't detected, use standard GL structure:
- Assets → Current Assets → Cash & Equivalents
- Liabilities → Current Liabilities → Accounts Payable  
- Equity → Retained Earnings
- Revenue → Sales Revenue → Product Sales
- Expenses → Operating Expenses → Office Supplies

**Google Sheets Integration**: Support external data sources like Nuvie data
- Fetch transaction data from Google Sheets API
- Apply same zero-onboarding categorization process
- Handle diverse data formats and schemas

### GLCodeAgent (General Ledger Integration)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/categories/gl_code_agent.py`

**Purpose**: Manage GL code assignments and 23 top-level financial reporting structure

```python
class GLCodeAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="gl_code_agent",
            model="gemini-2.0-flash",
            instruction="""You are a General Ledger expert managing GL code assignments.
            Map multi-level categories to standard 23 top-level financial reporting structure.
            Each category branch must have unique GL code identifier.
            Support rollup functionality for parent-child relationships.
            Ensure compatibility with accounting software (QuickBooks, SAP, Xero).""",
            description="GL code assignment and financial reporting structure management",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
    
    def _register_tools(self):
        """Register GL code management tools"""
        self.tools.extend([
            FunctionTool(func=self.assign_gl_codes),
            FunctionTool(func=self.create_category_hierarchy),
            FunctionTool(func=self.map_to_23_toplevel_structure),
            FunctionTool(func=self.calculate_rollup_values),
            FunctionTool(func=self.export_chart_of_accounts),
            FunctionTool(func=self.validate_gl_code_uniqueness)
        ])
```

**Key GL Tools**:
- `assign_gl_codes` - Assign unique GL codes to category branches
- `create_category_hierarchy` - Build multi-level category trees
- `map_to_23_toplevel_structure` - Map to standard financial reporting structure
- `calculate_rollup_values` - Calculate parent-child ledger rollups
- `export_chart_of_accounts` - Export to accounting software formats
- `validate_gl_code_uniqueness` - Ensure each branch has unique identifier

### TransactionsAgent (Transaction Analysis)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/transactions/agent.py`

**Purpose**: Transaction data analysis, entity extraction, pattern recognition

```python
class TransactionsAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="transactions_agent",
            model="gemini-2.0-flash",
            instruction="""You are a transaction analysis expert. Extract entities,
            identify patterns, and provide detailed analysis of financial transactions.
            Focus on accuracy and comprehensive data extraction.""",
            description="Specialist agent for transaction analysis and entity extraction",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
```

**Key Tools**:
- `extract_transaction_entities` - Merchant, location, type extraction
- `analyze_transaction_patterns` - Pattern recognition and anomaly detection
- `calculate_accuracy_metrics` - Categorization accuracy analysis
- `identify_duplicate_transactions` - Duplicate detection and handling
- `generate_transaction_insights` - Intelligent insights from transaction data

### FilesAgent (File Processing)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/files/agent.py`

**Purpose**: File upload processing, schema interpretation, data extraction

```python
class FilesAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="files_agent",
            model="gemini-2.0-flash",
            instruction="""You are a file processing expert specializing in financial data.
            Interpret file schemas, extract data accurately, and handle various formats
            including Excel, CSV, and banking exports.""",
            description="Specialist agent for file processing and schema interpretation",
            tools=[],
            enable_code_execution=False
        )
```

**Key Tools**:
- `interpret_file_schema` - AI-powered schema detection
- `extract_transaction_data` - Data extraction from files
- `validate_data_quality` - Data quality assessment
- `suggest_column_mappings` - Intelligent column mapping suggestions
- `handle_data_inconsistencies` - Error handling and data cleaning

### ReportsAgent (Report Generation)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/reports/agent.py`

**Purpose**: Intelligent report generation, data visualization, analytics

```python
class ReportsAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="reports_agent",
            model="gemini-2.0-flash",
            instruction="""You are a financial reporting expert. Generate comprehensive
            reports, create visualizations, and provide analytical insights from
            financial data. Focus on clarity and actionable information.""",
            description="Specialist agent for report generation and analytics",
            tools=[],
            enable_code_execution=False
        )
```

**Key Tools**:
- `generate_spending_reports` - Spending analysis reports
- `create_income_statements` - Financial statement generation
- `generate_trend_analysis` - Trend identification and reporting
- `create_custom_visualizations` - Chart and graph generation
- `export_report_data` - Data export in various formats

### CoordinatorAgent (Multi-Agent Orchestration)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/intelligence/coordinator_agent.py`

**Purpose**: Orchestrates multiple agents for complex workflows

```python
class CoordinatorAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="coordinator_agent",
            model="gemini-2.0-flash",
            instruction="""You are an agent coordinator responsible for orchestrating
            multiple specialized agents to complete complex tasks. Route queries to
            appropriate agents and combine their responses.""",
            description="Coordinator for multi-agent workflows",
            tools=[],
            enable_code_execution=False
        )
```

**Key Tools**:
- `route_query_to_agent` - Intelligent agent selection
- `coordinate_multi_agent_task` - Complex workflow orchestration
- `combine_agent_responses` - Response aggregation and synthesis
- `manage_agent_context` - Context sharing between agents
- `handle_agent_failures` - Error handling and fallback coordination

### OnboardingAgent (Customer Onboarding with Temporal Validation)
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/onboarding/agent.py`

**Purpose**: Manages customer onboarding workflow with temporal accuracy validation

```python
class OnboardingAgent(StandardGikiAgent):
    def __init__(self):
        super().__init__(
            name="onboarding_agent",
            model="gemini-2.0-flash",
            instruction="""You are a financial onboarding specialist responsible for
            guiding customers through the complete onboarding process. Ensure data is
            processed with 100% accuracy and validate temporal accuracy using progressive
            monthly testing. Follow the strict workflow: Files → Database → RAG → Validation → Production.""",
            description="Customer onboarding with temporal accuracy validation agent",
            tools=[],
            enable_code_execution=False
        )
        self._register_tools()
    
    def _register_tools(self):
        """Register onboarding-specific tools"""
        self.tools.extend([
            FunctionTool(func=self.process_historical_files),
            FunctionTool(func=self.validate_database_storage),
            FunctionTool(func=self.create_rag_corpus),
            FunctionTool(func=self.run_temporal_validation),
            FunctionTool(func=self.assess_production_readiness),
            FunctionTool(func=self.generate_onboarding_report)
        ])
```

**Key Financial Tools**:
- `process_historical_files` - Process Excel/CSV files with schema interpretation
- `validate_database_storage` - Ensure unified schema transformation success
- `create_rag_corpus` - Build Vertex AI RAG corpus from database patterns
- `run_temporal_validation` - Execute progressive monthly accuracy testing (July-Dec 2024)
- `assess_production_readiness` - Verify 100% accuracy requirement met
- `generate_onboarding_report` - Create comprehensive onboarding completion report

## Tool Function Patterns

### Tool Function Requirements
All tool functions must follow this pattern:

```python
async def tool_function_name(
    self,
    parameter1: str,
    parameter2: int = None,
    context: Dict = None
) -> Dict:
    """
    Tool function description for ADK registration.
    
    Args:
        parameter1: Description of parameter
        parameter2: Optional parameter description
        context: Agent execution context
        
    Returns:
        Dict with result data and metadata
    """
    try:
        # Use domain service (never direct database access)
        service = self._get_domain_service()
        result = await service.perform_operation(parameter1, parameter2)
        
        return {
            "success": True,
            "data": result,
            "metadata": {
                "tool_name": "tool_function_name",
                "execution_time": "0.5s",
                "confidence": 0.95
            }
        }
        
    except Exception as e:
        logger.error(f"Tool {tool_function_name} failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "metadata": {
                "tool_name": "tool_function_name",
                "error_type": type(e).__name__
            }
        }
```

### Tool → Service Integration Pattern
```python
# Tool functions MUST use domain services
class CategoryTools:
    def __init__(self, category_service: CategoryService):
        self.category_service = category_service
    
    async def categorize_transaction_tool(
        self,
        transaction_description: str,
        amount: float,
        context: Dict = None
    ) -> Dict:
        """Categorize a transaction using AI"""
        # Use shared service (no direct database access)
        result = await self.category_service.categorize_transaction(
            description=transaction_description,
            amount=amount
        )
        
        return {
            "success": True,
            "category_id": result.category_id,
            "category_name": result.category_name,
            "confidence": result.confidence,
            "reasoning": result.reasoning
        }
```

## Tool Categories by Domain

### Categories Domain Tools
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/categories/tools.py`

```python
# Category management tools
async def create_category_tool(name: str, gl_code: str, parent_id: int = None)
async def update_category_tool(category_id: int, updates: Dict)
async def delete_category_tool(category_id: int)
async def get_category_hierarchy_tool()
async def assign_gl_code_tool(category_id: int, gl_code: str)

# Categorization tools
async def categorize_single_transaction_tool(transaction_id: int)
async def categorize_batch_transactions_tool(transaction_ids: List[int])
async def suggest_category_tool(description: str, amount: float)
async def get_categorization_confidence_tool(transaction_id: int)
async def learn_from_feedback_tool(transaction_id: int, correct_category_id: int)
```

### Transactions Domain Tools
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/transactions/tools.py`

```python
# Transaction analysis tools
async def get_transactions_tool(filters: Dict, pagination: Dict)
async def analyze_spending_patterns_tool(date_range: Dict, categories: List[str])
async def extract_entities_tool(transaction_id: int)
async def calculate_accuracy_metrics_tool(date_range: Dict)
async def identify_anomalies_tool(transaction_ids: List[int])

# Transaction management tools
async def update_transaction_tool(transaction_id: int, updates: Dict)
async def merge_duplicate_transactions_tool(transaction_ids: List[int])
async def add_transaction_tags_tool(transaction_id: int, tags: List[str])
async def flag_transaction_tool(transaction_id: int, reason: str)
```

### Files Domain Tools
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/files/tools.py`

```python
# File processing tools
async def upload_file_tool(file_data: bytes, metadata: Dict)
async def interpret_schema_tool(file_id: str)
async def validate_data_quality_tool(file_id: str)
async def process_file_tool(file_id: str, column_mappings: Dict)
async def get_processing_status_tool(file_id: str)

# Schema interpretation tools
async def suggest_column_mappings_tool(file_id: str)
async def detect_date_format_tool(date_samples: List[str])
async def identify_currency_tool(amount_samples: List[str])
async def extract_sample_data_tool(file_id: str, num_rows: int)
```

### Reports Domain Tools
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/reports/tools.py`

```python
# Report generation tools
async def generate_spending_report_tool(date_range: Dict, grouping: str)
async def generate_income_statement_tool(date_range: Dict)
async def generate_trend_analysis_tool(metric: str, date_range: Dict)
async def create_custom_report_tool(configuration: Dict)
async def export_report_tool(report_id: str, format: str)

# Analytics tools
async def calculate_spending_trends_tool(categories: List[str], months: int)
async def identify_budget_variances_tool(budget: Dict, actual: Dict)
async def generate_insights_tool(data_scope: Dict)
async def create_visualization_tool(chart_type: str, data: Dict)
```

### Intelligence Domain Tools
**Location**: `apps/giki-ai-api/src/giki_ai_api/domains/intelligence/tools.py`

```python
# RAG and knowledge tools
async def query_rag_corpus_tool(query: str, context: Dict)
async def update_rag_corpus_tool(documents: List[Dict])
async def generate_insights_tool(data_scope: Dict)
async def answer_natural_language_query_tool(question: str, context: Dict)

# Audio processing tools
async def process_audio_input_tool(audio_data: bytes, format: str)
async def generate_audio_response_tool(text: str, voice_config: Dict)
async def transcribe_speech_tool(audio_data: bytes)

# Context management tools
async def store_conversation_context_tool(session_id: str, context: Dict)
async def retrieve_conversation_context_tool(session_id: str)
async def update_user_preferences_tool(user_id: str, preferences: Dict)
```

## Agent Communication Patterns

### Customer Query Processing Flow
```python
# 1. Customer Agent receives query
query = "Show me my dining expenses last month"

# 2. Agent analyzes query and selects appropriate tools
analysis = await customer_agent.analyze_query(query)
# -> Identifies need for transaction analysis

# 3. Agent calls transaction tools
transactions = await self.get_transactions_tool(
    filters={"category": "dining", "date_range": "last_month"}
)

# 4. Agent calls report tools
report = await self.generate_spending_report_tool(
    transactions=transactions.data
)

# 5. Agent generates natural language response
response = await customer_agent.enhanced_generate(
    prompt=f"Summarize dining expenses: {report.data}",
    context={"query": query, "user_preferences": user_prefs}
)
```

### Multi-Agent Coordination
```python
# Coordinator Agent orchestrating complex task
async def process_complex_query(self, query: str) -> Dict:
    """
    Example: "Categorize all my transactions and generate a spending report"
    """
    
    # 1. Route to Categories Agent for categorization
    categorization_result = await self.coordinate_with_agent(
        agent="categories_agent",
        task="categorize_all_transactions",
        data={"user_id": user_id}
    )
    
    # 2. Route to Reports Agent for report generation
    report_result = await self.coordinate_with_agent(
        agent="reports_agent", 
        task="generate_comprehensive_report",
        data={"transactions": categorization_result.data}
    )
    
    # 3. Combine results and generate response
    return await self.synthesize_responses([
        categorization_result,
        report_result
    ])
```

## Error Handling and Resilience

### Agent Error Handling
```python
async def handle_agent_error(self, error: Exception, context: Dict) -> Dict:
    """Standardized agent error handling"""
    
    if isinstance(error, ServiceError):
        # Business logic error - return structured error
        return {
            "success": False,
            "error_type": "SERVICE_ERROR",
            "message": str(error),
            "recoverable": True,
            "suggested_action": "retry_with_different_parameters"
        }
    
    elif isinstance(error, ValidationError):
        # Input validation error
        return {
            "success": False,
            "error_type": "VALIDATION_ERROR", 
            "message": str(error),
            "recoverable": True,
            "suggested_action": "correct_input_parameters"
        }
    
    else:
        # Unexpected error - log and return generic error
        logger.error(f"Unexpected agent error: {error}", extra=context)
        return {
            "success": False,
            "error_type": "UNEXPECTED_ERROR",
            "message": "An unexpected error occurred",
            "recoverable": False,
            "suggested_action": "contact_support"
        }
```

### Tool Failure Recovery
```python
async def tool_with_retry(self, tool_func, max_retries: int = 3, **kwargs):
    """Execute tool with automatic retry logic"""
    
    for attempt in range(max_retries):
        try:
            result = await tool_func(**kwargs)
            if result.get("success"):
                return result
                
        except Exception as e:
            if attempt == max_retries - 1:
                # Final attempt failed
                raise e
            
            # Wait before retry (exponential backoff)
            await asyncio.sleep(2 ** attempt)
    
    raise ServiceError("Tool failed after maximum retries")
```

## Performance Optimization

### Agent Response Time Targets
- **Simple queries**: <2 seconds
- **Complex analysis**: <10 seconds  
- **Report generation**: <15 seconds
- **File processing**: <30 seconds

### Optimization Strategies
```python
# 1. Parallel tool execution for independent operations
async def parallel_tool_execution(self, tools_and_params: List[Tuple]):
    """Execute multiple tools in parallel when possible"""
    
    tasks = [
        tool_func(**params) 
        for tool_func, params in tools_and_params
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return self.process_parallel_results(results)

# 2. Response streaming for long operations
async def stream_response(self, operation: str, **kwargs):
    """Stream partial results for long-running operations"""
    
    async for partial_result in self.execute_streaming_operation(operation, **kwargs):
        yield {
            "type": "partial_result",
            "data": partial_result,
            "progress": partial_result.get("progress", 0)
        }
    
    yield {
        "type": "final_result",
        "data": final_result,
        "progress": 100
    }

# 3. Context caching for repeated queries
@lru_cache(maxsize=128)
async def cached_agent_response(self, query_hash: str, context_hash: str):
    """Cache agent responses for identical queries"""
    return await self.generate_response(query, context)
```

## Testing Strategy

### Agent Testing Patterns
```python
# Unit tests for individual agents
async def test_categories_agent_categorization():
    agent = CategoriesAgent()
    
    result = await agent.categorize_single_transaction_tool(
        transaction_description="STARBUCKS #1234",
        amount=-5.75
    )
    
    assert result["success"] is True
    assert result["category_name"] == "Dining"
    assert result["confidence"] > 0.8

# Integration tests for agent workflows
async def test_customer_agent_spending_query():
    agent = CustomerAgent()
    
    response = await agent.process_query(
        query="How much did I spend on dining last month?",
        context={"user_id": 1, "tenant_id": 1}
    )
    
    assert "total" in response["data"]
    assert "dining" in response["response"].lower()
    assert len(response["data"]["transactions"]) > 0

# Multi-agent coordination tests
async def test_coordinator_complex_workflow():
    coordinator = CoordinatorAgent()
    
    result = await coordinator.process_complex_query(
        "Categorize my transactions and show spending by category"
    )
    
    assert result["categorization_complete"] is True
    assert result["report_generated"] is True
    assert len(result["spending_by_category"]) > 0
```

### Tool Testing
```python
# Tool function tests
async def test_categorization_tool():
    tool = CategoryTools(category_service=mock_service)
    
    result = await tool.categorize_transaction_tool(
        transaction_description="AMAZON.COM PURCHASE",
        amount=-25.99
    )
    
    assert result["success"] is True
    assert result["category_id"] is not None
    assert 0.0 <= result["confidence"] <= 1.0
```

## Security and Privacy

### Agent Security Requirements
- All agents operate within tenant boundaries
- No cross-tenant data access possible
- Agent memory is session-scoped only
- No persistent storage of conversation data
- All tool calls logged for audit

### Privacy Protection
```python
# Data anonymization for AI processing
def anonymize_for_ai(self, data: Dict) -> Dict:
    """Remove PII before sending to AI models"""
    
    anonymized = data.copy()
    
    # Remove sensitive fields
    sensitive_fields = ["ssn", "account_number", "routing_number"]
    for field in sensitive_fields:
        if field in anonymized:
            anonymized[field] = "[REDACTED]"
    
    # Hash identifying information
    if "email" in anonymized:
        anonymized["email"] = hashlib.sha256(
            anonymized["email"].encode()
        ).hexdigest()[:8]
    
    return anonymized
```

## Monitoring and Observability

### Agent Performance Metrics
- Response times per agent type
- Tool execution success rates
- User satisfaction scores
- Error rates and types
- Token usage and costs

### Logging Pattern
```python
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

async def log_agent_interaction(
    self,
    agent_name: str,
    query: str,
    response: Dict,
    execution_time: float,
    context: Dict = None
):
    """Structured logging for agent interactions"""
    
    log_data = {
        "event": "agent_interaction",
        "agent": agent_name,
        "query_hash": hashlib.sha256(query.encode()).hexdigest()[:8],
        "response_success": response.get("success", False),
        "execution_time": execution_time,
        "token_usage": response.get("metadata", {}).get("token_usage", 0),
        "tools_used": response.get("metadata", {}).get("tools_used", []),
        "tenant_id": context.get("tenant_id") if context else None
    }
    
    logger.info("Agent interaction completed", extra=log_data)
```

## RAG Implementation Details

### Vertex AI RAG Integration
The system uses Vertex AI RAG (Retrieval-Augmented Generation) for enhanced categorization accuracy by retrieving similar historical transactions as context.

### RAG Corpus Creation
```python
# Onboarding Service creates Vertex AI RAG corpus
corpus_resource_name = await self.vertex_client.create_rag_corpus(
    display_name=corpus_display_name
)
await self.vertex_client.upload_to_gcs(
    local_content_str=jsonl_content,
    gcs_uri=gcs_uri
)
await self.vertex_client.import_rag_files(
    corpus_name=corpus_resource_name,
    gcs_uris=[gcs_uri],
    chunk_size=settings.RAG_CHUNK_SIZE,
    chunk_overlap=settings.RAG_CHUNK_OVERLAP
)
```

### RAG-Enhanced Categorization
```python
# Categorization Agent with RAG context retrieval
contexts = await vertex_client.retrieve_contexts(
    corpus_name=corpus_status.corpus_name,
    query_text=transaction_description,
    top_k=top_n
)

# Agent uses retrieved context for better accuracy
agent_config = AgentConfig(
    model_name="gemini-2.0-flash-exp",
    rag_enabled=True,
    project=self.vertex_client.project_id,
    location=self.vertex_client.location
)
categorization_agent = CategorizationAgent(config=agent_config)
result = await categorization_agent.categorize_transaction_with_details(
    transaction_details, 
    rag_contexts=contexts
)
```

### RAG Infrastructure Requirements
- **GCS Bucket**: `giki-ai-gcs` for storing corpus data
- **Vertex AI RAG API**: Must be enabled in GCP project
- **Service Account**: Must have GCS and Vertex AI permissions
- **Async Vertex Client**: Proper initialization via `get_vertex_client()` dependency

### RAG Workflow
1. **Onboarding**: Customer transaction data indexed in Vertex AI RAG corpus
2. **Categorization**: New transactions retrieve similar historical examples
3. **Context Enhancement**: Agent uses retrieved context for better decisions
4. **Accuracy Improvement**: Customer-specific patterns improve categorization

## Future Enhancements

### Planned Agent Capabilities
1. **Learning Agents**: Continuous improvement from user feedback
2. **Predictive Agents**: Financial forecasting and budgeting
3. **Compliance Agents**: Automated regulatory reporting
4. **Integration Agents**: Third-party service connections
5. **Advisory Agents**: Financial planning and recommendations

### Emerging ADK Features
- Advanced tool chaining capabilities
- Cross-agent memory sharing
- Improved context management
- Enhanced error recovery
- Real-time collaboration between agents
