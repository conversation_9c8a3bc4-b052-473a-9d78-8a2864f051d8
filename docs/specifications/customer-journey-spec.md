# Customer Journey Specification

---
status: active
last-updated: 2025-06-25
update-frequency: monthly
update-triggers: workflow changes, customer feedback, journey updates
---

**Version**: 1.0  
**Status**: Active  
**Date**: January 2025

## Overview

This document defines the complete customer journey for the Giki AI financial transaction categorization platform. It clearly distinguishes between the two primary workflows: **Onboarding with Historical Data** and **Production Transaction Processing**.

## Key Concepts

### Two Distinct Workflows

1. **Onboarding Workflow** (Phase 1)
   - Purpose: Train the AI on customer's historical categorization patterns
   - Data: Historical transactions WITH category labels
   - Goal: Achieve >85% baseline accuracy before production

2. **Production Workflow** (Phase 2)
   - Purpose: Process new transactions using trained AI
   - Data: New transactions WITHOUT categories
   - Goal: Provide intelligent categorization for business insights

### Critical Distinction

- **Baseline Accuracy Measurement**: Comparing AI predictions against customer's original labels during onboarding
- **Runtime Categorization**: AI categorizing new transactions in production based on learned patterns

## Phase 1: Onboarding Journey

### Step 1: Welcome & Education
**Route**: `/onboarding`  
**Purpose**: Explain the importance of historical data for training

**User Experience**:
```
Welcome to Giki AI! 

To provide accurate categorization for your business, we need to learn from 
your historical transaction patterns. Please prepare 12+ months of transaction 
data that ALREADY includes your category labels.

Why? Your historical categorization patterns are unique to your business. 
By learning from your past decisions, we can accurately categorize future 
transactions automatically.
```

### Step 2: Historical Data Upload
**Route**: `/onboarding/upload`  
**API**: `POST /api/v1/onboarding/upload-historical-data`  
**Requirements**: 
- Files MUST contain category/label columns
- Minimum 12 months of data recommended
- Supported formats: Excel (.xlsx), CSV

**Validation**:
```python
if not has_category_columns(uploaded_file):
    raise ValidationError(
        "Historical data must include category labels for training. "
        "Please ensure your file has a column with your existing categories."
    )
```

### Step 3: Schema Interpretation
**Process**: AI interprets column mappings  
**Key Mapping**: `original_category` field must be identified

**Example Mapping**:
```json
{
    "date_column": "Transaction Date",
    "description_column": "Description",
    "amount_column": "Amount",
    "category_column": "Category",  // CRITICAL for training
    "vendor_column": "Merchant"
}
```

### Step 4: Database Storage
**Action**: Store transactions with original categories preserved

**Database Fields**:
```sql
-- transactions table
id, date, description, amount, vendor, 
original_category,  -- Customer's historical category
ai_category,        -- Will be populated during validation
upload_type,        -- 'onboarding'
tenant_id
```

### Step 5: RAG Corpus Building
**Process**: Build knowledge base from labeled transactions  
**API**: `POST /api/v1/onboarding/build-rag-corpus`

**Corpus Structure**:
```json
{
    "patterns": [
        {
            "description": "STARBUCKS #1234",
            "category": "Food & Dining",
            "confidence": 1.0,
            "source": "historical_data"
        }
    ]
}
```

### Step 6: Temporal Validation
**Purpose**: Measure baseline accuracy month-by-month  
**API**: `POST /api/v1/onboarding/validate-temporal-accuracy`

**Process**:
1. Use Jan-Jun data to train
2. Test on July data, measure accuracy
3. Add July to training, test on August
4. Continue through December
5. Must achieve >85% average accuracy

**Accuracy Calculation**:
```python
accuracy = (correct_predictions / total_transactions) * 100
where:
    correct_predictions = count where ai_category == original_category
```

### Step 7: Production Approval
**Criteria**: 
- Temporal validation shows >85% accuracy
- RAG corpus successfully built
- All required data processed

**API**: `POST /api/v1/onboarding/approve-for-production`

## Phase 2: Production Journey

### Step 1: New Transaction Upload
**Route**: `/upload`  
**API**: `POST /api/v1/files/upload-production-data`  
**Requirements**: 
- Files must NOT contain category columns
- Can be daily, weekly, or monthly uploads

**Validation**:
```python
if has_category_columns(uploaded_file):
    raise ValidationError(
        "Production files should not include categories. "
        "The AI will categorize these transactions based on your historical patterns."
    )
```

### Step 2: AI Categorization
**Process**: Use trained RAG corpus to categorize  
**Method**: Pattern matching against historical data

**Flow**:
1. Extract transaction features (description, amount, date)
2. Search RAG corpus for similar patterns
3. Apply matching category with confidence score
4. Flag low-confidence items for review

### Step 3: Review & Correction
**Route**: `/review`  
**Purpose**: Allow users to verify and correct AI categorizations

**Features**:
- See AI-assigned categories with confidence scores
- Bulk approve high-confidence categorizations
- Manually correct misclassified transactions
- Corrections improve future accuracy

### Step 4: Report Generation
**Route**: `/reports`  
**Purpose**: Generate financial insights from categorized data

**Available Reports**:
- Spending by category
- GL code mapping reports
- Trend analysis
- Budget vs actual
- Custom financial statements

### Step 5: Continuous Learning
**Process**: User corrections update RAG corpus  
**Note**: This happens transparently in background

**Update Flow**:
```python
if user_corrects_category:
    # Add correction to feedback loop
    add_to_rag_corpus(
        description=transaction.description,
        category=user_selected_category,
        source="user_correction"
    )
```

## User Interface Journeys

### Onboarding UI Flow
```
1. Welcome Screen
   "Let's train your AI assistant with your historical data"
   
2. File Upload
   "Upload 12+ months of categorized transactions"
   [Drag & drop Excel/CSV files]
   
3. Column Mapping Confirmation
   "We've identified these columns:"
   - Date: [Transaction Date]
   - Amount: [Amount]
   - Category: [Category] ← Required!
   
4. Processing Status
   "Building your personalized AI model..."
   [Progress: 45%]
   
5. Accuracy Validation
   "Testing accuracy month by month..."
   July: 87% ✓
   August: 89% ✓
   September: 91% ✓
   
6. Completion
   "Your AI is ready! Average accuracy: 88.5%"
   [Start Using Giki AI]
```

### Production UI Flow
```
1. Dashboard
   "Welcome back! Upload new transactions to categorize"
   
2. File Upload
   "Upload uncategorized transactions"
   [Drag & drop Excel/CSV files]
   
3. AI Processing
   "Categorizing 1,247 transactions..."
   [Progress: 78%]
   
4. Review Results
   "AI Categorization Complete"
   - High confidence: 1,189 (95%)
   - Needs review: 58 (5%)
   [Review Low Confidence] [Approve All]
   
5. Reports
   "Your financial insights are ready"
   [View Reports] [Export Data]
```

## API Journey Mapping

### Onboarding APIs (Sequential)
1. `GET /api/v1/onboarding/status` - Check onboarding progress
2. `POST /api/v1/onboarding/upload-historical-data` - Upload labeled data
3. `POST /api/v1/onboarding/build-rag-corpus` - Create knowledge base
4. `POST /api/v1/onboarding/validate-temporal-accuracy` - Test accuracy
5. `POST /api/v1/onboarding/approve-for-production` - Enable production

### Production APIs (Repeatable)
1. `POST /api/v1/files/upload-production-data` - Upload new transactions
2. `GET /api/v1/transactions?status=pending_review` - Get categorized data
3. `PUT /api/v1/transactions/{id}/category` - Correct categories
4. `GET /api/v1/reports/financial-summary` - Generate reports

## Error Handling & Edge Cases

### Common Onboarding Errors
1. **No Category Column**
   - Error: "Historical data must include categories"
   - Solution: Guide user to correct file format

2. **Insufficient Data**
   - Error: "Need at least 6 months of data"
   - Solution: Request more historical data

3. **Low Accuracy**
   - Error: "Accuracy below 85% threshold"
   - Solution: Review category consistency, add more data

### Common Production Errors
1. **Categories in Production File**
   - Error: "Production files should not have categories"
   - Solution: Remove category column before upload

2. **Unknown Transaction Pattern**
   - Action: Flag for manual review
   - Solution: User categorizes, system learns

## Success Metrics

### Onboarding Success
- Files uploaded successfully
- RAG corpus built with 1000+ patterns
- Temporal validation >85% accuracy
- Customer approves for production

### Production Success
- 95%+ transactions categorized with high confidence
- <5% require manual review
- Report generation in <2 seconds
- Customer satisfaction with accuracy

## Technical Implementation Notes

### Database Flags
```sql
-- uploads table
upload_type ENUM('onboarding', 'production')
has_category_labels BOOLEAN
baseline_accuracy DECIMAL(5,2)

-- transactions table  
original_category VARCHAR(255)  -- Only populated for onboarding
ai_category VARCHAR(255)        -- Always populated
confidence_score DECIMAL(3,2)   -- 0.00 to 1.00
needs_review BOOLEAN
```

### Service Separation
- `OnboardingService` - Handles historical data and training
- `CategorizationService` - Handles production categorization
- `AccuracyService` - Measures and tracks accuracy metrics

### Frontend Route Guards
```typescript
// Ensure onboarding is complete before accessing production
if (!tenant.onboarding_completed && route.path.startsWith('/upload')) {
    redirect('/onboarding');
}
```

## Migration Path for Existing Customers

For customers already using the system:
1. Run historical data through onboarding flow
2. Preserve any existing categorizations
3. Build RAG corpus from their data
4. Validate accuracy before switching to new flow

## Conclusion

This customer journey specification creates a clear distinction between:
- **Training Phase**: Learning from historical categorized data
- **Production Phase**: Categorizing new uncategorized data

By following this journey, customers understand exactly what data to provide at each stage, and the system maintains clear separation between baseline accuracy measurement and runtime categorization.