# Documentation Structure

**PURPOSE**: This describes the core documentation organization. The structure evolves with project needs.

## Current Documentation Structure
```
docs/
├── README.md                      # This file - structure documentation
├── requirements/                  # Business requirements and user stories
│   ├── requirements.md           # Main requirements document (BR-XXX definitions)
│   └── user-stories.md           # User stories in standard format (US-XXX)
│
├── specifications/                # Technical specifications and system design  
│   ├── system-design-spec.md     # Overall architecture and tech stack
│   ├── frontend-spec.md          # UI/UX specifications and components
│   ├── contracts-spec.md         # API endpoints and data contracts
│   ├── agents-and-tools-spec.md  # Agent architecture and tool definitions
│   ├── build-and-deploy-specification.md  # CI/CD and deployment specs
│   └── customer-journey-spec.md  # User workflow specifications
│
├── audits/                       # Living status documents (no date-specific files)
│   ├── README.md                # Audit process documentation
│   ├── performance-metrics.md   # Current API performance vs targets
│   ├── security-status.md       # Current vulnerabilities and security posture
│   ├── quality-metrics.md       # Test coverage, lint status, code quality
│   ├── functional-status.md     # Feature completion and AI accuracy
│   └── audit-history.md         # Historical findings and lessons learned
│
├── milestones/                   # Milestone tracking and progress dashboards
│   └── milestone-dashboard.md    # Current milestone progress (M1, M2, M3)
│
└── tasks/                        # Task management files
    ├── required-tasks.md         # Full backlog of all tasks (TASK-XXX)
    ├── active-tasks.md           # Current sprint tasks
    ├── completed-tasks.md        # Historical completed tasks
    ├── quality-gates.md          # Task quality criteria
    └── breakdowns/               # CRITICAL: Two-layer TODO system breakdowns
        ├── TASK-*-breakdown.md   # Detailed subtask breakdowns for each major task
```

## Documentation Guidelines
1. **Avoid date-specific files** - Use living documents that update over time
2. **Focus on permanent content** - Session summaries belong elsewhere
3. **Maintain organization** - Keep related content in appropriate subdirectories
4. **Evolve as needed** - Add new structures when project requirements change

## Key Subdirectories

**tasks/breakdowns/** 
- Detailed breakdown files for each major task
- Implements the TODO → Task → Breakdown TODO workflow
- Each breakdown contains subtasks, dependencies, and success criteria
- Essential for milestone-driven development (M1/M2/M3)

**Additional directories may be added as project needs evolve**

## What Each File Contains

**requirements/**
- `requirements.md`: BR-XXX business requirements with status tracking
- `user-stories.md`: US-XXX user stories linking to requirements

**specifications/**
- `system-design-spec.md`: Architecture, tech stack, deployment
- `frontend-spec.md`: Components, UI patterns, user experience
- `contracts-spec.md`: API endpoints, request/response formats
- `agents-and-tools-spec.md`: Agent implementation details
- `build-and-deploy-specification.md`: Docker, CI/CD, deployment
- `customer-journey-spec.md`: User workflows and interactions

**audits/**
- `README.md`: Audit process and methodology
- `performance-metrics.md`: Live API response times vs targets
- `security-status.md`: Current vulnerabilities and remediation
- `quality-metrics.md`: Test coverage, linting, code quality
- `functional-status.md`: Feature completion and accuracy tracking
- `audit-history.md`: Historical findings and improvement cycles

**milestones/**
- `milestone-dashboard.md`: Current milestone progress tracking (M1: Nuvie, M2: Rezolve, M3: giki.ai)

**tasks/**
- `required-tasks.md`: All tasks with RICE scores and priorities
- `active-tasks.md`: Current sprint work with status updates
- `completed-tasks.md`: Done tasks for historical reference
- `quality-gates.md`: Definition of done and quality criteria

## Examples of FORBIDDEN Files
- ❌ `audit-20250624-comprehensive.md` - Date-specific audit
- ❌ `session-summary-2025-06-24.md` - Session documentation
- ❌ `consolidation-report-2025-06-24.md` - Date-specific report
- ❌ `deployment-guide.md` - Should be in specifications/
- ❌ `architecture-decisions.md` - Should be in specifications/
- ❌ `INTEGRATION-TEST-FINDINGS-2025-06-15.md` - Date-specific finding

## How to Update Documentation
1. **Requirements change**: Update existing requirements.md or user-stories.md
2. **New specification**: Update relevant spec file (never create new ones)
3. **Audit findings**: Add to audit-history.md or update relevant audit file
4. **Task updates**: Modify active-tasks.md, move between files as needed

**Any deviation from this structure requires CLAUDE.md update first.**

## Metadata Headers (Living Documentation)

All documentation files include YAML metadata headers:

```yaml
---
status: active|deprecated|draft
last-updated: YYYY-MM-DD
update-frequency: daily|weekly|monthly|as-needed
update-triggers: [list of events that should trigger updates]
---
```

**Status Values:**
- `active`: Document is current and maintained
- `deprecated`: Document is outdated, should not be referenced
- `draft`: Document under development

**Update Frequencies:**
- `daily`: Critical operational docs (active-tasks.md, performance-metrics.md)
- `weekly`: Regular tracking docs (security-status.md, functional-status.md)
- `monthly`: Stable specifications and processes
- `as-needed`: Reference docs that change infrequently

**Common Triggers:**
Task changes, API modifications, performance tests, security scans, feature completions, infrastructure changes