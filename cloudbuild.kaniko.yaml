# Ultra-fast Kaniko build for maximum speed (15-45 seconds typical)
# Kaniko provides superior caching compared to standard Dock<PERSON> builds

substitutions:
  _REGION: us-central1
  _SERVICE_NAME: giki-ai-api
  _REGISTRY: us-central1-docker.pkg.dev
  _REPO: giki-ai

steps:
  # Build with <PERSON>nik<PERSON> for maximum caching efficiency
  - name: 'gcr.io/kaniko-project/executor:latest'
    args:
      - --dockerfile=Dockerfile.fast
      - --destination=${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:${SHORT_SHA}
      - --destination=${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:latest
      - --cache=true
      - --cache-ttl=72h
      - --cache-repo=${_REGISTRY}/${PROJECT_ID}/${_REPO}/cache
      - --skip-unused-stages=true
      - --use-new-run=true
      - --push-retry=3
    id: 'kaniko-build'

  # Deploy to Cloud Run with zero-downtime
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image=${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:${SHORT_SHA}'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--add-cloudsql-instances=${PROJECT_ID}:${_REGION}:giki-ai-postgres-prod'
      - '--set-env-vars=DATABASE_URL=postgresql+asyncpg://giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm@/giki_ai_db?host=/cloudsql/${PROJECT_ID}:${_REGION}:giki-ai-postgres-prod,ENVIRONMENT=production'
      - '--service-account=dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--concurrency=100'
      - '--timeout=300'
      - '--max-instances=10'
      - '--no-traffic'
    id: 'deploy'
    waitFor: ['kaniko-build']

  # Update traffic to new revision
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}'
      - '--to-latest'
      - '--region=${_REGION}'
    id: 'update-traffic'
    waitFor: ['deploy']

# High-performance build options
options:
  machineType: 'E2_HIGHCPU_32'  # Maximum CPU for fastest builds
  diskSizeGb: 200
  logging: CLOUD_LOGGING_ONLY

# Aggressive timeout (should complete in under 2 minutes)
timeout: '300s'