# Contributing to giki.ai

Welcome! This guide explains how to effectively contribute to the giki.ai project using our development system and Claude Code.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- pnpm 8+
- Python 3.12+
- uv (Python package manager)
- <PERSON> Code CLI

### Initial Setup
```bash
# 1. Clone the repository
git clone [repository-url]
cd giki-ai-workspace

# 2. Run the work command in Claude to get started
/project:work

# Or manually setup:
pnpm install
cd apps/giki-ai-api && uv sync
pnpm dev
```

## 📋 Development Workflow

### 1. Start Your Session
```bash
# In Claude Code, always start with:
/project:work

# This will:
# - Check active tasks
# - Show system status
# - Identify highest priority work
# - Set up your development environment
```

### 2. Understanding the Task System

Our development follows a strict pipeline:
```
Requirements → Specifications → Tasks → Development → Shipping → Audits
```

**Task Files**:
- `docs/tasks/active-tasks.md` - Current sprint work
- `docs/tasks/required-tasks.md` - Full backlog (150+ tasks)
- `docs/tasks/completed-tasks.md` - Finished work

**Task Priority**: We use RICE scoring (Reach × Impact × Confidence ÷ Effort)
- Always work on the highest RICE score MUST HAVE task
- Never skip to lower priority tasks

### 3. Development Process

#### Before Writing Code
```bash
# 1. Search for existing implementations
grep "feature_name" apps/

# 2. Read relevant specifications
@docs/specifications/[relevant-spec].md

# 3. Check for similar patterns
@domains/*/service.py
```

#### Writing Code
- **Rule #6**: Only modify production code in `apps/`
- Never create standalone scripts or demos
- Follow existing patterns in the codebase
- Reuse components when possible

#### Testing
```bash
# Find existing tests first
rg "test.*function_name" --type py
rg "describe.*ComponentName" --type ts

# Update tests before creating new ones
# Run tests
pnpm test
```

### 4. Quality Standards

**Before Every Commit**:
```bash
pnpm lint         # Must show "All files linted successfully"
pnpm test         # All tests must pass
pnpm cleanup      # Clean workspace
```

**Definition of Done**:
- [ ] Users can accomplish their goals
- [ ] No errors in normal workflows
- [ ] Data flows through entire pipeline
- [ ] E2E tests pass
- [ ] Deployed to production
- [ ] Verified with Playwright

### 5. Deployment

```bash
# Use the ship command
/project:ship

# This handles:
# - Build verification
# - Quality checks
# - Git commit/push
# - Production deployment
# - Playwright validation
```

## 🛠️ Key Commands

### Slash Commands (4 Intelligent Commands Only)
- `/project:work` - Comprehensive development (includes health checks, cleanup, performance debugging)
- `/project:ship` - Smart deployment (includes risk analysis, validation, rollback planning)
- `/project:audit` - Deep quality verification (includes visual testing, pattern analysis)
- `/project:consolidate` - Intelligent task management (includes TODO absorption, pattern recognition)

**Note**: These 4 commands adapt to any scenario - no additional commands needed

### Manual Commands
```bash
# Development
pnpm dev              # Start both servers
pnpm dev:api          # Backend only (:8000)
pnpm dev:app          # Frontend only (:4200)

# Testing
pnpm test             # All tests
./scripts/run-e2e-tests-autonomous.sh  # E2E with logging

# Deployment
pnpm deploy           # Full deployment
```

## 📐 Architecture Overview

```
Frontend (React) → Backend (FastAPI) → Database (PostgreSQL)
                          ↓
                    Vertex AI (RAG/LLM)
```

**Key Directories**:
- `apps/giki-ai-api/` - Backend API
- `apps/giki-ai-app/` - Frontend application
- `docs/` - Documentation and specifications
- `scripts/` - Utility scripts
- `.claude/commands/` - Custom slash commands

## 🔍 Common Patterns

### Finding Code
```bash
# Search by pattern
grep "upload.*file" apps/

# Find by file type
find . -name "*.service.ts"

# Read multiple files
@file1.py
@file2.py
@file3.py
# All files accessible via @ syntax
```

### Task Management
```bash
# Check your current work
@docs/tasks/active-tasks.md

# Update task status
# In active-tasks.md:
Status: Active:claude:2024-01-15-10:30
# When done:
Status: Completed (2024-01-15 14:30)
```

### Performance Debugging
```bash
# Use the work command for performance issues
/project:work
# The command will detect performance issues and debug them

# Or check manually
curl -w '%{time_total}' http://localhost:8000/api/v1/health
tail -f logs/api-server.log | grep "SLOW"
```

## ⚡ Performance Requirements

All endpoints must meet these targets:
- API Health: <200ms
- Authentication: <200ms
- Dashboard Load: <2s
- File Upload: <5s
- Transaction List: <500ms

## 🚨 Important Rules

1. **Always check active tasks first** - Never start random work
2. **Search before creating** - Reuse existing code
3. **No isolated scripts** - Only production code
4. **Zero warnings policy** - Lint must pass
5. **Work on master branch** - No feature branches
6. **Test everything** - Update tests for your changes
7. **Clean commits** - Clear messages referencing tasks

## 🔧 Troubleshooting

### API Running Slow
```bash
# Check database connections
tail -f logs/api-server.log | grep "connection"

# Run performance tests
./scripts/run-e2e-tests-autonomous.sh tests/e2e/auth-performance.spec.ts
```

### Port Already in Use
```bash
# Clean up processes
./scripts/cleanup-test-processes.sh

# Or manually
lsof -ti:8000 | xargs kill -9
lsof -ti:4200 | xargs kill -9
```

### Tests Failing
```bash
# Fix lint issues
pnpm lint --fix

# Run specific test
pnpm exec playwright test [test-name] --debug
```

## 📚 Documentation

- **CLAUDE.md** - Complete development guide and rules
- **Task Specifications** - `docs/specifications/`
- **Requirements** - `docs/requirements/`
- **Architecture** - `docs/architecture/`

## 🤝 Getting Help

1. Check CLAUDE.md for detailed rules and patterns
2. Review existing code for examples
3. Look at completed tasks for patterns
4. Use `/project:work` to diagnose and fix issues

## 🎯 Best Practices

### For Efficient Development
1. **Use parallel operations** - Read multiple files at once
2. **Build autonomous systems** - Create self-sufficient tools
3. **Follow the pipeline** - Requirements → Tasks → Code
4. **Maintain quality** - Lint, test, document
5. **Think before coding** - Understand the full context

### For Claude Code Users
1. **Start with slash commands** - They encode best practices
2. **Let Claude see errors** - Don't hide output
3. **Provide full context** - Share logs and errors
4. **Use Rule #20** - Build systems that provide visibility

## 📝 Example Contribution Flow

```bash
# 1. Start session
/project:work

# 2. Claude checks active tasks and picks highest priority
# Example: TASK-AUTH-001: Fix login performance

# 3. Search for relevant code
grep "login" apps/giki-ai-api/src/domains/auth/

# 4. Make changes following patterns

# 5. Test thoroughly
pnpm test:api -- auth

# 6. Deploy
/project:ship

# 7. Verify in production
# Claude uses Playwright to test the deployed fix

# 8. Run quality audit
/project:audit
```

---

Remember: The system is designed for autonomous operation. When you hit obstacles, research solutions and build tools that provide complete visibility rather than working with limited information. This is the key to effective development with Claude Code.