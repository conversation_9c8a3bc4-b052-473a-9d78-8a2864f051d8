# This file specifies files that are *not* uploaded to Google Cloud
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).
#
# For more information, run:
#   $ gcloud topic gcloudignore
#
.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line
# below:
.git
.gitignore
.github/

# Python pycache:
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.pyc

# Virtual environments
venv/
.venv/
env/
.env/
**/.venv/

# Node modules
node_modules/
**/node_modules/

# Build artifacts
dist/
build/
*.egg-info/
.nx/
.next/
**/*.egg-info/
**/dist/
**/build/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Test files
tests/
test/
**/tests/
**/test/
test-results/
coverage/
htmlcov/
.coverage
.pytest_cache/
pytest.ini
test_*.py
*_test.py
playwright-report/
vitest*

# Documentation
docs/
*.md
README.md
CONTRIBUTING.md
LICENSE

# Local data
data/
logs/
screenshots/
uploads/
temp_uploads/
video-recordings/

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files
.env*
!.env.production

# Frontend app (we're only deploying backend)
apps/giki-ai-app/

# Scripts (most not needed for production)
scripts/
*.sh

# Excel/CSV files
*.xlsx
*.xls
*.csv

# Service accounts (except production)
service-accounts/
!service-accounts/production/

# Config files not needed
firebase.json
.firebaserc
cloudbuild*.yaml
cloud-run-*.yaml
eslint*
tsconfig*
nx.json
playwright*
package*.json
pnpm*

# Performance and evidence
performance_evidence/
security_analysis.json
api_*.json

# But include necessary app files
!apps/giki-ai-api/src/
!apps/giki-ai-api/pyproject.toml
!apps/giki-ai-api/README.md
!pyproject.toml
!uv.lock
!Dockerfile